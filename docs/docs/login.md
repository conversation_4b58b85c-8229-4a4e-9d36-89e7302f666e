## 登录配置入口
> apps/[appName]/src/config/login.ts

```ts
export const loginConfig = {
  h5: {
    /** 是否 sccode 登录 */
    sccode: true,
    /** 是否微信静默授权登录 */
    silentLogin: false,
    /** 账号登录类型：'phone' | 'account' | false */
    accountLoginType: 'phone',
  },
  mini: {
    /** 是否小程序一键登录 */
    miniLogin: true,
    /** 账号登录类型：'phone' | 'account' | false */
    accountLoginType: 'phone',
    /** 是否使用历史账号 */
    historyAccounts: true,
  },
};
```

# H5 应用
> H5 登录方式
- sccode 登录
- 账号密码登录
- 微信静默授权
- token 过期刷新重新登陆

> 未登录情况
- !(token && phone && uid)

### sccode 登录
> 触发方式
- 链接上带有 sccode
- loginConfig.h5.sccode = true;

```ts
export const loginConfig = {
  h5: {
    /** 是否 sccode 登录 */
    sccode: true,
    ...
  },
  mini: {
    ...
  },
};
```


### 微信静默授权
> 触发方式

- loginConfig.h5.silentLogin === true
- 需要在微信环境下，wechat jssdk 授权成功后，且没有 sccode

```ts
export const loginConfig = {
  h5: {
    /** 是否微信静默授权登录 */
    silentLogin: false,
    ...
  },
  mini: {
    ...
  },
};
```

### token 过期刷新重新登陆
> 触发方式
- 接口请求状态码为 `401` 时，如果存在 `refreshToken`，则会刷新 token 和用户信息。

### 账号密码登录
> 触发方式
- loginConfig.h5.accountLoginType = 'phone' | 'account'

```ts
export const loginConfig = {
  h5: {
    /** 账号登录类型：'phone' | 'account' | false */
    accountLoginType: 'phone',
    ...
  },
  mini: {
    ...
  },
};
```

# 小程序
- 手机号授权登录
- 已绑定授权绑定手机号-自动登录
- token 过期刷新重新登陆
- 账号密码登录

### 手机号授权登录
> 触发方式
- 小程序唤起登录页面默认为手机号授权登录
- loginConfig.mini.miniLogin = true

```ts
export const loginConfig = {
  h5: {
    ...
  },
  mini: {
    /** 是否小程序一键登录 */
    miniLogin: true,
    ...
  },
};
```

### 自动登录
> 触发方式
- 用户已经授权绑定手机号，再次进入应用，且没有登录的时候，触发自动登录;

### token 过期刷新重新登陆
> 触发方式
- 接口请求状态码为 `401` 时，如果存在 `refreshToken`，则会刷新 token 和用户信息。

### 账号密码登录
> 触发方式

- 在登录页面可以通过按钮切换到账号密码登录
- loginConfig.h5.accountLoginType = 'phone' | 'account'
  
```ts
export const loginConfig = {
  h5: {
    ...
  },
  mini: {
    /** 账号登录类型：'phone' | 'account' | false */
    accountLoginType: 'phone',
    ...
  },
};
```

## 唤起登录页方式
- 直接调用 uni.scLogin()
```ts
uni.scLogin()
```

- 使用 sc-auth 组件

```vue
const scAuthRef = ref();
scAuthRef.init();

<sc-auth ref="scAuthRef" />
```

## 权限配置
> 当某个页面不需要登录权限时，可以在 pages.json 文件配置 `skip: true` 跳过权限，
> 此时页面不会主动弹出登录页面。

> 如果页面有需要权限的接口，会返回 `401` 状态码，导致会弹出登录页面，如果想避免这种情况，请把请求写到登录成功的回调里。
```json
{
  "path": "pages/index/index",
  "style": {
    "navigationBarTitleText": "穗时尚+",
    "navigationStyle": "custom",
    "enablePullDownRefresh": true
  },
  // 无需登录
  "meta": {
    "skip": true
  }
}
```


## 登录成功回调
> 使用场景
- 页面配置了 `skip: true` 跳过登录权限，但是某些接口需要再登录后请求的。(接口会401的情况)
- 如果页面和接口无强制需要权限，则无需使用回调，登录成功后会再次刷新页面。
- 其他情况不用关注是否需要回调，正常写生命周期就行，已经确保进入页面是已经登录状态。

### 方式1：通过引入 `sc-auth` 组件，组件的提供两个登录成功的回调事件。
> @success-every
- 登录成功后触发一次，且页面生命周期 `onShow` 触发且此时 `已登录` 会触发

> @success-once
- 登录成功后触发一次，且页面生命周期 `onLoad` 触发且此时 `已登录` 会触发

```vue
function onLoginSuccessEvery() {}

function onLoginSuccessOnce() {}

<sc-auth
  ref="scAuthRef"
  @success-every="onLoginSuccessEvery"
  @success-once="onLoginSuccessOnce">
</sc-auth>
```