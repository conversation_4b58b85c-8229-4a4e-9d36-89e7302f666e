# 开发

## 开发前准备

> 每次打包请拉取 `主模块` 和 `子模块` 最新的代码

```bash
git pull && cd apps/[name] && git pull && pnpm i
```

## 启动服务

```bash
cd apps/[name] && pnpm serve
```

## 环境变量

> 开发环境

- 读取 `.env.development` 文件

### 解决跨域问题

> 方式 1：配置本地代理

```
// 配置代理接口请求可能会很慢
VITE_APP_API=/java
```

> 方式 2：使用插件解决跨域(任选一个)

- [Allow CORS: Access-Control-Allow-Origin](https://chrome.google.com/webstore/detail/allow-cors-access-control/lhobafahddgcelffkeicbaginigeejlf)
- [Cross Domain - CORS](https://chrome.google.com/webstore/detail/cross-domain-cors/mjhpgnbimicffchbodmgfnemoghjakai)
- 直接配置对应的域名

```
VITE_APP_API=https://tst-app.shencom.cn
```
