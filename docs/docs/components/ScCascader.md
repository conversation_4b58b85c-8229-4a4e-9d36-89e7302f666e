## ScCascader
> 级联选择器
## Props

| 名称             | 类型                                                   | 默认值               | 描述                                                                                                 |
| ---------------- | ------------------------------------------------------ | -------------------- | ---------------------------------------------------------------------------------------------------- |
| title            | `string`                                               | `"Default Title"`    | 弹窗标题                                                                                            |
| range            | `RangeItem[]`                                          | -                    | 数据                                                                                                 |
| disabled         | `boolean`                                              | `false`              | 是否禁用                                                                                            |
| placeholder      | `string`                                               | `"Default Placeholder"` | placeholder                                                                                        |
| preload          | `boolean`                                              | -                    | 预加载数据                                                                                           |
| modelValue       | `string[]`                                             | `[]`                 | modelValue                                                                                          |
| rangeKey         | `string`                                               | `"Default Range Key"` | uni-data-picker -> map => text                                                                     |
| rangeValue       | `string`                                               | `"Default Range Value"` | uni-data-picker -> map => value                                                                    |
| contentClass     | `string \| object \| string[] \| object[]`              | `"Default Class"`     | class                                                                                               |
| lastText         | `boolean`                                              | `false`              | 是否添加'全部'选项                                                                                  |
| itemClass        | `string`                                               | `"Default Item Class"` | class                                                                                               |
| defalutColor     | `string`                                               | `"Default Color"`     | placeholder 颜色                                                                                    |
| defalutSize      | `string`                                               | `"Default Size"`      | placeholder 大小                                                                                    |
| iconName         | `string`                                               | `"Default Icon Name"` | iconName                                                                                           |
| iconClass        | `string`                                               | `"Default Icon Class"`| iconClass                                                                                           |
| iconSize         | `string \| number`                                     | `0`                  | iconSize                                                                                            |
| popupclosed      | `boolean`                                              | -                    | 是否每个选项都获取值                                                                                  |
| isHide           | `boolean`                                              | `false`              | 是否隐藏                                                                                            |
| isLesfClose      | `boolean`                                              | -                    | 是否在叶子节点关闭弹窗，设置为 true 需要手动调用 `hide()` 关闭                                        |
| disabledIndex    | `number`                                               | -                    | 禁用选择历史 index                                                                                  |

## Events

| 名称            | 类型                                            | 描述                       |
| --------------- | ----------------------------------------------- | -------------------------- |
| nodeclick       | `(e: RangeItem) => void`                         | 节点点击事件               |
| popupclosed     | `(e: ChangeType) => void`                         | 弹窗关闭事件               |
| popupopened     | `() => void`                                     | 弹窗打开事件               |
| change          | `(e: ChangeType) => void`                         | 值变化事件                 |
| update:modelValue | `(e: string[]) => void`                          | 更新 modelValue 事件        |

## Slots

| 名称      | 描述          |
| --------- | ------------- |
| default   | 默认插槽      |
| placeholder | 占位符插槽    |