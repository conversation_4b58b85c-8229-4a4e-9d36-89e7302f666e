## Props

| 名称 | 类型 | 默认值 | 描述 |
| ---- | ---- | ------ | ---- |
| config | `ScSearchCofing` | 未定义 | 配置项 |
| modelValue | `string` | 未定义 | 搜索框内容 |
| isShowSearch | `boolean` | `true` | 是否显示搜索框 |

## Methods

| 名称 | 参数 | 描述 |
| ---- | ---- | ---- |
| open | - | 打开组件 |
| close | - | 关闭组件 |
| getData | `...args: Parameters<typeof getListData>` | 重新获取数据 |
| select | `d: any` | 选择事件 |
| change | `flag: boolean` | 改变事件 |
| update:modelValue | `val: string` | 更新模型值事件 |

## Events

无

## 插槽

- default: 默认插槽
- top: 顶部插槽
- search-bar: 搜索栏插槽
- center: 中心插槽
- item: 项目插槽
- bottom: 底部插槽