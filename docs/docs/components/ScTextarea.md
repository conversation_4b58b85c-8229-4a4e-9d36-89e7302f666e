## Props

| 名称 | 类型 | 默认值 | 描述 |
| ---- | ---- | ------ | ---- |
| disabled | `boolean` | `false` | 是否禁用 |
| placeholder | `string` | `''` | 占位符 |
| form | `object` | `{}` | 表单对象 |
| maxlength | `string \| number` | `0` | 最大长度 |
| required | `boolean` | 未定义 | 是否必填 |
| className | `string` | `''` | 类名 |
| inputBorder | `boolean` | `false` | 是否显示边框 |
| isHide | `boolean` | `false` | 是否隐藏 |
| prop | `string` | `''` | 属性 |
| clearable | `boolean` | `false` | 是否可清空 |
| showLength | `boolean` | `false` | 是否显示长度 |

## Events

| 事件名 | 参数 | 描述 |
| ------ | ---- | ---- |
| change | `val: string` | 输入框内容改变时触发的事件 |
| update:modelValue | `val: string` | 输入框内容更新时触发的事件 |

## Slots

此组件没有插槽。