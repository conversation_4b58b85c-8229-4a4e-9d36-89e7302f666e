## ScMenus

## Props

| 名称 | 类型 | 默认值 | 描述 |
| --- | --- | --- | --- |
| group | string | - | 组名，必需 |
| name | string | - | 名称 |
| type | string | '' | 展示类型 |
| right | string | '' | 距离右侧的距离 |
| top | string | '' | 距离顶部的距离 |
| format | function | undefined | 过滤菜单数据的函数 |
| itemClass | string | '' | item的类名 |
| iconClass | string | '' | icon的类名 |
| iconSize | string\|number | 0 | icon的大小 |
| rowNumber | number | 0 | 一行展示的数量 |
| cardHeight | number | 0 | 卡片高度 |
| labelClass | string | '' | 字体的类名 |
| boxClass | string | '' | box的类名 |

## Methods

| 名称 | 参数 | 描述 |
| --- | --- | --- |
| init | isInit?: boolean, menu?: Dictionary<Menu[]> \| Menu[] | 初始化方法 |
| checkLimit | item: Menu | 检查限制的方法 |

## Events

| 名称 | 参数 | 描述 |
| --- | --- | --- |
| click | menu: Menu, isAuth?: boolean | 点击事件 |

## 插槽

| 名称 | 描述 |
| --- | --- |
| default | 默认插槽 |