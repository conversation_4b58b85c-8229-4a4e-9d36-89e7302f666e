## Props

| 属性名 | 类型 | 默认值 | 描述 |
| --- | --- | --- | --- |
| modelValue | UploadFile[] | `[]` | modelValue |
| uploadIcon | string | `'i-carbon-camera'` | 上传图标 |
| uploadIconSize | string \| number | `26` | 上传图标大小 |
| uploadIconColor | string | `'#D3D4D6'` | 上传图标颜色 |
| uploadText | string | `''` | 上传区域的提示文字 |
| imageMode | string | `'aspectFill'` | 预览上传的图片时的裁剪模式，和image组件mode属性一致 |
| width | string \| number | `80` | 内部预览图片区域和选择图片按钮的区域宽度，单位rpx，不能是百分比，或者auto |
| height | string \| number | `80` | 内部预览图片区域和选择图片按钮的区域高度，单位rpx，不能是百分比，或者auto |
| previewFullImage | boolean | `true` | 是否全屏预览 |
| accept | `'all'` \| `'media'` \| `'image'` \| `'file'` \| `'video'` | `'image'` | 接受的文件类型，可选值为all media image file video |
| multiple | boolean | `false` | 是否开启图片多选，部分安卓机型不支持，默认为 false |
| disabled | boolean | `false` | 是否启用(显示/隐藏)组件 |
| capture | Capture \| Capture[] | `['camera', 'album']` | 图片或视频拾取模式，当accept为image类型时设置capture可选额外camera可以直接调起摄像头 |
| compressed | boolean | `true` | 当accept为video时生效，是否压缩视频，默认为true |
| maxDuration | number | `60` | 当accept为video时生效，拍摄视频最长拍摄时间，单位秒 |
| sizeType | ('original' \| 'compressed')[] | `['compressed', 'original']` | original 原图，compressed 压缩图，默认二者都有，H5无效 |
| maxCount | number \| string | `52` | 最大选择图片的数量 |
| deletable | boolean | `true` | 是否显示删除图片的按钮 |
| previewImage | boolean | `true` | 是否在上传完成后展示预览图 |
| camera | `'back'` \| `'front'` | `'back'` | 当accept为video时生效，可选值为back或front |
| name | string | `'file'` | 标识符，可以在回调函数的第二项参数中获取 |
| afterRead | (files: ChooseFile[], index: number, name: string) => boolean | `undefined` | 读取后的处理函数 |
| useBeforeRead | boolean | `undefined` | 是否启用(显示/隐藏)组件 |
| beforeRead | (files: ChooseFile[], index: number, name: string) => boolean | `undefined` | 读取前的处理函数 |
| maxSize | string \| number | `Number.MAX_VALUE` | 选择单个文件的最大大小，单位B(byte)，默认不限制 |
| autoUpload | boolean | `true` | 是否自动上传，默认为 true |
| checkFileExist | boolean | `false` | 是否判断重复照片，默认为 false |
| uploadType | `'oss'` \| `'server'` | `'oss'` | 上传方式，默认: oss |
| isUpdateDB | boolean | `true` | 上传方式为 oss 时，是把文件信息更新到数据库 |
| isWrap | boolean | `true` | 是否换行 |
| deleteModelText | string | `''` | 删除提示文案 |
| extension | string[] | `[]` | 根据文件拓展名过滤，仅 type==file 时有效。每一项都不能是空字符串。默认不过滤。 |

## Methods

| 方法名 | 参数 | 描述 |
| --- | --- | --- |
| select | e: 'select', files: ChooseFile[], name: string | 选择文件时触发的事件 |
| beforeRead | e: 'beforeRead', file: any & { callback: (ok: boolean) => void } | 读取文件前触发的事件 |
| afterRead | e: 'afterRead', file: any | 读取文件后触发的事件 |
| oversize | e: 'oversize', file: any | 文件超过大小限制时触发的事件 |
| error | e: 'error', error: any | 发生错误时触发的事件 |
| remove | e: 'remove', file: any, name: string | 删除文件时触发的事件 |
| success | e: 'success', file: any, fileList: UploadFile[] | 上传成功时触发的事件 |
| fail | e: 'fail', file: any, fileList: UploadFile[] | 上传失败时触发的事件 |
| complete | e: 'complete', files: any | 上传完成时触发的事件 |
| load | e: 'load', onUpload: () => void | 加载时触发的事件 |
| update:modelValue | e: 'update:modelValue', fileList: UploadFile[] | 更新modelValue时触发的事件 |
| change | e: 'change', fileList: UploadFile[] | 选中文件发生改变时触发的事件 |
| preview | e: 'preview', file: UploadFile | 预览文件时触发的事件 |

## Slots

| 插槽名 | 描述 |
| --- | --- |
| default | 默认插槽 |
