## ScDataCheckbox
> checkbox
## Props

| 名称                | 类型                                       | 默认值                | 描述                          |
| ------------------- | ------------------------------------------ | --------------------- | ----------------------------- |
| selectedColor       | `{ type: string; default: string; }`        | `string`              | 选中颜色                      |
| multiple            | `{ type: boolean; }`                        | -                     | 是否多选                      |
| mode                | `{ type: 'default' \| 'button' \| 'tag' \| 'list'; default: string; }` | `string`              | 模式                          |
| range               | `{ type: any; default: never[]; }`           | `never[]`             | 数据范围                      |
| form                | `{ type: any; default: {}; }`                | `{}`                  | 表单数据                      |
| required            | `{ type: boolean; }`                         | -                     | 是否显示必填符号              |
| isHide              | `{ type: boolean; default: boolean; }`       | `boolean`             | 是否隐藏                      |
| prop                | `{ type: string; default: string; }`         | `string`              | 属性                          |
| selectedTextColor   | `{ type: string; default: string; }`         | `string`              | 选中文本颜色，如不填写则自动显示 |

## Methods

无

## Events

| 名称                    | 参数                  | 描述           |
| ----------------------- | --------------------- | -------------- |
| change                  | `val: number`         | change 事件    |
| update:modelValue       | `val: number`         | update 事件    |

## Slots

无