## ScBackgroundImage
> 背景图片

## 使用
```ts
// script setup
const url = 'bg.png';
```
```vue
<!-- template -->
<sc-background-image :url="url" size="cover" class="h-100">
  <view>内容</view>
</sc-background-image>
```

## Props

| 名称      | 类型               | 默认值    | 描述                  |
| --------- | ------------------ | --------- | --------------------- |
| repeat    | `string`           | no-repeat | background-repeat     |
| url       | `string`           | -         | URL (必填)            |
| zIndex    | `number`           | -1        | z-index               |
| opacity   | `string \| number` | 1         | 透明度                |
| size      | `string`           | '50%'     | size                  |
| positionX | `string`           | '50%'     | background-position-x |
| positionY | `string`           | '50%'     | background-position-y |
| className | `string`           | -         | 类名                  |

## 插槽

| 名称    | 描述     |
| ------- | -------- |
| default | 默认插槽 |