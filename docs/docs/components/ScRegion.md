## Props

| 名称 | 类型 | 默认值 | 描述 |
| ---- | ---- | ------ | ---- |
| deep | `boolean` | 未定义 | 返回数据层级 |
| disabled | `boolean` | `true` | 是否禁用 |
| placeholder | `string` | `""` | placeholder |
| level | `number \| Ref<number>` | `1` | 返回数据层级 |
| rootId | `string` | 未定义 | ids |
| isAll | `boolean` | `true` | 是否添加'全部'选项 |
| modelValue | `string[]` | `[]` | ids |
| contentClass | `string` | `""` | contentClass |
| lastText | `boolean` | `true` | 是否添加市级 |
| itemClass | `string` | `""` | itemClass |
| defalutColor | `string` | `""` | 默认颜色 |
| defalutSize | `string` | `""` | 默认字体大小 |
| iconName | `string` | `""` | iconName |
| iconClass | `string` | `""` | iconClass |
| iconSize | `string \| number` | `0` | iconSizee |
| popupclosed | `boolean` |  | 是否点击关闭 |
| lastId | `boolean` | `true` | 是否包括自身，配合 rootId 使用 |

## Methods

| 名称 | 参数 | 描述 |
| ---- | ---- | ---- |
| nodeclick | `e?: RangeItem` |  |
| popupopened |  |  |
| update:modelValue | `val: string[]` |  |
| change | `val?: ChangeType`, `localArr?: ResRegionList[]` |  |
| popupclosed | `val?: ChangeType`, `localArr?: ResRegionList[]` |  |

## Events

| 名称 | 参数 | 描述 |
| ---- | ---- | ---- |
| nodeclick |  |  |
| popupopened |  |