## Props

| 名称 | 类型 | 默认值 | 描述 |
| --- | --- | --- | --- |
| type | `PropType<string>` | - | 类型 |
| default | `string` | - | 默认值 |
| range | `PropType<any>` | `[]` | 数据范围 |
| form | `PropType<any>` | `{}` | 表单对象 |
| required | `PropType<boolean>` | - | 是否必填 |
| isHide | `PropType<boolean>` | `true` | 是否隐藏 |
| attr | `PropType<Record<string, any>>` | - | 补充属性 |

## Events

| 名称 | 参数 | 描述 |
| --- | --- | --- |
| change | `val: number` | change 事件 |
| update:modelValue | `val: number` | update:modelValue 事件 |