## ScButton
> 按钮
## Props

| 名称        | 类型                              | 默认值  | 描述                                 |
| ----------- | --------------------------------- | ------- | ------------------------------------ |
| type        | `'default' \| 'green' \| ...`     | -       | 类型                                 |
| block       | `boolean`                         | `false` | 是否块级元素                         |
| loading     | `boolean`                         | `false` | 是否加载中                           |
| round       | `boolean`                         | `false` | 圆角                                 |
| square      | `boolean`                         | `false` | 直角                                 |
| icon        | `string`                          | -       | 图标                                 |
| scope       | `string`                          | -       | 作用域                               |
| size        | `'large' \| 'small' \| ...`       | -       | 大小                                 |
| disabled    | `boolean`                         | `false` | 是否禁用                             |
| className   | `string`                          | -       | 类名                                 |
| openType    | `string`                          | -       | 打开类型                             |
| formType    | `string`                          | -       | 表单类型                             |
| plain       | `boolean`                         | `false` | 是否朴素按钮                         |
| iconPrefix  | `string`                          | -       | 图标前缀                             |

## Events

| 名称              | 类型                     | 描述             |
| ----------------- | ------------------------ | ---------------- |
| click             | `() => void`             | 点击事件         |
| getphonenumber    | `(e: any) => void`       | 获取手机号事件   |
| opensetting       | `(e: any) => void`       | 打开设置事件     |
| getuserinfo       | `(e: any) => void`       | 获取用户信息事件 |
| getAuthorize      | `(e: any) => void`       | 获取授权事件     |
| chooseavatar      | `(e: any) => void`       | 选择头像事件     |

## 插槽

| 名称     | 描述     |
| -------- | -------- |
| default  | 默认插槽 |