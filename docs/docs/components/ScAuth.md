## ScAuth
> 登录回调组件

## 使用
```ts
// script setup
function onLoginSuccessEvery() {}

function onLoginSuccessOnce() {}

const scAuthRef = ref();
```

```vue
<!-- template -->
<sc-auth
  ref="scAuthRef"
  @success-every="onLoginSuccessEvery"
  @success-once="onLoginSuccessOnce">
</sc-auth>
```


## props

无

## Methods

| 名称 | 描述         |
| ---- | ------------ |
| init | 唤起登录组件 |

## Events

| 名称         | 类型         | 描述         |
| ------------ | ------------ | ------------ |
| successEvery | `() => void` | 每次成功事件 |
| successOnce  | `() => void` | 单次成功事件 |

## 插槽

无