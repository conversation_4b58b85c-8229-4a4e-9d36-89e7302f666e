## Props

| 名称 | 类型 | 默认值 | 描述 |
| --- | --- | --- | --- |
| value | `number` | `undefined` | 输入框当前值 |
| color | `string` | `''` | 颜色 |
| disabled | `boolean` | `false` | 是否禁用 |
| background | `string` | `''` | 背景颜色 |
| step | `number` | `1` | 每次点击改变的间隔大小 |
| max | `number` | `Infinity` | 最大值 |
| min | `number` | `-Infinity` | 最小值 |
| modelValue | `number` | `undefined` | 输入框当前值 |
| inputType | `string` | `''` | 输入框的类型 |

## 事件

| 名称 | 参数 | 描述 |
| --- | --- | --- |
| change | `val: number` | 值发生变化时触发的事件 |
| input | `val: number` | 输入框值发生变化时触发的事件 |
| update:modelValue | `val: number` | 更新输入框当前值时触发的事件 |
| blur | `event: FocusEvent` | 失去焦点时触发的事件 |
| focus | `event: FocusEvent` | 获得焦点时触发的事件 |