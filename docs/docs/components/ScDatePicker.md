## ScDatePicker

## Props

| 名称              | 类型                                                                                   | 默认值          | 描述                                                                                                            |
| ----------------- | -------------------------------------------------------------------------------------- | --------------- | --------------------------------------------------------------------------------------------------------------- |
| type              | `{ type: 'date' \| 'datetime' \| 'daterange' \| 'datetimerange'; default: string; }` | `string`        | 类型                                                                                                            |
| start             | `{ type: string \| number; default: string; }`                                          | `string`        | 最小值，可以使用日期的字符串(String)、时间戳(Number)                                                             |
| end               | `{ type: string \| number; default: string; }`                                          | `string`        | 最大值，可以使用日期的字符串(String)、时间戳(Number)                                                             |
| returnType        | `{ type: 'string' \| 'date' \| 'timestamp'; default: string; }`                        | `string`        | 返回值格式                                                                                                      |
| disabled          | `{ type: boolean; default: boolean; }`                                                  | `boolean`       | 是否不可选择                                                                                                    |
| placeholder       | `{ type: string; default: string; }`                                                    | `string`        | placeholder                                                                                                    |
| form              | `{ type: any; default: {}; }`                                                          | `{}`            | form                                                                                                           |
| required          | `{ type: boolean; }`                                                                   | -               | 是否显示必填符号                                                                                                |
| align             | `{ type: 'left' \| 'center' \| 'right'; default: string; }`                            | `string`        | 文字位置                                                                                                        |
| inputBorder       | `{ type: boolean; default: boolean; }`                                                  | `boolean`       | 输入框border                                                                                                    |
| classNames        | `{ type: string; default: string; }`                                                    | `string`        | classNames                                                                                                     |
| iconName          | `{ type: string; default: string; }`                                                    | `string`        | iconName                                                                                                       |
| iconClass         | `{ type: string; default: string; }`                                                    | `string`        | iconClass                                                                                                      |
| iconSize          | `{ type: string \| number; default: number; }`                                          | `number`        | iconSize                                                                                                       |
| isHide            | `{ type: boolean; default: boolean; }`                                                  | `boolean`       | 是否隐藏                                                                                                        |
| prop              | `{ type: string; default: string; }`                                                    | `string`        | prop                                                                                                           |
| clearable         | `{ type: boolean; default: boolean; }`                                                  | `boolean`       | 是否显示清除按钮                                                                                                |
| hideSecond        | `{ type: boolean; }`                                                                   | -               | 是否显示秒，只显示时分                                                                                          |
| clearName         | `{ type: string; default: string; }`                                                    | `string`        | 清除按钮 icon                                                                                                  |
| rangeSeparator    | `{ type: string; default: string; }`                                                    | `string`        | 选择范围时的分隔符                                                                                              |

## Events

| 名称                 | 参数             | 描述           |
| -------------------- | ---------------- | -------------- |
| change               | `val: string`    | change 事件    |
| update:modelValue    | `val: string`    | update 事件    |
| maskClick            | -                | maskClick 事件 |

## Slots

| 名称                 | 描述           |
| -------------------- | -------------- |
| default              | -              |
| clear                | -              |
| icon                 | -              |