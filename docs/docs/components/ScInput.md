## ScInput

## Props

| 名称 | 类型 | 默认值 | 描述 |
| --- | --- | --- | --- |
| trim | `'both' \| 'left' \| 'right' \| 'none' \| ''` | `''` | 是否自动去除空格，传入类型为 Boolean 时，自动去除前后空格 |
| type | `'text' \| 'textarea' \| 'password' \| 'number' \| 'idcard' \| 'digit'` | `'text'` | type |
| disabled | `boolean` | `false` | 是否不可输入 |
| placeholder | `string` | `''` | 输入框的提示文字 |
| form | `any` | `{}` | form |
| maxlength | `number` | `-1` | 最大输入长度，设置为 -1 的时候不限制最大长度 |
| align | `'right' \| 'left' \| 'center'` | `'left'` | 文字位置 |
| modelValue | `string` | `''` | modelValue |
| className | `string` | `''` | className |
| inputBorder | `boolean` | `true` | 是否显示input输入框的边框 |
| iconName | `string` | `''` | 尾部图标 |
| iconClass | `string` | `''` | iconClass |
| iconSize | `string \| number` | `0` | iconSize |
| isHide | `boolean` | `false` | 是否隐藏 |
| prop | `string` | `''` | prop |
| clearable | `boolean` | `false` | 是否显示右侧清空内容的图标控件(输入框有内容且不禁用时显示)，点击可清空输入框内容 |
| placeholderStyle | `string` | `''` | placeholder的样式(内联样式，字符串)，如"color: #ddd" |
| confirmType | `string` | `''` | 设置键盘右下角按钮的文字，仅在type="text"时生效 |
| clearSize | `number` | `0` | 清除图标的大小，单位px |
| autoHeight | `boolean` | `false` | 是否自动增高输入区域，type为textarea时有效 |
| focus | `boolean` | - | 是否自动获得焦点 |

## Events

| 名称 | 参数 | 描述 |
| --- | --- | --- |
| blur | - | blur事件 |
| change | val: string | change事件 |
| click | - | click事件 |
| update:modelValue | val: string | 更新组件的modelValue事件 |

## 插槽

| 名称 | 描述 |
| --- | --- |
| icon | 尾部图标插槽 |