## Props

| 名称 | 类型 | 默认值 | 描述 |
| --- | --- | --- | --- |
| title | `string` | - | 标题，必需属性 |
| color | `string` | `''` | 标题字体颜色 |
| leftIcon | `string` | `''` | 左侧图标，与 sc-icon 的 name 属性相同 |
| autoBackType | `'none' \| 'back' \| 'home'` | `'back'` | 模式，必需属性 |
| iconShow | `boolean` | `true` | 是否显示图标 |

## 事件

| 名称 | 参数 | 描述 |
| --- | --- | --- |
| click-left | - | 点击左侧图标的事件 |

## Slots

| 名称 | 描述 |
| --- | --- |
| default | 默认插槽，用于放置组件的内容 |

