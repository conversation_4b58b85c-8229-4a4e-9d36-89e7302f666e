## Props

| 名称 | 类型 | 默认值 | 描述 |
| --- | --- | --- | --- |
| bgColor | `string` | `''` | 背景颜色 |
| content | `string` | `''` | 内容 |
| copyLink | `boolean` | `true` | 是否允许复制链接 |
| domain | `string` | `''` | 域名 |
| errorImg | `string` | `''` | 错误图片 |
| lazyLoad | `boolean` | `true` | 是否懒加载 |
| loadingImg | `string` | `''` | 加载中图片 |
| pauseVideo | `boolean` | `true` | 是否暂停视频 |
| previewImg | `boolean` | `true` | 是否预览图片 |
| scrollTable | `boolean` | `false` | 是否滚动表格 |
| selectable | `boolean` | `false` | 是否可选 |
| setTitle | `boolean` | `true` | 是否设置标题 |
| showImgMenu | `boolean` | `true` | 是否显示图片菜单 |
| tagStyle | `object` | `{}` | 标签样式 |
| useAnchor | `null` | - | 使用锚点 |

## 方法

| 名称 | 参数 | 描述 |
| --- | --- | --- |
| in | `page: any`, `selector: any`, `scrollTop: any` | 将锚点跳转范围限定在一个 scroll-view 内 |
| navigateTo | `id: any`, `offset: any` | 锚点跳转 |
| getText | - | 获取文本内容 |
| getRect | - | 获取内容大小和位置 |
| setContent | `content: any`, `append: any` | 设置内容 |
| _hook | `name: any` | 调用插件钩子函数 |
| _set | `nodes: any`, `append: any` | 设置内容 |
| _onMessage | `e: any` | 接收到 web-view 消息 |