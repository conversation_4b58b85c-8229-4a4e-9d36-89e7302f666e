## Props

| 名称 | 类型 | 默认值 | 描述 |
| ---- | ---- | ------ | ---- |
| padding | `string` | `''` | 边距 |
| current | `number` | 未定义 | 当前步骤，必需 |
| height | `number` | `0` | 高度 |
| fontSize | `number` | `0` | 字体大小 |
| background | `string` | `''` | 背景色 |
| activeColor | `string` | `''` | 活跃颜色 |
| tabs | `string[]` | 未定义 | 数据，必需 |
| normalColor | `string` | `''` | 默认颜色 |
| styleType | `string` | `''` | 样式类型 |
| tabsClass | `string` | `''` | tabClass |
| noFlex | `boolean` | `false` | 是否 `flex: none` |
| activeText | `number` | `0` | 活跃文案大小 |
| activeWeight | `string` | `''` | 活跃文案 weight |

## 事件

- `click-item(currentIndex: number)`: 点击项目的回调函数