## Props

| 名称 | 类型 | 默认值 | 描述 |
| --- | --- | --- | --- |
| type | `'date' \| 'picker'` | `'date'` | 类型 |
| cancelText | `string` | `'取消'` | 取消按钮文案 |
| cancelColor | `string` | `''` | 取消按钮颜色 |
| confirmText | `string` | `'确定'` | 确认按钮文案 |
| confirmColor | `string` | `''` | 确认按钮颜色 |
| border | `boolean` | `true` | 是否显示边框 |
| disabled | `boolean` | `false` | 是否禁用 |
| placeholder | `string` | `''` | placeholder |
| form | `any` | `{}` | form |
| align | `'left' \| 'center' \| 'right'` | `'left'` | 内容对齐方式 |
| modelValue | `any` | `''` | modelValue |
| isHide | `boolean` | `false` | 是否隐藏 |
| prop | `string` | `''` | prop |
| rangeMap | `[string, string, string] \| [string, string]` | `() => []` | 数据对应的字段 |
| itemHeight | `string` | `''` | itemHeight |
| ranges | `any[] \| Ref<any[]>` | `() => []` | 数据 |
| itemFontSize | `string` | `''` | itemFontSize |
| startYear | `number` | `0` | date 模式下设置最小年份 |
| endYear | `number` | `0` | date 模式下设置最大年份 |
| closeOnClickMask | `boolean` | `true` | 是否点击遮罩层关闭 |
| maxLength | `number` | `0` | 文案显示最大长度 |

## Slots

| 名称 | 描述 |
| --- | --- |
| default | 自定义组件的内容 |

## Methods

| 名称 | 参数 | 描述 |
| --- | --- | --- |
| open | - | 打开方法 |
| close | - | 关闭方法 |

## Events

| 名称 | 参数 | 描述 |
| --- | --- | --- |
| change | `val: any, index?: number \| undefined` | change 事件 |
| confirm | `val: any, index?: number \| undefined` | confirm 事件 |
| cancel | `val: any` | cancel 事件 |
| update:modelValue | `val: any` | update:modelValue 事件 |