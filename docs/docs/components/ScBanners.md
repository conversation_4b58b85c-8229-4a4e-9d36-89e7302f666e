## ScBanners
> 轮播图


## Props

| 名称             | 类型                               | 默认值  | 描述                           |
| ---------------- | ---------------------------------- | ------- | ------------------------------ |
| group            | `string`                           | -       | 分组 (必填)                    |
| autoplay         | `boolean`                          | `true`  | 是否自动切换                   |
| interval         | `number`                           | `0`     | 自动切换时间间隔                |
| current          | `number`                           | `0`     | 当前所在滑块的 index           |
| className        | `string`                           | -       | 类名                           |
| imageClass       | `string`                           | -       | 图片类名                       |
| indicatorDots    | `boolean`                          | `true`  | 是否显示面板指示点             |

## Methods

| 名称  | 描述           |
| ----- | -------------- |
| init  | 初始化方法     |

## Events

| 名称   | 类型                                                    | 描述                     |
| ------ | ------------------------------------------------------- | ------------------------ |
| click  | `(banner: Banner, type?: 'authorize' | undefined) => void` | 点击事件                 |

## 插槽

无