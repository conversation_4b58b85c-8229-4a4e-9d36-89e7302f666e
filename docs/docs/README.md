# 起步

> 项目介绍

## clone 主模块

```bash
git clone https://gitlab.shencom.cn/web/recycle/monorepo-uni/recycle-uni-main.git
```

## 安装依赖

```bash
pnpm i
```

## 操作子模块(拉取、添加、删除)

```bash
// 拉取子模块后再安装一遍依赖
pnpm sub
```

> 添加子模块规范

- 模块名
- [模板仓库](https://gitlab.shencom.cn/web/recycle/monorepo-uni/recycle-sub-template.git)
- 复制模板仓库 -> 重命名 -> 重新初始化 git 仓库
- 项目 git 仓库目录 [monorepo-uni](https://gitlab.shencom.cn/web/recycle/monorepo-uni)
- 默认目录 `apps`
- 默认分支 `release`

## 启动服务

```bash
cd app/[子模块名] && pnpm serve
```

## 配置 oss 秘钥

```bash
// 配置 oss 秘钥后方可打包上传代码到 oss
pnpm oss
```

# 文档目录

```text
├── docs
│   ├── README.md
│   ├── components
│   │   ├── README.md
│   │   ├── ScAuth.md # 登录唤起和回调
│   │   ├── ScAvatar.md # 头像
│   │   ├── ScBackgroundImage.md # 背景图
│   │   ├── ScBanners.md  # 轮播图
│   │   ├── ScBindPhone.md  # 绑定手机号
│   │   ├── ScButton.md # 按钮
│   │   ├── ScButtonFixed.md  # 安全区域
│   │   ├── ScButtonSubscribe.md # 订阅消息
│   │   ├── ScCascader.md # 级联选择器
│   │   ├── ScCategory.md # 分类
│   │   ├── ScCopyright.md # 版权说明
│   │   ├── ScDataCheckbox.md # 多选框
│   │   ├── ScDatePicker.md # 日期选择器
│   │   ├── ScDateSelect.md # 日期选择器
│   │   ├── ScDivider.md  # 分割线
│   │   ├── ScEmpty.md  # 空状态
│   │   ├── ScFilterTabs.md # 筛选
│   │   ├── ScForm.md # 表单
│   │   ├── ScGroup.md # 分组
│   │   ├── ScIcon.md # 图标
│   │   ├── ScInput.md # 输入框
│   │   ├── ScIntegralTips.md # 积分提示
│   │   ├── ScList.md # 列表
│   │   ├── ScLogo.md # logo
│   │   ├── ScMedal.md # 勋章
│   │   ├── ScMenus.md # 菜单
│   │   ├── ScMessage.md # 消息提示
│   │   ├── ScNavBar.md # 导航栏
│   │   ├── ScNumberBox.md # 数字输入框
│   │   ├── ScParse.md  # 富文本
│   │   ├── ScPermission.md # 权限
│   │   ├── ScPicker.md # 选择器
│   │   ├── ScRate.md # 评分
│   │   ├── ScRegion.md # 地区选择器
│   │   ├── ScSearch.md # 搜索选择列表
│   │   ├── ScSelect.md
│   │   ├── ScSteps.md # 步骤条
│   │   ├── ScTabs.md # 标签页
│   │   ├── ScTag.md  # 标签
│   │   ├── ScTextarea.md # 多行输入框
│   │   └── ScUpload.md # 上传
│   ├── packages
│   │   ├── @shencom.md
│   │   ├── README.md
│   │   ├── rms
│   │   │   ├── README.md
│   │   │   ├── api.md # 项目接口
│   │   │   ├── components.md # 项目组件
│   │   │   ├── login.md  # 登录
│   │   │   ├── register.md # 注册
│   │   │   ├── router.md # 路由
│   │   │   ├── service.md # 一些封装的接口
│   │   │   └── utils.md # 一些封装的方法
│   │   ├── tailwindcss.md # tailwindcss注意事项
│   │   ├── uniapp.md # uniapp注意事项
│   │   ├── unocss.md # unocss注意事项
│   │   └── vue.md # vue注意事项
│   ├── login.md # 登录说明
│   ├── utils.md # 方法说明
│   ├── 开发.md # 开发阶段说明
│   └── 构建.md # 构建打包阶段说明
└── example # 组件实例
```
