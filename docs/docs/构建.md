# 构建

## 准备

> 每次打包请拉取 `主模块` 和 `子模块` 最新的代码

```bash
git pull && cd apps/[name] && git pull
```

## 打包子应用

> 测试服

```sh
cd apps/[name] && pnpm tst
```

> 正式服

- 直接更新 oss

```sh
cd apps/[name] && pnpm build
```

- 独立部署政务云（上传后需使用独立部署 oss 下载工具下载代码，并压缩给负责更新的后端部署）

```sh
cd apps/[name] && pnpm build -d 项目文件夹
# 部署oss路径为: /plugins/scloud/platform/项目文件夹
# eg: pnpm build -d futian-h5
```

- 服务端独立部署，前端指向 oss，直接指定独立部署的 oss 项目文件夹

```sh
cd apps/[name] && pnpm build --deployOss 独立部署oss项目文件夹
# 部署oss路径为: /plugins/scloud/项目文件夹
# eg: pnpm build --deployOss bfbapp
```

> 直接上传，添加参数 `--u`

```sh
# 不用再次打包构建，直接上传构建完的代码，前提是已经打包构建过一次代码
pnpm run test --u
```

## 打包分支规范

- 主模块
  - 一般在 release 分支
- 子模块
  - 测试服默认在 `develop`
  - 正式服默认在 `release`
- 发版正式服是需要打版本

```bash
pnpm release && git push
```

## 环境变量

- 本地在 `.env.development` 文件中查看
- 测试服在 `.env.tst` 文件中查看
- 正式服 `.env.production` 文件中查看

> VITE_APP_API

- 请求域名
- 本地默认为测试服域名
- 线上 `h5` 默认为空，取访问链接上的域名，`小程序` 则是对应的域名

> VITE_APP_SCID

- 应用默认租户(scid)
- `h5` 可通过链接上 `query` 参数改变
  - eg: `https://tst-app.shencom.cn/recycle-supervisor/index.html?scid=sc8820513B9B1903E4#/`
- `小程序` 默认固定不可变

> VITE_APP_IS_DEV

- 是否为 `Dev` 环境

> VITE_APP_IS_PRO

- 是否为 `Pro` 环境
-

## 项目部署位置

### 测试 OSS

```text
/scplugins/plugins/test/scloud/app
```

### 正式 OSS

```text
/scplugins/plugins/scloud/app
```
