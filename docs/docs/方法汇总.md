## @rms/utils

### AdapterScanCode
> 扫码获取二维码内容
```ts
import { AdapterScanCode } from '@/utils';

AdapterScanCode({
  needResult: 1,
  scanType: ['qrCode'],
  success: (res) => {
    const text = res.result.replace(/\n/g, ' ');

    house.value.source = text;
    const info = text.replace(/([^\s：]+：)/g, '').split(' ');
    if (info.length >= 2) {
      [house.value.address, house.value.code] = info;
    }
  },
});
```

### AdapterLogin
> 小程序登录

```ts
import { AdapterLogin } from '@/utils';

const data = await AdapterLogin();
```


### AdapterCheckSession
> 小程序检查登录状态是否过期

```ts
import { AdapterCheckSession } from '@/utils';

const data = await AdapterCheckSession();
```

### InitMap
> 初始化地图

```ts
import { InitMap } from '@/utils';

InitMap({
  cdn,
  id,
});
```

### _AMap
> 高德地图

```ts
import { _AMap } from '@/utils';

// 获取经纬度
const lngLat = await _AMap.geolocation();
```



### getImageInfo
> [获取图片信息](https://uniapp.dcloud.net.cn/api/media/image.html#getimageinfo)

```ts
import { getImageInfo } from '@/utils';

const bgImg = await getImageInfo(bg2Img);
const { width, height, path } = bgImg;
```

### rpx2px
> 根据2倍屏幕像素比获取尺寸大小

```ts
import { rpx2px } from '@/utils';

const width = rpx2px(w);
```

### canvasToTempFilePath
> [导出图片](https://uniapp.dcloud.net.cn/api/canvas/canvasToTempFilePath.html#canvastotempfilepath)

```ts
import { canvasToTempFilePath } from '@/utils';

const filePath = await canvasToTempFilePath(canvasId);
```

### compressPic
> 图片压缩

- 只适配 h5
```ts
import { compressPic } from '@/utils';

const blod = await compressPic(file)
```

### decrypt
> jsencrypt

```ts
import { decrypt } from '@/utils';

const res = await decrypt(password, key);
```

### Dialog
> 显示模态弹窗

```ts
import { Dialog } from '@/utils';

const flag = await Dialog('这是内容', {
  showCancel: true,
  confirmColor: '#387ef5',
  confirmText: '我知道了',
},)
```

### exceptionHandler
> 全局错误处理

```ts
import { exceptionHandler } from '@/utils';

try {
  await http();
} catch(error) {
  exceptionHandler(error);
}
```

### exceptionHandler
> 全局错误处理, 会 toast 展示错误信息

```ts
import { exceptionToast } from '@/utils';

try {
  await http();
} catch(error) {
  exceptionToast(error, '错误补充');
}
```

### returnErrmsg
> 获取错误信息

```ts
import { returnErrmsg } from '@/utils';

try {
  await http();
} catch(error) {
  const errmsg = returnErrmsg(error);
}
```

/** 环境开发环境判断 */
export const IsDev = import.meta.env.MODE === 'development';
export const IsPro = !!import.meta.env.VITE_APP_IS_PRO;
export const IsTst = !IsDev && !IsPro;

export const Platform = process.env.UNI_PLATFORM;

export const IsH5 = Platform === 'h5';
export const IsAlipay = Platform === 'mp-alipay';
export const IsWeixin = Platform === 'mp-weixin';
export const IsAppPlus = Platform === 'app-plus';


### IsDev
> 是否为开发环境
```ts
import { IsDev } from '@/utils';

if(IsDev) {
  ...
}
```

### IsTst
> 是否为测试环境
```ts
import { IsTst } from '@/utils';

if(IsTst) {
  ...
}
```

### IsPro
> 是否为正式环境
```ts
import { IsPro } from '@/utils';

if(IsPro) {
  ...
}
```

### Platform
> 当前环境(h5|mp-alipay|mp-weixin|app-plus..)

```ts
import { Platform } from '@/utils';
```

### IsH5
> 是否为 H5
```ts
import { IsH5 } from '@/utils';

if(IsH5) {
  ...
}
```

### IsAlipay
> 是否为支付宝小程序
```ts
import { IsAlipay } from '@/utils';

if(IsAlipay) {
  ...
}
```

### IsWeixin
> 是否为微信小程序
```ts
import { IsWeixin } from '@/utils';

if(IsWeixin) {
  ...
}
```

### IsAppPlus
> 是否为app
```ts
import { IsAppPlus } from '@/utils';

if(IsAppPlus) {
  ...
}
```

### rpx2rem
> rpx2rem
```ts
import { rpx2rem } from '@/utils';

rpx2rem(20)

```
### rpx2rem2
> rpx2rem2
```ts
import { rpx2rem2 } from '@/utils';

rpx2rem2('20 30')
```

### ToMoney
> 处理金额格式

- @param {String,Number} val 金额
- @param {Number} decimalLength 小数点位数(默认:2)

```ts
import { ToMoney } from '@/utils';

const res = ToMoney(1000); // 1,000.00
const res = ToMoney(1000, 2); // 1,000.00
const res = ToMoney(1000, 0); // 1,000
```

### toKmUnit
> 添加距离单位(m|km)

```ts
import { ToMoney } from '@/utils';

const res = toKmUnit(2000); // 2km
const res = toKmUnit(200); // 200m
```

### StartFillZero
> 头部补零

- @param {number} num - 需补 0 的数值
- @param {number} size - 加上 0 位的位数

```ts
import { StartFillZero } from '@/utils';

const res = StartFillZero(15, 3); // '015'
```

### EndFillZero
> 尾部补零

- @param {number} num - 需补 0 的数值
- @param {number} size - 加上 0 位的位数

```ts
import { EndFillZero } from '@/utils';

const res = EndFillZero(15, 3); // '150'
```

### GetUrlKey
> 获取链接 query 上对应 name 的 值

- @param {string} name - 需获取的 query 的 name
- @param {string} href - url 链接

```ts
import { GetUrlKey } from '@/utils';

const res = GetUrlKey(
  'k', 
  'https://tst-app.shencom.cn/a/index.html#/points-record?k=123'
); // 123
```

### GetUrlParam
> 获取链接 query，转换为对象

```ts
import { GetUrlParam } from '@/utils';

// { k: 123, q: 123 }
GetUrlParam('https://tst-app.shencom.cn/a/index.html#/points-record?k=123&q=321');
```

### formatNewsUrlParams
> 解析路由字段

```ts
import { formatNewsUrlParams } from '@/utils';

const [year, month, date, newsId] = formatNewsUrlParams(url);
```

### handleRegionData
> 根据 id 返回对应的原始区域数据

```ts
import { handleRegionData } from '@/utils';

const regionRange = handleRegionData([regionPid, regionId, regionCid], region);
```

### ObjectToArray
> 对象转数组

```ts
import { ObjectToArray } from '@/utils';

const arr = ObjectToArray(obj);
```

### parseUrlParams
> 解析带参二维码路由参数

```ts
import { parseUrlParams } from '@/utils';

const arr = parseUrlParams(obj);
```

### objectToUrlParams
> 对象转 url query 参数

```ts
import { objectToUrlParams } from '@/utils';

const str = objectToUrlParams(obj);
```

### throttle
> 节流函数

```ts
import { throttle } from '@/utils';

const throttleFn = throttle(fn, 200);
```

### debounce
> 防抖函数

- @param func 执行函数
- @param delay 延迟时间 ms
- @param options { isImmediate: 是否立即执行, maxWait: 最大等待时间, callback: 回调 }

```ts
import { debounce } from '@/utils';

const debounceFn1 = throttle(fn, 200);
const debounceFn2 = throttle(fn, 200, { isImmediate: true });
```

### getOptionLabel

- @param value string | number
- @param options array
- @returns string
- 
```ts
import { getOptionLabel } from '@/utils';

const str = getOptionLabel(value, options)
```

### _GetUrlKey
> 获取路由参数
- 只支持 h5

```ts
import { _GetUrlKey } from '@/utils';

const val = _GetUrlKey('key')
```

### getAuthLink
> 处理跳转链接，h5跳转若需要权限，路由拼接sccode
- @param {string} url 目标页面路径
- @param {boolean} isshare 是否添加分享参数

```ts
import { getAuthLink } from '@/utils';

getAuthLink(url, isShare)
```


### Loading
> loading
- Toast 会覆盖 Loading。

```ts
import { Loading } from '@/utils';

// 打开 loading
Loading.show('加载中');

// 关闭 loading
Loading.hide();
```

### getLocationInfo
> 小程序获取定位
>
- @param isContrary 是否逆解析
- @param options UniApp.GetLocationOptions & CustomOptions
- @returns 位置信息，逆解析结果

```ts
import { getLocationInfo } from '@/utils';

const res = getLocationInfo(true, {
  isHighAccuracy: true,
  highAccuracyExpireTime: 10000,
});
```

### Navigator
> 路由跳转

```ts
import { Navigator } from '@/utils';

Navigator.push('/pages/index/index');

Navigator.replace('/pages/index/index', {
  success: () => {},
  fail: () => {},
});

Navigator.back(1);
```

### queueLoad
> 队列加载 script, 防止重复加载。

```ts
import { queueLoad } from '@rms/utils';

queueLoad({
  id,
  src: cdn,
  name: 'amap',
});
```

### replaceCss | replaceJs
> 解决小程序类名转义失败，需手动转义情况

```ts
import { queueLoad } from '@rms/utils';

const newClass =replaceJs('justify-center');
```

### SentryInstall
> h5 接入 Sentry

```ts
import { SentryInstall } from '@/utils';

const app = createSSRApp(App);

// #ifdef H5
SentryInstall({
  Vue: app,
  version,
  projectName: name,
  dsn,
  UserInfo,
  scid,
});
// #endif
```


### Stroage
> 本地存储方法

```ts
import { Stroage } from '@/utils';

// 获取
Stroage.get(key);
// 设置
Stroage.set(key, value);
// 设置期限
Stroage.set(key, value，60);
// 移除
Stroage.remove(key);

// 移除所有 storage @param isAll 移除所有缓存是否包括 `lasting_` 后缀的 storage
Stroage.clear(isAll);
// 选择性移除 storage @param{string[]} ignore 要忽略的key，前缀匹配
Stroage.matchClear(ignores);

// 获取 key 前缀为 `user_` 的 storage
Stroage.getUser(key);
// 设置 key 前缀为 `user_` 的 storage
Stroage.setUser(key, value);
// 移除 key 前缀为 `user_` 的 storage
Stroage.removeUser(key);

// 获取 key 前缀为 `data_` 的 storage
Stroage.getData(key);
// 设置 key 前缀为 `data_` 的 storage
Stroage.setData()key, value;
// 移除 key 前缀为 `data_` 的 storage
Stroage.removeData(key);

// 获取 key 前缀为 `lasting_` 的 storage
Stroage.getLasting(key);
// 设置 key 前缀为 `lasting_` 的 storage
Stroage.setLasting(key, value);
// 移除 key 前缀为 `lasting_` 的 storage
Stroage.removeLasting(key);

// 获取 key 前缀为 `state_` 的 storage
// state数据用于通信，只存在一次，且初始化会被清空
Stroage.getState(key);
// 设置 key 前缀为 `state_` 的 storage
Stroage.setState(key, value);
// 移除 key 前缀为 `state_` 的 storage
Stroage.removeState(key);
```

### Toast
> 提示

```ts
import { Toast } from '@/utils';

Toast.show('提示');
Toast.success('成功提示');
Toast.error('失败提示');
Toast.warning('警告提示');
```

### updateMiniprogram | clearOldStorage
> updateMiniprogram: 检查小程序最新版本，并强制更新到最新版本

> clearOldStorage: 清除旧版本的缓存数据，包括旧环境（正式服/测试服切换）、旧小程序版本

- 只支持小程序
```ts
import { updateMiniprogram, clearOldStorage } from '@/utils';

// #ifdef MP-WEIXIN
updateMiniprogram();
clearOldStorage(version, Storage);
// #endif
```

### utilsOss
> oss相关路径

```ts
import { utilsOss } from '@/utils';

/** 本地或 oss 项目图片路径 */
utilsOss.imgPath;

/** oss 图片路径 */
utilsOss.ossImgPath;

/** oss 资源根路径 */
utilsOss.ossPath;

/** 本地项目图片路径 */
utilsOss.localityPath;

/** 项目 logo */
utilsOss.logo;


/** 图片缩放，默认: `m:mfit w:100 h:100` */
utilsOss.zoom;

/** 图片百分比缩放，默认: `50%` */
utilsOss.zoomPercent(40);

/** 自定义图片缩放
 * - [参考链接](https://help.aliyun.com/document_detail/44688.html)
 */
utilsOss.zoomCustom(param);

/** 是否为 oss 链接 */
utilsOss.isOssHost(url);

/** 图片是否可缩放 */
utilsOss.isImageZoom(url);

/** 处理oss图片后缀名浏览器不兼容 */
utilsOss.handleImageExt(url, 'png');

/** 处理oss图片缩放 */
utilsOss.handleImageZoom(url, utilsOss.zoomPercent(30));
```

### previewMedia
> 预览图片和视频
- 只支持小程序

```ts
import { previewMedia } from '@/utils';

previewMedia(url);
```


### chooseLocation
> 打开地图选择位置

```ts
import { chooseLocation } from '@/utils';

const { latitude, longitude, address } = await chooseLocation();
```

### compareVersion
> 版本号比较
> 
```ts
import { compareVersion } from '@/utils';

// -1 0 1
const res = await compareVersion(v1, v2);
```

### openDocument
> 预览 PDF、Word 和 Excel 文件

```ts
import { openDocument } from '@/utils';

openDocument(filepath);
```

### getWxLocation
> 微信 h5 jssdk 获取定位方法

```ts
import { getWxLocation } from '@/utils';

const location = await getWxLocation({
  $wx: uni.$wx,
  type: 'gcj02',
});
```

### reviewOrCopyUrl
- 预览或下载文件,兼容苹果手机预览文件损坏问题

```ts
import { reviewOrCopyUrl } from '@/utils';

// 支持 review 的格式 => ['pdf', 'docx', 'xlsx', 'pptx', 'ppt'];
reviewOrCopyUrl(url)
```

### scanCode
> 扫码 -> 建议使用 `AdapterScanCode`


### getPrivacySetting
> 获取用户隐私协议

```ts
import { getPrivacySetting } from '@/utils';

if(IsWeixin) {
  const { needAuthorization, privacyContractName } = await getPrivacySetting();
}
```

### vConsoleInit
> 加载 console

```ts
import { vConsoleInit } from '@/utils';

vConsoleInit({
  // 测试环境是否开启
  tst: true,
  // 本地环境是否开启
  dev: false,
  // 正式环境是否开启
  pro: false,
});
```

### Wechat
> h5 微信 jsssdk 初始化

```ts
import { Wechat } from '@/utils';

// 初始化 wechat
Wechat.init({
  scid,
  url,
});
```