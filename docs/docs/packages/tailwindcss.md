# tailwindcss
> 遇到 `tailwindcss` 的问题，请先到官网寻找答案
- [tailwindcss 中文官网](https://www.tailwindcss.cn/docs/installation)

## 注意事项

### 单位大小
> p-1 => 1 表示 `4px` | `8rpx`
> 尽量少使用 `p-[8rpx]` 这种固定写法，多使用 `p-1` 这种写法

### 颜色
- [查看相关颜色](https://www.tailwindcss.cn/docs/customizing-colors)
- [修改主题色方法](https://www.tailwindcss.cn/docs/theme)

> 禁止使用 `bg-[#0ff0ff]`这种固定写法，全部使用 `bg-slate-200` 这种写法，
> 如果颜色满足不了需求，可以修改项目主题色 


## 自定义类
> 项目自定义的 taillwindcss 类名

### ellipsis-[1-9]
> 文字超出，显示省略号, ellipsis-1 到 ellipsis-9，数字表示多少行

```vue
<view class="ellipsis-1"></view>
```
> 编译后
```css
.ellipsis-1 {
  overflow: 'hidden',
  'text-overflow': 'ellipsis',
  'word-break': 'break-all',
  display: '-webkit-box',
  '-webkit-line-clamp': 1,
  '-webkit-box-orient': 'vertical',
}

```

### icon-[10-99]
> 给定 `width` 和 `height`, 常用于给定 icon 的大小或小盒子的大小。 
```vue
<view class="icon-10"></view>
```
> 编译后
```css
.icon-10 {
  width: 20rpx;
  height: 20rpx;
}
```


### bg-base
> 常用背景色
```vue
<view class="bg-base"></view>
```
```css
.bg-base {
  background-color: #f3f4f6;
}
```