# shencom 相关npm包


## [@shencom/cli](https://gitlab.shencom.cn/web/tools/cli)
- SC · 脚手架

## [@shencom/request](https://gitlab.shencom.cn/web/tools/npm-monorepo/-/tree/master/packages/shencom-request)
- 封装 axios 请求，兼容了 uni-app 的 uni.request 和 uni.uploadFile 的适配器


## [@shencom/api](https://gitlab.shencom.cn/web/tools/npm-monorepo/-/tree/master/packages/shencom-api)
- Shencom api group

> 使用方式
```ts
import { ApiWechatLogout } from '@shencom/api';
```

## [@shencom/npmlog](https://gitlab.shencom.cn/web/tools/npm-monorepo/-/tree/master/packages/shencom-npmlog)
- Shencom node npm log
 
> 使用方式
```ts
import log from '@shencom/npmlog';
log.info('info')
```

## [@shencom/typing](https://gitlab.shencom.cn/web/tools/npm-monorepo/-/tree/master/packages/shencom-typing)
- Shencom typescript types

> 使用方式
```ts
import { ReturnPromiseType } from '@shencom/typing';
```

## [@shencom/utils](https://gitlab.shencom.cn/web/tools/npm-monorepo/-/tree/master/packages/shencom-utils)
- SC · 工具库, 下面的 @shencom/utils-* 为子包，均已在 @shencom/utils 导出

> 使用方法
```ts
// @shencom/utlis 已在 @/utisl 导出
const { ObjectToArray } from '@/utils';
```

### @shencom/utils-tenant
- Tenant租户信息

### @shencom/utils-array
- 数组相关工具方法

### @shencom/utils-formatter
- 数据格式化

### @shencom/utils-judge
- 判断数据格式等工具

### @shencom/utils-browser
- 获取浏览器系统信息

### @shencom/utils-oss
- Oss静态资源工具

### @shencom/utils-storage
- Storage工具

### @shencom/utils-date
- 日期时间处理工具

### @shencom/utils-platform
- 平台判断工具

### @shencom/utils-validate
- 正则校验工具

### @shencom/utils-userinfo
- 用户统一管理工具包

### @shencom/utils-tree
- 树形数据结构相关工具方法

### @shencom/utils-object
- 对象相关工具方法

### @shencom/utils-string
- 字符串相关工具方法

### @shencom/utils-resource
- 资源统一管理工具包