## @rms/service
> api

### ServiceImgByIds
> 通过 id 获取文件, 并缓存到本地

```ts
import { ServiceImgByIds } from '@rms/service';

const data = await ServiceImgByIds(ids);
```

### ServiceSetGis
> 设置 gis 信息

```ts
import { ServiceSetGis } from '@rms/service';

const { id } = await ServiceSetGis(
  {
    latitude: lat,
    longitude: lng,
  },
  address,
);
```

### ServiceGisByIds
> 通过 id 获取 gis, 并缓存到本地

```ts
import { ServiceGisByIds } from '@rms/service';

const data = await ServiceGisByIds(ids);
```

### ServiceGetBanner
> 获取 banner, 并缓存到本地

```ts
import { ServiceGetBanner } from '@rms/service';

const data = await ServiceGetBanner(group, isInit);
```

### ServiceGetMenu
> 获取 menu, 并缓存到本地

```ts
import { ServiceGetMenu } from '@rms/service';

const data = await ServiceGetMenu(group, isInit);
```

### ServiceGetResource
> 通过 id 获取资源, 并缓存到本地

```ts
import { ServiceGetResource } from '@rms/service';

const data = await ServiceGetResource(ids, isInit);
```

### ServiceGetCategoryOrArticles
> 获取专栏和文章

```ts
import { ServiceGetCategoryOrArticles } from '@rms/service';


// 获取专栏
const data = await ServiceGetCategoryOrArticles('category');
// 获取文章
const data = await ServiceGetCategoryOrArticles('articles', { id });
```

### ServiceRegion
> 获取区域
-  @param isAll  boolean -> 是否每项加上父级项
-  @param level  1 | 2 | 3 | 4 -> 取几层，基于 深圳市-区-街道-社区 对应 1-2-3-4，不受 rootId 的影响
-  @param rootId string -> 查找初始层级
-  @param deep  boolean -> 是否包括自身，配合 rootId 使用

```ts
import { ServiceRegion } from '@rms/service';

const regionOption = await ServiceRegion();
```