# @rms/api


## login
- 部分登录相关api

### ApiGetWxCode
- 获取微信静默授权 `code`
```ts
import { ApiGetWxCode } from '@rms/api';

// 自动重定向
await ApiGetWxCode();
```

### ApiGetWxInfo
- 微信静默授权，通过 `code` 获取用户信息

```ts
import { ApiGetWxInfo } from '@rms/api';

const data = await ApiGetWxInfo(code);
```


## medal
- 勋章墙


### ApiGetMedalLook
- 获取未查看勋章

```ts
import { ApiGetMedalLook } from '@rms/api';

const medalInfo = await ApiGetMedalLook({ business });
```

### ApiGetMedalShow
- 查看勋章

```ts
import { ApiGetMedalShow } from '@rms/api';

const medal = await ApiGetMedalShow({ id: medalInfo.medalId });
```


## region
- 区域接口

### ApiRegionList
- 获取地区

```ts
import { ApiRegionList } from '@rms/api';

const data = await ApiRegionList(params);
```

### ApiRegionGuangzhouList
- 广州区域接口

```ts
import { ApiRegionGuangzhouList } from '@rms/api';

const data = await ApiRegionGuangzhouList(params);
```

## resource
- 公共资源接口

### ApiResourceMenu
- php 菜单接口

```ts
import { ApiResourceMenu } from '@rms/api';

const data = await ApiResourceMenu({
  group: groups,
  size: 2e4,
});
```

### ApiResourceMenuJava
- java 菜单接口 (目前只有广州住户在用)

```ts
import { ApiResourceMenuJava } from '@rms/api';

const data = await ApiResourceMenuJava({
  group: groups,
  size: 2e4,
});
```

### ApiResourceBanner
- php 轮播图接口

```ts
import { ApiResourceBanner } from '@rms/api';

const data = await ApiResourceBanner({
  group: groups,
  size: 2e4,
});
```

### ApiResourceBannerJava
- java 轮播图接口 (目前只有广州住户在用)

```ts
import { ApiResourceBannerJava } from '@rms/api';

const data = await ApiResourceBannerJava({
  group: groups,
  size: 2e4,
});
```

### ApiResourceFile
- 获取文件资源

```ts
import { ApiResourceFile } from '@rms/api';

const { data } = await ApiResourceFile({
  query: ApiQueryHandler(ids, 'id', 'select'),
});
```


## tracker
- 埋点

### ApiEventTrack
- 事件埋点

```ts
import { ApiEventTrack } from '@rms/api';

ApiEventTrack(params);
```