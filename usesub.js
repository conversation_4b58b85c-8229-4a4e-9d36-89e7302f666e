const spawn = require('child_process').spawn;
const fs = require('fs');

const gitModulesPath = '.gitmodules';

function getModules(code) {
  const reg =
    /\[submodule "(?<name>.+)"\]\n\spath = (?<path>.+)\n\surl = (?<url>.+)(?:\n\sbranch = (?<default_branch>.+))?/g;
  let temp;
  const res = [];
  // eslint-disable-next-line no-cond-assign
  while ((temp = reg.exec(code))) {
    res.push(temp.groups);
  }

  return res;
}

function initConfig() {
  const file_content = fs.readFileSync(gitModulesPath, { encoding: 'utf-8' });

  return getModules(file_content);
}

const packages = initConfig();

const commit = ['commit', '-m', `'feat: 升级 wot-design-uni 版本'`];
const push = ['push'];
const pushO = ['push', '--set-upstream', 'origin', 'hotfix/request'];
const add = ['add', '.'];
const pull = ['pull'];
const checkoutR = ['checkout', 'release'];
const checkoutB = ['checkout', '-b', 'hotfix/request'];

function initCommon() {
  const cli = push;
  packages.forEach((item) => {
    const sp = spawn('git', ['-C', `./${item.path}`, ...cli], {
      stdio: [0, 1, 2],
      shell: true,
    });

    sp.on('data', (data, res) => {
      console.log(data);
      console.log(res);
    });
  });
}

initCommon();
