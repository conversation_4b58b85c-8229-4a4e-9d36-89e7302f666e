## 需求

按照原型图，实现工程绑定邀请码弹框

![原型图](requirement/工程绑定邀请码弹框.png)

1. 点击工程生成二维码按钮，弹出弹框
2. 通过工程 id 调用接口生成邀请码
3. 弹框中可以点击复制二维码按钮
4. 二维码链接为`https://shenan.com/invite-code?code=邀请码`
5. 点击背景遮罩，弹框消失

模块目录: `apps/shenan-pioneer/src/pages/engineering`

工程列表页: `apps/shenan-pioneer/src/pages/engineering/list.vue`

## 接口

`/sporadic/project/invite-code/generate`

post 请求

```json
{
  "id": "1234567890" // 工程id
}
```

返回

```json
{
  "expireAt": 1756519557363,
  "expireSeconds": 604800,
  "generatedAt": 1755914757363,
  "inviteCode": "L1mczkx8bj",
  "projectId": "1723884024468418560",
  "projectName": "0816-6深圳中学加装电梯采购专项"
}
```

## 具体要求：

1. 首先阅读并分析当前文件中的详细需求
2. 实现相应的业务逻辑，包括但不限于：
   - 数据验证逻辑
   - 业务规则处理
3. 确保实现符合需求文档中的所有相关规范和约束
4. 保持代码的可读性和可维护性
5. 不需要文档和测试用例

请先查看需求文档内容，然后提供具体的实现方案和代码修改建议。
