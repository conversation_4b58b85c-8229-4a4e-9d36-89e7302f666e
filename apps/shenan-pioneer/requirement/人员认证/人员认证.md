# 人员认证

**注意**: 所有文件都需在`apps/shenan-pioneer/src`目录下创建, 不要在其他目录下创建

新增人员认证功能, 人员认证功能分为两个页面, 第一个页面是选择人员类型, 第二个页面是根据选择的人员类型,跳转到对应的表单

人员认证功能, 用于记录人员信息, 用于后续的监管

## 具体要求：

1. 首先阅读并分析当前文件中的详细需求
2. 实现相应的业务逻辑，包括但不限于：
   - 数据验证逻辑
   - 业务规则处理
3. 确保实现符合需求文档中的所有相关规范和约束
4. 保持代码的可读性和可维护性
5. 不需要文档和测试用例

请先查看需求文档内容，然后提供具体的实现方案和代码修改建议。

## page1

选择人员类型

- 施工负责人
- 建设方（业主）
- 施工工人
- 施工监理

原型图: `requirement/人员认证/image.png`

## page2

原型图: `requirement/人员认证/image1.png`

根据选择的人员类型,跳转到对应的表单

### 角色对应表单说明

- 施工负责人/施工工人
  必填: 姓名、手机号、身份证、个人相片
  选填: 技术工种类型、证书编号、工种证件有效期、证书照片

- 施工监理
  必填: 姓名、手机号、身份证、个人相片、监理员证件编号、监理员证件有效期、监理员证件照片

- 建设方(业主)
  必填: 姓名、手机号、身份证

#### 部分表单说明

需要获取提交者的用户信息,进行和表单中的手机号进行对比,如果不一样抛出异常

如果角色存在则进行更新

#### 工种类型说明

木工、泥工、钢筋工、凝泥土工、油漆工、玻璃工、起重工、吊车司机和指挥、电焊工、机修工、维修电工、测量工、防水工、架子工、水工、电工、杂工

#### 提交按

文案: 提交认证

提交完成后跳转到首页

## 文件结构

页面文件夹: `apps/shenan-pioneer/src/pages`
组件文件夹: `apps/shenan-pioneer/src/components`
api 文件夹: `apps/shenan-pioneer/src/api`
utils 文件夹: `apps/shenan-pioneer/src/utils`

## 路由

路由配置文件: `apps/shenan-pioneer/src/pages.json`

## 接口

url: `/engineering/members/authentication`

post 请求

body

```java
public class MemberAuthenticationDTO implements Serializable {

    /**
     * 成员姓名 - 必填
     */
    @NotBlank(message = "姓名不能为空")
    private String realname;

    /**
     * 手机号码 - 必填
     */
    @NotBlank(message = "手机号不能为空")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    private String mobile;

    /**
     * 职位类型 - 必填
     * 1-建设方（业主）, 2-施工单位负责人, 3-施工工人, 4-施工监理
     */
    @NotNull(message = "职位类型不能为空")
    private Integer type;

    /**
     * 身份证号 - 必填
     */
    @NotBlank(message = "身份证号不能为空")
    @Pattern(regexp = "^[1-9]\\d{5}(18|19|20)\\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$",
             message = "身份证号格式不正确")
    private String idCard;

    /**
     * 个人相片 - 施工负责人/施工工人/施工监理必填
     */
    private String personalPhoto;

    /**
     * 技术工种类型 - 施工负责人/施工工人选填
     */
    private String workTypeName;

    /**
     * 证书编号 - 施工负责人/施工工人选填
     */
    private String certificateNumber;

    /**
     * 工种证件有效期开始时间 - 施工负责人/施工工人选填
     */
    private Date certificateStartDate;

    /**
     * 工种证件有效期结束时间 - 施工负责人/施工工人选填
     */
    private Date certificateEndDate;

    /**
     * 证书照片 - 施工负责人/施工工人选填
     */
    private String certificatePic;

    /**
     * 监理员证件编号 - 施工监理必填
     */
    private String supervisorCertificateNumber;

    /**
     * 监理员证件有效期开始时间 - 施工监理必填
     */
    private Date supervisorCertificateStartDate;

    /**
     * 监理员证件有效期结束时间 - 施工监理必填
     */
    private Date supervisorCertificateEndDate;

    /**
     * 监理员证件照片 - 施工监理必填
     */
    private String supervisorCertificatePic;

    /**
     * 描述
     */
    private String desc;

    /**
     * 项目ID - 可选，用于关联项目
     */
    private String projectId;

    /**
     * 组织ID - 可选
     */
    private String organizationId;
}

```

响应

```java
public class EngineeringMembersRespDTO implements Serializable {

    /**
     * ID
     */
    @Excel(name = "ID", width = 25)
    private String id;

    /**
     * 用户id
     */
    @Excel(name = "用户id", width = 25)
    private String userId;

    /**
     * 成员姓名
     */
    @Excel(name = "成员姓名", width = 25)
    private String realname;

    /**
     * 手机号码
     */
    @Excel(name = "手机号码", width = 25)
    private String mobile;

    /**
     * 职位类型
     */
    @Excel(name = "职位类型", width = 25)
    private Integer type;

    /**
     * 身份证号
     */
    @Excel(name = "身份证号", width = 25)
    private String idCard;

    /**
     * 工种名称
     */
    @Excel(name = "工种名称", width = 25)
    private String workTypeName;

    /**
     * 证书编号
     */
    @Excel(name = "证书编号", width = 25)
    private String certificateNumber;

    /**
     * 证书有效日期-开始时间
     */
    @Excel(name = "证书有效日期-开始时间", width = 25)
    private java.util.Date certificateStartDate;

    /**
     * 证书有效日期-结束时间
     */
    @Excel(name = "证书有效日期-结束时间", width = 25)
    private java.util.Date certificateEndDate;

    /**
     * 证书图片
     */
    @Excel(name = "证书图片", width = 25)
    private String certificatePic;

    /**
     * 描述
     */
    @Excel(name = "描述", width = 25)
    private String desc;

    /**
     * 有效状态，0-无效，1-有效
     */
    @Excel(name = "有效状态，0-无效，1-有效", width = 25)
    private Integer status;

    /**
     * 创建时间
     */
    @Excel(name = "创建时间", width = 25)
    private java.util.Date createdAt;

    /**
     * 最后更新时间
     */
    @Excel(name = "最后更新时间", width = 25)
    private java.util.Date updatedAt;

}
```
