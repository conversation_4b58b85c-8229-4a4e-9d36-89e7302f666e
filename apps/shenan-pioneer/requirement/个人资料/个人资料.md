# 个人资料

## 修改我的页面

当前页面: `apps/shenan-pioneer/src/pages/user/index.vue`

上方显示: 头像 姓名 手机号

下方列表: 个人资料,点击进入个人资料页面

底部: 退出登录,保持原功能

## 个人资料页面

资料页面的字段可以参考 `apps/shenan-pioneer/src/pages/authentication/form.vue`

> 新增页面

表单信息
头像 可修改 非必填
姓名 可修改 非必填
性别 可修改 非必填
昵称 可修改 非必填
手机号 不可修改 必填
身份证号 可修改 非必填

按钮
修改资料: 点击后开启表单可以修改模式
返回: 返回上一页

开启修改模式后, 按钮显示为
保持修改: 添加后提交接口
取消: 关闭修改模式,返回上一页

> 认证人员：

如果用户为认证人员,则添加额外的字段

- 施工负责人/施工工人
  必填: 本人照片
  选填: 技术工种类型、证书编号、工种证件有效期、证书照片

- 施工监理
  必填: 本人照片
  必填: 监理员证件编号、监理员证件有效期、监理员证件照片

- 建设方(业主)
  没有额外字段

## 相关字段

```sql
CREATE TABLE `sys_users` (
  `id` bigint(20) NOT NULL,
  `username` varchar(256) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '用户登录名',
  `password` varchar(1024) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '用户密码',
  `nickname` varchar(256) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '用户屏显昵称，可以不同用户登录名',
  `email` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '邮箱',
  `realname` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '用户真实姓名',
  `pid` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '身份证号',
  `pid_card_thumb1` varchar(200) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '身份证证件正面（印有国徽图案、签发机关和有效期限）照片',
  `pid_card_thumb2` varchar(200) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '身份证证件反面（印有个人基本信息和照片）照片',
  `avatar` varchar(512) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '用户个人图像',
  `phone` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '手机号码',
  `address` varchar(150) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '联系地址',
  `emergency_contact` varchar(300) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '紧急联系人信息',
  `servicer_id` int(12) DEFAULT '0' COMMENT '专属客服id，（为0表示其为无专属客服的管理用户）',
  `deleted_at` datetime DEFAULT NULL COMMENT '被软删除时间',
  `created_at` datetime NOT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '修改更新时间',
  `is_lock` tinyint(3) DEFAULT '0' COMMENT '是否锁定限制用户登录，1锁定,0正常',
  `confirmation_code` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '确认码',
  `confirmed` tinyint(1) DEFAULT '0' COMMENT '是否已通过验证 0：未通过 1：通过',
  `remember_token` varchar(60) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'Laravel 追加的记住令牌',
  `info` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备注',
  `sex` tinyint(4) DEFAULT NULL COMMENT '性别：1，男；2，女；3，保密',
  `job_number` varchar(120) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '工号',
  `enterprise_email` varchar(120) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '企业邮箱',
  `location` varchar(120) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '办公地点',
  `birth` datetime DEFAULT NULL COMMENT '生日',
  `sign` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '个人签名',
  `region_id` bigint(20) DEFAULT NULL COMMENT '地区',
  `qr` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '微信二维码',
  `type` tinyint(4) DEFAULT '1' COMMENT '用户类型  0.游客 1.后台 2.微信 ',
  `verified` tinyint(4) DEFAULT NULL COMMENT '实名认证 0.未审核 1.通过 2.未通过 3.取消',
  `verify_time` datetime DEFAULT NULL COMMENT '申请实名认证时间',
  `verified_at` datetime DEFAULT NULL COMMENT '认证时间',
  `remark` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '审核意见',
  `last_time` datetime DEFAULT NULL COMMENT '最后一次访问时间',
  `last_ip` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '最后一次访问ip',
  `login_times` int(11) DEFAULT NULL COMMENT '登录次数',
  `last_organization` bigint(20) DEFAULT NULL COMMENT '上次选择的组织-管理端，关联xsgc_organization',
  `last_organization_mobile` bigint(20) DEFAULT NULL COMMENT '上次选择的组织-移动端，关联xsgc_organization',
  PRIMARY KEY (`id`),
  KEY `user_nickname_index` (`nickname`(191)),
  KEY `user_phone_index` (`phone`),
  KEY `user_username_idk` (`username`(191)),
  KEY `user_pid_ink` (`pid`),
  KEY `user_email_ink` (`email`),
  KEY `user_realname_index` (`realname`(191)) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';

CREATE TABLE `engineering_members` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `user_id` bigint(20) DEFAULT '0' COMMENT '用户id',
  `realname` varchar(64) CHARACTER SET utf8mb4 DEFAULT NULL COMMENT '成员姓名',
  `mobile` varchar(16) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '手机号码',
  `type` int(8) DEFAULT NULL COMMENT '职位类型',
  `id_card` varchar(30) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '身份证号',
  `work_type_name` varchar(30) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '工种名称',
  `certificate_number` varchar(30) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '证书编号',
  `certificate_start_date` date DEFAULT NULL COMMENT '证书有效日期-开始时间',
  `certificate_end_date` date DEFAULT NULL COMMENT '证书有效日期-结束时间',
  `certificate_pic` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '证书图片',
  `desc` varchar(1024) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '描述',
  `status` tinyint(4) DEFAULT '1' COMMENT '有效状态，0-无效，1-有效',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '最后更新时间',
  `is_deleted` tinyint(4) DEFAULT '0' COMMENT '是否删除',
  `deleted_at` datetime DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`) USING BTREE,
  KEY `mobile` (`mobile`) USING BTREE,
  KEY `idx_deletedat_realname_userid` (`is_deleted`,`realname`,`user_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1730699059430002689 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='小散工程-工程人员';
```

## 接口

更新成员信息:

url: `/engineering/members/update`

post 请求

body:

```class
public class EngineeringMembersUpdateDTO implements Serializable {

    /**
     * ID
     */
    @NotBlank(message = "id不能为空")
    private String id;

    /**
     * 用户id
     */
    private String userId;

    /**
     * 成员姓名
     */
    private String realname;

    /**
     * 手机号码
     */
    private String mobile;

    /**
     * 职位类型
     */
    private Integer type;

    /**
     * 身份证号
     */
    private String idCard;

    /**
     * 工种名称
     */
    private String workTypeName;

    /**
     * 证书编号
     */
    private String certificateNumber;

    /**
     * 证书有效日期-开始时间
     */
    private java.util.Date certificateStartDate;

    /**
     * 证书有效日期-结束时间
     */
    private java.util.Date certificateEndDate;

    /**
     * 证书图片
     */
    private String certificatePic;

    /**
     * 描述
     */
    private String desc;

    /**
     * 有效状态，0-无效，1-有效
     */
    private Integer status;


    /**
     * 组织id
     */
    private String organizationId;



    //===================
    /**
     * 项目id
     */
    private String projectId;

}
```

响应:

```ts
{
  "errcode": "0000",
  "errmsg": "成功"
}
```

更新用户信息:

url: `/sys/user/update`

post 请求

body:

```ts
{
  id: string;
  avatar: string;
  nickname: string;
  sex: number;
  realname: string;
  pid: string;
}
```

响应:

```ts
{
  "errcode": "0000",
  "errmsg": "成功"
}
```

## 规范

1. 首先阅读并分析当前文件中的详细需求
2. 实现相应的业务逻辑，包括但不限于：
   - 数据验证逻辑
   - 业务规则处理
3. 确保实现符合需求文档中的所有相关规范和约束
4. 保持代码的可读性和可维护性
5. 不需要文档和测试用例

请先查看需求文档内容，然后提供具体的实现方案和代码修改建议。

**注意**:

- 所有文件都需在`apps/shenan-pioneer/src`目录下创建, 不要在其他目录下创建
- 路由配置文件: `apps/shenan-pioneer/src/pages.json`
- 页面文件夹: `apps/shenan-pioneer/src/pages`
- 组件文件夹: `apps/shenan-pioneer/src/components`
- api 文件夹: `apps/shenan-pioneer/src/api`
- utils 文件夹: `apps/shenan-pioneer/src/utils`
