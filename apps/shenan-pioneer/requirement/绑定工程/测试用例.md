# 绑定工程功能测试用例

## 测试环境准备

1. 确保应用已正确配置 API 地址
2. 准备测试用的备案号和邀请码
3. 准备测试用的二维码（包含邀请码参数）
4. 确保设备有相机权限（用于扫码测试）

## 功能测试用例

### 1. 页面访问测试

**测试步骤：**
1. 在应用中导航到绑定工程页面
2. 检查页面是否正常加载

**预期结果：**
- 页面正常显示
- 包含备案号输入框、邀请码输入框、扫码按钮、查找按钮
- 页面样式正常

### 2. 表单验证测试

**测试步骤：**
1. 不输入任何内容，直接点击查找按钮
2. 只输入备案号，点击查找按钮
3. 只输入邀请码，点击查找按钮

**预期结果：**
- 显示相应的验证错误提示
- 必填字段高亮显示错误状态

### 3. 扫码功能测试

**测试步骤：**
1. 点击扫码按钮
2. 扫描包含邀请码的二维码
3. 扫描不包含邀请码的二维码

**预期结果：**
- 正确的二维码：邀请码自动填入输入框，显示成功提示
- 错误的二维码：显示"二维码格式不正确"提示

### 4. 用户认证检查测试

#### 4.1 未认证用户测试

**测试步骤：**
1. 使用未认证的用户账号
2. 输入正确的备案号和邀请码
3. 点击查找按钮

**预期结果：**
- 显示认证提示弹框
- 弹框包含认证提示信息和操作按钮
- 点击"我要认证"跳转到认证页面
- 点击"稍后认证"关闭弹框

#### 4.2 已认证用户测试

**测试步骤：**
1. 使用已认证的用户账号
2. 输入正确的备案号和邀请码
3. 点击查找按钮

**预期结果：**
- 不显示认证提示弹框
- 直接进行工程查找

### 5. 工程查找测试

#### 5.1 正确信息查找

**测试步骤：**
1. 使用已认证用户
2. 输入正确的备案号和邀请码
3. 点击查找按钮

**预期结果：**
- 显示加载状态
- 成功后显示工程信息弹框
- 弹框包含完整的工程信息

#### 5.2 错误信息查找

**测试步骤：**
1. 使用已认证用户
2. 输入错误的备案号或邀请码
3. 点击查找按钮

**预期结果：**
- 显示加载状态
- 失败后显示错误提示
- 不显示工程信息弹框

### 6. 工程信息展示测试

**测试步骤：**
1. 成功查找到工程信息
2. 检查弹框中显示的信息

**预期结果：**
- 所有字段正确显示
- 时间格式正确
- 金额和面积格式正确
- 操作按钮正常显示

### 7. 工程绑定测试

#### 7.1 确认绑定

**测试步骤：**
1. 查看工程信息弹框
2. 点击"确定绑定"按钮

**预期结果：**
- 显示加载状态
- 绑定成功后显示成功提示
- 自动返回上一页面

#### 7.2 取消绑定

**测试步骤：**
1. 查看工程信息弹框
2. 点击"取消"按钮

**预期结果：**
- 关闭弹框
- 不进行绑定操作

### 8. 网络异常测试

**测试步骤：**
1. 断开网络连接
2. 尝试查找工程或绑定工程

**预期结果：**
- 显示网络错误提示
- 不会出现应用崩溃

### 9. 界面适配测试

**测试步骤：**
1. 在不同尺寸的设备上测试
2. 旋转屏幕测试

**预期结果：**
- 界面在不同设备上正常显示
- 响应式布局正常工作

## 性能测试

### 1. 页面加载性能
- 页面首次加载时间 < 2秒
- 组件渲染流畅

### 2. 接口响应性能
- API 请求响应时间 < 5秒
- 加载状态正确显示

## 兼容性测试

### 1. 平台兼容性
- H5 端正常运行
- 微信小程序正常运行
- 支付宝小程序正常运行

### 2. 设备兼容性
- iOS 设备正常运行
- Android 设备正常运行
- 不同分辨率设备适配正常

## 安全测试

### 1. 输入验证
- 防止 XSS 攻击
- 输入长度限制正确

### 2. 权限验证
- 认证检查正确执行
- 未认证用户无法绑定工程

## 回归测试

在每次代码修改后，重新执行以上所有测试用例，确保功能正常。
