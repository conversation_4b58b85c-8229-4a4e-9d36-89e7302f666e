# 绑定工程功能实现说明

## 功能概述

根据需求文档实现了完整的绑定工程功能，包括表单输入、扫码功能、工程信息展示和绑定确认等功能。

## 实现的文件

### 1. API 接口 (`apps/shenan-pioneer/src/api/modules/engineering.ts`)

新增了以下接口：

- `ApiSearchProject`: 查找工程信息接口

  - URL: `/sporadic/project/invite-code/project`
  - 方法: POST
  - 参数: `{ projectNumber: string, inviteCode: string }`
  - 返回: 工程详细信息

- `ApiBindProject`: 绑定工程接口

  - URL: `/sporadic/project/invite-code/bind`
  - 方法: POST
  - 参数: `{ inviteCode: string, projectNumber: string }`
  - 返回: 绑定结果

- `ApiCheckUserAuthentication`: 检查用户认证状态接口
  - URL: `/sys/role/user/organization/all/roles`
  - 方法: POST
  - 参数: `{}`
  - 返回: 用户角色信息数组

### 2. 绑定工程页面 (`apps/shenan-pioneer/src/pages/engineering/bind.vue`)

主要功能：

- 表单输入（备案号、邀请码）
- 扫码功能（点击扫码按钮调用摄像头扫描二维码）
- 表单验证
- 用户认证状态检查
- 调用查找工程接口
- 显示工程信息弹框
- 显示认证提示弹框
- 处理绑定确认

### 3. 工程信息弹框组件 (`apps/shenan-pioneer/src/components/project-info-modal.vue`)

显示内容：

- 备案编号
- 工程名称
- 工程分类
- 工程金额
- 实际施工面积
- 工程开始时间
- 工程结束时间
- 所在社区
- 详细地址
- 建设单位
- 施工单位

操作按钮：

- 取消
- 确定绑定

### 4. 认证提示弹框组件 (`apps/shenan-pioneer/src/components/authentication-prompt-modal.vue`)

功能：

- 提示用户未完成身份认证
- 引导用户进行身份认证
- 提供"稍后认证"和"我要认证"选项

操作按钮：

- 稍后认证（关闭弹框）
- 我要认证（跳转到认证页面）

### 5. 绑定成功页面 (`apps/shenan-pioneer/src/pages/engineering/bind-success.vue`)

功能：

- 显示绑定成功状态（成功图标和提示文字）
- 展示绑定的工程详细信息
- 提供查看工程详情和返回首页的操作

页面内容：

- 成功图标（带脉冲动画效果）
- "工程绑定成功"标题和描述
- 工程信息卡片（包含所有关键信息）
- 操作按钮（查看工程详情、返回首页）
- 温馨提示

### 6. 路由配置 (`apps/shenan-pioneer/src/pages.json`)

新增路由：

```json
{
  "path": "pages/engineering/bind",
  "style": {
    "navigationBarTitleText": "绑定工程"
  }
},
{
  "path": "pages/engineering/bind-success",
  "style": {
    "navigationBarTitleText": "绑定成功"
  }
}
```

## 技术特点

### 1. 扫码功能

- 使用项目现有的 `scanCode` 工具函数
- 支持解析二维码中的邀请码参数
- 自动填充到表单中

### 2. 表单验证

- 使用 uni-forms 组件进行表单验证
- 必填字段验证
- 用户友好的错误提示

### 3. 响应式设计

- 使用 TailwindCSS 进行样式设计
- 适配移动端界面
- 卡片式布局，美观易用

### 4. 错误处理

- 完善的错误处理机制
- 用户友好的错误提示
- 网络请求失败重试提示

### 5. 用户认证检查

- 在查找工程前检查用户认证状态
- 未认证用户显示认证提示弹框
- 引导用户完成身份认证

### 6. 用户体验

- 加载状态显示
- 成功/失败反馈
- 自动跳转功能
- 友好的认证提示

## 使用方式

1. 在应用中导航到绑定工程页面：

   ```javascript
   uni.navigateTo({
     url: '/pages/engineering/bind',
   });
   ```

2. 用户可以：
   - 手动输入备案号和邀请码
   - 点击扫码按钮扫描二维码获取邀请码
   - 点击查找按钮查看工程信息（会先检查认证状态）
   - 如果未认证，会显示认证提示弹框
   - 确认信息后点击绑定
   - 绑定成功后自动跳转到绑定成功页面
   - 在成功页面查看工程详情或返回首页

## 依赖说明

- 使用了项目现有的工具函数和组件
- 兼容现有的 API 结构和错误处理机制
- 遵循项目的代码规范和样式指南

## 注意事项

1. 扫码功能需要相机权限
2. 网络请求需要正确的 API 地址配置
3. 二维码格式需要包含 `code=邀请码` 参数
4. 绑定成功后会跳转到绑定成功页面
5. 用户必须先完成身份认证才能绑定工程
6. 未认证用户会被引导到认证页面 `/pages/authentication/personnel-type`
7. 绑定成功页面通过 URL 参数传递工程信息
8. 返回首页使用 `uni.redirectTo` 方法

## 测试建议

1. 测试表单验证功能
2. 测试扫码功能（需要真实设备）
3. 测试网络请求和错误处理
4. 测试不同屏幕尺寸的适配
5. 测试绑定流程的完整性
6. 测试绑定成功页面的显示和功能
7. 测试工程信息参数传递的正确性
8. 测试返回首页功能
9. 测试查看工程详情功能
10. 测试页面动画效果
