## 具体要求：

1. 首先阅读并分析当前文件中的详细需求
2. 实现相应的业务逻辑，包括但不限于：
   - 数据验证逻辑
   - 业务规则处理
3. 确保实现符合需求文档中的所有相关规范和约束
4. 保持代码的可读性和可维护性
5. 不需要文档和测试用例

请先查看需求文档内容，然后提供具体的实现方案和代码修改建议。

**注意**: 所有文件都需在`apps/shenan-pioneer/src`目录下创建, 不要在其他目录下创建

## 文件结构

页面文件夹: `apps/shenan-pioneer/src/pages`
组件文件夹: `apps/shenan-pioneer/src/components`
api 文件夹: `apps/shenan-pioneer/src/api`
utils 文件夹: `apps/shenan-pioneer/src/utils`

## 路由

路由配置文件: `apps/shenan-pioneer/src/pages.json`

## page1

有一个表单

备案号: 输入框
邀请码: 输入框,后面有个 icon,点击可以扫码二维码

按钮: 查找

点击查找按钮

显示弹框

- 接口
  url： /sporadic/project/invite-code/project

请求方法：POST
请求参数：

```json
{
  "projectNumber": "jiangbian082802",
  "inviteCode": "6azKJxkycn"
}
```

响应数据：

```json
{
  "data": {
    "actualEndAt": "2025-08-28",
    "actualStartAt": "2025-08-28",
    "address": "广东省深圳市龙岗区南湾街道富璟花园",
    "amount": 1000.0,
    "area": 56.82,
    "cateId": "1728149031942234112",
    "cateName": "工程类别123",
    "catePid": "2",
    "constructorCharger": "吴明",
    "constructorName": "航程街道建设单位1",
    "contractorCharger": "沈小青",
    "contractorChargerMobile": "18696333123",
    "contractorId": "1703516871471845376",
    "contractorName": "施工单位1",
    "createdAt": "2025-08-28",
    "districtName": "宝安区",
    "endAt": "2025-09-30",
    "flow": 1,
    "flowId": "1728234146900353024",
    "id": "1728234145948246016",
    "lat": 22.604724,
    "lng": 114.146083,
    "memoRespDTOList": [],
    "monitorFlag": 0,
    "name": "江边082802",
    "orderId": "1728234146615140352",
    "ownerMobile": "18696333122",
    "pCateName": "零星作业",
    "poiId": "1728234145876942848",
    "projectNumber": "jiangbian082802",
    "qrCodeUrl": "https://tst-ai.vansafe.cn/f0/sc047f71a5d4ef1134-public/test/sc/files/772B42C8F59E4CADA248F51BEBA35889.png",
    "regionCid": "832789087816819157",
    "regionId": "832789087816818862",
    "regionPid": "83",
    "startAt": "2025-09-01",
    "status": 0,
    "statusName": "未开始",
    "streetName": "松岗街道",
    "updatedAt": "2025-08-29",
    "villageName": "江边社区"
  },
  "errcode": "0000",
  "errmsg": "成功"
}
```

### 弹框内容

备案编号: xxx
工程名称: xxx
工程分类: xxx
工程金额: xxx
实际施工面积: xxx
工程开始时间: xxx
工程结束时间: xxx
所在社区: xxx
详细地址: xxx
建设单位: xxx
施工单位: xxx

按钮: 确定绑定

### 确定绑定接口

url： /sporadic/project/invite-code/bind
请求方法：POST
请求参数：

```json
{
  "inviteCode": "10",
  "projectNumber": "76"
}
```

响应数据：

```json
{
  "errcode": "0000",
  "errmsg": "成功"
}
```

## 新增需求

### 用户是否认证

在绑定工程之前,需要进行对用户是否认证的校验,
只要接口存在数据,就可以认为用户进行过认证操作

认证页面: `pages/authentication/personnel-type`

### UI

提示用户没有认证,需要前去认证在进行绑定工程

按钮: 我要认证

### 接口

url: `/sys/role/user/organization/all/roles`

post 请求

请求数据:

```json
{}
```

响应数据:

```json
{
  "data": [
    {
      "displayName": "施工单位负责人",
      "roleName": "Construction:unitleader"
    }
  ],
  "errcode": "0000",
  "errmsg": "成功"
}
```
