<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <!-- 跨域问题 -->

  <script>
    var coverSupport =
      'CSS' in window &&
      typeof CSS.supports === 'function' &&
      (CSS.supports('top: env(a)') || CSS.supports('top: constant(a)'));
    document.write(
      '<meta name="viewport" content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0' +
      (coverSupport ? ', viewport-fit=cover' : '') +
      '" />',
    );

  </script>

  <!-- #ifdef H5 -->
  <!-- <script src="https://scplugins.oss-cn-shenzhen.aliyuncs.com/plugins/cdn/jessibuca/jessibuca2.js"></script> -->
  <script>
    var global = globalThis;

  </script>
  <script>
    this.globalThis || (this.globalThis = this);

  </script>
  <!-- #endif -->


  <title></title>
  <!--preload-links-->
  <!--app-context-->
</head>

<body>
  <div id="app">
    <!--app-html-->
  </div>
  <script type="module" src="/src/main.ts"></script>

</body>

</html>