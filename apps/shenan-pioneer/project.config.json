{"description": "项目配置文件。", "packOptions": {"ignore": [{"value": "static/**/*.+(png|jpg|jpeg|gif|svg)", "type": "glob"}, {"value": "static/files/*", "type": "glob"}], "include": [{"value": "static/images/logo.png", "type": "file"}, {"value": "static/images/tabs/*", "type": "glob"}, {"value": "static/images/+(success|error|warning).png", "type": "glob"}]}, "setting": {"urlCheck": false, "es6": true, "postcss": true, "minified": true, "newFeature": true, "bigPackageSizeSupport": true, "coverView": true, "lazyloadPlaceholderEnable": false, "preloadBackgroundData": false, "autoAudits": false, "uglifyFileName": false, "uploadWithSourceMap": true, "enhance": true, "useMultiFrameRuntime": true, "showShadowRootInWxmlPanel": true, "packNpmManually": false, "packNpmRelationList": [], "minifyWXSS": true, "useStaticServer": true, "showES6CompileOption": false, "checkInvalidKey": true, "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "disableUseStrict": false, "useCompilerPlugins": false, "minifyWXML": true}, "compileType": "miniprogram", "libVersion": "", "appid": "wx265fec14f1841914", "projectname": "", "condition": {"search": {"current": -1, "list": []}, "conversation": {"current": -1, "list": []}, "game": {"current": -1, "list": []}, "miniprogram": {"current": -1, "list": []}}, "srcMiniprogramRoot": "dist/", "miniprogramRoot": "dist/"}