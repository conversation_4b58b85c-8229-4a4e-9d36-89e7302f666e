import { AppConfigProps } from '@rms/types';
import { defaultCndConfig } from '../../shared/cdn';
import { name, version } from './package.json';
import { _GetUrlKey } from '@rms/utils';

export const AppConfig: AppConfigProps = {
  base: {
    name,
    version,
    scid: _GetUrlKey('scid') || import.meta.env.VITE_APP_SCID,
    logo: '',
    geoKey: '5a440faf77903a10b09684db6fc5f859',
    securityJsCode: 'd1affcd1747a21b5cad1824ebf5e3fa2',
    geoServiceKey: '',
    appid: 'wx265fec14f1841914',
  },
  login: {
    h5: {
      sccode: true,
      silentLogin: false,
      accountLoginType: 'phone',
    },
    mini: {
      miniLogin: true,
      accountLoginType: 'phone',
      historyAccounts: true,
    },
  },
  cdn: {
    ...defaultCndConfig,
    amap: {
      ...defaultCndConfig.amap,
      load: true,
    },
    vConsole: {
      ...defaultCndConfig.vConsole,
      dev: false,
      tst: false,
      pro: false,
    },
  },
};
