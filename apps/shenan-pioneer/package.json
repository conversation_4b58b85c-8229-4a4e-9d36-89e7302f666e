{"name": "<PERSON><PERSON>-pioneer", "version": "1.0.0", "scripts": {"serve": "uni", "test": "tsx ./build/index --mode tst", "build": "tsx ./build/index --mode production", "serve:mp": "cross-env UNI_OUTPUT_DIR=dist uni -p mp-weixin", "tst:mp": "tsx ./build/index --mode tst -p mp-weixin", "build:mp": "tsx ./build/index --mode production -p mp-weixin", "dev:app": "uni -p app", "dev:custom": "uni -p", "dev:h5": "uni", "dev:h5:ssr": "uni --ssr", "dev:mp-alipay": "uni -p mp-alipay", "dev:mp-baidu": "uni -p mp-baidu", "dev:mp-kuaishou": "uni -p mp-kua<PERSON>ou", "dev:mp-lark": "uni -p mp-lark", "dev:mp-qq": "uni -p mp-qq", "dev:mp-toutiao": "uni -p mp-to<PERSON><PERSON>", "dev:mp-weixin": "uni -p mp-weixin", "dev:quickapp-webview": "uni -p quickapp-webview", "dev:quickapp-webview-huawei": "uni -p quickapp-webview-huawei", "dev:quickapp-webview-union": "uni -p quickapp-webview-union", "build:app": "uni build -p app", "build:custom": "uni build -p", "build:h5": "uni build", "build:h5:ssr": "uni build --ssr", "build:mp-alipay": "uni build -p mp-alipay", "build:mp-baidu": "uni build -p mp-baidu", "build:mp-kuaishou": "uni build -p mp-kuaishou", "build:mp-lark": "uni build -p mp-lark", "build:mp-qq": "uni build -p mp-qq", "build:mp-toutiao": "uni build -p mp-to<PERSON>ao", "build:mp-weixin": "uni build -p mp-weixin", "build:quickapp-webview": "uni build -p quickapp-webview", "build:quickapp-webview-huawei": "uni build -p quickapp-webview-huawei", "build:quickapp-webview-union": "uni build -p quickapp-webview-union", "prepare": "husky install", "format": "../../prettier -w './apps/**/src/**/*.{vue,ts,tsx}'", "lint": "../../eslint ./apps/**/src --ext .vue,.js,.ts,.jsx,.tsx --fix", "commit": "git-cz", "release": "standard-version", "preinstall": "node ../../scripts/preinstall.js"}, "lint-staged": {"*.{vue,ts,tsx}": ["prettier -w", "eslint --fix"]}, "dependencies": {"@dcloudio/uni-app": "3.0.0-4020420240722002", "@dcloudio/uni-app-plus": "3.0.0-4020420240722002", "@dcloudio/uni-components": "3.0.0-4020420240722002", "@dcloudio/uni-h5": "3.0.0-4020420240722002", "@dcloudio/uni-mp-alipay": "3.0.0-4020420240722002", "@dcloudio/uni-mp-baidu": "3.0.0-4020420240722002", "@dcloudio/uni-mp-kuaishou": "3.0.0-4020420240722002", "@dcloudio/uni-mp-lark": "3.0.0-4020420240722002", "@dcloudio/uni-mp-qq": "3.0.0-4020420240722002", "@dcloudio/uni-mp-toutiao": "3.0.0-4020420240722002", "@dcloudio/uni-mp-weixin": "3.0.0-4020420240722002", "@dcloudio/uni-quickapp-webview": "3.0.0-4020420240722002", "@dcloudio/uni-ui": "^1.4.22", "@rms/api": "workspace:*", "@rms/cdn": "workspace:*", "@rms/components": "workspace:*", "@rms/isz": "workspace:*", "@rms/login": "workspace:*", "@rms/register": "workspace:*", "@rms/router": "workspace:*", "@rms/service": "workspace:*", "@rms/style": "workspace:*", "@rms/userinfo": "workspace:*", "@rms/utils": "workspace:*", "@shencom/api": "1.12.0", "@shencom/request": "1.5.1", "@shencom/utils": "^1.9.0", "@shencom/wechat-opencv": "^1.0.1", "@vue/shared": "3.4.21", "@vueuse/core": "^13.1.0", "clipboard": "^2.0.11", "crypto-js": "^4.2.0", "html2canvas": "1.0.0-rc.4", "jsencrypt": "^3.3.2", "lodash-es": "^4.17.21", "pinia": "2.0.36", "qrcode-vue3": "^1.7.1", "vue": "3.4.21", "vue3-canvas-poster": "^1.0.1", "weapp-tailwindcss-webpack-plugin": "1.3.4", "wot-design-uni": "^1.9.1"}, "devDependencies": {"@commitlint/cli": "^16.2.3", "@commitlint/config-conventional": "^16.2.1", "@dcloudio/types": "3.4.11", "@dcloudio/uni-automator": "3.0.0-4020420240722002", "@dcloudio/uni-cli-shared": "3.0.0-4020420240722002", "@dcloudio/vite-plugin-uni": "3.0.0-4020420240722002", "@iconify/json": "^2.1.28", "@rms/types": "workspace:*", "@shencom/cli": "^1.4.0", "@shencom/oss-upload": "^2.6.0", "@shencom/utils": "^1.13.3", "@types/amap-js-api": "^1.4.10", "@types/amap-js-api-geocoder": "^1.4.1", "@types/amap-js-api-geolocation": "^1.4.1", "@types/amap-js-api-place-search": "^1.4.1", "@types/crypto-js": "^4.2.1", "@types/lodash-es": "^4.17.6", "@types/node": "^17.0.23", "@types/rollup-plugin-visualizer": "^4.2.1", "@typescript-eslint/eslint-plugin": "^5.18.0", "@typescript-eslint/parser": "^5.18.0", "@vitejs/plugin-legacy": "^3.0.1", "autoprefixer": "^10.4.4", "commitizen": "^4.2.4", "cz-conventional-changelog": "^3.3.0", "eslint": "^8.13.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-import": "^2.26.0", "eslint-plugin-prettier": "^4.0.0", "eslint-plugin-vue": "^8.6.0", "husky": "^7.0.4", "lint-staged": "^12.3.7", "pinia-plugin-unistorage": "^0.1.2", "postcss-pxtorem": "^6.0.0", "postcss-rem-to-responsive-pixel": "^5.1.1", "prettier": "^2.6.2", "rollup-plugin-visualizer": "^5.6.0", "sass": "^1.50.0", "standard-version": "^9.3.2", "tailwindcss": "^3.4.10", "tsx": "^4.6.1", "typescript": "5.3.2", "unocss": "0.58.9", "vite": "5.2.8"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "engines": {"node": ">16.0.0", "pnpm": ">7.0.0"}}