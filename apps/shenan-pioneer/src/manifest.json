{
  "name": "",
  "appid": "wx265fec14f1841914",
  "description": "recycle-uni vue3",
  "versionName": "1.0.0",
  "versionCode": "100",
  "transformPx": false,
  /* 5+App特有相关 */
  "h5": {
    "sdkConfigs": {
      // 使用地图或位置相关功能必须填写其一
      "maps": {
        "qqmap": {
          // 腾讯地图秘钥 https://lbs.qq.com/dev/console/key/manage
          "key": "QEEBZ-ZRRW6-K4RSJ-MDM6M-5XZGE-PZF5W"
        }
      }
    }
  },
  "app-plus": {
    "usingComponents": true,
    "nvueStyleCompiler": "uni-app",
    "compilerVersion": 3,
    "splashscreen": {
      "alwaysShowBeforeRender": true,
      "waiting": true,
      "autoclose": true,
      "delay": 0
    },
    /* 模块配置 */
    "modules": {},
    /* 应用发布信息 */
    "distribute": {
      /* android打包配置 */
      "android": {
        "permissions": [
          "<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\"/>",
          "<uses-permission android:name=\"android.permission.MOUNT_UNMOUNT_FILESYSTEMS\"/>",
          "<uses-permission android:name=\"android.permission.VIBRATE\"/>",
          "<uses-permission android:name=\"android.permission.READ_LOGS\"/>",
          "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>",
          "<uses-feature android:name=\"android.hardware.camera.autofocus\"/>",
          "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\"/>",
          "<uses-permission android:name=\"android.permission.CAMERA\"/>",
          "<uses-permission android:name=\"android.permission.GET_ACCOUNTS\"/>",
          "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\"/>",
          "<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\"/>",
          "<uses-permission android:name=\"android.permission.WAKE_LOCK\"/>",
          "<uses-permission android:name=\"android.permission.FLASHLIGHT\"/>",
          "<uses-feature android:name=\"android.hardware.camera\"/>",
          "<uses-permission android:name=\"android.permission.WRITE_SETTINGS\"/>"
        ]
      },
      /* ios打包配置 */
      "ios": {},
      /* SDK配置 */
      "sdkConfigs": {}
    }
  },
  /* 快应用特有相关 */
  "quickapp": {},
  /* 小程序特有相关 */
  "mp-weixin": {
    "appid": "wx265fec14f1841914",
    "setting": {
      "urlCheck": false,
      "coverView": true,
      "es6": true,
      "postcss": true,
      "lazyloadPlaceholderEnable": false,
      "preloadBackgroundData": false,
      "minified": true,
      "autoAudits": false,
      "uglifyFileName": false,
      "uploadWithSourceMap": true,
      "enhance": true,
      "useMultiFrameRuntime": true,
      "showShadowRootInWxmlPanel": true,
      "packNpmManually": false,
      "packNpmRelationList": [],
      "minifyWXSS": true,
      "useStaticServer": true,
      "showES6CompileOption": false,
      "checkInvalidKey": true,
      "bigPackageSizeSupport": true,
      "babelSetting": {
        "ignore": [],
        "disablePlugins": [],
        "outputPath": ""
      },
      "disableUseStrict": false,
      "useCompilerPlugins": false,
      "minifyWXML": true
    },
    "permission": {
      "scope.userLocation": {
        "desc": "你的位置信息将用于小程序位置接口的效果展示"
      }
    },
    "packOptions": {
      "ignore": [
        {
          "value": "static/**/*.+(png|jpg|jpeg|gif|svg)",
          "type": "glob"
        },
        {
          "value": "static/files/*",
          "type": "glob"
        }
      ],
      "include": [
        {
          "value": "static/images/logo.png",
          "type": "file"
        },
        {
          "value": "static/images/tabs/*",
          "type": "glob"
        },
        {
          "value": "static/images/+(success|error|warning).png",
          "type": "glob"
        }
      ]
    },
    "plugins": {},
    "requiredPrivateInfos": ["getLocation", "onLocationChange", "chooseLocation"],
    /* 按需注入 */
    // "lazyCodeLoading": "requiredComponents",
    "usingComponents": true
  },
  "mp-alipay": {
    "usingComponents": true
  },
  "mp-baidu": {
    "usingComponents": true
  },
  "mp-toutiao": {
    "usingComponents": true
  },
  "uniStatistics": {
    "enable": false
  },
  "vueVersion": "3"
}
