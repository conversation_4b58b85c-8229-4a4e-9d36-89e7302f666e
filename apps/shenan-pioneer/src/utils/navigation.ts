import { Dictionary } from '@rms/types';
import { IsDev, <PERSON>H<PERSON>, <PERSON><PERSON><PERSON>, Navigator, objectToUrlParams } from '@rms/utils';

export function toPageFormat(route: string, params: Dictionary) {
  if (IsDev || IsH5) {
    Navigator.push(route, params);
    return;
  }
  const url = `https://${
    IsPro ? 'ai-app.vansafe.cn' : 'tst-ai.vansafe.cn'
  }/shenan-pioneer/index.html#`;

  const item: any = {
    permit: 1,
    type: 'url',
    link: `${url}${route}?${objectToUrlParams(params)}`,
  };
  Navigator.toMenuPage(item);
}
