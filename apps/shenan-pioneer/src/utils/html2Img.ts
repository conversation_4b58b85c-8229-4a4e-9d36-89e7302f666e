import html2canvas from 'html2canvas';

const handleHtml2Img = (domId: string) =>
  new Promise((resolve: (value: string) => void) => {
    // 获取想要转换的 DOM 节点
    const dom = document.getElementById(domId);
    // DOM 节点计算后宽高
    const { width, height } = (dom as HTMLElement).getBoundingClientRect();
    // 获取像素比
    const dpr = window.devicePixelRatio || 1;
    // 创建自定义 canvas 元素
    const canvas = document.createElement('canvas');
    // 设定 canvas 元素属性宽高为 DOM 节点宽高 * 像素比
    canvas.width = width * dpr;
    canvas.height = height * dpr;
    // 设定 canvas css宽高为 DOM 节点宽高
    canvas.style.width = `${width}px`;
    canvas.style.height = `${height}px`;
    // 获取画笔
    // 将所有绘制内容放大像素比倍,解决dpr不同图片模糊问题
    // context.scale(dpr, dpr);
    // 将自定义 canvas 作为配置项传入，开始绘制
    html2canvas(dom as HTMLElement, {
      canvas,
      useCORS: true,
      allowTaint: true,
      scale: 2,
      backgroundColor: 'transparent',
    }).then((canvasUrl) => {
      resolve(canvasUrl.toDataURL('image/png', 1.0));
      canvas.remove();
    });
  });

export default handleHtml2Img;
