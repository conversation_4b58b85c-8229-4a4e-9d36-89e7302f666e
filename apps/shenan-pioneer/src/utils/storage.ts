import { StorageLRU } from '@rms/utils';
import { scid } from '@/config/base';

const Storage = new StorageLRU({
  scid,
  get: uni.getStorageSync,
  set: uni.setStorageSync,
  remove: uni.removeStorageSync,
  clear: uni.clearStorageSync,
  keys: () => uni.getStorageInfoSync().keys,
  maxSize: () => uni.getStorageInfoSync().limitSize,
  currentSize: () => uni.getStorageInfoSync().currentSize,
  _purgeLoadMax: 1000,
  useless: ['lasting_cache_gis', 'lasting_cache_image'],
  ignore: ['QMAPI_'],
});

export default Storage;
