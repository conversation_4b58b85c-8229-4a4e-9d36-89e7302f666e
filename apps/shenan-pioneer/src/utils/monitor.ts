type NameMapColorType = (
  name: string,
  config?: { opacity?: number; light?: number },
) => {
  dark: string;
  light: string;
  darkA: string;
  lightA: string;
};

export const nameMapColor: NameMapColorType = (
  name: string,
  config = { opacity: 1, light: 50 },
) => {
  let hash = 0;
  for (let i = 0; i < name.length; i++) {
    // eslint-disable-next-line no-bitwise
    hash = (hash << 5) - hash + name.charCodeAt(i);
    // eslint-disable-next-line no-bitwise
    hash |= 0;
  }
  const result = Math.abs(hash);
  const h = result % 360; // 色相
  const s = result % 100; // 饱和度
  const l = result % 80; // 亮度
  return {
    dark: `hsl(${h}, ${s}%, ${l}%)`,
    light: `hsl(${h}, ${s}%, ${l + (config?.light || 0)}%)`,
    darkA: `hsla(${h}, ${s}%, ${l}%, ${config.opacity})`,
    lightA: `hsla(${h}, ${s}%, ${l + (config?.light || 0)}%, ${config.opacity})`,
  };
};
