import { Storage } from '@/utils';

export interface RecycleRecord {
  id: number;
  name: string;
  code: string;
  address: string;
  status: number;
  latitude?: number;
  longitude?: number;
  realname?: string;
  phone?: string;
  projectName?: string;
  service?: string;
  time?: string;
  createTime?: string;
}

class MockDataManager {
  private storageKey = 'recycleRecords';

  // 初始化模拟数据
  private defaultData: RecycleRecord[] = [
    {
      id: 1,
      name: '花园小区A209',
      code: 'BA98976756675',
      address: '宝安区新桥街道黄埔社区花园小区A29',
      status: 1, // 待勘查
      latitude: 22.5431,
      longitude: 113.969,
      realname: '张三',
      phone: '13800138000',
      projectName: '小微工程安装',
      service: '设备安装',
      time: '2024-01-15 14:00',
      createTime: '2024-01-10 10:30',
    },
    {
      id: 2,
      name: '阳光花园B301',
      code: 'BA98976756676',
      address: '南山区科技园阳光花园B301',
      status: 2, // 待安装
      latitude: 22.5329,
      longitude: 113.9441,
      realname: '李四',
      phone: '13800138001',
      projectName: '小微工程安装',
      service: '设备安装',
      time: '2024-01-16 09:00',
      createTime: '2024-01-11 15:20',
    },
    {
      id: 3,
      name: '金桂花园C108',
      code: 'BA98976756677',
      address: '福田区华强北金桂花园C108',
      status: 3, // 已完成
      latitude: 22.5455,
      longitude: 114.0683,
      realname: '王五',
      phone: '13800138002',
      projectName: '小微工程安装',
      service: '设备安装',
      time: '2024-01-12 16:30',
      createTime: '2024-01-08 11:45',
    },
    {
      id: 4,
      name: '海景豪庭D205',
      code: 'BA98976756678',
      address: '龙岗区布吉街道海景豪庭D205',
      status: 1, // 待勘查
      latitude: 22.5912,
      longitude: 114.1361,
      realname: '赵六',
      phone: '13800138003',
      projectName: '小微工程安装',
      service: '设备安装',
      time: '2024-01-17 10:00',
      createTime: '2024-01-12 09:15',
    },
    {
      id: 5,
      name: '翠竹苑E302',
      code: 'BA98976756679',
      address: '罗湖区东门翠竹苑E302',
      status: 2, // 待安装
      latitude: 22.5649,
      longitude: 114.1095,
      realname: '孙七',
      phone: '13800138004',
      projectName: '小微工程安装',
      service: '设备安装',
      time: '2024-01-18 13:30',
      createTime: '2024-01-13 16:22',
    },
  ];

  // 获取所有记录
  getAllRecords(): RecycleRecord[] {
    try {
      const stored = uni.getStorageSync(this.storageKey);
      if (stored && Array.isArray(stored)) {
        return stored;
      }
      // 如果没有存储数据，使用默认数据并保存
      this.saveRecords(this.defaultData);
      return this.defaultData;
    } catch (error) {
      console.error('获取存储数据失败:', error);
      return this.defaultData;
    }
  }

  // 根据类型筛选记录
  getRecordsByType(type: number): RecycleRecord[] {
    const allRecords = this.getAllRecords();
    if (type === 0) {
      return allRecords; // 全部
    }
    return allRecords.filter((record) => record.status === type);
  }

  // 保存记录
  saveRecords(records: RecycleRecord[]): void {
    try {
      uni.setStorageSync(this.storageKey, records);
    } catch (error) {
      console.error('保存数据失败:', error);
    }
  }

  // 添加新记录
  addRecord(record: Omit<RecycleRecord, 'id' | 'createTime'>): RecycleRecord {
    const allRecords = this.getAllRecords();
    const newId = Math.max(...allRecords.map((r) => r.id), 0) + 1;
    const newRecord: RecycleRecord = {
      ...record,
      id: newId,
      createTime: new Date().toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
      }),
    };

    allRecords.unshift(newRecord); // 添加到开头
    this.saveRecords(allRecords);
    return newRecord;
  }

  // 更新记录状态
  updateRecordStatus(id: number, status: number): boolean {
    const allRecords = this.getAllRecords();
    const index = allRecords.findIndex((record) => record.id === id);
    if (index > -1) {
      allRecords[index].status = status;
      this.saveRecords(allRecords);
      return true;
    }
    return false;
  }

  // 删除记录
  deleteRecord(id: number): boolean {
    const allRecords = this.getAllRecords();
    const filteredRecords = allRecords.filter((record) => record.id !== id);
    if (filteredRecords.length < allRecords.length) {
      this.saveRecords(filteredRecords);
      return true;
    }
    return false;
  }

  // 生成唯一编码
  generateCode(): string {
    const timestamp = Date.now().toString(36);
    const random = Math.random().toString(36).substr(2, 5);
    return `BA${timestamp}${random}`.toUpperCase();
  }
}

// 导出单例实例
export const mockDataManager = new MockDataManager();

// 模拟API函数
export function ApiInstallRecord(params: {
  type: number;
}): Promise<{ content: RecycleRecord[]; total: number }> {
  return new Promise((resolve) => {
    setTimeout(() => {
      const records = mockDataManager.getRecordsByType(params.type);
      resolve({ content: records, total: records.length });
    }, 300);
  });
}

export function ApiRecycleRecord(params: any) {
  return new Promise<{ content: RecycleRecord[]; total: number }>((resolve) => {
    setTimeout(() => {
      const records = mockDataManager.getRecordsByType(params.type || 0);
      resolve({ content: records, total: records.length });
    }, 500);
  });
}
