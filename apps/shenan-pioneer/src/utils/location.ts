import { ServiceGisByIds } from '@rms/service/src';
import { Dictionary } from '@rms/types';
import { IsWeixinH5, IsMiniProgram } from '@rms/utils/src';

export async function onLocation({ projectPoiId, projectName }: Dictionary) {
  if (!projectPoiId) return;

  const gisInfo = await ServiceGisByIds(projectPoiId);
  const options = {
    longitude: +gisInfo[0].lng,
    latitude: +gisInfo[0].lat,
    name: projectName || '工程地址',
    address: gisInfo[0].addr,
  };

  if (IsWeixinH5 || IsMiniProgram) {
    uni.$wx.openLocation(options);
  } else {
    uni.openLocation(options);
  }
}
