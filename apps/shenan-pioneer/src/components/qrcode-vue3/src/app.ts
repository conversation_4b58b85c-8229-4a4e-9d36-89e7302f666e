import VQRCodeStyling from './core/QRCodeStyling';
import dotTypes from './constants/dotTypes';
import cornerDotTypes from './constants/cornerDotTypes';
import cornerSquareTypes from './constants/cornerSquareTypes';
import errorCorrectionLevels from './constants/errorCorrectionLevels';
import errorCorrectionPercents from './constants/errorCorrectionPercents';
import modes from './constants/modes';
import qrTypes from './constants/qrTypes';

export {
  dotTypes,
  cornerDotTypes,
  cornerSquareTypes,
  errorCorrectionLevels,
  errorCorrectionPercents,
  modes,
  qrTypes,
};
export default VQRCodeStyling;
