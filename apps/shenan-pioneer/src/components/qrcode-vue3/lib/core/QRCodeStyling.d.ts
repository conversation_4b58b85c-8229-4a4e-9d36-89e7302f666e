import QRCanvas from './QRCanvas';
import { type Options, type RequiredOptions } from './QROptions';
import type { Extension, QRCode } from '../types';
type DownloadOptions = {
  name?: string;
  extension?: Extension;
};
export default class QRCodeStyling {
  _options: RequiredOptions;
  _container?: HTMLElement;
  _canvas?: QRCanvas;
  _qr?: QRCode;
  _drawingPromise?: Promise<void>;
  constructor(options?: Partial<Options>);
  static _clearContainer(container?: HTMLElement): void;
  update(options?: Partial<Options>): void;
  append(container?: HTMLElement): void;
  getImageUrl(extension: string): Promise<string>;
  download(downloadOptions?: Partial<DownloadOptions>): void;
}
export {};
