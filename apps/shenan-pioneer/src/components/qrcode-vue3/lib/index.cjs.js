"use strict";var vo=Object.defineProperty;var _o=(e,t,n)=>t in e?vo(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n;var ne=(e,t,n)=>(_o(e,typeof t!="symbol"?t+"":t,n),n);/**
* @vue/shared v3.5.3
* (c) 2018-present <PERSON><PERSON> (<PERSON>) <PERSON> and Vue contributors
* @license MIT
**//*! #__NO_SIDE_EFFECTS__ */function mo(e,t){const n=new Set(e.split(","));return t?r=>n.has(r.toLowerCase()):r=>n.has(r)}const ve=process.env.NODE_ENV!=="production"?Object.freeze({}):{},wo=process.env.NODE_ENV!=="production"?Object.freeze([]):[],se=()=>{},bo=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&(e.charCodeAt(2)>122||e.charCodeAt(2)<97),ae=Object.assign,yo=(e,t)=>{const n=e.indexOf(t);n>-1&&e.splice(n,1)},Eo=Object.prototype.hasOwnProperty,X=(e,t)=>Eo.call(e,t),H=Array.isArray,ke=e=>Vt(e)==="[object Map]",ar=e=>Vt(e)==="[object Set]",q=e=>typeof e=="function",fe=e=>typeof e=="string",Fe=e=>typeof e=="symbol",J=e=>e!==null&&typeof e=="object",cr=e=>(J(e)||q(e))&&q(e.then)&&q(e.catch),ur=Object.prototype.toString,Vt=e=>ur.call(e),lr=e=>Vt(e).slice(8,-1),fr=e=>Vt(e)==="[object Object]",gn=e=>fe(e)&&e!=="NaN"&&e[0]!=="-"&&""+parseInt(e,10)===e,dr=e=>{const t=Object.create(null);return n=>t[n]||(t[n]=e(n))},pr=dr(e=>e.charAt(0).toUpperCase()+e.slice(1)),Oo=dr(e=>e?`on${pr(e)}`:""),Re=(e,t)=>!Object.is(e,t),No=(e,t,n,r=!1)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,writable:r,value:n})},xo=e=>{const t=fe(e)?Number(e):NaN;return isNaN(t)?e:t};let qn;const hr=()=>qn||(qn=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:{});function vn(e){if(H(e)){const t={};for(let n=0;n<e.length;n++){const r=e[n],o=fe(r)?Mo(r):vn(r);if(o)for(const i in o)t[i]=o[i]}return t}else if(fe(e)||J(e))return e}const Do=/;(?![^(]*\))/g,So=/:([^]+)/,Co=/\/\*[^]*?\*\//g;function Mo(e){const t={};return e.replace(Co,"").split(Do).forEach(n=>{if(n){const r=n.split(So);r.length>1&&(t[r[0].trim()]=r[1].trim())}}),t}function We(e){let t="";if(fe(e))t=e;else if(H(e))for(let n=0;n<e.length;n++){const r=We(e[n]);r&&(t+=r+" ")}else if(J(e))for(const n in e)e[n]&&(t+=n+" ");return t.trim()}const gr=e=>!!(e&&e.__v_isRef===!0),vr=e=>fe(e)?e:e==null?"":H(e)||J(e)&&(e.toString===ur||!q(e.toString))?gr(e)?vr(e.value):JSON.stringify(e,_r,2):String(e),_r=(e,t)=>gr(t)?_r(e,t.value):ke(t)?{[`Map(${t.size})`]:[...t.entries()].reduce((n,[r,o],i)=>(n[Ut(r,i)+" =>"]=o,n),{})}:ar(t)?{[`Set(${t.size})`]:[...t.values()].map(n=>Ut(n))}:Fe(t)?Ut(t):J(t)&&!H(t)&&!fr(t)?String(t):t,Ut=(e,t="")=>{var n;return Fe(e)?`Symbol(${(n=e.description)!=null?n:t})`:e};/**
* @vue/reactivity v3.5.3
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/function Oe(e,...t){console.warn(`[Vue warn] ${e}`,...t)}let j;const Kt=new WeakSet;class Io{constructor(t){this.fn=t,this.deps=void 0,this.depsTail=void 0,this.flags=5,this.nextEffect=void 0,this.cleanup=void 0,this.scheduler=void 0}pause(){this.flags|=64}resume(){this.flags&64&&(this.flags&=-65,Kt.has(this)&&(Kt.delete(this),this.trigger()))}notify(){this.flags&2&&!(this.flags&32)||this.flags&8||(this.flags|=8,this.nextEffect=it,it=this)}run(){if(!(this.flags&1))return this.fn();this.flags|=2,Hn(this),wr(this);const t=j,n=we;j=this,we=!0;try{return this.fn()}finally{process.env.NODE_ENV!=="production"&&j!==this&&Oe("Active effect was not restored correctly - this is likely a Vue internal bug."),br(this),j=t,we=n,this.flags&=-3}}stop(){if(this.flags&1){for(let t=this.deps;t;t=t.nextDep)wn(t);this.deps=this.depsTail=void 0,Hn(this),this.onStop&&this.onStop(),this.flags&=-2}}trigger(){this.flags&64?Kt.add(this):this.scheduler?this.scheduler():this.runIfDirty()}runIfDirty(){on(this)&&this.run()}get dirty(){return on(this)}}let mr=0,it;function _n(){mr++}function mn(){if(--mr>0)return;let e;for(;it;){let t=it;for(it=void 0;t;){const n=t.nextEffect;if(t.nextEffect=void 0,t.flags&=-9,t.flags&1)try{t.trigger()}catch(r){e||(e=r)}t=n}}if(e)throw e}function wr(e){for(let t=e.deps;t;t=t.nextDep)t.version=-1,t.prevActiveLink=t.dep.activeLink,t.dep.activeLink=t}function br(e){let t,n=e.depsTail;for(let r=n;r;r=r.prevDep)r.version===-1?(r===n&&(n=r.prevDep),wn(r),To(r)):t=r,r.dep.activeLink=r.prevActiveLink,r.prevActiveLink=void 0;e.deps=t,e.depsTail=n}function on(e){for(let t=e.deps;t;t=t.nextDep)if(t.dep.version!==t.version||t.dep.computed&&yr(t.dep.computed)===!1||t.dep.version!==t.version)return!0;return!!e._dirty}function yr(e){if(e.flags&2)return!1;if(e.flags&4&&!(e.flags&16)||(e.flags&=-17,e.globalVersion===at))return;e.globalVersion=at;const t=e.dep;if(e.flags|=2,t.version>0&&!e.isSSR&&!on(e)){e.flags&=-3;return}const n=j,r=we;j=e,we=!0;try{wr(e);const o=e.fn(e._value);(t.version===0||Re(o,e._value))&&(e._value=o,t.version++)}catch(o){throw t.version++,o}finally{j=n,we=r,br(e),e.flags&=-3}}function wn(e){const{dep:t,prevSub:n,nextSub:r}=e;if(n&&(n.nextSub=r,e.prevSub=void 0),r&&(r.prevSub=n,e.nextSub=void 0),t.subs===e&&(t.subs=n),!t.subs&&t.computed){t.computed.flags&=-5;for(let o=t.computed.deps;o;o=o.nextDep)wn(o)}}function To(e){const{prevDep:t,nextDep:n}=e;t&&(t.nextDep=n,e.prevDep=void 0),n&&(n.prevDep=t,e.nextDep=void 0)}let we=!0;const Er=[];function ze(){Er.push(we),we=!1}function et(){const e=Er.pop();we=e===void 0?!0:e}function Hn(e){const{cleanup:t}=e;if(e.cleanup=void 0,t){const n=j;j=void 0;try{t()}finally{j=n}}}let at=0;class bn{constructor(t){this.computed=t,this.version=0,this.activeLink=void 0,this.subs=void 0,process.env.NODE_ENV!=="production"&&(this.subsHead=void 0)}track(t){if(!j||!we||j===this.computed)return;let n=this.activeLink;if(n===void 0||n.sub!==j)n=this.activeLink={dep:this,sub:j,version:this.version,nextDep:void 0,prevDep:void 0,nextSub:void 0,prevSub:void 0,prevActiveLink:void 0},j.deps?(n.prevDep=j.depsTail,j.depsTail.nextDep=n,j.depsTail=n):j.deps=j.depsTail=n,j.flags&4&&Or(n);else if(n.version===-1&&(n.version=this.version,n.nextDep)){const r=n.nextDep;r.prevDep=n.prevDep,n.prevDep&&(n.prevDep.nextDep=r),n.prevDep=j.depsTail,n.nextDep=void 0,j.depsTail.nextDep=n,j.depsTail=n,j.deps===n&&(j.deps=r)}return process.env.NODE_ENV!=="production"&&j.onTrack&&j.onTrack(ae({effect:j},t)),n}trigger(t){this.version++,at++,this.notify(t)}notify(t){_n();try{if(process.env.NODE_ENV!=="production")for(let n=this.subsHead;n;n=n.nextSub)process.env.NODE_ENV!=="production"&&n.sub.onTrigger&&!(n.sub.flags&8)&&n.sub.onTrigger(ae({effect:n.sub},t));for(let n=this.subs;n;n=n.prevSub)n.sub.notify()}finally{mn()}}}function Or(e){const t=e.dep.computed;if(t&&!e.dep.subs){t.flags|=20;for(let r=t.deps;r;r=r.nextDep)Or(r)}const n=e.dep.subs;n!==e&&(e.prevSub=n,n&&(n.nextSub=e)),process.env.NODE_ENV!=="production"&&e.dep.subsHead===void 0&&(e.dep.subsHead=e),e.dep.subs=e}const sn=new WeakMap,$e=Symbol(process.env.NODE_ENV!=="production"?"Object iterate":""),an=Symbol(process.env.NODE_ENV!=="production"?"Map keys iterate":""),ct=Symbol(process.env.NODE_ENV!=="production"?"Array iterate":"");function z(e,t,n){if(we&&j){let r=sn.get(e);r||sn.set(e,r=new Map);let o=r.get(n);o||r.set(n,o=new bn),process.env.NODE_ENV!=="production"?o.track({target:e,type:t,key:n}):o.track()}}function Te(e,t,n,r,o,i){const s=sn.get(e);if(!s){at++;return}let c=[];if(t==="clear")c=[...s.values()];else{const l=H(e),N=l&&gn(n);if(l&&n==="length"){const S=Number(r);s.forEach((a,h)=>{(h==="length"||h===ct||!Fe(h)&&h>=S)&&c.push(a)})}else{const S=a=>a&&c.push(a);switch(n!==void 0&&S(s.get(n)),N&&S(s.get(ct)),t){case"add":l?N&&S(s.get("length")):(S(s.get($e)),ke(e)&&S(s.get(an)));break;case"delete":l||(S(s.get($e)),ke(e)&&S(s.get(an)));break;case"set":ke(e)&&S(s.get($e));break}}}_n();for(const l of c)process.env.NODE_ENV!=="production"?l.trigger({target:e,type:t,key:n,newValue:r,oldValue:o,oldTarget:i}):l.trigger();mn()}function je(e){const t=F(e);return t===e?t:(z(t,"iterate",ct),le(e)?t:t.map(ie))}function yn(e){return z(e=F(e),"iterate",ct),e}const Po={__proto__:null,[Symbol.iterator](){return Wt(this,Symbol.iterator,ie)},concat(...e){return je(this).concat(...e.map(t=>H(t)?je(t):t))},entries(){return Wt(this,"entries",e=>(e[1]=ie(e[1]),e))},every(e,t){return ye(this,"every",e,t,void 0,arguments)},filter(e,t){return ye(this,"filter",e,t,n=>n.map(ie),arguments)},find(e,t){return ye(this,"find",e,t,ie,arguments)},findIndex(e,t){return ye(this,"findIndex",e,t,void 0,arguments)},findLast(e,t){return ye(this,"findLast",e,t,ie,arguments)},findLastIndex(e,t){return ye(this,"findLastIndex",e,t,void 0,arguments)},forEach(e,t){return ye(this,"forEach",e,t,void 0,arguments)},includes(...e){return Qt(this,"includes",e)},indexOf(...e){return Qt(this,"indexOf",e)},join(e){return je(this).join(e)},lastIndexOf(...e){return Qt(this,"lastIndexOf",e)},map(e,t){return ye(this,"map",e,t,void 0,arguments)},pop(){return tt(this,"pop")},push(...e){return tt(this,"push",e)},reduce(e,...t){return jn(this,"reduce",e,t)},reduceRight(e,...t){return jn(this,"reduceRight",e,t)},shift(){return tt(this,"shift")},some(e,t){return ye(this,"some",e,t,void 0,arguments)},splice(...e){return tt(this,"splice",e)},toReversed(){return je(this).toReversed()},toSorted(e){return je(this).toSorted(e)},toSpliced(...e){return je(this).toSpliced(...e)},unshift(...e){return tt(this,"unshift",e)},values(){return Wt(this,"values",ie)}};function Wt(e,t,n){const r=yn(e),o=r[t]();return r!==e&&!le(e)&&(o._next=o.next,o.next=()=>{const i=o._next();return i.value&&(i.value=n(i.value)),i}),o}const Ro=Array.prototype;function ye(e,t,n,r,o,i){const s=yn(e),c=s!==e&&!le(e),l=s[t];if(l!==Ro[t]){const a=l.apply(e,i);return c?ie(a):a}let N=n;s!==e&&(c?N=function(a,h){return n.call(this,ie(a),h,e)}:n.length>2&&(N=function(a,h){return n.call(this,a,h,e)}));const S=l.call(s,N,r);return c&&o?o(S):S}function jn(e,t,n,r){const o=yn(e);let i=n;return o!==e&&(le(e)?n.length>3&&(i=function(s,c,l){return n.call(this,s,c,l,e)}):i=function(s,c,l){return n.call(this,s,ie(c),l,e)}),o[t](i,...r)}function Qt(e,t,n){const r=F(e);z(r,"iterate",ct);const o=r[t](...n);return(o===-1||o===!1)&&Dt(n[0])?(n[0]=F(n[0]),r[t](...n)):o}function tt(e,t,n=[]){ze(),_n();const r=F(e)[t].apply(e,n);return mn(),et(),r}const Ao=mo("__proto__,__v_isRef,__isVue"),Nr=new Set(Object.getOwnPropertyNames(Symbol).filter(e=>e!=="arguments"&&e!=="caller").map(e=>Symbol[e]).filter(Fe));function Bo(e){Fe(e)||(e=String(e));const t=F(this);return z(t,"has",e),t.hasOwnProperty(e)}class xr{constructor(t=!1,n=!1){this._isReadonly=t,this._isShallow=n}get(t,n,r){const o=this._isReadonly,i=this._isShallow;if(n==="__v_isReactive")return!o;if(n==="__v_isReadonly")return o;if(n==="__v_isShallow")return i;if(n==="__v_raw")return r===(o?i?Ir:Mr:i?Jo:Cr).get(t)||Object.getPrototypeOf(t)===Object.getPrototypeOf(r)?t:void 0;const s=H(t);if(!o){let l;if(s&&(l=Po[n]))return l;if(n==="hasOwnProperty")return Bo}const c=Reflect.get(t,n,re(t)?t:r);return(Fe(n)?Nr.has(n):Ao(n))||(o||z(t,"get",n),i)?c:re(c)?s&&gn(n)?c:c.value:J(c)?o?Tr(c):Nn(c):c}}class Vo extends xr{constructor(t=!1){super(!1,t)}set(t,n,r,o){let i=t[n];if(!this._isShallow){const l=Ne(i);if(!le(r)&&!Ne(r)&&(i=F(i),r=F(r)),!H(t)&&re(i)&&!re(r))return l?!1:(i.value=r,!0)}const s=H(t)&&gn(n)?Number(n)<t.length:X(t,n),c=Reflect.set(t,n,r,re(t)?t:o);return t===F(o)&&(s?Re(r,i)&&Te(t,"set",n,r,i):Te(t,"add",n,r)),c}deleteProperty(t,n){const r=X(t,n),o=t[n],i=Reflect.deleteProperty(t,n);return i&&r&&Te(t,"delete",n,void 0,o),i}has(t,n){const r=Reflect.has(t,n);return(!Fe(n)||!Nr.has(n))&&z(t,"has",n),r}ownKeys(t){return z(t,"iterate",H(t)?"length":$e),Reflect.ownKeys(t)}}class Dr extends xr{constructor(t=!1){super(!0,t)}set(t,n){return process.env.NODE_ENV!=="production"&&Oe(`Set operation on key "${String(n)}" failed: target is readonly.`,t),!0}deleteProperty(t,n){return process.env.NODE_ENV!=="production"&&Oe(`Delete operation on key "${String(n)}" failed: target is readonly.`,t),!0}}const ko=new Vo,$o=new Dr,Lo=new Dr(!0),En=e=>e,kt=e=>Reflect.getPrototypeOf(e);function pt(e,t,n=!1,r=!1){e=e.__v_raw;const o=F(e),i=F(t);n||(Re(t,i)&&z(o,"get",t),z(o,"get",i));const{has:s}=kt(o),c=r?En:n?Dn:ie;if(s.call(o,t))return c(e.get(t));if(s.call(o,i))return c(e.get(i));e!==o&&e.get(t)}function ht(e,t=!1){const n=this.__v_raw,r=F(n),o=F(e);return t||(Re(e,o)&&z(r,"has",e),z(r,"has",o)),e===o?n.has(e):n.has(e)||n.has(o)}function gt(e,t=!1){return e=e.__v_raw,!t&&z(F(e),"iterate",$e),Reflect.get(e,"size",e)}function Un(e,t=!1){!t&&!le(e)&&!Ne(e)&&(e=F(e));const n=F(this);return kt(n).has.call(n,e)||(n.add(e),Te(n,"add",e,e)),this}function Kn(e,t,n=!1){!n&&!le(t)&&!Ne(t)&&(t=F(t));const r=F(this),{has:o,get:i}=kt(r);let s=o.call(r,e);s?process.env.NODE_ENV!=="production"&&Sr(r,o,e):(e=F(e),s=o.call(r,e));const c=i.call(r,e);return r.set(e,t),s?Re(t,c)&&Te(r,"set",e,t,c):Te(r,"add",e,t),this}function Wn(e){const t=F(this),{has:n,get:r}=kt(t);let o=n.call(t,e);o?process.env.NODE_ENV!=="production"&&Sr(t,n,e):(e=F(e),o=n.call(t,e));const i=r?r.call(t,e):void 0,s=t.delete(e);return o&&Te(t,"delete",e,void 0,i),s}function Qn(){const e=F(this),t=e.size!==0,n=process.env.NODE_ENV!=="production"?ke(e)?new Map(e):new Set(e):void 0,r=e.clear();return t&&Te(e,"clear",void 0,void 0,n),r}function vt(e,t){return function(r,o){const i=this,s=i.__v_raw,c=F(s),l=t?En:e?Dn:ie;return!e&&z(c,"iterate",$e),s.forEach((N,S)=>r.call(o,l(N),l(S),i))}}function _t(e,t,n){return function(...r){const o=this.__v_raw,i=F(o),s=ke(i),c=e==="entries"||e===Symbol.iterator&&s,l=e==="keys"&&s,N=o[e](...r),S=n?En:t?Dn:ie;return!t&&z(i,"iterate",l?an:$e),{next(){const{value:a,done:h}=N.next();return h?{value:a,done:h}:{value:c?[S(a[0]),S(a[1])]:S(a),done:h}},[Symbol.iterator](){return this}}}}function De(e){return function(...t){if(process.env.NODE_ENV!=="production"){const n=t[0]?`on key "${t[0]}" `:"";Oe(`${pr(e)} operation ${n}failed: target is readonly.`,F(this))}return e==="delete"?!1:e==="clear"?void 0:this}}function Fo(){const e={get(i){return pt(this,i)},get size(){return gt(this)},has:ht,add:Un,set:Kn,delete:Wn,clear:Qn,forEach:vt(!1,!1)},t={get(i){return pt(this,i,!1,!0)},get size(){return gt(this)},has:ht,add(i){return Un.call(this,i,!0)},set(i,s){return Kn.call(this,i,s,!0)},delete:Wn,clear:Qn,forEach:vt(!1,!0)},n={get(i){return pt(this,i,!0)},get size(){return gt(this,!0)},has(i){return ht.call(this,i,!0)},add:De("add"),set:De("set"),delete:De("delete"),clear:De("clear"),forEach:vt(!0,!1)},r={get(i){return pt(this,i,!0,!0)},get size(){return gt(this,!0)},has(i){return ht.call(this,i,!0)},add:De("add"),set:De("set"),delete:De("delete"),clear:De("clear"),forEach:vt(!0,!0)};return["keys","values","entries",Symbol.iterator].forEach(i=>{e[i]=_t(i,!1,!1),n[i]=_t(i,!0,!1),t[i]=_t(i,!1,!0),r[i]=_t(i,!0,!0)}),[e,n,t,r]}const[qo,Ho,jo,Uo]=Fo();function On(e,t){const n=t?e?Uo:jo:e?Ho:qo;return(r,o,i)=>o==="__v_isReactive"?!e:o==="__v_isReadonly"?e:o==="__v_raw"?r:Reflect.get(X(n,o)&&o in r?n:r,o,i)}const Ko={get:On(!1,!1)},Wo={get:On(!0,!1)},Qo={get:On(!0,!0)};function Sr(e,t,n){const r=F(n);if(r!==n&&t.call(e,r)){const o=lr(e);Oe(`Reactive ${o} contains both the raw and reactive versions of the same object${o==="Map"?" as keys":""}, which can lead to inconsistencies. Avoid differentiating between the raw and reactive versions of an object and only use the reactive version if possible.`)}}const Cr=new WeakMap,Jo=new WeakMap,Mr=new WeakMap,Ir=new WeakMap;function Yo(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}function Go(e){return e.__v_skip||!Object.isExtensible(e)?0:Yo(lr(e))}function Nn(e){return Ne(e)?e:xn(e,!1,ko,Ko,Cr)}function Tr(e){return xn(e,!0,$o,Wo,Mr)}function mt(e){return xn(e,!0,Lo,Qo,Ir)}function xn(e,t,n,r,o){if(!J(e))return process.env.NODE_ENV!=="production"&&Oe(`value cannot be made ${t?"readonly":"reactive"}: ${String(e)}`),e;if(e.__v_raw&&!(t&&e.__v_isReactive))return e;const i=o.get(e);if(i)return i;const s=Go(e);if(s===0)return e;const c=new Proxy(e,s===2?r:n);return o.set(e,c),c}function Qe(e){return Ne(e)?Qe(e.__v_raw):!!(e&&e.__v_isReactive)}function Ne(e){return!!(e&&e.__v_isReadonly)}function le(e){return!!(e&&e.__v_isShallow)}function Dt(e){return e?!!e.__v_raw:!1}function F(e){const t=e&&e.__v_raw;return t?F(t):e}function Xo(e){return Object.isExtensible(e)&&No(e,"__v_skip",!0),e}const ie=e=>J(e)?Nn(e):e,Dn=e=>J(e)?Tr(e):e;function re(e){return e?e.__v_isRef===!0:!1}function Zo(e){return zo(e,!1)}function zo(e,t){return re(e)?e:new ei(e,t)}class ei{constructor(t,n){this.dep=new bn,this.__v_isRef=!0,this.__v_isShallow=!1,this._rawValue=n?t:F(t),this._value=n?t:ie(t),this.__v_isShallow=n}get value(){return process.env.NODE_ENV!=="production"?this.dep.track({target:this,type:"get",key:"value"}):this.dep.track(),this._value}set value(t){const n=this._rawValue,r=this.__v_isShallow||le(t)||Ne(t);t=r?t:F(t),Re(t,n)&&(this._rawValue=t,this._value=r?t:ie(t),process.env.NODE_ENV!=="production"?this.dep.trigger({target:this,type:"set",key:"value",newValue:t,oldValue:n}):this.dep.trigger())}}function ti(e){return re(e)?e.value:e}const ni={get:(e,t,n)=>t==="__v_raw"?e:ti(Reflect.get(e,t,n)),set:(e,t,n,r)=>{const o=e[t];return re(o)&&!re(n)?(o.value=n,!0):Reflect.set(e,t,n,r)}};function Pr(e){return Qe(e)?e:new Proxy(e,ni)}class ri{constructor(t,n,r){this.fn=t,this.setter=n,this._value=void 0,this.dep=new bn(this),this.__v_isRef=!0,this.deps=void 0,this.depsTail=void 0,this.flags=16,this.globalVersion=at-1,this.effect=this,this.__v_isReadonly=!n,this.isSSR=r}notify(){j!==this?(this.flags|=16,this.dep.notify()):process.env.NODE_ENV}get value(){const t=process.env.NODE_ENV!=="production"?this.dep.track({target:this,type:"get",key:"value"}):this.dep.track();return yr(this),t&&(t.version=this.dep.version),this._value}set value(t){this.setter?this.setter(t):process.env.NODE_ENV!=="production"&&Oe("Write operation failed: computed value is readonly")}}function oi(e,t,n=!1){let r,o;q(e)?r=e:(r=e.get,o=e.set);const i=new ri(r,o,n);return process.env.NODE_ENV!=="production"&&t&&!n&&(i.onTrack=t.onTrack,i.onTrigger=t.onTrigger),i}const wt={},St=new WeakMap;let Ve;function ii(e,t=!1,n=Ve){if(n){let r=St.get(n);r||St.set(n,r=[]),r.push(e)}else process.env.NODE_ENV!=="production"&&!t&&Oe("onWatcherCleanup() was called when there was no active watcher to associate with.")}function si(e,t,n=ve){const{immediate:r,deep:o,once:i,scheduler:s,augmentJob:c,call:l}=n,N=$=>{(n.onWarn||Oe)("Invalid watch source: ",$,"A watch source can only be a getter/effect function, a ref, a reactive object, or an array of these types.")},S=$=>o?$:le($)||o===!1||o===0?Ce($,1):Ce($);let a,h,T,k,U=!1,ee=!1;if(re(e)?(h=()=>e.value,U=le(e)):Qe(e)?(h=()=>S(e),U=!0):H(e)?(ee=!0,U=e.some($=>Qe($)||le($)),h=()=>e.map($=>{if(re($))return $.value;if(Qe($))return S($);if(q($))return l?l($,2):$();process.env.NODE_ENV!=="production"&&N($)})):q(e)?t?h=l?()=>l(e,2):e:h=()=>{if(T){ze();try{T()}finally{et()}}const $=Ve;Ve=a;try{return l?l(e,3,[k]):e(k)}finally{Ve=$}}:(h=se,process.env.NODE_ENV!=="production"&&N(e)),t&&o){const $=h,v=o===!0?1/0:o;h=()=>Ce($(),v)}const Y=()=>{a.stop()};if(i)if(t){const $=t;t=(...v)=>{$(...v),Y()}}else{const $=h;h=()=>{$(),Y()}}let Q=ee?new Array(e.length).fill(wt):wt;const te=$=>{if(!(!(a.flags&1)||!a.dirty&&!$))if(t){const v=a.run();if(o||U||(ee?v.some((D,m)=>Re(D,Q[m])):Re(v,Q))){T&&T();const D=Ve;Ve=a;try{const m=[v,Q===wt?void 0:ee&&Q[0]===wt?[]:Q,k];l?l(t,3,m):t(...m),Q=v}finally{Ve=D}}}else a.run()};return c&&c(te),a=new Io(h),a.scheduler=s?()=>s(te,!1):te,k=$=>ii($,!1,a),T=a.onStop=()=>{const $=St.get(a);if($){if(l)l($,4);else for(const v of $)v();St.delete(a)}},process.env.NODE_ENV!=="production"&&(a.onTrack=n.onTrack,a.onTrigger=n.onTrigger),t?r?te(!0):Q=a.run():s?s(te.bind(null,!0),!0):a.run(),Y.pause=a.pause.bind(a),Y.resume=a.resume.bind(a),Y.stop=Y,Y}function Ce(e,t=1/0,n){if(t<=0||!J(e)||e.__v_skip||(n=n||new Set,n.has(e)))return e;if(n.add(e),t--,re(e))Ce(e.value,t,n);else if(H(e))for(let r=0;r<e.length;r++)Ce(e[r],t,n);else if(ar(e)||ke(e))e.forEach(r=>{Ce(r,t,n)});else if(fr(e)){for(const r in e)Ce(e[r],t,n);for(const r of Object.getOwnPropertySymbols(e))Object.prototype.propertyIsEnumerable.call(e,r)&&Ce(e[r],t,n)}return e}/**
* @vue/runtime-core v3.5.3
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/const Le=[];function Rr(e){Le.push(e)}function Ar(){Le.pop()}let Jt=!1;function L(e,...t){if(Jt)return;Jt=!0,ze();const n=Le.length?Le[Le.length-1].component:null,r=n&&n.appContext.config.warnHandler,o=ai();if(r)$t(r,n,11,[e+t.map(i=>{var s,c;return(c=(s=i.toString)==null?void 0:s.call(i))!=null?c:JSON.stringify(i)}).join(""),n&&n.proxy,o.map(({vnode:i})=>`at <${Vn(n,i.type)}>`).join(`
`),o]);else{const i=[`[Vue warn]: ${e}`,...t];o.length&&i.push(`
`,...ci(o)),console.warn(...i)}et(),Jt=!1}function ai(){let e=Le[Le.length-1];if(!e)return[];const t=[];for(;e;){const n=t[0];n&&n.vnode===e?n.recurseCount++:t.push({vnode:e,recurseCount:0});const r=e.component&&e.component.parent;e=r&&r.vnode}return t}function ci(e){const t=[];return e.forEach((n,r)=>{t.push(...r===0?[]:[`
`],...ui(n))}),t}function ui({vnode:e,recurseCount:t}){const n=t>0?`... (${t} recursive calls)`:"",r=e.component?e.component.parent==null:!1,o=` at <${Vn(e.component,e.type,r)}`,i=">"+n;return e.props?[o,...li(e.props),i]:[o+i]}function li(e){const t=[],n=Object.keys(e);return n.slice(0,3).forEach(r=>{t.push(...Br(r,e[r]))}),n.length>3&&t.push(" ..."),t}function Br(e,t,n){return fe(t)?(t=JSON.stringify(t),n?t:[`${e}=${t}`]):typeof t=="number"||typeof t=="boolean"||t==null?n?t:[`${e}=${t}`]:re(t)?(t=Br(e,F(t.value),!0),n?t:[`${e}=Ref<`,t,">"]):q(t)?[`${e}=fn${t.name?`<${t.name}>`:""}`]:(t=F(t),n?t:[`${e}=`,t])}function fi(e,t){process.env.NODE_ENV!=="production"&&e!==void 0&&(typeof e!="number"?L(`${t} is not a valid number - got ${JSON.stringify(e)}.`):isNaN(e)&&L(`${t} is NaN - the duration expression might be incorrect.`))}const Sn={sp:"serverPrefetch hook",bc:"beforeCreate hook",c:"created hook",bm:"beforeMount hook",m:"mounted hook",bu:"beforeUpdate hook",u:"updated",bum:"beforeUnmount hook",um:"unmounted hook",a:"activated hook",da:"deactivated hook",ec:"errorCaptured hook",rtc:"renderTracked hook",rtg:"renderTriggered hook",0:"setup function",1:"render function",2:"watcher getter",3:"watcher callback",4:"watcher cleanup function",5:"native event handler",6:"component event handler",7:"vnode hook",8:"directive hook",9:"transition hook",10:"app errorHandler",11:"app warnHandler",12:"ref function",13:"async component loader",14:"scheduler flush",15:"component update",16:"app unmount cleanup function"};function $t(e,t,n,r){try{return r?e(...r):e()}catch(o){Ft(o,t,n)}}function Lt(e,t,n,r){if(q(e)){const o=$t(e,t,n,r);return o&&cr(o)&&o.catch(i=>{Ft(i,t,n)}),o}if(H(e)){const o=[];for(let i=0;i<e.length;i++)o.push(Lt(e[i],t,n,r));return o}else process.env.NODE_ENV!=="production"&&L(`Invalid value type passed to callWithAsyncErrorHandling(): ${typeof e}`)}function Ft(e,t,n,r=!0){const o=t?t.vnode:null,{errorHandler:i,throwUnhandledErrorInProduction:s}=t&&t.appContext.config||ve;if(t){let c=t.parent;const l=t.proxy,N=process.env.NODE_ENV!=="production"?Sn[n]:`https://vuejs.org/error-reference/#runtime-${n}`;for(;c;){const S=c.ec;if(S){for(let a=0;a<S.length;a++)if(S[a](e,l,N)===!1)return}c=c.parent}if(i){ze(),$t(i,null,10,[e,l,N]),et();return}}di(e,n,o,r,s)}function di(e,t,n,r=!0,o=!1){if(process.env.NODE_ENV!=="production"){const i=Sn[t];if(n&&Rr(n),L(`Unhandled error${i?` during execution of ${i}`:""}`),n&&Ar(),r)throw e;console.error(e)}else{if(o)throw e;console.error(e)}}let Ct=!1,cn=!1;const _e=[];let Ee=0;const Je=[];let Se=null,Ke=0;const Vr=Promise.resolve();let Cn=null;const pi=100;function hi(e){const t=Cn||Vr;return e?t.then(this?e.bind(this):e):t}function gi(e){let t=Ct?Ee+1:0,n=_e.length;for(;t<n;){const r=t+n>>>1,o=_e[r],i=ut(o);i<e||i===e&&o.flags&2?t=r+1:n=r}return t}function Mn(e){if(!(e.flags&1)){const t=ut(e),n=_e[_e.length-1];!n||!(e.flags&2)&&t>=ut(n)?_e.push(e):_e.splice(gi(t),0,e),e.flags|=1,kr()}}function kr(){!Ct&&!cn&&(cn=!0,Cn=Vr.then($r))}function Mt(e){H(e)?Je.push(...e):Se&&e.id===-1?Se.splice(Ke+1,0,e):e.flags&1||(Je.push(e),e.flags|=1),kr()}function vi(e){if(Je.length){const t=[...new Set(Je)].sort((n,r)=>ut(n)-ut(r));if(Je.length=0,Se){Se.push(...t);return}for(Se=t,process.env.NODE_ENV!=="production"&&(e=e||new Map),Ke=0;Ke<Se.length;Ke++){const n=Se[Ke];process.env.NODE_ENV!=="production"&&Lr(e,n)||(n.flags&4&&(n.flags&=-2),n.flags&8||n(),n.flags&=-2)}Se=null,Ke=0}}const ut=e=>e.id==null?e.flags&2?-1:1/0:e.id;function $r(e){cn=!1,Ct=!0,process.env.NODE_ENV!=="production"&&(e=e||new Map);const t=process.env.NODE_ENV!=="production"?n=>Lr(e,n):se;try{for(Ee=0;Ee<_e.length;Ee++){const n=_e[Ee];if(n&&!(n.flags&8)){if(process.env.NODE_ENV!=="production"&&t(n))continue;n.flags&4&&(n.flags&=-2),$t(n,n.i,n.i?15:14),n.flags&=-2}}}finally{for(;Ee<_e.length;Ee++){const n=_e[Ee];n&&(n.flags&=-2)}Ee=0,_e.length=0,vi(e),Ct=!1,Cn=null,(_e.length||Je.length)&&$r(e)}}function Lr(e,t){if(!e.has(t))e.set(t,1);else{const n=e.get(t);if(n>pi){const r=t.i,o=r&&uo(r.type);return Ft(`Maximum recursive updates exceeded${o?` in component <${o}>`:""}. This means you have a reactive effect that is mutating its own dependencies and thus recursively triggering itself. Possible sources include component template, render function, updated hook or watcher source function.`,null,10),!0}else e.set(t,n+1)}}const Et=new Map;process.env.NODE_ENV!=="production"&&(hr().__VUE_HMR_RUNTIME__={createRecord:Yt(_i),rerender:Yt(mi),reload:Yt(wi)});const It=new Map;function _i(e,t){return It.has(e)?!1:(It.set(e,{initialDef:Tt(t),instances:new Set}),!0)}function Tt(e){return lo(e)?e.__vccOpts:e}function mi(e,t){const n=It.get(e);n&&(n.initialDef.render=t,[...n.instances].forEach(r=>{t&&(r.render=t,Tt(r.type).render=t),r.renderCache=[],r.update()}))}function wi(e,t){const n=It.get(e);if(!n)return;t=Tt(t),Jn(n.initialDef,t);const r=[...n.instances];for(let o=0;o<r.length;o++){const i=r[o],s=Tt(i.type);let c=Et.get(s);c||(s!==n.initialDef&&Jn(s,t),Et.set(s,c=new Set)),c.add(i),i.appContext.propsCache.delete(i.type),i.appContext.emitsCache.delete(i.type),i.appContext.optionsCache.delete(i.type),i.ceReload?(c.add(i),i.ceReload(t.styles),c.delete(i)):i.parent?Mn(()=>{i.parent.update(),c.delete(i)}):i.appContext.reload?i.appContext.reload():typeof window<"u"?window.location.reload():console.warn("[HMR] Root or manually mounted instance modified. Full reload required."),i.root.ce&&i!==i.root&&i.root.ce._removeChildStyle(s)}Mt(()=>{Et.clear()})}function Jn(e,t){ae(e,t);for(const n in e)n!=="__file"&&!(n in t)&&delete e[n]}function Yt(e){return(t,n)=>{try{return e(t,n)}catch(r){console.error(r),console.warn("[HMR] Something went wrong during Vue component hot-reload. Full reload required.")}}}let Me,rt=[],un=!1;function Fr(e,...t){Me?Me.emit(e,...t):un||rt.push({event:e,args:t})}function qr(e,t){var n,r;Me=e,Me?(Me.enabled=!0,rt.forEach(({event:o,args:i})=>Me.emit(o,...i)),rt=[]):typeof window<"u"&&window.HTMLElement&&!((r=(n=window.navigator)==null?void 0:n.userAgent)!=null&&r.includes("jsdom"))?((t.__VUE_DEVTOOLS_HOOK_REPLAY__=t.__VUE_DEVTOOLS_HOOK_REPLAY__||[]).push(i=>{qr(i,t)}),setTimeout(()=>{Me||(t.__VUE_DEVTOOLS_HOOK_REPLAY__=null,un=!0,rt=[])},3e3)):(un=!0,rt=[])}const bi=yi("component:updated");/*! #__NO_SIDE_EFFECTS__ */function yi(e){return t=>{Fr(e,t.appContext.app,t.uid,t.parent?t.parent.uid:void 0,t)}}const Ei=Hr("perf:start"),Oi=Hr("perf:end");function Hr(e){return(t,n,r)=>{Fr(e,t.appContext.app,t.uid,t,n,r)}}let pe=null,jr=null;function Yn(e){const t=pe;return pe=e,jr=e&&e.type.__scopeId||null,t}function Ni(e,t=pe,n){if(!t||e._n)return e;const r=(...o)=>{r._d&&nr(-1);const i=Yn(t);let s;try{s=e(...o)}finally{Yn(i),r._d&&nr(1)}return process.env.NODE_ENV!=="production"&&bi(t),s};return r._n=!0,r._c=!0,r._d=!0,r}const xi=e=>e.__isTeleport;function Ur(e,t){e.shapeFlag&6&&e.component?(e.transition=t,Ur(e.component.subTree,t)):e.shapeFlag&128?(e.ssContent.transition=t.clone(e.ssContent),e.ssFallback.transition=t.clone(e.ssFallback)):e.transition=t}/*! #__NO_SIDE_EFFECTS__ */function Kr(e,t){return q(e)?(()=>ae({name:e.name},t,{setup:e}))():e}function Di(e){e.ids=[e.ids[0]+e.ids[2]+++"-",0,0]}const Si=e=>e.type.__isKeepAlive;function Ci(e,t){Wr(e,"a",t)}function Mi(e,t){Wr(e,"da",t)}function Wr(e,t,n=ce){const r=e.__wdc||(e.__wdc=()=>{let o=n;for(;o;){if(o.isDeactivated)return;o=o.parent}return e()});if(qt(t,r,n),n){let o=n.parent;for(;o&&o.parent;)Si(o.parent.vnode)&&Ii(r,t,n,o),o=o.parent}}function Ii(e,t,n,r){const o=qt(t,e,r,!0);Qr(()=>{yo(r[t],o)},n)}function qt(e,t,n=ce,r=!1){if(n){const o=n[e]||(n[e]=[]),i=t.__weh||(t.__weh=(...s)=>{ze();const c=Bn(n),l=Lt(t,n,e,s);return c(),et(),l});return r?o.unshift(i):o.push(i),i}else if(process.env.NODE_ENV!=="production"){const o=Oo(Sn[e].replace(/ hook$/,""));L(`${o} is called when there is no active component instance to be associated with. Lifecycle injection APIs can only be used during execution of setup(). If you are using async setup(), make sure to register lifecycle hooks before the first await statement.`)}}const xe=e=>(t,n=ce)=>{(!Ht||e==="sp")&&qt(e,(...r)=>t(...r),n)},Ti=xe("bm"),Pi=xe("m"),Ri=xe("bu"),Ai=xe("u"),Bi=xe("bum"),Qr=xe("um"),Vi=xe("sp"),ki=xe("rtg"),$i=xe("rtc");function Li(e,t=ce){qt("ec",e,t)}const Jr=Symbol.for("v-ndc"),ln=e=>e?ws(e)?Es(e):ln(e.parent):null,st=ae(Object.create(null),{$:e=>e,$el:e=>e.vnode.el,$data:e=>e.data,$props:e=>process.env.NODE_ENV!=="production"?mt(e.props):e.props,$attrs:e=>process.env.NODE_ENV!=="production"?mt(e.attrs):e.attrs,$slots:e=>process.env.NODE_ENV!=="production"?mt(e.slots):e.slots,$refs:e=>process.env.NODE_ENV!=="production"?mt(e.refs):e.refs,$parent:e=>ln(e.parent),$root:e=>ln(e.root),$host:e=>e.ce,$emit:e=>e.emit,$options:e=>Tn(e),$forceUpdate:e=>e.f||(e.f=()=>{Mn(e.update)}),$nextTick:e=>e.n||(e.n=hi.bind(e.proxy)),$watch:e=>ns.bind(e)}),In=e=>e==="_"||e==="$",Gt=(e,t)=>e!==ve&&!e.__isScriptSetup&&X(e,t),Fi={get({_:e},t){if(t==="__v_skip")return!0;const{ctx:n,setupState:r,data:o,props:i,accessCache:s,type:c,appContext:l}=e;if(process.env.NODE_ENV!=="production"&&t==="__isVue")return!0;let N;if(t[0]!=="$"){const T=s[t];if(T!==void 0)switch(T){case 1:return r[t];case 2:return o[t];case 4:return n[t];case 3:return i[t]}else{if(Gt(r,t))return s[t]=1,r[t];if(o!==ve&&X(o,t))return s[t]=2,o[t];if((N=e.propsOptions[0])&&X(N,t))return s[t]=3,i[t];if(n!==ve&&X(n,t))return s[t]=4,n[t];fn&&(s[t]=0)}}const S=st[t];let a,h;if(S)return t==="$attrs"?(z(e.attrs,"get",""),process.env.NODE_ENV!=="production"&&void 0):process.env.NODE_ENV!=="production"&&t==="$slots"&&z(e,"get",t),S(e);if((a=c.__cssModules)&&(a=a[t]))return a;if(n!==ve&&X(n,t))return s[t]=4,n[t];if(h=l.config.globalProperties,X(h,t))return h[t];process.env.NODE_ENV!=="production"&&pe&&(!fe(t)||t.indexOf("__v")!==0)&&(o!==ve&&In(t[0])&&X(o,t)?L(`Property ${JSON.stringify(t)} must be accessed via $data because it starts with a reserved character ("$" or "_") and is not proxied on the render context.`):e===pe&&L(`Property ${JSON.stringify(t)} was accessed during render but is not defined on instance.`))},set({_:e},t,n){const{data:r,setupState:o,ctx:i}=e;return Gt(o,t)?(o[t]=n,!0):process.env.NODE_ENV!=="production"&&o.__isScriptSetup&&X(o,t)?(L(`Cannot mutate <script setup> binding "${t}" from Options API.`),!1):r!==ve&&X(r,t)?(r[t]=n,!0):X(e.props,t)?(process.env.NODE_ENV!=="production"&&L(`Attempting to mutate prop "${t}". Props are readonly.`),!1):t[0]==="$"&&t.slice(1)in e?(process.env.NODE_ENV!=="production"&&L(`Attempting to mutate public property "${t}". Properties starting with $ are reserved and readonly.`),!1):(process.env.NODE_ENV!=="production"&&t in e.appContext.config.globalProperties?Object.defineProperty(i,t,{enumerable:!0,configurable:!0,value:n}):i[t]=n,!0)},has({_:{data:e,setupState:t,accessCache:n,ctx:r,appContext:o,propsOptions:i}},s){let c;return!!n[s]||e!==ve&&X(e,s)||Gt(t,s)||(c=i[0])&&X(c,s)||X(r,s)||X(st,s)||X(o.config.globalProperties,s)},defineProperty(e,t,n){return n.get!=null?e._.accessCache[t]=0:X(n,"value")&&this.set(e,t,n.value,null),Reflect.defineProperty(e,t,n)}};process.env.NODE_ENV!=="production"&&(Fi.ownKeys=e=>(L("Avoid app logic that relies on enumerating keys on a component instance. The keys will be empty in production mode to avoid performance overhead."),Reflect.ownKeys(e)));function qi(e){const{ctx:t,setupState:n}=e;Object.keys(F(n)).forEach(r=>{if(!n.__isScriptSetup){if(In(r[0])){L(`setup() return property ${JSON.stringify(r)} should not start with "$" or "_" which are reserved prefixes for Vue internals.`);return}Object.defineProperty(t,r,{enumerable:!0,configurable:!0,get:()=>n[r],set:se})}})}function Gn(e){return H(e)?e.reduce((t,n)=>(t[n]=null,t),{}):e}function Hi(){const e=Object.create(null);return(t,n)=>{e[n]?L(`${t} property "${n}" is already defined in ${e[n]}.`):e[n]=t}}let fn=!0;function ji(e){const t=Tn(e),n=e.proxy,r=e.ctx;fn=!1,t.beforeCreate&&Xn(t.beforeCreate,e,"bc");const{data:o,computed:i,methods:s,watch:c,provide:l,inject:N,created:S,beforeMount:a,mounted:h,beforeUpdate:T,updated:k,activated:U,deactivated:ee,beforeDestroy:Y,beforeUnmount:Q,destroyed:te,unmounted:$,render:v,renderTracked:D,renderTriggered:m,errorCaptured:C,serverPrefetch:d,expose:g,inheritAttrs:f,components:u,directives:O,filters:y}=t,b=process.env.NODE_ENV!=="production"?Hi():null;if(process.env.NODE_ENV!=="production"){const[M]=e.propsOptions;if(M)for(const P in M)b("Props",P)}if(N&&Ui(N,r,b),s)for(const M in s){const P=s[M];q(P)?(process.env.NODE_ENV!=="production"?Object.defineProperty(r,M,{value:P.bind(n),configurable:!0,enumerable:!0,writable:!0}):r[M]=P.bind(n),process.env.NODE_ENV!=="production"&&b("Methods",M)):process.env.NODE_ENV!=="production"&&L(`Method "${M}" has type "${typeof P}" in the component definition. Did you reference the function correctly?`)}if(o){process.env.NODE_ENV!=="production"&&!q(o)&&L("The data option must be a function. Plain object usage is no longer supported.");const M=o.call(n,n);if(process.env.NODE_ENV!=="production"&&cr(M)&&L("data() returned a Promise - note data() cannot be async; If you intend to perform data fetching before component renders, use async setup() + <Suspense>."),!J(M))process.env.NODE_ENV!=="production"&&L("data() should return an object.");else if(e.data=Nn(M),process.env.NODE_ENV!=="production")for(const P in M)b("Data",P),In(P[0])||Object.defineProperty(r,P,{configurable:!0,enumerable:!0,get:()=>M[P],set:se})}if(fn=!0,i)for(const M in i){const P=i[M],K=q(P)?P.bind(n,n):q(P.get)?P.get.bind(n,n):se;process.env.NODE_ENV!=="production"&&K===se&&L(`Computed property "${M}" has no getter.`);const Z=!q(P)&&q(P.set)?P.set.bind(n):process.env.NODE_ENV!=="production"?()=>{L(`Write operation failed: computed property "${M}" is readonly.`)}:se,de=xs({get:K,set:Z});Object.defineProperty(r,M,{enumerable:!0,configurable:!0,get:()=>de.value,set:me=>de.value=me}),process.env.NODE_ENV!=="production"&&b("Computed",M)}if(c)for(const M in c)Yr(c[M],r,n,M);if(l){const M=q(l)?l.call(n):l;Reflect.ownKeys(M).forEach(P=>{Yi(P,M[P])})}S&&Xn(S,e,"c");function R(M,P){H(P)?P.forEach(K=>M(K.bind(n))):P&&M(P.bind(n))}if(R(Ti,a),R(Pi,h),R(Ri,T),R(Ai,k),R(Ci,U),R(Mi,ee),R(Li,C),R($i,D),R(ki,m),R(Bi,Q),R(Qr,$),R(Vi,d),H(g))if(g.length){const M=e.exposed||(e.exposed={});g.forEach(P=>{Object.defineProperty(M,P,{get:()=>n[P],set:K=>n[P]=K})})}else e.exposed||(e.exposed={});v&&e.render===se&&(e.render=v),f!=null&&(e.inheritAttrs=f),u&&(e.components=u),O&&(e.directives=O),d&&Di(e)}function Ui(e,t,n=se){H(e)&&(e=dn(e));for(const r in e){const o=e[r];let i;J(o)?"default"in o?i=Ot(o.from||r,o.default,!0):i=Ot(o.from||r):i=Ot(o),re(i)?Object.defineProperty(t,r,{enumerable:!0,configurable:!0,get:()=>i.value,set:s=>i.value=s}):t[r]=i,process.env.NODE_ENV!=="production"&&n("Inject",r)}}function Xn(e,t,n){Lt(H(e)?e.map(r=>r.bind(t.proxy)):e.bind(t.proxy),t,n)}function Yr(e,t,n,r){let o=r.includes(".")?Zr(n,r):()=>n[r];if(fe(e)){const i=t[e];q(i)?Xt(o,i):process.env.NODE_ENV!=="production"&&L(`Invalid watch handler specified by key "${e}"`,i)}else if(q(e))Xt(o,e.bind(n));else if(J(e))if(H(e))e.forEach(i=>Yr(i,t,n,r));else{const i=q(e.handler)?e.handler.bind(n):t[e.handler];q(i)?Xt(o,i,e):process.env.NODE_ENV!=="production"&&L(`Invalid watch handler specified by key "${e.handler}"`,i)}else process.env.NODE_ENV!=="production"&&L(`Invalid watch option: "${r}"`,e)}function Tn(e){const t=e.type,{mixins:n,extends:r}=t,{mixins:o,optionsCache:i,config:{optionMergeStrategies:s}}=e.appContext,c=i.get(t);let l;return c?l=c:!o.length&&!n&&!r?l=t:(l={},o.length&&o.forEach(N=>Pt(l,N,s,!0)),Pt(l,t,s)),J(t)&&i.set(t,l),l}function Pt(e,t,n,r=!1){const{mixins:o,extends:i}=t;i&&Pt(e,i,n,!0),o&&o.forEach(s=>Pt(e,s,n,!0));for(const s in t)if(r&&s==="expose")process.env.NODE_ENV!=="production"&&L('"expose" option is ignored when declared in mixins or extends. It should only be declared in the base component itself.');else{const c=Ki[s]||n&&n[s];e[s]=c?c(e[s],t[s]):t[s]}return e}const Ki={data:Zn,props:zn,emits:zn,methods:ot,computed:ot,beforeCreate:oe,created:oe,beforeMount:oe,mounted:oe,beforeUpdate:oe,updated:oe,beforeDestroy:oe,beforeUnmount:oe,destroyed:oe,unmounted:oe,activated:oe,deactivated:oe,errorCaptured:oe,serverPrefetch:oe,components:ot,directives:ot,watch:Qi,provide:Zn,inject:Wi};function Zn(e,t){return t?e?function(){return ae(q(e)?e.call(this,this):e,q(t)?t.call(this,this):t)}:t:e}function Wi(e,t){return ot(dn(e),dn(t))}function dn(e){if(H(e)){const t={};for(let n=0;n<e.length;n++)t[e[n]]=e[n];return t}return e}function oe(e,t){return e?[...new Set([].concat(e,t))]:t}function ot(e,t){return e?ae(Object.create(null),e,t):t}function zn(e,t){return e?H(e)&&H(t)?[...new Set([...e,...t])]:ae(Object.create(null),Gn(e),Gn(t??{})):t}function Qi(e,t){if(!e)return t;if(!t)return e;const n=ae(Object.create(null),e);for(const r in t)n[r]=oe(e[r],t[r]);return n}let Ji=null;function Yi(e,t){if(!ce)process.env.NODE_ENV!=="production"&&L("provide() can only be used inside setup().");else{let n=ce.provides;const r=ce.parent&&ce.parent.provides;r===n&&(n=ce.provides=Object.create(r)),n[e]=t}}function Ot(e,t,n=!1){const r=ce||pe;if(r||Ji){const o=r?r.parent==null?r.vnode.appContext&&r.vnode.appContext.provides:r.parent.provides:void 0;if(o&&e in o)return o[e];if(arguments.length>1)return n&&q(t)?t.call(r&&r.proxy):t;process.env.NODE_ENV!=="production"&&L(`injection "${String(e)}" not found.`)}else process.env.NODE_ENV!=="production"&&L("inject() can only be used inside setup() or functional components.")}const Gi={},Gr=e=>Object.getPrototypeOf(e)===Gi;let nt,Ie;function Xi(e,t){e.appContext.config.performance&&Rt()&&Ie.mark(`vue-${t}-${e.uid}`),process.env.NODE_ENV!=="production"&&Ei(e,t,Rt()?Ie.now():Date.now())}function Zi(e,t){if(e.appContext.config.performance&&Rt()){const n=`vue-${t}-${e.uid}`,r=n+":end";Ie.mark(r),Ie.measure(`<${Vn(e,e.type)}> ${t}`,n,r),Ie.clearMarks(n),Ie.clearMarks(r)}process.env.NODE_ENV!=="production"&&Oi(e,t,Rt()?Ie.now():Date.now())}function Rt(){return nt!==void 0||(typeof window<"u"&&window.performance?(nt=!0,Ie=window.performance):nt=!1),nt}const zi=ls,es=Symbol.for("v-scx"),ts=()=>{{const e=Ot(es);return e||process.env.NODE_ENV!=="production"&&L("Server rendering context not provided. Make sure to only call useSSRContext() conditionally in the server build."),e}};function Xt(e,t,n){return process.env.NODE_ENV!=="production"&&!q(t)&&L("`watch(fn, options?)` signature has been moved to a separate API. Use `watchEffect(fn, options?)` instead. `watch` now only supports `watch(source, cb, options?) signature."),Xr(e,t,n)}function Xr(e,t,n=ve){const{immediate:r,deep:o,flush:i,once:s}=n;process.env.NODE_ENV!=="production"&&!t&&(r!==void 0&&L('watch() "immediate" option is only respected when using the watch(source, callback, options?) signature.'),o!==void 0&&L('watch() "deep" option is only respected when using the watch(source, callback, options?) signature.'),s!==void 0&&L('watch() "once" option is only respected when using the watch(source, callback, options?) signature.'));const c=ae({},n);process.env.NODE_ENV!=="production"&&(c.onWarn=L);let l;if(Ht)if(i==="sync"){const h=ts();l=h.__watcherHandles||(h.__watcherHandles=[])}else if(!t||r)c.once=!0;else return{stop:se,resume:se,pause:se};const N=ce;c.call=(h,T,k)=>Lt(h,N,T,k);let S=!1;i==="post"?c.scheduler=h=>{zi(h,N&&N.suspense)}:i!=="sync"&&(S=!0,c.scheduler=(h,T)=>{T?h():Mn(h)}),c.augmentJob=h=>{t&&(h.flags|=4),S&&(h.flags|=2,N&&(h.id=N.uid,h.i=N))};const a=si(e,t,c);return l&&l.push(a),a}function ns(e,t,n){const r=this.proxy,o=fe(e)?e.includes(".")?Zr(r,e):()=>r[e]:e.bind(r,r);let i;q(t)?i=t:(i=t.handler,n=t);const s=Bn(this),c=Xr(o,i.bind(r),n);return s(),c}function Zr(e,t){const n=t.split(".");return()=>{let r=e;for(let o=0;o<n.length&&r;o++)r=r[n[o]];return r}}function zr(e,t=!0){let n;for(let r=0;r<e.length;r++){const o=e[r];if(Rn(o)){if(o.type!==Xe||o.children==="v-if"){if(n)return;if(n=o,process.env.NODE_ENV!=="production"&&t&&n.patchFlag>0&&n.patchFlag&2048)return zr(n.children)}}else return}return n}function eo({vnode:e,parent:t},n){for(;t;){const r=t.subTree;if(r.suspense&&r.suspense.activeBranch===e&&(r.el=e.el),r===e)(e=t.vnode).el=n,t=t.parent;else break}}const rs=e=>e.__isSuspense;let pn=0;const os={name:"Suspense",__isSuspense:!0,process(e,t,n,r,o,i,s,c,l,N){if(e==null)ss(t,n,r,o,i,s,c,l,N);else{if(i&&i.deps>0&&!e.suspense.isInFallback){t.suspense=e.suspense,t.suspense.vnode=t,t.el=e.el;return}as(e,t,n,r,o,s,c,l,N)}},hydrate:cs,normalize:us},is=os;function lt(e,t){const n=e.props&&e.props[t];q(n)&&n()}function ss(e,t,n,r,o,i,s,c,l){const{p:N,o:{createElement:S}}=l,a=S("div"),h=e.suspense=to(e,o,r,t,a,n,i,s,c,l);N(null,h.pendingBranch=e.ssContent,a,null,r,h,i,s),h.deps>0?(lt(e,"onPending"),lt(e,"onFallback"),N(null,e.ssFallback,t,n,r,null,i,s),Ye(h,e.ssFallback)):h.resolve(!1,!0)}function as(e,t,n,r,o,i,s,c,{p:l,um:N,o:{createElement:S}}){const a=t.suspense=e.suspense;a.vnode=t,t.el=e.el;const h=t.ssContent,T=t.ssFallback,{activeBranch:k,pendingBranch:U,isInFallback:ee,isHydrating:Y}=a;if(U)a.pendingBranch=h,zt(h,U)?(l(U,h,a.hiddenContainer,null,o,a,i,s,c),a.deps<=0?a.resolve():ee&&(Y||(l(k,T,n,r,o,null,i,s,c),Ye(a,T)))):(a.pendingId=pn++,Y?(a.isHydrating=!1,a.activeBranch=U):N(U,o,a),a.deps=0,a.effects.length=0,a.hiddenContainer=S("div"),ee?(l(null,h,a.hiddenContainer,null,o,a,i,s,c),a.deps<=0?a.resolve():(l(k,T,n,r,o,null,i,s,c),Ye(a,T))):k&&zt(h,k)?(l(k,h,n,r,o,a,i,s,c),a.resolve(!0)):(l(null,h,a.hiddenContainer,null,o,a,i,s,c),a.deps<=0&&a.resolve()));else if(k&&zt(h,k))l(k,h,n,r,o,a,i,s,c),Ye(a,h);else if(lt(t,"onPending"),a.pendingBranch=h,h.shapeFlag&512?a.pendingId=h.component.suspenseId:a.pendingId=pn++,l(null,h,a.hiddenContainer,null,o,a,i,s,c),a.deps<=0)a.resolve();else{const{timeout:Q,pendingId:te}=a;Q>0?setTimeout(()=>{a.pendingId===te&&a.fallback(T)},Q):Q===0&&a.fallback(T)}}let er=!1;function to(e,t,n,r,o,i,s,c,l,N,S=!1){process.env.NODE_ENV!=="production"&&!er&&(er=!0,console[console.info?"info":"log"]("<Suspense> is an experimental feature and its API will likely change."));const{p:a,m:h,um:T,n:k,o:{parentNode:U,remove:ee}}=N;let Y;const Q=fs(e);Q&&t&&t.pendingBranch&&(Y=t.pendingId,t.deps++);const te=e.props?xo(e.props.timeout):void 0;process.env.NODE_ENV!=="production"&&fi(te,"Suspense timeout");const $=i,v={vnode:e,parent:t,parentComponent:n,namespace:s,container:r,hiddenContainer:o,deps:0,pendingId:pn++,timeout:typeof te=="number"?te:-1,activeBranch:null,pendingBranch:null,isInFallback:!S,isHydrating:S,isUnmounted:!1,effects:[],resolve(D=!1,m=!1){if(process.env.NODE_ENV!=="production"){if(!D&&!v.pendingBranch)throw new Error("suspense.resolve() is called without a pending branch.");if(v.isUnmounted)throw new Error("suspense.resolve() is called on an already unmounted suspense boundary.")}const{vnode:C,activeBranch:d,pendingBranch:g,pendingId:f,effects:u,parentComponent:O,container:y}=v;let b=!1;v.isHydrating?v.isHydrating=!1:D||(b=d&&g.transition&&g.transition.mode==="out-in",b&&(d.transition.afterLeave=()=>{f===v.pendingId&&(h(g,y,i===$?k(d):i,0),Mt(u))}),d&&(U(d.el)===y&&(i=k(d)),T(d,O,v,!0)),b||h(g,y,i,0)),Ye(v,g),v.pendingBranch=null,v.isInFallback=!1;let R=v.parent,M=!1;for(;R;){if(R.pendingBranch){R.effects.push(...u),M=!0;break}R=R.parent}!M&&!b&&Mt(u),v.effects=[],Q&&t&&t.pendingBranch&&Y===t.pendingId&&(t.deps--,t.deps===0&&!m&&t.resolve()),lt(C,"onResolve")},fallback(D){if(!v.pendingBranch)return;const{vnode:m,activeBranch:C,parentComponent:d,container:g,namespace:f}=v;lt(m,"onFallback");const u=k(C),O=()=>{v.isInFallback&&(a(null,D,g,u,d,null,f,c,l),Ye(v,D))},y=D.transition&&D.transition.mode==="out-in";y&&(C.transition.afterLeave=O),v.isInFallback=!0,T(C,d,null,!0),y||O()},move(D,m,C){v.activeBranch&&h(v.activeBranch,D,m,C),v.container=D},next(){return v.activeBranch&&k(v.activeBranch)},registerDep(D,m,C){const d=!!v.pendingBranch;d&&v.deps++;const g=D.vnode.el;D.asyncDep.catch(f=>{Ft(f,D,0)}).then(f=>{if(D.isUnmounted||v.isUnmounted||v.pendingId!==D.suspenseId)return;D.asyncResolved=!0;const{vnode:u}=D;process.env.NODE_ENV!=="production"&&Rr(u),bs(D,f,!1),g&&(u.el=g);const O=!g&&D.subTree.el;m(D,u,U(g||D.subTree.el),g?null:k(D.subTree),v,s,C),O&&ee(O),eo(D,u.el),process.env.NODE_ENV!=="production"&&Ar(),d&&--v.deps===0&&v.resolve()})},unmount(D,m){v.isUnmounted=!0,v.activeBranch&&T(v.activeBranch,n,D,m),v.pendingBranch&&T(v.pendingBranch,n,D,m)}};return v}function cs(e,t,n,r,o,i,s,c,l){const N=t.suspense=to(t,r,n,e.parentNode,document.createElement("div"),null,o,i,s,c,!0),S=l(e,N.pendingBranch=t.ssContent,n,N,i,s);return N.deps===0&&N.resolve(!1,!0),S}function us(e){const{shapeFlag:t,children:n}=e,r=t&32;e.ssContent=tr(r?n.default:n),e.ssFallback=r?tr(n.fallback):Pe(Xe)}function tr(e){let t;if(q(e)){const n=Ze&&e._c;n&&(e._d=!1,Ge()),e=e(),n&&(e._d=!0,t=ue,ro())}if(H(e)){const n=zr(e);process.env.NODE_ENV!=="production"&&!n&&e.filter(r=>r!==Jr).length>0&&L("<Suspense> slots expect a single root node."),e=n}return e=gs(e),t&&!e.dynamicChildren&&(e.dynamicChildren=t.filter(n=>n!==e)),e}function ls(e,t){t&&t.pendingBranch?H(e)?t.effects.push(...e):t.effects.push(e):Mt(e)}function Ye(e,t){e.activeBranch=t;const{vnode:n,parentComponent:r}=e;let o=t.el;for(;!o&&t.component;)t=t.component.subTree,o=t.el;n.el=o,r&&r.subTree===n&&(r.vnode.el=o,eo(r,o))}function fs(e){const t=e.props&&e.props.suspensible;return t!=null&&t!==!1}const Pn=Symbol.for("v-fgt"),no=Symbol.for("v-txt"),Xe=Symbol.for("v-cmt"),Nt=[];let ue=null;function Ge(e=!1){Nt.push(ue=e?null:[])}function ro(){Nt.pop(),ue=Nt[Nt.length-1]||null}let Ze=1;function nr(e){Ze+=e,e<0&&ue&&(ue.hasOnce=!0)}function oo(e){return e.dynamicChildren=Ze>0?ue||wo:null,ro(),Ze>0&&ue&&ue.push(e),e}function Zt(e,t,n,r,o,i){return oo(At(e,t,n,r,o,i,!0))}function io(e,t,n,r,o){return oo(Pe(e,t,n,r,o,!0))}function Rn(e){return e?e.__v_isVNode===!0:!1}function zt(e,t){if(process.env.NODE_ENV!=="production"&&t.shapeFlag&6&&e.component){const n=Et.get(t.type);if(n&&n.has(e.component))return e.shapeFlag&=-257,t.shapeFlag&=-513,!1}return e.type===t.type&&e.key===t.key}const ds=(...e)=>ao(...e),so=({key:e})=>e??null,xt=({ref:e,ref_key:t,ref_for:n})=>(typeof e=="number"&&(e=""+e),e!=null?fe(e)||re(e)||q(e)?{i:pe,r:e,k:t,f:!!n}:e:null);function At(e,t=null,n=null,r=0,o=null,i=e===Pn?0:1,s=!1,c=!1){const l={__v_isVNode:!0,__v_skip:!0,type:e,props:t,key:t&&so(t),ref:t&&xt(t),scopeId:jr,slotScopeIds:null,children:n,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetStart:null,targetAnchor:null,staticCount:0,shapeFlag:i,patchFlag:r,dynamicProps:o,dynamicChildren:null,appContext:null,ctx:pe};return c?(An(l,n),i&128&&e.normalize(l)):n&&(l.shapeFlag|=fe(n)?8:16),process.env.NODE_ENV!=="production"&&l.key!==l.key&&L("VNode created with invalid key (NaN). VNode type:",l.type),Ze>0&&!s&&ue&&(l.patchFlag>0||i&6)&&l.patchFlag!==32&&ue.push(l),l}const Pe=process.env.NODE_ENV!=="production"?ds:ao;function ao(e,t=null,n=null,r=0,o=null,i=!1){if((!e||e===Jr)&&(process.env.NODE_ENV!=="production"&&!e&&L(`Invalid vnode type when creating vnode: ${e}.`),e=Xe),Rn(e)){const c=ft(e,t,!0);return n&&An(c,n),Ze>0&&!i&&ue&&(c.shapeFlag&6?ue[ue.indexOf(e)]=c:ue.push(c)),c.patchFlag=-2,c}if(lo(e)&&(e=e.__vccOpts),t){t=ps(t);let{class:c,style:l}=t;c&&!fe(c)&&(t.class=We(c)),J(l)&&(Dt(l)&&!H(l)&&(l=ae({},l)),t.style=vn(l))}const s=fe(e)?1:rs(e)?128:xi(e)?64:J(e)?4:q(e)?2:0;return process.env.NODE_ENV!=="production"&&s&4&&Dt(e)&&(e=F(e),L("Vue received a Component that was made a reactive object. This can lead to unnecessary performance overhead and should be avoided by marking the component with `markRaw` or using `shallowRef` instead of `ref`.",`
Component that was made reactive: `,e)),At(e,t,n,r,o,s,i,!0)}function ps(e){return e?Dt(e)||Gr(e)?ae({},e):e:null}function ft(e,t,n=!1,r=!1){const{props:o,ref:i,patchFlag:s,children:c,transition:l}=e,N=t?_s(o||{},t):o,S={__v_isVNode:!0,__v_skip:!0,type:e.type,props:N,key:N&&so(N),ref:t&&t.ref?n&&i?H(i)?i.concat(xt(t)):[i,xt(t)]:xt(t):i,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:process.env.NODE_ENV!=="production"&&s===-1&&H(c)?c.map(co):c,target:e.target,targetStart:e.targetStart,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:t&&e.type!==Pn?s===-1?16:s|16:s,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:l,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&ft(e.ssContent),ssFallback:e.ssFallback&&ft(e.ssFallback),el:e.el,anchor:e.anchor,ctx:e.ctx,ce:e.ce};return l&&r&&Ur(S,l.clone(S)),S}function co(e){const t=ft(e);return H(e.children)&&(t.children=e.children.map(co)),t}function hs(e=" ",t=0){return Pe(no,null,e,t)}function en(e="",t=!1){return t?(Ge(),io(Xe,null,e)):Pe(Xe,null,e)}function gs(e){return e==null||typeof e=="boolean"?Pe(Xe):H(e)?Pe(Pn,null,e.slice()):typeof e=="object"?vs(e):Pe(no,null,String(e))}function vs(e){return e.el===null&&e.patchFlag!==-1||e.memo?e:ft(e)}function An(e,t){let n=0;const{shapeFlag:r}=e;if(t==null)t=null;else if(H(t))n=16;else if(typeof t=="object")if(r&65){const o=t.default;o&&(o._c&&(o._d=!1),An(e,o()),o._c&&(o._d=!0));return}else{n=32;const o=t._;!o&&!Gr(t)?t._ctx=pe:o===3&&pe&&(pe.slots._===1?t._=1:(t._=2,e.patchFlag|=1024))}else q(t)?(t={default:t,_ctx:pe},n=32):(t=String(t),r&64?(n=16,t=[hs(t)]):n=8);e.children=t,e.shapeFlag|=n}function _s(...e){const t={};for(let n=0;n<e.length;n++){const r=e[n];for(const o in r)if(o==="class")t.class!==r.class&&(t.class=We([t.class,r.class]));else if(o==="style")t.style=vn([t.style,r.style]);else if(bo(o)){const i=t[o],s=r[o];s&&i!==s&&!(H(i)&&i.includes(s))&&(t[o]=i?[].concat(i,s):s)}else o!==""&&(t[o]=r[o])}return t}let ce=null;const ms=()=>ce||pe;let hn;{const e=hr(),t=(n,r)=>{let o;return(o=e[n])||(o=e[n]=[]),o.push(r),i=>{o.length>1?o.forEach(s=>s(i)):o[0](i)}};hn=t("__VUE_INSTANCE_SETTERS__",n=>ce=n),t("__VUE_SSR_SETTERS__",n=>Ht=n)}const Bn=e=>{const t=ce;return hn(e),e.scope.on(),()=>{e.scope.off(),hn(t)}};function ws(e){return e.vnode.shapeFlag&4}let Ht=!1;function bs(e,t,n){q(t)?e.type.__ssrInlineRender?e.ssrRender=t:e.render=t:J(t)?(process.env.NODE_ENV!=="production"&&Rn(t)&&L("setup() should not return VNodes directly - return a render function instead."),process.env.NODE_ENV!=="production"&&(e.devtoolsRawSetupState=t),e.setupState=Pr(t),process.env.NODE_ENV!=="production"&&qi(e)):process.env.NODE_ENV!=="production"&&t!==void 0&&L(`setup() should return an object. Received: ${t===null?"null":typeof t}`),ys(e,n)}let rr;function ys(e,t,n){const r=e.type;if(!e.render){if(!t&&rr&&!r.render){const o=r.template||Tn(e).template;if(o){process.env.NODE_ENV!=="production"&&Xi(e,"compile");const{isCustomElement:i,compilerOptions:s}=e.appContext.config,{delimiters:c,compilerOptions:l}=r,N=ae(ae({isCustomElement:i,delimiters:c},s),l);r.render=rr(o,N),process.env.NODE_ENV!=="production"&&Zi(e,"compile")}}e.render=r.render||se}{const o=Bn(e);ze();try{ji(e)}finally{et(),o()}}process.env.NODE_ENV!=="production"&&!r.render&&e.render===se&&!t&&(r.template?L('Component provided template option but runtime compilation is not supported in this build of Vue. Configure your bundler to alias "vue" to "vue/dist/vue.esm-bundler.js".'):L("Component is missing template or render function: ",r))}process.env.NODE_ENV;function Es(e){return e.exposed?e.exposeProxy||(e.exposeProxy=new Proxy(Pr(Xo(e.exposed)),{get(t,n){if(n in t)return t[n];if(n in st)return st[n](e)},has(t,n){return n in t||n in st}})):e.proxy}const Os=/(?:^|[-_])(\w)/g,Ns=e=>e.replace(Os,t=>t.toUpperCase()).replace(/[-_]/g,"");function uo(e,t=!0){return q(e)?e.displayName||e.name:e.name||t&&e.__name}function Vn(e,t,n=!1){let r=uo(t);if(!r&&t.__file){const o=t.__file.match(/([^/\\]+)\.\w+$/);o&&(r=o[1])}if(!r&&e&&e.parent){const o=i=>{for(const s in i)if(i[s]===t)return s};r=o(e.components||e.parent.type.components)||o(e.appContext.components)}return r?Ns(r):n?"App":"Anonymous"}function lo(e){return q(e)&&"__vccOpts"in e}const xs=(e,t)=>{const n=oi(e,t,Ht);if(process.env.NODE_ENV!=="production"){const r=ms();r&&r.appContext.config.warnRecursiveComputed&&(n._warnRecursive=!0)}return n};function Ds(){if(process.env.NODE_ENV==="production"||typeof window>"u")return;const e={style:"color:#3ba776"},t={style:"color:#1677ff"},n={style:"color:#f5222d"},r={style:"color:#eb2f96"},o={__vue_custom_formatter:!0,header(a){return J(a)?a.__isVue?["div",e,"VueInstance"]:re(a)?["div",{},["span",e,S(a)],"<",c("_value"in a?a._value:a),">"]:Qe(a)?["div",{},["span",e,le(a)?"ShallowReactive":"Reactive"],"<",c(a),`>${Ne(a)?" (readonly)":""}`]:Ne(a)?["div",{},["span",e,le(a)?"ShallowReadonly":"Readonly"],"<",c(a),">"]:null:null},hasBody(a){return a&&a.__isVue},body(a){if(a&&a.__isVue)return["div",{},...i(a.$)]}};function i(a){const h=[];a.type.props&&a.props&&h.push(s("props",F(a.props))),a.setupState!==ve&&h.push(s("setup",a.setupState)),a.data!==ve&&h.push(s("data",F(a.data)));const T=l(a,"computed");T&&h.push(s("computed",T));const k=l(a,"inject");return k&&h.push(s("injected",k)),h.push(["div",{},["span",{style:r.style+";opacity:0.66"},"$ (internal): "],["object",{object:a}]]),h}function s(a,h){return h=ae({},h),Object.keys(h).length?["div",{style:"line-height:1.25em;margin-bottom:0.6em"},["div",{style:"color:#476582"},a],["div",{style:"padding-left:1.25em"},...Object.keys(h).map(T=>["div",{},["span",r,T+": "],c(h[T],!1)])]]:["span",{}]}function c(a,h=!0){return typeof a=="number"?["span",t,a]:typeof a=="string"?["span",n,JSON.stringify(a)]:typeof a=="boolean"?["span",r,a]:J(a)?["object",{object:h?F(a):a}]:["span",n,String(a)]}function l(a,h){const T=a.type;if(q(T))return;const k={};for(const U in a.ctx)N(T,U,h)&&(k[U]=a.ctx[U]);return k}function N(a,h,T){const k=a[T];if(H(k)&&k.includes(h)||J(k)&&h in k||a.extends&&N(a.extends,h,T)||a.mixins&&a.mixins.some(U=>N(U,h,T)))return!0}function S(a){return le(a)?"ShallowRef":a.effect?"ComputedRef":"Ref"}window.devtoolsFormatters?window.devtoolsFormatters.push(o):window.devtoolsFormatters=[o]}process.env.NODE_ENV;process.env.NODE_ENV;process.env.NODE_ENV;/**
* vue v3.5.3
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/function Ss(){Ds()}process.env.NODE_ENV!=="production"&&Ss();const tn={numeric:"Numeric",alphanumeric:"Alphanumeric",byte:"Byte",kanji:"Kanji"};function Cs(e){switch(!0){case/^[0-9]*$/.test(e):return tn.numeric;case/^[0-9A-Z $%*+\-./:]*$/.test(e):return tn.alphanumeric;default:return tn.byte}}const bt=e=>!!e&&typeof e=="object"&&!Array.isArray(e);function Bt(e,...t){if(!t.length)return e;const n=t.shift();return n===void 0||!bt(e)||!bt(n)?e:(e={...e},Object.keys(n).forEach(r=>{const o=e[r],i=n[r];Array.isArray(o)&&Array.isArray(i)?e[r]=i:bt(o)&&bt(i)?e[r]=Bt(Object.assign({},o),i):e[r]=i}),Bt(e,...t))}function Ms(e,t){const n=document.createElement("a");n.download=t,n.href=e,document.body.appendChild(n),n.click(),document.body.removeChild(n)}function Is({originalHeight:e,originalWidth:t,maxHiddenDots:n,maxHiddenAxisDots:r,dotSize:o}){const i={x:0,y:0},s={x:0,y:0};if(e<=0||t<=0||n<=0||o<=0)return{height:0,width:0,hideYDots:0,hideXDots:0};const c=e/t;return i.x=Math.floor(Math.sqrt(n/c)),i.x<=0&&(i.x=1),r&&r<i.x&&(i.x=r),i.x%2===0&&i.x--,s.x=i.x*o,i.y=1+2*Math.ceil((i.x*c-1)/2),s.y=Math.round(s.x*c),(i.y*i.x>n||r&&r<i.y)&&(r&&r<i.y?(i.y=r,i.y%2===0&&i.x--):i.y-=2,s.y=i.y*o,i.x=1+2*Math.ceil((i.y/c-1)/2),s.x=Math.round(s.y/c)),{height:s.y,width:s.x,hideYDots:i.y,hideXDots:i.x}}const Ts={L:.07,M:.15,Q:.25,H:.3},Ue={dots:"dots",rounded:"rounded",classy:"classy",classyRounded:"classy-rounded",square:"square",extraRounded:"extra-rounded"};class nn{constructor({context:t,type:n}){ne(this,"_context");ne(this,"_type");this._context=t,this._type=n}draw(t,n,r,o){const i=this._context,s=this._type;let c;switch(s){case Ue.dots:c=this._drawDot;break;case Ue.classy:c=this._drawClassy;break;case Ue.classyRounded:c=this._drawClassyRounded;break;case Ue.rounded:c=this._drawRounded;break;case Ue.extraRounded:c=this._drawExtraRounded;break;case Ue.square:default:c=this._drawSquare}c.call(this,{x:t,y:n,size:r,context:i,getNeighbor:o})}_rotateFigure({x:t,y:n,size:r,context:o,rotation:i,draw:s}){const c=t+r/2,l=n+r/2;o.translate(c,l),i&&o.rotate(i),s(),o.closePath(),i&&o.rotate(-i),o.translate(-c,-l)}_basicDot(t){const{size:n,context:r}=t;this._rotateFigure({...t,draw:()=>{r.moveTo(0,0),r.arc(0,0,n/2,0,Math.PI*2)}})}_basicSquare(t){const{size:n,context:r}=t;this._rotateFigure({...t,draw:()=>{r.moveTo(0,0),r.rect(-n/2,-n/2,n,n)}})}_basicSideRounded(t){const{size:n,context:r}=t;this._rotateFigure({...t,draw:()=>{r.moveTo(0,0),r.arc(0,0,n/2,-Math.PI/2,Math.PI/2),r.lineTo(-n/2,n/2),r.lineTo(-n/2,-n/2),r.lineTo(0,-n/2)}})}_basicCornerRounded(t){const{size:n,context:r}=t;this._rotateFigure({...t,draw:()=>{r.moveTo(0,0),r.arc(0,0,n/2,-Math.PI/2,0),r.lineTo(n/2,n/2),r.lineTo(-n/2,n/2),r.lineTo(-n/2,-n/2),r.lineTo(0,-n/2)}})}_basicCornerExtraRounded(t){const{size:n,context:r}=t;this._rotateFigure({...t,draw:()=>{r.moveTo(0,0),r.arc(-n/2,n/2,n,-Math.PI/2,0),r.lineTo(-n/2,n/2),r.lineTo(-n/2,-n/2)}})}_basicCornersRounded(t){const{size:n,context:r}=t;this._rotateFigure({...t,draw:()=>{r.moveTo(0,0),r.arc(0,0,n/2,-Math.PI/2,0),r.lineTo(n/2,n/2),r.lineTo(0,n/2),r.arc(0,0,n/2,Math.PI/2,Math.PI),r.lineTo(-n/2,-n/2),r.lineTo(0,-n/2)}})}_basicCornersExtraRounded(t){const{size:n,context:r}=t;this._rotateFigure({...t,draw:()=>{r.moveTo(0,0),r.arc(-n/2,n/2,n,-Math.PI/2,0),r.arc(n/2,-n/2,n,Math.PI/2,Math.PI)}})}_drawDot({x:t,y:n,size:r,context:o}){this._basicDot({x:t,y:n,size:r,context:o,rotation:0})}_drawSquare({x:t,y:n,size:r,context:o}){this._basicSquare({x:t,y:n,size:r,context:o,rotation:0})}_drawRounded({x:t,y:n,size:r,context:o,getNeighbor:i}){const s=+i(-1,0),c=+i(1,0),l=+i(0,-1),N=+i(0,1),S=s+c+l+N;if(S===0){this._basicDot({x:t,y:n,size:r,context:o,rotation:0});return}if(S>2||s&&c||l&&N){this._basicSquare({x:t,y:n,size:r,context:o,rotation:0});return}if(S===2){let a=0;s&&l?a=Math.PI/2:l&&c?a=Math.PI:c&&N&&(a=-Math.PI/2),this._basicCornerRounded({x:t,y:n,size:r,context:o,rotation:a});return}if(S===1){let a=0;l?a=Math.PI/2:c?a=Math.PI:N&&(a=-Math.PI/2),this._basicSideRounded({x:t,y:n,size:r,context:o,rotation:a})}}_drawExtraRounded({x:t,y:n,size:r,context:o,getNeighbor:i}){const s=+i(-1,0),c=+i(1,0),l=+i(0,-1),N=+i(0,1),S=s+c+l+N;if(S===0){this._basicDot({x:t,y:n,size:r,context:o,rotation:0});return}if(S>2||s&&c||l&&N){this._basicSquare({x:t,y:n,size:r,context:o,rotation:0});return}if(S===2){let a=0;s&&l?a=Math.PI/2:l&&c?a=Math.PI:c&&N&&(a=-Math.PI/2),this._basicCornerExtraRounded({x:t,y:n,size:r,context:o,rotation:a});return}if(S===1){let a=0;l?a=Math.PI/2:c?a=Math.PI:N&&(a=-Math.PI/2),this._basicSideRounded({x:t,y:n,size:r,context:o,rotation:a})}}_drawClassy({x:t,y:n,size:r,context:o,getNeighbor:i}){const s=+i(-1,0),c=+i(1,0),l=+i(0,-1),N=+i(0,1);if(s+c+l+N===0){this._basicCornersRounded({x:t,y:n,size:r,context:o,rotation:Math.PI/2});return}if(!s&&!l){this._basicCornerRounded({x:t,y:n,size:r,context:o,rotation:-Math.PI/2});return}if(!c&&!N){this._basicCornerRounded({x:t,y:n,size:r,context:o,rotation:Math.PI/2});return}this._basicSquare({x:t,y:n,size:r,context:o,rotation:0})}_drawClassyRounded({x:t,y:n,size:r,context:o,getNeighbor:i}){const s=+i(-1,0),c=+i(1,0),l=+i(0,-1),N=+i(0,1);if(s+c+l+N===0){this._basicCornersRounded({x:t,y:n,size:r,context:o,rotation:Math.PI/2});return}if(!s&&!l){this._basicCornerExtraRounded({x:t,y:n,size:r,context:o,rotation:-Math.PI/2});return}if(!c&&!N){this._basicCornerExtraRounded({x:t,y:n,size:r,context:o,rotation:Math.PI/2});return}this._basicSquare({x:t,y:n,size:r,context:o,rotation:0})}}const rn={dot:"dot",square:"square",extraRounded:"extra-rounded"};class Ps{constructor({context:t,type:n}){ne(this,"_context");ne(this,"_type");this._context=t,this._type=n}draw(t,n,r,o){const i=this._context,s=this._type;let c;switch(s){case rn.square:c=this._drawSquare;break;case rn.extraRounded:c=this._drawExtraRounded;break;case rn.dot:default:c=this._drawDot}c.call(this,{x:t,y:n,size:r,context:i,rotation:o})}_rotateFigure({x:t,y:n,size:r,context:o,rotation:i,draw:s}){const c=t+r/2,l=n+r/2;o.translate(c,l),i&&o.rotate(i),s(),o.closePath(),i&&o.rotate(-i),o.translate(-c,-l)}_basicDot(t){const{size:n,context:r}=t,o=n/7;this._rotateFigure({...t,draw:()=>{r.arc(0,0,n/2,0,Math.PI*2),r.arc(0,0,n/2-o,0,Math.PI*2)}})}_basicSquare(t){const{size:n,context:r}=t,o=n/7;this._rotateFigure({...t,draw:()=>{r.rect(-n/2,-n/2,n,n),r.rect(-n/2+o,-n/2+o,n-2*o,n-2*o)}})}_basicExtraRounded(t){const{size:n,context:r}=t,o=n/7;this._rotateFigure({...t,draw:()=>{r.arc(-o,-o,2.5*o,Math.PI,-Math.PI/2),r.lineTo(o,-3.5*o),r.arc(o,-o,2.5*o,-Math.PI/2,0),r.lineTo(3.5*o,-o),r.arc(o,o,2.5*o,0,Math.PI/2),r.lineTo(-o,3.5*o),r.arc(-o,o,2.5*o,Math.PI/2,Math.PI),r.lineTo(-3.5*o,-o),r.arc(-o,-o,1.5*o,Math.PI,-Math.PI/2),r.lineTo(o,-2.5*o),r.arc(o,-o,1.5*o,-Math.PI/2,0),r.lineTo(2.5*o,-o),r.arc(o,o,1.5*o,0,Math.PI/2),r.lineTo(-o,2.5*o),r.arc(-o,o,1.5*o,Math.PI/2,Math.PI),r.lineTo(-2.5*o,-o)}})}_drawDot({x:t,y:n,size:r,context:o,rotation:i}){this._basicDot({x:t,y:n,size:r,context:o,rotation:i})}_drawSquare({x:t,y:n,size:r,context:o,rotation:i}){this._basicSquare({x:t,y:n,size:r,context:o,rotation:i})}_drawExtraRounded({x:t,y:n,size:r,context:o,rotation:i}){this._basicExtraRounded({x:t,y:n,size:r,context:o,rotation:i})}}const or={dot:"dot",square:"square"};class Rs{constructor({context:t,type:n}){ne(this,"_context");ne(this,"_type");this._context=t,this._type=n}draw(t,n,r,o){const i=this._context;switch(this._type){case or.square:this._drawSquare({x:t,y:n,size:r,context:i,rotation:o});break;case or.dot:default:this._drawDot({x:t,y:n,size:r,context:i,rotation:o})}}_rotateFigure({x:t,y:n,size:r,context:o,rotation:i,draw:s}){const c=t+r/2,l=n+r/2;o.moveTo(0,0),o.translate(c,l),i&&o.rotate(i),s(),o.closePath(),i&&o.rotate(-i),o.translate(-c,-l)}_drawDot(t){const{size:n,context:r}=t;this._rotateFigure({...t,draw:()=>{r.moveTo(0,0),r.arc(0,0,n/2,0,Math.PI*2)}})}_drawSquare(t){const{size:n,context:r}=t;this._rotateFigure({...t,draw:()=>{r.moveTo(0,0),r.rect(-n/2,-n/2,n,n)}})}}const As={radial:"radial",linear:"linear"},Ae=[[1,1,1,1,1,1,1],[1,0,0,0,0,0,1],[1,0,0,0,0,0,1],[1,0,0,0,0,0,1],[1,0,0,0,0,0,1],[1,0,0,0,0,0,1],[1,1,1,1,1,1,1]],Be=[[0,0,0,0,0,0,0],[0,0,0,0,0,0,0],[0,0,1,1,1,0,0],[0,0,1,1,1,0,0],[0,0,1,1,1,0,0],[0,0,0,0,0,0,0],[0,0,0,0,0,0,0]];class Bs{constructor(t){ne(this,"_canvas");ne(this,"_options");ne(this,"_qr");ne(this,"_image");this._canvas=document.createElement("canvas"),this._canvas.width=t.width,this._canvas.height=t.height,this._options=t}get context(){return this._canvas.getContext("2d")}get width(){return this._canvas.width}get height(){return this._canvas.height}getCanvas(){return this._canvas}clear(){const t=this.context;t&&t.clearRect(0,0,this._canvas.width,this._canvas.height)}async drawQR(t){const n=t.getModuleCount(),r=Math.min(this._options.width,this._options.height)-this._options.margin*2,o=Math.floor(r/n);let i={hideXDots:0,hideYDots:0,width:0,height:0};if(this._qr=t,this._options.image){if(await this.loadImage(),!this._image)return;const{imageOptions:s,qrOptions:c}=this._options,l=s.imageSize*Ts[c.errorCorrectionLevel],N=Math.floor(l*n*n);i=Is({originalWidth:this._image.width,originalHeight:this._image.height,maxHiddenDots:N,maxHiddenAxisDots:n-14,dotSize:o})}this.clear(),this.drawBackground(),this.drawDots((s,c)=>{var l,N,S,a,h,T;return!(this._options.imageOptions.hideBackgroundDots&&s>=(n-i.hideXDots)/2&&s<(n+i.hideXDots)/2&&c>=(n-i.hideYDots)/2&&c<(n+i.hideYDots)/2||(l=Ae[s])!=null&&l[c]||(N=Ae[s-n+7])!=null&&N[c]||(S=Ae[s])!=null&&S[c-n+7]||(a=Be[s])!=null&&a[c]||(h=Be[s-n+7])!=null&&h[c]||(T=Be[s])!=null&&T[c-n+7])}),this.drawCorners(),this._options.image&&this.drawImage({width:i.width,height:i.height,count:n,dotSize:o})}drawBackground(){const t=this.context,n=this._options;if(t){if(n.backgroundOptions.gradient){const r=n.backgroundOptions.gradient,o=this._createGradient({context:t,options:r,additionalRotation:0,x:0,y:0,size:this._canvas.width>this._canvas.height?this._canvas.width:this._canvas.height});r.colorStops.forEach(({offset:i,color:s})=>{o.addColorStop(i,s)}),t.fillStyle=o}else n.backgroundOptions.color&&(t.fillStyle=n.backgroundOptions.color);t.fillRect(0,0,this._canvas.width,this._canvas.height)}}drawDots(t){if(!this._qr)throw"QR code is not defined";const n=this.context;if(!n)throw"QR code is not defined";const r=this._options,o=this._qr.getModuleCount();if(o>r.width||o>r.height)throw"The canvas is too small.";const i=Math.min(r.width,r.height)-r.margin*2,s=Math.floor(i/o),c=Math.floor((r.width-o*s)/2),l=Math.floor((r.height-o*s)/2),N=new nn({context:n,type:r.dotsOptions.type});n.beginPath();for(let S=0;S<o;S++)for(let a=0;a<o;a++)t&&!t(S,a)||this._qr.isDark(S,a)&&N.draw(c+S*s,l+a*s,s,(h,T)=>S+h<0||a+T<0||S+h>=o||a+T>=o||t&&!t(S+h,a+T)?!1:!!this._qr&&this._qr.isDark(S+h,a+T));if(r.dotsOptions.gradient){const S=r.dotsOptions.gradient,a=this._createGradient({context:n,options:S,additionalRotation:0,x:c,y:l,size:o*s});S.colorStops.forEach(({offset:h,color:T})=>{a.addColorStop(h,T)}),n.fillStyle=n.strokeStyle=a}else r.dotsOptions.color&&(n.fillStyle=n.strokeStyle=r.dotsOptions.color);n.fill("evenodd")}drawCorners(t){if(!this._qr)throw"QR code is not defined";const n=this.context;if(!n)throw"QR code is not defined";const r=this._options,o=this._qr.getModuleCount(),i=Math.min(r.width,r.height)-r.margin*2,s=Math.floor(i/o),c=s*7,l=s*3,N=Math.floor((r.width-o*s)/2),S=Math.floor((r.height-o*s)/2);[[0,0,0],[1,0,Math.PI/2],[0,1,-Math.PI/2]].forEach(([a,h,T])=>{var ee,Y,Q,te,$,v,D,m,C,d;if(t&&!t(a,h))return;const k=N+a*s*(o-7),U=S+h*s*(o-7);if((ee=r.cornersSquareOptions)!=null&&ee.type){const g=new Ps({context:n,type:(Y=r.cornersSquareOptions)==null?void 0:Y.type});n.beginPath(),g.draw(k,U,c,T)}else{const g=new nn({context:n,type:r.dotsOptions.type});n.beginPath();for(let f=0;f<Ae.length;f++)for(let u=0;u<Ae[f].length;u++)(Q=Ae[f])!=null&&Q[u]&&g.draw(k+f*s,U+u*s,s,(O,y)=>{var b;return!!((b=Ae[f+O])!=null&&b[u+y])})}if((te=r.cornersSquareOptions)!=null&&te.gradient){const g=r.cornersSquareOptions.gradient,f=this._createGradient({context:n,options:g,additionalRotation:T,x:k,y:U,size:c});g.colorStops.forEach(({offset:u,color:O})=>{f.addColorStop(u,O)}),n.fillStyle=n.strokeStyle=f}else($=r.cornersSquareOptions)!=null&&$.color&&(n.fillStyle=n.strokeStyle=r.cornersSquareOptions.color);if(n.fill("evenodd"),(v=r.cornersDotOptions)!=null&&v.type){const g=new Rs({context:n,type:(D=r.cornersDotOptions)==null?void 0:D.type});n.beginPath(),g.draw(k+s*2,U+s*2,l,T)}else{const g=new nn({context:n,type:r.dotsOptions.type});n.beginPath();for(let f=0;f<Be.length;f++)for(let u=0;u<Be[f].length;u++)(m=Be[f])!=null&&m[u]&&g.draw(k+f*s,U+u*s,s,(O,y)=>{var b;return!!((b=Be[f+O])!=null&&b[u+y])})}if((C=r.cornersDotOptions)!=null&&C.gradient){const g=r.cornersDotOptions.gradient,f=this._createGradient({context:n,options:g,additionalRotation:T,x:k+s*2,y:U+s*2,size:l});g.colorStops.forEach(({offset:u,color:O})=>{f.addColorStop(u,O)}),n.fillStyle=n.strokeStyle=f}else(d=r.cornersDotOptions)!=null&&d.color&&(n.fillStyle=n.strokeStyle=r.cornersDotOptions.color);n.fill("evenodd")})}loadImage(){return new Promise((t,n)=>{const r=this._options,o=new Image;if(!r.image)return n("Image is not defined");typeof r.imageOptions.crossOrigin=="string"&&(o.crossOrigin=r.imageOptions.crossOrigin),this._image=o,o.onload=()=>{t()},o.src=r.image})}drawImage({width:t,height:n,count:r,dotSize:o}){const i=this.context;if(!i)throw"canvasContext is not defined";if(!this._image)throw"image is not defined";const s=this._options,c=Math.floor((s.width-r*o)/2),l=Math.floor((s.height-r*o)/2),N=c+s.imageOptions.margin+(r*o-t)/2,S=l+s.imageOptions.margin+(r*o-n)/2,a=t-s.imageOptions.margin*2,h=n-s.imageOptions.margin*2;i.drawImage(this._image,N,S,a<0?0:a,h<0?0:h)}_createGradient({context:t,options:n,additionalRotation:r,x:o,y:i,size:s}){let c;if(n.type===As.radial)c=t.createRadialGradient(o+s/2,i+s/2,0,o+s/2,i+s/2,s/2);else{const l=((n.rotation||0)+r)%(2*Math.PI),N=(l+2*Math.PI)%(2*Math.PI);let S=o+s/2,a=i+s/2,h=o+s/2,T=i+s/2;N>=0&&N<=.25*Math.PI||N>1.75*Math.PI&&N<=2*Math.PI?(S=S-s/2,a=a-s/2*Math.tan(l),h=h+s/2,T=T+s/2*Math.tan(l)):N>.25*Math.PI&&N<=.75*Math.PI?(a=a-s/2,S=S-s/2/Math.tan(l),T=T+s/2,h=h+s/2/Math.tan(l)):N>.75*Math.PI&&N<=1.25*Math.PI?(S=S+s/2,a=a+s/2*Math.tan(l),h=h-s/2,T=T-s/2*Math.tan(l)):N>1.25*Math.PI&&N<=1.75*Math.PI&&(a=a+s/2,S=S+s/2/Math.tan(l),T=T-s/2,h=h-s/2/Math.tan(l)),c=t.createLinearGradient(Math.round(S),Math.round(a),Math.round(h),Math.round(T))}return c}}const fo={};for(let e=0;e<=40;e++)fo[e]=e;const Vs={L:"L",M:"M",Q:"Q",H:"H"},ir={width:300,height:300,data:"",margin:0,qrOptions:{typeNumber:fo[0],mode:void 0,errorCorrectionLevel:Vs.Q},imageOptions:{hideBackgroundDots:!0,imageSize:.4,crossOrigin:void 0,margin:0},dotsOptions:{type:"square",color:"#000"},backgroundOptions:{color:"#fff"}};function yt(e){const t={...e};if(!t.colorStops||!t.colorStops.length)throw"Field 'colorStops' is required in gradient";return t.rotation?t.rotation=Number(t.rotation):t.rotation=0,t.colorStops=t.colorStops.map(n=>({...n,offset:Number(n.offset)})),t}function sr(e){const t={...e};return t.width=Number(t.width),t.height=Number(t.height),t.margin=Number(t.margin),t.imageOptions={...t.imageOptions,hideBackgroundDots:!!t.imageOptions.hideBackgroundDots,imageSize:Number(t.imageOptions.imageSize),margin:Number(t.imageOptions.margin)},t.margin>Math.min(t.width,t.height)&&(t.margin=Math.min(t.width,t.height)),t.dotsOptions={...t.dotsOptions},t.dotsOptions.gradient&&(t.dotsOptions.gradient=yt(t.dotsOptions.gradient)),t.cornersSquareOptions&&(t.cornersSquareOptions={...t.cornersSquareOptions},t.cornersSquareOptions.gradient&&(t.cornersSquareOptions.gradient=yt(t.cornersSquareOptions.gradient))),t.cornersDotOptions&&(t.cornersDotOptions={...t.cornersDotOptions},t.cornersDotOptions.gradient&&(t.cornersDotOptions.gradient=yt(t.cornersDotOptions.gradient))),t.backgroundOptions&&(t.backgroundOptions={...t.backgroundOptions},t.backgroundOptions.gradient&&(t.backgroundOptions.gradient=yt(t.backgroundOptions.gradient))),t}function ks(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var po={exports:{}};(function(e,t){var n=function(){var r=function(v,D){var m=236,C=17,d=v,g=i[D],f=null,u=0,O=null,y=[],b={},R=function(_,w){u=d*4+17,f=function(p){for(var E=new Array(p),x=0;x<p;x+=1){E[x]=new Array(p);for(var I=0;I<p;I+=1)E[x][I]=null}return E}(u),M(0,0),M(u-7,0),M(0,u-7),Z(),K(),me(_,w),d>=7&&de(_),O==null&&(O=ho(d,g,y)),be(O,w)},M=function(_,w){for(var p=-1;p<=7;p+=1)if(!(_+p<=-1||u<=_+p))for(var E=-1;E<=7;E+=1)w+E<=-1||u<=w+E||(0<=p&&p<=6&&(E==0||E==6)||0<=E&&E<=6&&(p==0||p==6)||2<=p&&p<=4&&2<=E&&E<=4?f[_+p][w+E]=!0:f[_+p][w+E]=!1)},P=function(){for(var _=0,w=0,p=0;p<8;p+=1){R(!0,p);var E=c.getLostPoint(b);(p==0||_>E)&&(_=E,w=p)}return w},K=function(){for(var _=8;_<u-8;_+=1)f[_][6]==null&&(f[_][6]=_%2==0);for(var w=8;w<u-8;w+=1)f[6][w]==null&&(f[6][w]=w%2==0)},Z=function(){for(var _=c.getPatternPosition(d),w=0;w<_.length;w+=1)for(var p=0;p<_.length;p+=1){var E=_[w],x=_[p];if(f[E][x]==null)for(var I=-2;I<=2;I+=1)for(var B=-2;B<=2;B+=1)I==-2||I==2||B==-2||B==2||I==0&&B==0?f[E+I][x+B]=!0:f[E+I][x+B]=!1}},de=function(_){for(var w=c.getBCHTypeNumber(d),p=0;p<18;p+=1){var E=!_&&(w>>p&1)==1;f[Math.floor(p/3)][p%3+u-8-3]=E}for(var p=0;p<18;p+=1){var E=!_&&(w>>p&1)==1;f[p%3+u-8-3][Math.floor(p/3)]=E}},me=function(_,w){for(var p=g<<3|w,E=c.getBCHTypeInfo(p),x=0;x<15;x+=1){var I=!_&&(E>>x&1)==1;x<6?f[x][8]=I:x<8?f[x+1][8]=I:f[u-15+x][8]=I}for(var x=0;x<15;x+=1){var I=!_&&(E>>x&1)==1;x<8?f[8][u-x-1]=I:x<9?f[8][15-x-1+1]=I:f[8][15-x-1]=I}f[u-8][8]=!_},be=function(_,w){for(var p=-1,E=u-1,x=7,I=0,B=c.getMaskFunction(w),A=u-1;A>0;A-=2)for(A==6&&(A-=1);;){for(var W=0;W<2;W+=1)if(f[E][A-W]==null){var G=!1;I<_.length&&(G=(_[I]>>>x&1)==1);var V=B(E,A-W);V&&(G=!G),f[E][A-W]=G,x-=1,x==-1&&(I+=1,x=7)}if(E+=p,E<0||u<=E){E-=p,p=-p;break}}},qe=function(_,w){for(var p=0,E=0,x=0,I=new Array(w.length),B=new Array(w.length),A=0;A<w.length;A+=1){var W=w[A].dataCount,G=w[A].totalCount-W;E=Math.max(E,W),x=Math.max(x,G),I[A]=new Array(W);for(var V=0;V<I[A].length;V+=1)I[A][V]=255&_.getBuffer()[V+p];p+=W;var he=c.getErrorCorrectPolynomial(G),ge=N(I[A],he.getLength()-1),$n=ge.mod(he);B[A]=new Array(he.getLength()-1);for(var V=0;V<B[A].length;V+=1){var Ln=V+$n.getLength()-B[A].length;B[A][V]=Ln>=0?$n.getAt(Ln):0}}for(var Fn=0,V=0;V<w.length;V+=1)Fn+=w[V].totalCount;for(var jt=new Array(Fn),dt=0,V=0;V<E;V+=1)for(var A=0;A<w.length;A+=1)V<I[A].length&&(jt[dt]=I[A][V],dt+=1);for(var V=0;V<x;V+=1)for(var A=0;A<w.length;A+=1)V<B[A].length&&(jt[dt]=B[A][V],dt+=1);return jt},ho=function(_,w,p){for(var E=S.getRSBlocks(_,w),x=a(),I=0;I<p.length;I+=1){var B=p[I];x.put(B.getMode(),4),x.put(B.getLength(),c.getLengthInBits(B.getMode(),_)),B.write(x)}for(var A=0,I=0;I<E.length;I+=1)A+=E[I].dataCount;if(x.getLengthInBits()>A*8)throw"code length overflow. ("+x.getLengthInBits()+">"+A*8+")";for(x.getLengthInBits()+4<=A*8&&x.put(0,4);x.getLengthInBits()%8!=0;)x.putBit(!1);for(;!(x.getLengthInBits()>=A*8||(x.put(m,8),x.getLengthInBits()>=A*8));)x.put(C,8);return qe(x,E)};b.addData=function(_,w){w=w||"Byte";var p=null;switch(w){case"Numeric":p=h(_);break;case"Alphanumeric":p=T(_);break;case"Byte":p=k(_);break;case"Kanji":p=U(_);break;default:throw"mode:"+w}y.push(p),O=null},b.isDark=function(_,w){if(_<0||u<=_||w<0||u<=w)throw _+","+w;return f[_][w]},b.getModuleCount=function(){return u},b.make=function(){if(d<1){for(var _=1;_<40;_++){for(var w=S.getRSBlocks(_,g),p=a(),E=0;E<y.length;E++){var x=y[E];p.put(x.getMode(),4),p.put(x.getLength(),c.getLengthInBits(x.getMode(),_)),x.write(p)}for(var I=0,E=0;E<w.length;E++)I+=w[E].dataCount;if(p.getLengthInBits()<=I*8)break}d=_}R(!1,P())},b.createTableTag=function(_,w){_=_||2,w=typeof w>"u"?_*4:w;var p="";p+='<table style="',p+=" border-width: 0px; border-style: none;",p+=" border-collapse: collapse;",p+=" padding: 0px; margin: "+w+"px;",p+='">',p+="<tbody>";for(var E=0;E<b.getModuleCount();E+=1){p+="<tr>";for(var x=0;x<b.getModuleCount();x+=1)p+='<td style="',p+=" border-width: 0px; border-style: none;",p+=" border-collapse: collapse;",p+=" padding: 0px; margin: 0px;",p+=" width: "+_+"px;",p+=" height: "+_+"px;",p+=" background-color: ",p+=b.isDark(E,x)?"#000000":"#ffffff",p+=";",p+='"/>';p+="</tr>"}return p+="</tbody>",p+="</table>",p},b.createSvgTag=function(_,w,p,E){var x={};typeof arguments[0]=="object"&&(x=arguments[0],_=x.cellSize,w=x.margin,p=x.alt,E=x.title),_=_||2,w=typeof w>"u"?_*4:w,p=typeof p=="string"?{text:p}:p||{},p.text=p.text||null,p.id=p.text?p.id||"qrcode-description":null,E=typeof E=="string"?{text:E}:E||{},E.text=E.text||null,E.id=E.text?E.id||"qrcode-title":null;var I=b.getModuleCount()*_+w*2,B,A,W,G,V="",he;for(he="l"+_+",0 0,"+_+" -"+_+",0 0,-"+_+"z ",V+='<svg version="1.1" xmlns="http://www.w3.org/2000/svg"',V+=x.scalable?"":' width="'+I+'px" height="'+I+'px"',V+=' viewBox="0 0 '+I+" "+I+'" ',V+=' preserveAspectRatio="xMinYMin meet"',V+=E.text||p.text?' role="img" aria-labelledby="'+He([E.id,p.id].join(" ").trim())+'"':"",V+=">",V+=E.text?'<title id="'+He(E.id)+'">'+He(E.text)+"</title>":"",V+=p.text?'<description id="'+He(p.id)+'">'+He(p.text)+"</description>":"",V+='<rect width="100%" height="100%" fill="white" cx="0" cy="0"/>',V+='<path d="',W=0;W<b.getModuleCount();W+=1)for(G=W*_+w,B=0;B<b.getModuleCount();B+=1)b.isDark(W,B)&&(A=B*_+w,V+="M"+A+","+G+he);return V+='" stroke="transparent" fill="black"/>',V+="</svg>",V},b.createDataURL=function(_,w){_=_||2,w=typeof w>"u"?_*4:w;var p=b.getModuleCount()*_+w*2,E=w,x=p-w;return $(p,p,function(I,B){if(E<=I&&I<x&&E<=B&&B<x){var A=Math.floor((I-E)/_),W=Math.floor((B-E)/_);return b.isDark(W,A)?0:1}else return 1})},b.createImgTag=function(_,w,p){_=_||2,w=typeof w>"u"?_*4:w;var E=b.getModuleCount()*_+w*2,x="";return x+="<img",x+=' src="',x+=b.createDataURL(_,w),x+='"',x+=' width="',x+=E,x+='"',x+=' height="',x+=E,x+='"',p&&(x+=' alt="',x+=He(p),x+='"'),x+="/>",x};var He=function(_){for(var w="",p=0;p<_.length;p+=1){var E=_.charAt(p);switch(E){case"<":w+="&lt;";break;case">":w+="&gt;";break;case"&":w+="&amp;";break;case'"':w+="&quot;";break;default:w+=E;break}}return w},go=function(_){var w=1;_=typeof _>"u"?w*2:_;var p=b.getModuleCount()*w+_*2,E=_,x=p-_,I,B,A,W,G,V={"██":"█","█ ":"▀"," █":"▄","  ":" "},he={"██":"▀","█ ":"▀"," █":" ","  ":" "},ge="";for(I=0;I<p;I+=2){for(A=Math.floor((I-E)/w),W=Math.floor((I+1-E)/w),B=0;B<p;B+=1)G="█",E<=B&&B<x&&E<=I&&I<x&&b.isDark(A,Math.floor((B-E)/w))&&(G=" "),E<=B&&B<x&&E<=I+1&&I+1<x&&b.isDark(W,Math.floor((B-E)/w))?G+=" ":G+="█",ge+=_<1&&I+1>=x?he[G]:V[G];ge+=`
`}return p%2&&_>0?ge.substring(0,ge.length-p-1)+Array(p+1).join("▀"):ge.substring(0,ge.length-1)};return b.createASCII=function(_,w){if(_=_||1,_<2)return go(w);_-=1,w=typeof w>"u"?_*2:w;var p=b.getModuleCount()*_+w*2,E=w,x=p-w,I,B,A,W,G=Array(_+1).join("██"),V=Array(_+1).join("  "),he="",ge="";for(I=0;I<p;I+=1){for(A=Math.floor((I-E)/_),ge="",B=0;B<p;B+=1)W=1,E<=B&&B<x&&E<=I&&I<x&&b.isDark(A,Math.floor((B-E)/_))&&(W=0),ge+=W?G:V;for(A=0;A<_;A+=1)he+=ge+`
`}return he.substring(0,he.length-1)},b.renderTo2dContext=function(_,w){w=w||2;for(var p=b.getModuleCount(),E=0;E<p;E++)for(var x=0;x<p;x++)_.fillStyle=b.isDark(E,x)?"black":"white",_.fillRect(E*w,x*w,w,w)},b};r.stringToBytesFuncs={default:function(v){for(var D=[],m=0;m<v.length;m+=1){var C=v.charCodeAt(m);D.push(C&255)}return D}},r.stringToBytes=r.stringToBytesFuncs.default,r.createStringToBytes=function(v,D){var m=function(){for(var d=Q(v),g=function(){var K=d.read();if(K==-1)throw"eof";return K},f=0,u={};;){var O=d.read();if(O==-1)break;var y=g(),b=g(),R=g(),M=String.fromCharCode(O<<8|y),P=b<<8|R;u[M]=P,f+=1}if(f!=D)throw f+" != "+D;return u}(),C="?".charCodeAt(0);return function(d){for(var g=[],f=0;f<d.length;f+=1){var u=d.charCodeAt(f);if(u<128)g.push(u);else{var O=m[d.charAt(f)];typeof O=="number"?(O&255)==O?g.push(O):(g.push(O>>>8),g.push(O&255)):g.push(C)}}return g}};var o={MODE_NUMBER:1,MODE_ALPHA_NUM:2,MODE_8BIT_BYTE:4,MODE_KANJI:8},i={L:1,M:0,Q:3,H:2},s={PATTERN000:0,PATTERN001:1,PATTERN010:2,PATTERN011:3,PATTERN100:4,PATTERN101:5,PATTERN110:6,PATTERN111:7},c=function(){var v=[[],[6,18],[6,22],[6,26],[6,30],[6,34],[6,22,38],[6,24,42],[6,26,46],[6,28,50],[6,30,54],[6,32,58],[6,34,62],[6,26,46,66],[6,26,48,70],[6,26,50,74],[6,30,54,78],[6,30,56,82],[6,30,58,86],[6,34,62,90],[6,28,50,72,94],[6,26,50,74,98],[6,30,54,78,102],[6,28,54,80,106],[6,32,58,84,110],[6,30,58,86,114],[6,34,62,90,118],[6,26,50,74,98,122],[6,30,54,78,102,126],[6,26,52,78,104,130],[6,30,56,82,108,134],[6,34,60,86,112,138],[6,30,58,86,114,142],[6,34,62,90,118,146],[6,30,54,78,102,126,150],[6,24,50,76,102,128,154],[6,28,54,80,106,132,158],[6,32,58,84,110,136,162],[6,26,54,82,110,138,166],[6,30,58,86,114,142,170]],D=1335,m=7973,C=21522,d={},g=function(f){for(var u=0;f!=0;)u+=1,f>>>=1;return u};return d.getBCHTypeInfo=function(f){for(var u=f<<10;g(u)-g(D)>=0;)u^=D<<g(u)-g(D);return(f<<10|u)^C},d.getBCHTypeNumber=function(f){for(var u=f<<12;g(u)-g(m)>=0;)u^=m<<g(u)-g(m);return f<<12|u},d.getPatternPosition=function(f){return v[f-1]},d.getMaskFunction=function(f){switch(f){case s.PATTERN000:return function(u,O){return(u+O)%2==0};case s.PATTERN001:return function(u,O){return u%2==0};case s.PATTERN010:return function(u,O){return O%3==0};case s.PATTERN011:return function(u,O){return(u+O)%3==0};case s.PATTERN100:return function(u,O){return(Math.floor(u/2)+Math.floor(O/3))%2==0};case s.PATTERN101:return function(u,O){return u*O%2+u*O%3==0};case s.PATTERN110:return function(u,O){return(u*O%2+u*O%3)%2==0};case s.PATTERN111:return function(u,O){return(u*O%3+(u+O)%2)%2==0};default:throw"bad maskPattern:"+f}},d.getErrorCorrectPolynomial=function(f){for(var u=N([1],0),O=0;O<f;O+=1)u=u.multiply(N([1,l.gexp(O)],0));return u},d.getLengthInBits=function(f,u){if(1<=u&&u<10)switch(f){case o.MODE_NUMBER:return 10;case o.MODE_ALPHA_NUM:return 9;case o.MODE_8BIT_BYTE:return 8;case o.MODE_KANJI:return 8;default:throw"mode:"+f}else if(u<27)switch(f){case o.MODE_NUMBER:return 12;case o.MODE_ALPHA_NUM:return 11;case o.MODE_8BIT_BYTE:return 16;case o.MODE_KANJI:return 10;default:throw"mode:"+f}else if(u<41)switch(f){case o.MODE_NUMBER:return 14;case o.MODE_ALPHA_NUM:return 13;case o.MODE_8BIT_BYTE:return 16;case o.MODE_KANJI:return 12;default:throw"mode:"+f}else throw"type:"+u},d.getLostPoint=function(f){for(var u=f.getModuleCount(),O=0,y=0;y<u;y+=1)for(var b=0;b<u;b+=1){for(var R=0,M=f.isDark(y,b),P=-1;P<=1;P+=1)if(!(y+P<0||u<=y+P))for(var K=-1;K<=1;K+=1)b+K<0||u<=b+K||P==0&&K==0||M==f.isDark(y+P,b+K)&&(R+=1);R>5&&(O+=3+R-5)}for(var y=0;y<u-1;y+=1)for(var b=0;b<u-1;b+=1){var Z=0;f.isDark(y,b)&&(Z+=1),f.isDark(y+1,b)&&(Z+=1),f.isDark(y,b+1)&&(Z+=1),f.isDark(y+1,b+1)&&(Z+=1),(Z==0||Z==4)&&(O+=3)}for(var y=0;y<u;y+=1)for(var b=0;b<u-6;b+=1)f.isDark(y,b)&&!f.isDark(y,b+1)&&f.isDark(y,b+2)&&f.isDark(y,b+3)&&f.isDark(y,b+4)&&!f.isDark(y,b+5)&&f.isDark(y,b+6)&&(O+=40);for(var b=0;b<u;b+=1)for(var y=0;y<u-6;y+=1)f.isDark(y,b)&&!f.isDark(y+1,b)&&f.isDark(y+2,b)&&f.isDark(y+3,b)&&f.isDark(y+4,b)&&!f.isDark(y+5,b)&&f.isDark(y+6,b)&&(O+=40);for(var de=0,b=0;b<u;b+=1)for(var y=0;y<u;y+=1)f.isDark(y,b)&&(de+=1);var me=Math.abs(100*de/u/u-50)/5;return O+=me*10,O},d}(),l=function(){for(var v=new Array(256),D=new Array(256),m=0;m<8;m+=1)v[m]=1<<m;for(var m=8;m<256;m+=1)v[m]=v[m-4]^v[m-5]^v[m-6]^v[m-8];for(var m=0;m<255;m+=1)D[v[m]]=m;var C={};return C.glog=function(d){if(d<1)throw"glog("+d+")";return D[d]},C.gexp=function(d){for(;d<0;)d+=255;for(;d>=256;)d-=255;return v[d]},C}();function N(v,D){if(typeof v.length>"u")throw v.length+"/"+D;var m=function(){for(var d=0;d<v.length&&v[d]==0;)d+=1;for(var g=new Array(v.length-d+D),f=0;f<v.length-d;f+=1)g[f]=v[f+d];return g}(),C={};return C.getAt=function(d){return m[d]},C.getLength=function(){return m.length},C.multiply=function(d){for(var g=new Array(C.getLength()+d.getLength()-1),f=0;f<C.getLength();f+=1)for(var u=0;u<d.getLength();u+=1)g[f+u]^=l.gexp(l.glog(C.getAt(f))+l.glog(d.getAt(u)));return N(g,0)},C.mod=function(d){if(C.getLength()-d.getLength()<0)return C;for(var g=l.glog(C.getAt(0))-l.glog(d.getAt(0)),f=new Array(C.getLength()),u=0;u<C.getLength();u+=1)f[u]=C.getAt(u);for(var u=0;u<d.getLength();u+=1)f[u]^=l.gexp(l.glog(d.getAt(u))+g);return N(f,0).mod(d)},C}var S=function(){var v=[[1,26,19],[1,26,16],[1,26,13],[1,26,9],[1,44,34],[1,44,28],[1,44,22],[1,44,16],[1,70,55],[1,70,44],[2,35,17],[2,35,13],[1,100,80],[2,50,32],[2,50,24],[4,25,9],[1,134,108],[2,67,43],[2,33,15,2,34,16],[2,33,11,2,34,12],[2,86,68],[4,43,27],[4,43,19],[4,43,15],[2,98,78],[4,49,31],[2,32,14,4,33,15],[4,39,13,1,40,14],[2,121,97],[2,60,38,2,61,39],[4,40,18,2,41,19],[4,40,14,2,41,15],[2,146,116],[3,58,36,2,59,37],[4,36,16,4,37,17],[4,36,12,4,37,13],[2,86,68,2,87,69],[4,69,43,1,70,44],[6,43,19,2,44,20],[6,43,15,2,44,16],[4,101,81],[1,80,50,4,81,51],[4,50,22,4,51,23],[3,36,12,8,37,13],[2,116,92,2,117,93],[6,58,36,2,59,37],[4,46,20,6,47,21],[7,42,14,4,43,15],[4,133,107],[8,59,37,1,60,38],[8,44,20,4,45,21],[12,33,11,4,34,12],[3,145,115,1,146,116],[4,64,40,5,65,41],[11,36,16,5,37,17],[11,36,12,5,37,13],[5,109,87,1,110,88],[5,65,41,5,66,42],[5,54,24,7,55,25],[11,36,12,7,37,13],[5,122,98,1,123,99],[7,73,45,3,74,46],[15,43,19,2,44,20],[3,45,15,13,46,16],[1,135,107,5,136,108],[10,74,46,1,75,47],[1,50,22,15,51,23],[2,42,14,17,43,15],[5,150,120,1,151,121],[9,69,43,4,70,44],[17,50,22,1,51,23],[2,42,14,19,43,15],[3,141,113,4,142,114],[3,70,44,11,71,45],[17,47,21,4,48,22],[9,39,13,16,40,14],[3,135,107,5,136,108],[3,67,41,13,68,42],[15,54,24,5,55,25],[15,43,15,10,44,16],[4,144,116,4,145,117],[17,68,42],[17,50,22,6,51,23],[19,46,16,6,47,17],[2,139,111,7,140,112],[17,74,46],[7,54,24,16,55,25],[34,37,13],[4,151,121,5,152,122],[4,75,47,14,76,48],[11,54,24,14,55,25],[16,45,15,14,46,16],[6,147,117,4,148,118],[6,73,45,14,74,46],[11,54,24,16,55,25],[30,46,16,2,47,17],[8,132,106,4,133,107],[8,75,47,13,76,48],[7,54,24,22,55,25],[22,45,15,13,46,16],[10,142,114,2,143,115],[19,74,46,4,75,47],[28,50,22,6,51,23],[33,46,16,4,47,17],[8,152,122,4,153,123],[22,73,45,3,74,46],[8,53,23,26,54,24],[12,45,15,28,46,16],[3,147,117,10,148,118],[3,73,45,23,74,46],[4,54,24,31,55,25],[11,45,15,31,46,16],[7,146,116,7,147,117],[21,73,45,7,74,46],[1,53,23,37,54,24],[19,45,15,26,46,16],[5,145,115,10,146,116],[19,75,47,10,76,48],[15,54,24,25,55,25],[23,45,15,25,46,16],[13,145,115,3,146,116],[2,74,46,29,75,47],[42,54,24,1,55,25],[23,45,15,28,46,16],[17,145,115],[10,74,46,23,75,47],[10,54,24,35,55,25],[19,45,15,35,46,16],[17,145,115,1,146,116],[14,74,46,21,75,47],[29,54,24,19,55,25],[11,45,15,46,46,16],[13,145,115,6,146,116],[14,74,46,23,75,47],[44,54,24,7,55,25],[59,46,16,1,47,17],[12,151,121,7,152,122],[12,75,47,26,76,48],[39,54,24,14,55,25],[22,45,15,41,46,16],[6,151,121,14,152,122],[6,75,47,34,76,48],[46,54,24,10,55,25],[2,45,15,64,46,16],[17,152,122,4,153,123],[29,74,46,14,75,47],[49,54,24,10,55,25],[24,45,15,46,46,16],[4,152,122,18,153,123],[13,74,46,32,75,47],[48,54,24,14,55,25],[42,45,15,32,46,16],[20,147,117,4,148,118],[40,75,47,7,76,48],[43,54,24,22,55,25],[10,45,15,67,46,16],[19,148,118,6,149,119],[18,75,47,31,76,48],[34,54,24,34,55,25],[20,45,15,61,46,16]],D=function(d,g){var f={};return f.totalCount=d,f.dataCount=g,f},m={},C=function(d,g){switch(g){case i.L:return v[(d-1)*4+0];case i.M:return v[(d-1)*4+1];case i.Q:return v[(d-1)*4+2];case i.H:return v[(d-1)*4+3];default:return}};return m.getRSBlocks=function(d,g){var f=C(d,g);if(typeof f>"u")throw"bad rs block @ typeNumber:"+d+"/errorCorrectionLevel:"+g;for(var u=f.length/3,O=[],y=0;y<u;y+=1)for(var b=f[y*3+0],R=f[y*3+1],M=f[y*3+2],P=0;P<b;P+=1)O.push(D(R,M));return O},m}(),a=function(){var v=[],D=0,m={};return m.getBuffer=function(){return v},m.getAt=function(C){var d=Math.floor(C/8);return(v[d]>>>7-C%8&1)==1},m.put=function(C,d){for(var g=0;g<d;g+=1)m.putBit((C>>>d-g-1&1)==1)},m.getLengthInBits=function(){return D},m.putBit=function(C){var d=Math.floor(D/8);v.length<=d&&v.push(0),C&&(v[d]|=128>>>D%8),D+=1},m},h=function(v){var D=o.MODE_NUMBER,m=v,C={};C.getMode=function(){return D},C.getLength=function(f){return m.length},C.write=function(f){for(var u=m,O=0;O+2<u.length;)f.put(d(u.substring(O,O+3)),10),O+=3;O<u.length&&(u.length-O==1?f.put(d(u.substring(O,O+1)),4):u.length-O==2&&f.put(d(u.substring(O,O+2)),7))};var d=function(f){for(var u=0,O=0;O<f.length;O+=1)u=u*10+g(f.charAt(O));return u},g=function(f){if("0"<=f&&f<="9")return f.charCodeAt(0)-"0".charCodeAt(0);throw"illegal char :"+f};return C},T=function(v){var D=o.MODE_ALPHA_NUM,m=v,C={};C.getMode=function(){return D},C.getLength=function(g){return m.length},C.write=function(g){for(var f=m,u=0;u+1<f.length;)g.put(d(f.charAt(u))*45+d(f.charAt(u+1)),11),u+=2;u<f.length&&g.put(d(f.charAt(u)),6)};var d=function(g){if("0"<=g&&g<="9")return g.charCodeAt(0)-"0".charCodeAt(0);if("A"<=g&&g<="Z")return g.charCodeAt(0)-"A".charCodeAt(0)+10;switch(g){case" ":return 36;case"$":return 37;case"%":return 38;case"*":return 39;case"+":return 40;case"-":return 41;case".":return 42;case"/":return 43;case":":return 44;default:throw"illegal char :"+g}};return C},k=function(v){var D=o.MODE_8BIT_BYTE,m=r.stringToBytes(v),C={};return C.getMode=function(){return D},C.getLength=function(d){return m.length},C.write=function(d){for(var g=0;g<m.length;g+=1)d.put(m[g],8)},C},U=function(v){var D=o.MODE_KANJI,m=r.stringToBytesFuncs.SJIS;if(!m)throw"sjis not supported.";(function(g,f){var u=m(g);if(u.length!=2||(u[0]<<8|u[1])!=f)throw"sjis not supported."})("友",38726);var C=m(v),d={};return d.getMode=function(){return D},d.getLength=function(g){return~~(C.length/2)},d.write=function(g){for(var f=C,u=0;u+1<f.length;){var O=(255&f[u])<<8|255&f[u+1];if(33088<=O&&O<=40956)O-=33088;else if(57408<=O&&O<=60351)O-=49472;else throw"illegal char at "+(u+1)+"/"+O;O=(O>>>8&255)*192+(O&255),g.put(O,13),u+=2}if(u<f.length)throw"illegal char at "+(u+1)},d},ee=function(){var v=[],D={};return D.writeByte=function(m){v.push(m&255)},D.writeShort=function(m){D.writeByte(m),D.writeByte(m>>>8)},D.writeBytes=function(m,C,d){C=C||0,d=d||m.length;for(var g=0;g<d;g+=1)D.writeByte(m[g+C])},D.writeString=function(m){for(var C=0;C<m.length;C+=1)D.writeByte(m.charCodeAt(C))},D.toByteArray=function(){return v},D.toString=function(){var m="";m+="[";for(var C=0;C<v.length;C+=1)C>0&&(m+=","),m+=v[C];return m+="]",m},D},Y=function(){var v=0,D=0,m=0,C="",d={},g=function(u){C+=String.fromCharCode(f(u&63))},f=function(u){if(!(u<0)){if(u<26)return 65+u;if(u<52)return 97+(u-26);if(u<62)return 48+(u-52);if(u==62)return 43;if(u==63)return 47}throw"n:"+u};return d.writeByte=function(u){for(v=v<<8|u&255,D+=8,m+=1;D>=6;)g(v>>>D-6),D-=6},d.flush=function(){if(D>0&&(g(v<<6-D),v=0,D=0),m%3!=0)for(var u=3-m%3,O=0;O<u;O+=1)C+="="},d.toString=function(){return C},d},Q=function(v){var D=v,m=0,C=0,d=0,g={};g.read=function(){for(;d<8;){if(m>=D.length){if(d==0)return-1;throw"unexpected end of file./"+d}var u=D.charAt(m);if(m+=1,u=="=")return d=0,-1;if(u.match(/^\s$/))continue;C=C<<6|f(u.charCodeAt(0)),d+=6}var O=C>>>d-8&255;return d-=8,O};var f=function(u){if(65<=u&&u<=90)return u-65;if(97<=u&&u<=122)return u-97+26;if(48<=u&&u<=57)return u-48+52;if(u==43)return 62;if(u==47)return 63;throw"c:"+u};return g},te=function(v,D){var m=v,C=D,d=new Array(v*D),g={};g.setPixel=function(y,b,R){d[b*m+y]=R},g.write=function(y){y.writeString("GIF87a"),y.writeShort(m),y.writeShort(C),y.writeByte(128),y.writeByte(0),y.writeByte(0),y.writeByte(0),y.writeByte(0),y.writeByte(0),y.writeByte(255),y.writeByte(255),y.writeByte(255),y.writeString(","),y.writeShort(0),y.writeShort(0),y.writeShort(m),y.writeShort(C),y.writeByte(0);var b=2,R=u(b);y.writeByte(b);for(var M=0;R.length-M>255;)y.writeByte(255),y.writeBytes(R,M,255),M+=255;y.writeByte(R.length-M),y.writeBytes(R,M,R.length-M),y.writeByte(0),y.writeString(";")};var f=function(y){var b=y,R=0,M=0,P={};return P.write=function(K,Z){if(K>>>Z)throw"length over";for(;R+Z>=8;)b.writeByte(255&(K<<R|M)),Z-=8-R,K>>>=8-R,M=0,R=0;M=K<<R|M,R=R+Z},P.flush=function(){R>0&&b.writeByte(M)},P},u=function(y){for(var b=1<<y,R=(1<<y)+1,M=y+1,P=O(),K=0;K<b;K+=1)P.add(String.fromCharCode(K));P.add(String.fromCharCode(b)),P.add(String.fromCharCode(R));var Z=ee(),de=f(Z);de.write(b,M);var me=0,be=String.fromCharCode(d[me]);for(me+=1;me<d.length;){var qe=String.fromCharCode(d[me]);me+=1,P.contains(be+qe)?be=be+qe:(de.write(P.indexOf(be),M),P.size()<4095&&(P.size()==1<<M&&(M+=1),P.add(be+qe)),be=qe)}return de.write(P.indexOf(be),M),de.write(R,M),de.flush(),Z.toByteArray()},O=function(){var y={},b=0,R={};return R.add=function(M){if(R.contains(M))throw"dup key:"+M;y[M]=b,b+=1},R.size=function(){return b},R.indexOf=function(M){return y[M]},R.contains=function(M){return typeof y[M]<"u"},R};return g},$=function(v,D,m){for(var C=te(v,D),d=0;d<D;d+=1)for(var g=0;g<v;g+=1)C.setPixel(g,d,m(g,d));var f=ee();C.write(f);for(var u=Y(),O=f.toByteArray(),y=0;y<O.length;y+=1)u.writeByte(O[y]);return u.flush(),"data:image/gif;base64,"+u};return r}();(function(){n.stringToBytesFuncs["UTF-8"]=function(r){function o(i){for(var s=[],c=0;c<i.length;c++){var l=i.charCodeAt(c);l<128?s.push(l):l<2048?s.push(192|l>>6,128|l&63):l<55296||l>=57344?s.push(224|l>>12,128|l>>6&63,128|l&63):(c++,l=65536+((l&1023)<<10|i.charCodeAt(c)&1023),s.push(240|l>>18,128|l>>12&63,128|l>>6&63,128|l&63))}return s}return o(r)}})(),function(r){e.exports=r()}(function(){return n})})(po);var $s=po.exports;const Ls=ks($s);class kn{constructor(t){ne(this,"_options");ne(this,"_container");ne(this,"_canvas");ne(this,"_qr");ne(this,"_drawingPromise");this._options=t?sr(Bt(ir,t)):ir,this.update()}static _clearContainer(t){t&&(t.innerHTML="")}update(t){kn._clearContainer(this._container),this._options=t?sr(Bt(this._options,t)):this._options,this._options.data&&(this._qr=Ls(this._options.qrOptions.typeNumber,this._options.qrOptions.errorCorrectionLevel),this._qr.addData(this._options.data,this._options.qrOptions.mode||Cs(this._options.data)),this._qr.make(),this._canvas=new Bs(this._options),this._drawingPromise=this._canvas.drawQR(this._qr),this.append(this._container))}append(t){if(t){if(typeof t.appendChild!="function")throw"Container should be a single DOM node";this._canvas&&t.appendChild(this._canvas.getCanvas()),this._container=t}}async getImageUrl(t){return this._drawingPromise&&await this._drawingPromise===void 0&&this._canvas?this._canvas.getCanvas().toDataURL(`image/${t}`):""}download(t){this._drawingPromise&&this._drawingPromise.then(()=>{if(!this._canvas)return;const n=t,r=n.extension||"png",o=n.name||"qr",i=this._canvas.getCanvas().toDataURL(`image/${r}`);Ms(i,`${o}.${r}`)})}}const Fs={key:0},qs=["src"],Hs={key:1},js=Kr({__name:"QRCodeVue3Async",props:{value:{default:""},width:{default:300},height:{default:300},margin:{default:0},imgclass:{default:""},myclass:{default:""},downloadButton:{default:""},buttonName:{default:"Download"},qrOptions:{default:{typeNumber:0,mode:"Byte",errorCorrectionLevel:"Q"}},imageOptions:{default:{hideBackgroundDots:!0,imageSize:.4,margin:0}},dotsOptions:{default:{type:"dots",color:"#6a1a4c",gradient:{type:"linear",rotation:0,colorStops:[{offset:0,color:"#6a1a4c"},{offset:1,color:"#6a1a4c"}]}}},backgroundOptions:{default:{color:"#ffffff"}},cornersSquareOptions:{default:{type:"dot",color:"#000000"}},cornersDotOptions:{default:{type:void 0,color:"#000000"}},fileExt:{default:"png"},image:{default:""},download:{type:Boolean,default:!1},downloadOptions:{default:{name:"vqr",extension:"png"}},isHide:{type:Boolean}},emits:["imageLoaded"],setup(e,{emit:t}){const n=t,r=e,o=new kn({data:r.value,width:r.width,height:r.height,margin:r.margin,qrOptions:r.qrOptions,imageOptions:r.imageOptions,dotsOptions:r.dotsOptions,backgroundOptions:r.backgroundOptions,image:r.image,cornersSquareOptions:r.cornersSquareOptions,cornersDotOptions:r.cornersDotOptions}),i=Zo("");async function s(){i.value=await o.getImageUrl(r.fileExt),n("imageLoaded",i.value)}s();function c(){o.download(r.downloadOptions)}return(l,N)=>l.isHide?en("",!0):(Ge(),Zt("div",Fs,[i.value?(Ge(),Zt("div",{key:0,class:We(l.myclass)},[At("img",{src:i.value,class:We(l.imgclass),crossorigin:"anonymous"},null,10,qs)],2)):en("",!0),i.value&&l.download?(Ge(),Zt("div",Hs,[At("button",{onClick:c,class:We(l.downloadButton)},vr(l.buttonName),3)])):en("",!0)]))}}),Us=Kr({__name:"QRCodeVue3",props:{value:{default:""},width:{default:300},height:{default:300},margin:{default:0},imgclass:{default:""},myclass:{default:""},downloadButton:{default:""},buttonName:{default:"Download"},qrOptions:{default:{typeNumber:0,mode:"Byte",errorCorrectionLevel:"Q"}},imageOptions:{default:{hideBackgroundDots:!0,imageSize:.4,margin:0}},dotsOptions:{default:{type:"dots",color:"#6a1a4c",gradient:{type:"linear",rotation:0,colorStops:[{offset:0,color:"#6a1a4c"},{offset:1,color:"#6a1a4c"}]}}},backgroundOptions:{default:{color:"#ffffff"}},cornersSquareOptions:{default:{type:"dot",color:"#000000"}},cornersDotOptions:{default:{type:void 0,color:"#000000"}},fileExt:{default:"png"},image:{default:""},download:{type:Boolean,default:!1},downloadOptions:{default:{name:"vqr",extension:"png"}},isHide:{type:Boolean}},emits:["imageLoaded"],setup(e,{emit:t}){const n=t,r=e;function o(i){n("imageLoaded",i)}return(i,s)=>(Ge(),io(is,null,{default:Ni(()=>[Pe(js,{"background-options":r.backgroundOptions,"button-name":r.buttonName,"corners-dot-options":r.cornersDotOptions,"corners-square-options":r.cornersSquareOptions,"dots-options":r.dotsOptions,download:r.download,"download-button":r.downloadButton,"download-options":r.downloadOptions,"file-ext":r.fileExt,height:r.height,image:r.image,"image-options":r.imageOptions,imgclass:r.imgclass,margin:r.margin,value:r.value,myclass:r.myclass,"qr-options":r.qrOptions,width:r.width,"is-hide":r.isHide,onImageLoaded:o},null,8,["background-options","button-name","corners-dot-options","corners-square-options","dots-options","download","download-button","download-options","file-ext","height","image","image-options","imgclass","margin","value","myclass","qr-options","width","is-hide"])]),_:1}))}});module.exports=Us;
