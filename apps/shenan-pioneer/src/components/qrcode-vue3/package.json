{"name": "qrcode-vue3", "private": false, "version": "1.7.1", "type": "module", "description": "Add a style and an image to your QR code Vue3", "main": "./dist/index.umd.js", "module": "./dist/index.es.js", "types": "src/index.d.ts", "files": ["src", "lib", "dist"], "dependencies": {"qrcode-generator": "^1.4.4"}, "devDependencies": {"@typescript-eslint/eslint-plugin": "^5.62.0", "@typescript-eslint/parser": "^5.62.0", "@vitejs/plugin-vue": "^4.6.2", "@vue/compiler-sfc": "*", "canvas": "^2.11.2", "clean-webpack-plugin": "^4.0.0", "copyfiles": "^2.4.1", "eslint": "^7.32.0", "eslint-config-prettier": "^8.10.0", "eslint-loader": "^4.0.2", "eslint-plugin-jest": "^27.9.0", "eslint-plugin-prettier": "^4.2.1", "html-webpack-plugin": "^5.6.0", "jest": "^29.7.0", "jest-environment-jsdom-fifteen": "^1.0.2", "path": "^0.12.7", "prettier": "^2.8.8", "rollup-plugin-typescript2": "^0.34.1", "ts-jest": "^29.2.5", "ts-loader": "^9.5.1", "typescript": "^5.5.4", "vite": "^4.5.3", "vite-plugin-dts": "^2.3.0", "vue": "^3.3.2", "vue-loader": "^17.4.2", "vue-tsc": "^2.1.6", "webpack": "^5.94.0", "webpack-cli": "^5.1.4", "webpack-dev-server": "^4.15.2", "webpack-merge": "^5.10.0"}, "scripts": {"test": "jest --passWithNoTests", "dev:vite": "vite", "build": "rm dist -rf && vue-tsc && vite build", "preview:vite": "vite preview", "buildAndPublish": "rm dist -rf && vue-tsc && vite build && npm publish"}, "repository": {"type": "git", "url": "git+https://github.com/scholtz/qrcode-vue3.git"}, "keywords": ["qr", "qrcode", "qr-code", "js", "qrjs", "qrstyling", "styling", "qrbranding", "branding", "qrimage", "image", "qrlogo", "logo", "design"], "author": "<PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, Diadal <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/scholtz/qrcode-vue3/issues"}, "homepage": "https://github.com/scholtz/qrcode-vue3"}