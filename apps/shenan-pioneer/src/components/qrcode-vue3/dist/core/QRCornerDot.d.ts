import type { CornerDotType } from '../types';
type DrawArgs = {
  x: number;
  y: number;
  size: number;
  context: CanvasRenderingContext2D;
  rotation: number;
};
type RotateFigureArgs = {
  x: number;
  y: number;
  size: number;
  context: CanvasRenderingContext2D;
  rotation: number;
  draw: () => void;
};
export default class QRCornerDot {
  _context: CanvasRenderingContext2D;
  _type: CornerDotType;
  constructor({ context, type }: { context: CanvasRenderingContext2D; type: CornerDotType });
  draw(x: number, y: number, size: number, rotation: number): void;
  _rotateFigure({ x, y, size, context, rotation, draw }: RotateFigureArgs): void;
  _drawDot(args: DrawArgs): void;
  _drawSquare(args: DrawArgs): void;
}
export {};
