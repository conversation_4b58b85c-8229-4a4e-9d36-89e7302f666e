(function(me,Q){typeof exports=="object"&&typeof module<"u"?module.exports=Q():typeof define=="function"&&define.amd?define(Q):(me=typeof globalThis<"u"?globalThis:me||self,me.QRCodeVue3=Q())})(this,function(){"use strict";var qs=Object.defineProperty;var Hs=(me,Q,Be)=>Q in me?qs(me,Q,{enumerable:!0,configurable:!0,writable:!0,value:Be}):me[Q]=Be;var oe=(me,Q,Be)=>(Hs(me,typeof Q!="symbol"?Q+"":Q,Be),Be);/**
* @vue/shared v3.5.3
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**//*! #__NO_SIDE_EFFECTS__ */function me(e,t){const n=new Set(e.split(","));return t?r=>n.has(r.toLowerCase()):r=>n.has(r)}const Q=process.env.NODE_ENV!=="production"?Object.freeze({}):{},Be=process.env.NODE_ENV!=="production"?Object.freeze([]):[],ie=()=>{},vo=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&(e.charCodeAt(2)>122||e.charCodeAt(2)<97),se=Object.assign,_o=(e,t)=>{const n=e.indexOf(t);n>-1&&e.splice(n,1)},mo=Object.prototype.hasOwnProperty,G=(e,t)=>mo.call(e,t),H=Array.isArray,Ve=e=>ht(e)==="[object Map]",Fn=e=>ht(e)==="[object Set]",q=e=>typeof e=="function",ue=e=>typeof e=="string",ke=e=>typeof e=="symbol",Y=e=>e!==null&&typeof e=="object",qn=e=>(Y(e)||q(e))&&q(e.then)&&q(e.catch),Hn=Object.prototype.toString,ht=e=>Hn.call(e),jn=e=>ht(e).slice(8,-1),Un=e=>ht(e)==="[object Object]",Kt=e=>ue(e)&&e!=="NaN"&&e[0]!=="-"&&""+parseInt(e,10)===e,Kn=e=>{const t=Object.create(null);return n=>t[n]||(t[n]=e(n))},Wn=Kn(e=>e.charAt(0).toUpperCase()+e.slice(1)),wo=Kn(e=>e?`on${Wn(e)}`:""),Se=(e,t)=>!Object.is(e,t),bo=(e,t,n,r=!1)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,writable:r,value:n})},yo=e=>{const t=ue(e)?Number(e):NaN;return isNaN(t)?e:t};let Qn;const Jn=()=>Qn||(Qn=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:{});function Wt(e){if(H(e)){const t={};for(let n=0;n<e.length;n++){const r=e[n],o=ue(r)?xo(r):Wt(r);if(o)for(const i in o)t[i]=o[i]}return t}else if(ue(e)||Y(e))return e}const Eo=/;(?![^(]*\))/g,Oo=/:([^]+)/,No=/\/\*[^]*?\*\//g;function xo(e){const t={};return e.replace(No,"").split(Eo).forEach(n=>{if(n){const r=n.split(Oo);r.length>1&&(t[r[0].trim()]=r[1].trim())}}),t}function je(e){let t="";if(ue(e))t=e;else if(H(e))for(let n=0;n<e.length;n++){const r=je(e[n]);r&&(t+=r+" ")}else if(Y(e))for(const n in e)e[n]&&(t+=n+" ");return t.trim()}const Yn=e=>!!(e&&e.__v_isRef===!0),Gn=e=>ue(e)?e:e==null?"":H(e)||Y(e)&&(e.toString===Hn||!q(e.toString))?Yn(e)?Gn(e.value):JSON.stringify(e,Xn,2):String(e),Xn=(e,t)=>Yn(t)?Xn(e,t.value):Ve(t)?{[`Map(${t.size})`]:[...t.entries()].reduce((n,[r,o],i)=>(n[Qt(r,i)+" =>"]=o,n),{})}:Fn(t)?{[`Set(${t.size})`]:[...t.values()].map(n=>Qt(n))}:ke(t)?Qt(t):Y(t)&&!H(t)&&!Un(t)?String(t):t,Qt=(e,t="")=>{var n;return ke(e)?`Symbol(${(n=e.description)!=null?n:t})`:e};/**
* @vue/reactivity v3.5.3
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/function ye(e,...t){console.warn(`[Vue warn] ${e}`,...t)}let j;const Jt=new WeakSet;class Do{constructor(t){this.fn=t,this.deps=void 0,this.depsTail=void 0,this.flags=5,this.nextEffect=void 0,this.cleanup=void 0,this.scheduler=void 0}pause(){this.flags|=64}resume(){this.flags&64&&(this.flags&=-65,Jt.has(this)&&(Jt.delete(this),this.trigger()))}notify(){this.flags&2&&!(this.flags&32)||this.flags&8||(this.flags|=8,this.nextEffect=rt,rt=this)}run(){if(!(this.flags&1))return this.fn();this.flags|=2,rr(this),zn(this);const t=j,n=we;j=this,we=!0;try{return this.fn()}finally{process.env.NODE_ENV!=="production"&&j!==this&&ye("Active effect was not restored correctly - this is likely a Vue internal bug."),er(this),j=t,we=n,this.flags&=-3}}stop(){if(this.flags&1){for(let t=this.deps;t;t=t.nextDep)Zt(t);this.deps=this.depsTail=void 0,rr(this),this.onStop&&this.onStop(),this.flags&=-2}}trigger(){this.flags&64?Jt.add(this):this.scheduler?this.scheduler():this.runIfDirty()}runIfDirty(){Xt(this)&&this.run()}get dirty(){return Xt(this)}}let Zn=0,rt;function Yt(){Zn++}function Gt(){if(--Zn>0)return;let e;for(;rt;){let t=rt;for(rt=void 0;t;){const n=t.nextEffect;if(t.nextEffect=void 0,t.flags&=-9,t.flags&1)try{t.trigger()}catch(r){e||(e=r)}t=n}}if(e)throw e}function zn(e){for(let t=e.deps;t;t=t.nextDep)t.version=-1,t.prevActiveLink=t.dep.activeLink,t.dep.activeLink=t}function er(e){let t,n=e.depsTail;for(let r=n;r;r=r.prevDep)r.version===-1?(r===n&&(n=r.prevDep),Zt(r),So(r)):t=r,r.dep.activeLink=r.prevActiveLink,r.prevActiveLink=void 0;e.deps=t,e.depsTail=n}function Xt(e){for(let t=e.deps;t;t=t.nextDep)if(t.dep.version!==t.version||t.dep.computed&&tr(t.dep.computed)===!1||t.dep.version!==t.version)return!0;return!!e._dirty}function tr(e){if(e.flags&2)return!1;if(e.flags&4&&!(e.flags&16)||(e.flags&=-17,e.globalVersion===ot))return;e.globalVersion=ot;const t=e.dep;if(e.flags|=2,t.version>0&&!e.isSSR&&!Xt(e)){e.flags&=-3;return}const n=j,r=we;j=e,we=!0;try{zn(e);const o=e.fn(e._value);(t.version===0||Se(o,e._value))&&(e._value=o,t.version++)}catch(o){throw t.version++,o}finally{j=n,we=r,er(e),e.flags&=-3}}function Zt(e){const{dep:t,prevSub:n,nextSub:r}=e;if(n&&(n.nextSub=r,e.prevSub=void 0),r&&(r.prevSub=n,e.nextSub=void 0),t.subs===e&&(t.subs=n),!t.subs&&t.computed){t.computed.flags&=-5;for(let o=t.computed.deps;o;o=o.nextDep)Zt(o)}}function So(e){const{prevDep:t,nextDep:n}=e;t&&(t.nextDep=n,e.prevDep=void 0),n&&(n.prevDep=t,e.nextDep=void 0)}let we=!0;const nr=[];function Ue(){nr.push(we),we=!1}function Ke(){const e=nr.pop();we=e===void 0?!0:e}function rr(e){const{cleanup:t}=e;if(e.cleanup=void 0,t){const n=j;j=void 0;try{t()}finally{j=n}}}let ot=0;class zt{constructor(t){this.computed=t,this.version=0,this.activeLink=void 0,this.subs=void 0,process.env.NODE_ENV!=="production"&&(this.subsHead=void 0)}track(t){if(!j||!we||j===this.computed)return;let n=this.activeLink;if(n===void 0||n.sub!==j)n=this.activeLink={dep:this,sub:j,version:this.version,nextDep:void 0,prevDep:void 0,nextSub:void 0,prevSub:void 0,prevActiveLink:void 0},j.deps?(n.prevDep=j.depsTail,j.depsTail.nextDep=n,j.depsTail=n):j.deps=j.depsTail=n,j.flags&4&&or(n);else if(n.version===-1&&(n.version=this.version,n.nextDep)){const r=n.nextDep;r.prevDep=n.prevDep,n.prevDep&&(n.prevDep.nextDep=r),n.prevDep=j.depsTail,n.nextDep=void 0,j.depsTail.nextDep=n,j.depsTail=n,j.deps===n&&(j.deps=r)}return process.env.NODE_ENV!=="production"&&j.onTrack&&j.onTrack(se({effect:j},t)),n}trigger(t){this.version++,ot++,this.notify(t)}notify(t){Yt();try{if(process.env.NODE_ENV!=="production")for(let n=this.subsHead;n;n=n.nextSub)process.env.NODE_ENV!=="production"&&n.sub.onTrigger&&!(n.sub.flags&8)&&n.sub.onTrigger(se({effect:n.sub},t));for(let n=this.subs;n;n=n.prevSub)n.sub.notify()}finally{Gt()}}}function or(e){const t=e.dep.computed;if(t&&!e.dep.subs){t.flags|=20;for(let r=t.deps;r;r=r.nextDep)or(r)}const n=e.dep.subs;n!==e&&(e.prevSub=n,n&&(n.nextSub=e)),process.env.NODE_ENV!=="production"&&e.dep.subsHead===void 0&&(e.dep.subsHead=e),e.dep.subs=e}const en=new WeakMap,$e=Symbol(process.env.NODE_ENV!=="production"?"Object iterate":""),tn=Symbol(process.env.NODE_ENV!=="production"?"Map keys iterate":""),it=Symbol(process.env.NODE_ENV!=="production"?"Array iterate":"");function z(e,t,n){if(we&&j){let r=en.get(e);r||en.set(e,r=new Map);let o=r.get(n);o||r.set(n,o=new zt),process.env.NODE_ENV!=="production"?o.track({target:e,type:t,key:n}):o.track()}}function Ce(e,t,n,r,o,i){const s=en.get(e);if(!s){ot++;return}let c=[];if(t==="clear")c=[...s.values()];else{const f=H(e),N=f&&Kt(n);if(f&&n==="length"){const S=Number(r);s.forEach((a,h)=>{(h==="length"||h===it||!ke(h)&&h>=S)&&c.push(a)})}else{const S=a=>a&&c.push(a);switch(n!==void 0&&S(s.get(n)),N&&S(s.get(it)),t){case"add":f?N&&S(s.get("length")):(S(s.get($e)),Ve(e)&&S(s.get(tn)));break;case"delete":f||(S(s.get($e)),Ve(e)&&S(s.get(tn)));break;case"set":Ve(e)&&S(s.get($e));break}}}Yt();for(const f of c)process.env.NODE_ENV!=="production"?f.trigger({target:e,type:t,key:n,newValue:r,oldValue:o,oldTarget:i}):f.trigger();Gt()}function We(e){const t=F(e);return t===e?t:(z(t,"iterate",it),fe(e)?t:t.map(ae))}function nn(e){return z(e=F(e),"iterate",it),e}const Co={__proto__:null,[Symbol.iterator](){return rn(this,Symbol.iterator,ae)},concat(...e){return We(this).concat(...e.map(t=>H(t)?We(t):t))},entries(){return rn(this,"entries",e=>(e[1]=ae(e[1]),e))},every(e,t){return Ee(this,"every",e,t,void 0,arguments)},filter(e,t){return Ee(this,"filter",e,t,n=>n.map(ae),arguments)},find(e,t){return Ee(this,"find",e,t,ae,arguments)},findIndex(e,t){return Ee(this,"findIndex",e,t,void 0,arguments)},findLast(e,t){return Ee(this,"findLast",e,t,ae,arguments)},findLastIndex(e,t){return Ee(this,"findLastIndex",e,t,void 0,arguments)},forEach(e,t){return Ee(this,"forEach",e,t,void 0,arguments)},includes(...e){return on(this,"includes",e)},indexOf(...e){return on(this,"indexOf",e)},join(e){return We(this).join(e)},lastIndexOf(...e){return on(this,"lastIndexOf",e)},map(e,t){return Ee(this,"map",e,t,void 0,arguments)},pop(){return st(this,"pop")},push(...e){return st(this,"push",e)},reduce(e,...t){return ir(this,"reduce",e,t)},reduceRight(e,...t){return ir(this,"reduceRight",e,t)},shift(){return st(this,"shift")},some(e,t){return Ee(this,"some",e,t,void 0,arguments)},splice(...e){return st(this,"splice",e)},toReversed(){return We(this).toReversed()},toSorted(e){return We(this).toSorted(e)},toSpliced(...e){return We(this).toSpliced(...e)},unshift(...e){return st(this,"unshift",e)},values(){return rn(this,"values",ae)}};function rn(e,t,n){const r=nn(e),o=r[t]();return r!==e&&!fe(e)&&(o._next=o.next,o.next=()=>{const i=o._next();return i.value&&(i.value=n(i.value)),i}),o}const Mo=Array.prototype;function Ee(e,t,n,r,o,i){const s=nn(e),c=s!==e&&!fe(e),f=s[t];if(f!==Mo[t]){const a=f.apply(e,i);return c?ae(a):a}let N=n;s!==e&&(c?N=function(a,h){return n.call(this,ae(a),h,e)}:n.length>2&&(N=function(a,h){return n.call(this,a,h,e)}));const S=f.call(s,N,r);return c&&o?o(S):S}function ir(e,t,n,r){const o=nn(e);let i=n;return o!==e&&(fe(e)?n.length>3&&(i=function(s,c,f){return n.call(this,s,c,f,e)}):i=function(s,c,f){return n.call(this,s,ae(c),f,e)}),o[t](i,...r)}function on(e,t,n){const r=F(e);z(r,"iterate",it);const o=r[t](...n);return(o===-1||o===!1)&&Et(n[0])?(n[0]=F(n[0]),r[t](...n)):o}function st(e,t,n=[]){Ue(),Yt();const r=F(e)[t].apply(e,n);return Gt(),Ke(),r}const Io=me("__proto__,__v_isRef,__isVue"),sr=new Set(Object.getOwnPropertyNames(Symbol).filter(e=>e!=="arguments"&&e!=="caller").map(e=>Symbol[e]).filter(ke));function To(e){ke(e)||(e=String(e));const t=F(this);return z(t,"has",e),t.hasOwnProperty(e)}class ar{constructor(t=!1,n=!1){this._isReadonly=t,this._isShallow=n}get(t,n,r){const o=this._isReadonly,i=this._isShallow;if(n==="__v_isReactive")return!o;if(n==="__v_isReadonly")return o;if(n==="__v_isShallow")return i;if(n==="__v_raw")return r===(o?i?vr:gr:i?Uo:hr).get(t)||Object.getPrototypeOf(t)===Object.getPrototypeOf(r)?t:void 0;const s=H(t);if(!o){let f;if(s&&(f=Co[n]))return f;if(n==="hasOwnProperty")return To}const c=Reflect.get(t,n,re(t)?t:r);return(ke(n)?sr.has(n):Io(n))||(o||z(t,"get",n),i)?c:re(c)?s&&Kt(n)?c:c.value:Y(c)?o?_r(c):cn(c):c}}class Po extends ar{constructor(t=!1){super(!1,t)}set(t,n,r,o){let i=t[n];if(!this._isShallow){const f=Oe(i);if(!fe(r)&&!Oe(r)&&(i=F(i),r=F(r)),!H(t)&&re(i)&&!re(r))return f?!1:(i.value=r,!0)}const s=H(t)&&Kt(n)?Number(n)<t.length:G(t,n),c=Reflect.set(t,n,r,re(t)?t:o);return t===F(o)&&(s?Se(r,i)&&Ce(t,"set",n,r,i):Ce(t,"add",n,r)),c}deleteProperty(t,n){const r=G(t,n),o=t[n],i=Reflect.deleteProperty(t,n);return i&&r&&Ce(t,"delete",n,void 0,o),i}has(t,n){const r=Reflect.has(t,n);return(!ke(n)||!sr.has(n))&&z(t,"has",n),r}ownKeys(t){return z(t,"iterate",H(t)?"length":$e),Reflect.ownKeys(t)}}class cr extends ar{constructor(t=!1){super(!0,t)}set(t,n){return process.env.NODE_ENV!=="production"&&ye(`Set operation on key "${String(n)}" failed: target is readonly.`,t),!0}deleteProperty(t,n){return process.env.NODE_ENV!=="production"&&ye(`Delete operation on key "${String(n)}" failed: target is readonly.`,t),!0}}const Ro=new Po,Ao=new cr,Bo=new cr(!0),sn=e=>e,gt=e=>Reflect.getPrototypeOf(e);function vt(e,t,n=!1,r=!1){e=e.__v_raw;const o=F(e),i=F(t);n||(Se(t,i)&&z(o,"get",t),z(o,"get",i));const{has:s}=gt(o),c=r?sn:n?fn:ae;if(s.call(o,t))return c(e.get(t));if(s.call(o,i))return c(e.get(i));e!==o&&e.get(t)}function _t(e,t=!1){const n=this.__v_raw,r=F(n),o=F(e);return t||(Se(e,o)&&z(r,"has",e),z(r,"has",o)),e===o?n.has(e):n.has(e)||n.has(o)}function mt(e,t=!1){return e=e.__v_raw,!t&&z(F(e),"iterate",$e),Reflect.get(e,"size",e)}function ur(e,t=!1){!t&&!fe(e)&&!Oe(e)&&(e=F(e));const n=F(this);return gt(n).has.call(n,e)||(n.add(e),Ce(n,"add",e,e)),this}function fr(e,t,n=!1){!n&&!fe(t)&&!Oe(t)&&(t=F(t));const r=F(this),{has:o,get:i}=gt(r);let s=o.call(r,e);s?process.env.NODE_ENV!=="production"&&pr(r,o,e):(e=F(e),s=o.call(r,e));const c=i.call(r,e);return r.set(e,t),s?Se(t,c)&&Ce(r,"set",e,t,c):Ce(r,"add",e,t),this}function lr(e){const t=F(this),{has:n,get:r}=gt(t);let o=n.call(t,e);o?process.env.NODE_ENV!=="production"&&pr(t,n,e):(e=F(e),o=n.call(t,e));const i=r?r.call(t,e):void 0,s=t.delete(e);return o&&Ce(t,"delete",e,void 0,i),s}function dr(){const e=F(this),t=e.size!==0,n=process.env.NODE_ENV!=="production"?Ve(e)?new Map(e):new Set(e):void 0,r=e.clear();return t&&Ce(e,"clear",void 0,void 0,n),r}function wt(e,t){return function(r,o){const i=this,s=i.__v_raw,c=F(s),f=t?sn:e?fn:ae;return!e&&z(c,"iterate",$e),s.forEach((N,S)=>r.call(o,f(N),f(S),i))}}function bt(e,t,n){return function(...r){const o=this.__v_raw,i=F(o),s=Ve(i),c=e==="entries"||e===Symbol.iterator&&s,f=e==="keys"&&s,N=o[e](...r),S=n?sn:t?fn:ae;return!t&&z(i,"iterate",f?tn:$e),{next(){const{value:a,done:h}=N.next();return h?{value:a,done:h}:{value:c?[S(a[0]),S(a[1])]:S(a),done:h}},[Symbol.iterator](){return this}}}}function Me(e){return function(...t){if(process.env.NODE_ENV!=="production"){const n=t[0]?`on key "${t[0]}" `:"";ye(`${Wn(e)} operation ${n}failed: target is readonly.`,F(this))}return e==="delete"?!1:e==="clear"?void 0:this}}function Vo(){const e={get(i){return vt(this,i)},get size(){return mt(this)},has:_t,add:ur,set:fr,delete:lr,clear:dr,forEach:wt(!1,!1)},t={get(i){return vt(this,i,!1,!0)},get size(){return mt(this)},has:_t,add(i){return ur.call(this,i,!0)},set(i,s){return fr.call(this,i,s,!0)},delete:lr,clear:dr,forEach:wt(!1,!0)},n={get(i){return vt(this,i,!0)},get size(){return mt(this,!0)},has(i){return _t.call(this,i,!0)},add:Me("add"),set:Me("set"),delete:Me("delete"),clear:Me("clear"),forEach:wt(!0,!1)},r={get(i){return vt(this,i,!0,!0)},get size(){return mt(this,!0)},has(i){return _t.call(this,i,!0)},add:Me("add"),set:Me("set"),delete:Me("delete"),clear:Me("clear"),forEach:wt(!0,!0)};return["keys","values","entries",Symbol.iterator].forEach(i=>{e[i]=bt(i,!1,!1),n[i]=bt(i,!0,!1),t[i]=bt(i,!1,!0),r[i]=bt(i,!0,!0)}),[e,n,t,r]}const[ko,$o,Lo,Fo]=Vo();function an(e,t){const n=t?e?Fo:Lo:e?$o:ko;return(r,o,i)=>o==="__v_isReactive"?!e:o==="__v_isReadonly"?e:o==="__v_raw"?r:Reflect.get(G(n,o)&&o in r?n:r,o,i)}const qo={get:an(!1,!1)},Ho={get:an(!0,!1)},jo={get:an(!0,!0)};function pr(e,t,n){const r=F(n);if(r!==n&&t.call(e,r)){const o=jn(e);ye(`Reactive ${o} contains both the raw and reactive versions of the same object${o==="Map"?" as keys":""}, which can lead to inconsistencies. Avoid differentiating between the raw and reactive versions of an object and only use the reactive version if possible.`)}}const hr=new WeakMap,Uo=new WeakMap,gr=new WeakMap,vr=new WeakMap;function Ko(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}function Wo(e){return e.__v_skip||!Object.isExtensible(e)?0:Ko(jn(e))}function cn(e){return Oe(e)?e:un(e,!1,Ro,qo,hr)}function _r(e){return un(e,!0,Ao,Ho,gr)}function yt(e){return un(e,!0,Bo,jo,vr)}function un(e,t,n,r,o){if(!Y(e))return process.env.NODE_ENV!=="production"&&ye(`value cannot be made ${t?"readonly":"reactive"}: ${String(e)}`),e;if(e.__v_raw&&!(t&&e.__v_isReactive))return e;const i=o.get(e);if(i)return i;const s=Wo(e);if(s===0)return e;const c=new Proxy(e,s===2?r:n);return o.set(e,c),c}function Qe(e){return Oe(e)?Qe(e.__v_raw):!!(e&&e.__v_isReactive)}function Oe(e){return!!(e&&e.__v_isReadonly)}function fe(e){return!!(e&&e.__v_isShallow)}function Et(e){return e?!!e.__v_raw:!1}function F(e){const t=e&&e.__v_raw;return t?F(t):e}function Qo(e){return Object.isExtensible(e)&&bo(e,"__v_skip",!0),e}const ae=e=>Y(e)?cn(e):e,fn=e=>Y(e)?_r(e):e;function re(e){return e?e.__v_isRef===!0:!1}function Jo(e){return Yo(e,!1)}function Yo(e,t){return re(e)?e:new Go(e,t)}class Go{constructor(t,n){this.dep=new zt,this.__v_isRef=!0,this.__v_isShallow=!1,this._rawValue=n?t:F(t),this._value=n?t:ae(t),this.__v_isShallow=n}get value(){return process.env.NODE_ENV!=="production"?this.dep.track({target:this,type:"get",key:"value"}):this.dep.track(),this._value}set value(t){const n=this._rawValue,r=this.__v_isShallow||fe(t)||Oe(t);t=r?t:F(t),Se(t,n)&&(this._rawValue=t,this._value=r?t:ae(t),process.env.NODE_ENV!=="production"?this.dep.trigger({target:this,type:"set",key:"value",newValue:t,oldValue:n}):this.dep.trigger())}}function Xo(e){return re(e)?e.value:e}const Zo={get:(e,t,n)=>t==="__v_raw"?e:Xo(Reflect.get(e,t,n)),set:(e,t,n,r)=>{const o=e[t];return re(o)&&!re(n)?(o.value=n,!0):Reflect.set(e,t,n,r)}};function mr(e){return Qe(e)?e:new Proxy(e,Zo)}class zo{constructor(t,n,r){this.fn=t,this.setter=n,this._value=void 0,this.dep=new zt(this),this.__v_isRef=!0,this.deps=void 0,this.depsTail=void 0,this.flags=16,this.globalVersion=ot-1,this.effect=this,this.__v_isReadonly=!n,this.isSSR=r}notify(){j!==this?(this.flags|=16,this.dep.notify()):process.env.NODE_ENV}get value(){const t=process.env.NODE_ENV!=="production"?this.dep.track({target:this,type:"get",key:"value"}):this.dep.track();return tr(this),t&&(t.version=this.dep.version),this._value}set value(t){this.setter?this.setter(t):process.env.NODE_ENV!=="production"&&ye("Write operation failed: computed value is readonly")}}function ei(e,t,n=!1){let r,o;q(e)?r=e:(r=e.get,o=e.set);const i=new zo(r,o,n);return process.env.NODE_ENV!=="production"&&t&&!n&&(i.onTrack=t.onTrack,i.onTrigger=t.onTrigger),i}const Ot={},Nt=new WeakMap;let Le;function ti(e,t=!1,n=Le){if(n){let r=Nt.get(n);r||Nt.set(n,r=[]),r.push(e)}else process.env.NODE_ENV!=="production"&&!t&&ye("onWatcherCleanup() was called when there was no active watcher to associate with.")}function ni(e,t,n=Q){const{immediate:r,deep:o,once:i,scheduler:s,augmentJob:c,call:f}=n,N=L=>{(n.onWarn||ye)("Invalid watch source: ",L,"A watch source can only be a getter/effect function, a ref, a reactive object, or an array of these types.")},S=L=>o?L:fe(L)||o===!1||o===0?Ie(L,1):Ie(L);let a,h,T,$,U=!1,te=!1;if(re(e)?(h=()=>e.value,U=fe(e)):Qe(e)?(h=()=>S(e),U=!0):H(e)?(te=!0,U=e.some(L=>Qe(L)||fe(L)),h=()=>e.map(L=>{if(re(L))return L.value;if(Qe(L))return S(L);if(q(L))return f?f(L,2):L();process.env.NODE_ENV!=="production"&&N(L)})):q(e)?t?h=f?()=>f(e,2):e:h=()=>{if(T){Ue();try{T()}finally{Ke()}}const L=Le;Le=a;try{return f?f(e,3,[$]):e($)}finally{Le=L}}:(h=ie,process.env.NODE_ENV!=="production"&&N(e)),t&&o){const L=h,v=o===!0?1/0:o;h=()=>Ie(L(),v)}const X=()=>{a.stop()};if(i)if(t){const L=t;t=(...v)=>{L(...v),X()}}else{const L=h;h=()=>{L(),X()}}let J=te?new Array(e.length).fill(Ot):Ot;const ne=L=>{if(!(!(a.flags&1)||!a.dirty&&!L))if(t){const v=a.run();if(o||U||(te?v.some((D,m)=>Se(D,J[m])):Se(v,J))){T&&T();const D=Le;Le=a;try{const m=[v,J===Ot?void 0:te&&J[0]===Ot?[]:J,$];f?f(t,3,m):t(...m),J=v}finally{Le=D}}}else a.run()};return c&&c(ne),a=new Do(h),a.scheduler=s?()=>s(ne,!1):ne,$=L=>ti(L,!1,a),T=a.onStop=()=>{const L=Nt.get(a);if(L){if(f)f(L,4);else for(const v of L)v();Nt.delete(a)}},process.env.NODE_ENV!=="production"&&(a.onTrack=n.onTrack,a.onTrigger=n.onTrigger),t?r?ne(!0):J=a.run():s?s(ne.bind(null,!0),!0):a.run(),X.pause=a.pause.bind(a),X.resume=a.resume.bind(a),X.stop=X,X}function Ie(e,t=1/0,n){if(t<=0||!Y(e)||e.__v_skip||(n=n||new Set,n.has(e)))return e;if(n.add(e),t--,re(e))Ie(e.value,t,n);else if(H(e))for(let r=0;r<e.length;r++)Ie(e[r],t,n);else if(Fn(e)||Ve(e))e.forEach(r=>{Ie(r,t,n)});else if(Un(e)){for(const r in e)Ie(e[r],t,n);for(const r of Object.getOwnPropertySymbols(e))Object.prototype.propertyIsEnumerable.call(e,r)&&Ie(e[r],t,n)}return e}/**
* @vue/runtime-core v3.5.3
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/const Fe=[];function wr(e){Fe.push(e)}function br(){Fe.pop()}let ln=!1;function k(e,...t){if(ln)return;ln=!0,Ue();const n=Fe.length?Fe[Fe.length-1].component:null,r=n&&n.appContext.config.warnHandler,o=ri();if(r)xt(r,n,11,[e+t.map(i=>{var s,c;return(c=(s=i.toString)==null?void 0:s.call(i))!=null?c:JSON.stringify(i)}).join(""),n&&n.proxy,o.map(({vnode:i})=>`at <${An(n,i.type)}>`).join(`
`),o]);else{const i=[`[Vue warn]: ${e}`,...t];o.length&&i.push(`
`,...oi(o)),console.warn(...i)}Ke(),ln=!1}function ri(){let e=Fe[Fe.length-1];if(!e)return[];const t=[];for(;e;){const n=t[0];n&&n.vnode===e?n.recurseCount++:t.push({vnode:e,recurseCount:0});const r=e.component&&e.component.parent;e=r&&r.vnode}return t}function oi(e){const t=[];return e.forEach((n,r)=>{t.push(...r===0?[]:[`
`],...ii(n))}),t}function ii({vnode:e,recurseCount:t}){const n=t>0?`... (${t} recursive calls)`:"",r=e.component?e.component.parent==null:!1,o=` at <${An(e.component,e.type,r)}`,i=">"+n;return e.props?[o,...si(e.props),i]:[o+i]}function si(e){const t=[],n=Object.keys(e);return n.slice(0,3).forEach(r=>{t.push(...yr(r,e[r]))}),n.length>3&&t.push(" ..."),t}function yr(e,t,n){return ue(t)?(t=JSON.stringify(t),n?t:[`${e}=${t}`]):typeof t=="number"||typeof t=="boolean"||t==null?n?t:[`${e}=${t}`]:re(t)?(t=yr(e,F(t.value),!0),n?t:[`${e}=Ref<`,t,">"]):q(t)?[`${e}=fn${t.name?`<${t.name}>`:""}`]:(t=F(t),n?t:[`${e}=`,t])}function ai(e,t){process.env.NODE_ENV!=="production"&&e!==void 0&&(typeof e!="number"?k(`${t} is not a valid number - got ${JSON.stringify(e)}.`):isNaN(e)&&k(`${t} is NaN - the duration expression might be incorrect.`))}const dn={sp:"serverPrefetch hook",bc:"beforeCreate hook",c:"created hook",bm:"beforeMount hook",m:"mounted hook",bu:"beforeUpdate hook",u:"updated",bum:"beforeUnmount hook",um:"unmounted hook",a:"activated hook",da:"deactivated hook",ec:"errorCaptured hook",rtc:"renderTracked hook",rtg:"renderTriggered hook",0:"setup function",1:"render function",2:"watcher getter",3:"watcher callback",4:"watcher cleanup function",5:"native event handler",6:"component event handler",7:"vnode hook",8:"directive hook",9:"transition hook",10:"app errorHandler",11:"app warnHandler",12:"ref function",13:"async component loader",14:"scheduler flush",15:"component update",16:"app unmount cleanup function"};function xt(e,t,n,r){try{return r?e(...r):e()}catch(o){St(o,t,n)}}function Dt(e,t,n,r){if(q(e)){const o=xt(e,t,n,r);return o&&qn(o)&&o.catch(i=>{St(i,t,n)}),o}if(H(e)){const o=[];for(let i=0;i<e.length;i++)o.push(Dt(e[i],t,n,r));return o}else process.env.NODE_ENV!=="production"&&k(`Invalid value type passed to callWithAsyncErrorHandling(): ${typeof e}`)}function St(e,t,n,r=!0){const o=t?t.vnode:null,{errorHandler:i,throwUnhandledErrorInProduction:s}=t&&t.appContext.config||Q;if(t){let c=t.parent;const f=t.proxy,N=process.env.NODE_ENV!=="production"?dn[n]:`https://vuejs.org/error-reference/#runtime-${n}`;for(;c;){const S=c.ec;if(S){for(let a=0;a<S.length;a++)if(S[a](e,f,N)===!1)return}c=c.parent}if(i){Ue(),xt(i,null,10,[e,f,N]),Ke();return}}ci(e,n,o,r,s)}function ci(e,t,n,r=!0,o=!1){if(process.env.NODE_ENV!=="production"){const i=dn[t];if(n&&wr(n),k(`Unhandled error${i?` during execution of ${i}`:""}`),n&&br(),r)throw e;console.error(e)}else{if(o)throw e;console.error(e)}}let Ct=!1,pn=!1;const ge=[];let Ne=0;const Je=[];let Te=null,Ye=0;const Er=Promise.resolve();let hn=null;const ui=100;function fi(e){const t=hn||Er;return e?t.then(this?e.bind(this):e):t}function li(e){let t=Ct?Ne+1:0,n=ge.length;for(;t<n;){const r=t+n>>>1,o=ge[r],i=at(o);i<e||i===e&&o.flags&2?t=r+1:n=r}return t}function gn(e){if(!(e.flags&1)){const t=at(e),n=ge[ge.length-1];!n||!(e.flags&2)&&t>=at(n)?ge.push(e):ge.splice(li(t),0,e),e.flags|=1,Or()}}function Or(){!Ct&&!pn&&(pn=!0,hn=Er.then(Nr))}function Mt(e){H(e)?Je.push(...e):Te&&e.id===-1?Te.splice(Ye+1,0,e):e.flags&1||(Je.push(e),e.flags|=1),Or()}function di(e){if(Je.length){const t=[...new Set(Je)].sort((n,r)=>at(n)-at(r));if(Je.length=0,Te){Te.push(...t);return}for(Te=t,process.env.NODE_ENV!=="production"&&(e=e||new Map),Ye=0;Ye<Te.length;Ye++){const n=Te[Ye];process.env.NODE_ENV!=="production"&&xr(e,n)||(n.flags&4&&(n.flags&=-2),n.flags&8||n(),n.flags&=-2)}Te=null,Ye=0}}const at=e=>e.id==null?e.flags&2?-1:1/0:e.id;function Nr(e){pn=!1,Ct=!0,process.env.NODE_ENV!=="production"&&(e=e||new Map);const t=process.env.NODE_ENV!=="production"?n=>xr(e,n):ie;try{for(Ne=0;Ne<ge.length;Ne++){const n=ge[Ne];if(n&&!(n.flags&8)){if(process.env.NODE_ENV!=="production"&&t(n))continue;n.flags&4&&(n.flags&=-2),xt(n,n.i,n.i?15:14),n.flags&=-2}}}finally{for(;Ne<ge.length;Ne++){const n=ge[Ne];n&&(n.flags&=-2)}Ne=0,ge.length=0,di(e),Ct=!1,hn=null,(ge.length||Je.length)&&Nr(e)}}function xr(e,t){if(!e.has(t))e.set(t,1);else{const n=e.get(t);if(n>ui){const r=t.i,o=r&&io(r.type);return St(`Maximum recursive updates exceeded${o?` in component <${o}>`:""}. This means you have a reactive effect that is mutating its own dependencies and thus recursively triggering itself. Possible sources include component template, render function, updated hook or watcher source function.`,null,10),!0}else e.set(t,n+1)}}const It=new Map;process.env.NODE_ENV!=="production"&&(Jn().__VUE_HMR_RUNTIME__={createRecord:vn(pi),rerender:vn(hi),reload:vn(gi)});const Tt=new Map;function pi(e,t){return Tt.has(e)?!1:(Tt.set(e,{initialDef:Pt(t),instances:new Set}),!0)}function Pt(e){return so(e)?e.__vccOpts:e}function hi(e,t){const n=Tt.get(e);n&&(n.initialDef.render=t,[...n.instances].forEach(r=>{t&&(r.render=t,Pt(r.type).render=t),r.renderCache=[],r.update()}))}function gi(e,t){const n=Tt.get(e);if(!n)return;t=Pt(t),Dr(n.initialDef,t);const r=[...n.instances];for(let o=0;o<r.length;o++){const i=r[o],s=Pt(i.type);let c=It.get(s);c||(s!==n.initialDef&&Dr(s,t),It.set(s,c=new Set)),c.add(i),i.appContext.propsCache.delete(i.type),i.appContext.emitsCache.delete(i.type),i.appContext.optionsCache.delete(i.type),i.ceReload?(c.add(i),i.ceReload(t.styles),c.delete(i)):i.parent?gn(()=>{i.parent.update(),c.delete(i)}):i.appContext.reload?i.appContext.reload():typeof window<"u"?window.location.reload():console.warn("[HMR] Root or manually mounted instance modified. Full reload required."),i.root.ce&&i!==i.root&&i.root.ce._removeChildStyle(s)}Mt(()=>{It.clear()})}function Dr(e,t){se(e,t);for(const n in e)n!=="__file"&&!(n in t)&&delete e[n]}function vn(e){return(t,n)=>{try{return e(t,n)}catch(r){console.error(r),console.warn("[HMR] Something went wrong during Vue component hot-reload. Full reload required.")}}}let Pe,ct=[],_n=!1;function Sr(e,...t){Pe?Pe.emit(e,...t):_n||ct.push({event:e,args:t})}function Cr(e,t){var n,r;Pe=e,Pe?(Pe.enabled=!0,ct.forEach(({event:o,args:i})=>Pe.emit(o,...i)),ct=[]):typeof window<"u"&&window.HTMLElement&&!((r=(n=window.navigator)==null?void 0:n.userAgent)!=null&&r.includes("jsdom"))?((t.__VUE_DEVTOOLS_HOOK_REPLAY__=t.__VUE_DEVTOOLS_HOOK_REPLAY__||[]).push(i=>{Cr(i,t)}),setTimeout(()=>{Pe||(t.__VUE_DEVTOOLS_HOOK_REPLAY__=null,_n=!0,ct=[])},3e3)):(_n=!0,ct=[])}const vi=_i("component:updated");/*! #__NO_SIDE_EFFECTS__ */function _i(e){return t=>{Sr(e,t.appContext.app,t.uid,t.parent?t.parent.uid:void 0,t)}}const mi=Mr("perf:start"),wi=Mr("perf:end");function Mr(e){return(t,n,r)=>{Sr(e,t.appContext.app,t.uid,t,n,r)}}let pe=null,Ir=null;function Tr(e){const t=pe;return pe=e,Ir=e&&e.type.__scopeId||null,t}function bi(e,t=pe,n){if(!t||e._n)return e;const r=(...o)=>{r._d&&Zr(-1);const i=Tr(t);let s;try{s=e(...o)}finally{Tr(i),r._d&&Zr(1)}return process.env.NODE_ENV!=="production"&&vi(t),s};return r._n=!0,r._c=!0,r._d=!0,r}const yi=e=>e.__isTeleport;function Pr(e,t){e.shapeFlag&6&&e.component?(e.transition=t,Pr(e.component.subTree,t)):e.shapeFlag&128?(e.ssContent.transition=t.clone(e.ssContent),e.ssFallback.transition=t.clone(e.ssFallback)):e.transition=t}/*! #__NO_SIDE_EFFECTS__ */function Rr(e,t){return q(e)?(()=>se({name:e.name},t,{setup:e}))():e}function Ei(e){e.ids=[e.ids[0]+e.ids[2]+++"-",0,0]}const Oi=e=>e.type.__isKeepAlive;function Ni(e,t){Ar(e,"a",t)}function xi(e,t){Ar(e,"da",t)}function Ar(e,t,n=de){const r=e.__wdc||(e.__wdc=()=>{let o=n;for(;o;){if(o.isDeactivated)return;o=o.parent}return e()});if(Rt(t,r,n),n){let o=n.parent;for(;o&&o.parent;)Oi(o.parent.vnode)&&Di(r,t,n,o),o=o.parent}}function Di(e,t,n,r){const o=Rt(t,e,r,!0);Br(()=>{_o(r[t],o)},n)}function Rt(e,t,n=de,r=!1){if(n){const o=n[e]||(n[e]=[]),i=t.__weh||(t.__weh=(...s)=>{Ue();const c=Rn(n),f=Dt(t,n,e,s);return c(),Ke(),f});return r?o.unshift(i):o.push(i),i}else if(process.env.NODE_ENV!=="production"){const o=wo(dn[e].replace(/ hook$/,""));k(`${o} is called when there is no active component instance to be associated with. Lifecycle injection APIs can only be used during execution of setup(). If you are using async setup(), make sure to register lifecycle hooks before the first await statement.`)}}const xe=e=>(t,n=de)=>{(!Ft||e==="sp")&&Rt(e,(...r)=>t(...r),n)},Si=xe("bm"),Ci=xe("m"),Mi=xe("bu"),Ii=xe("u"),Ti=xe("bum"),Br=xe("um"),Pi=xe("sp"),Ri=xe("rtg"),Ai=xe("rtc");function Bi(e,t=de){Rt("ec",e,t)}const Vr=Symbol.for("v-ndc"),mn=e=>e?hs(e)?_s(e):mn(e.parent):null,ut=se(Object.create(null),{$:e=>e,$el:e=>e.vnode.el,$data:e=>e.data,$props:e=>process.env.NODE_ENV!=="production"?yt(e.props):e.props,$attrs:e=>process.env.NODE_ENV!=="production"?yt(e.attrs):e.attrs,$slots:e=>process.env.NODE_ENV!=="production"?yt(e.slots):e.slots,$refs:e=>process.env.NODE_ENV!=="production"?yt(e.refs):e.refs,$parent:e=>mn(e.parent),$root:e=>mn(e.root),$host:e=>e.ce,$emit:e=>e.emit,$options:e=>En(e),$forceUpdate:e=>e.f||(e.f=()=>{gn(e.update)}),$nextTick:e=>e.n||(e.n=fi.bind(e.proxy)),$watch:e=>Zi.bind(e)}),wn=e=>e==="_"||e==="$",bn=(e,t)=>e!==Q&&!e.__isScriptSetup&&G(e,t),Vi={get({_:e},t){if(t==="__v_skip")return!0;const{ctx:n,setupState:r,data:o,props:i,accessCache:s,type:c,appContext:f}=e;if(process.env.NODE_ENV!=="production"&&t==="__isVue")return!0;let N;if(t[0]!=="$"){const T=s[t];if(T!==void 0)switch(T){case 1:return r[t];case 2:return o[t];case 4:return n[t];case 3:return i[t]}else{if(bn(r,t))return s[t]=1,r[t];if(o!==Q&&G(o,t))return s[t]=2,o[t];if((N=e.propsOptions[0])&&G(N,t))return s[t]=3,i[t];if(n!==Q&&G(n,t))return s[t]=4,n[t];yn&&(s[t]=0)}}const S=ut[t];let a,h;if(S)return t==="$attrs"?(z(e.attrs,"get",""),process.env.NODE_ENV!=="production"&&void 0):process.env.NODE_ENV!=="production"&&t==="$slots"&&z(e,"get",t),S(e);if((a=c.__cssModules)&&(a=a[t]))return a;if(n!==Q&&G(n,t))return s[t]=4,n[t];if(h=f.config.globalProperties,G(h,t))return h[t];process.env.NODE_ENV!=="production"&&pe&&(!ue(t)||t.indexOf("__v")!==0)&&(o!==Q&&wn(t[0])&&G(o,t)?k(`Property ${JSON.stringify(t)} must be accessed via $data because it starts with a reserved character ("$" or "_") and is not proxied on the render context.`):e===pe&&k(`Property ${JSON.stringify(t)} was accessed during render but is not defined on instance.`))},set({_:e},t,n){const{data:r,setupState:o,ctx:i}=e;return bn(o,t)?(o[t]=n,!0):process.env.NODE_ENV!=="production"&&o.__isScriptSetup&&G(o,t)?(k(`Cannot mutate <script setup> binding "${t}" from Options API.`),!1):r!==Q&&G(r,t)?(r[t]=n,!0):G(e.props,t)?(process.env.NODE_ENV!=="production"&&k(`Attempting to mutate prop "${t}". Props are readonly.`),!1):t[0]==="$"&&t.slice(1)in e?(process.env.NODE_ENV!=="production"&&k(`Attempting to mutate public property "${t}". Properties starting with $ are reserved and readonly.`),!1):(process.env.NODE_ENV!=="production"&&t in e.appContext.config.globalProperties?Object.defineProperty(i,t,{enumerable:!0,configurable:!0,value:n}):i[t]=n,!0)},has({_:{data:e,setupState:t,accessCache:n,ctx:r,appContext:o,propsOptions:i}},s){let c;return!!n[s]||e!==Q&&G(e,s)||bn(t,s)||(c=i[0])&&G(c,s)||G(r,s)||G(ut,s)||G(o.config.globalProperties,s)},defineProperty(e,t,n){return n.get!=null?e._.accessCache[t]=0:G(n,"value")&&this.set(e,t,n.value,null),Reflect.defineProperty(e,t,n)}};process.env.NODE_ENV!=="production"&&(Vi.ownKeys=e=>(k("Avoid app logic that relies on enumerating keys on a component instance. The keys will be empty in production mode to avoid performance overhead."),Reflect.ownKeys(e)));function ki(e){const{ctx:t,setupState:n}=e;Object.keys(F(n)).forEach(r=>{if(!n.__isScriptSetup){if(wn(r[0])){k(`setup() return property ${JSON.stringify(r)} should not start with "$" or "_" which are reserved prefixes for Vue internals.`);return}Object.defineProperty(t,r,{enumerable:!0,configurable:!0,get:()=>n[r],set:ie})}})}function kr(e){return H(e)?e.reduce((t,n)=>(t[n]=null,t),{}):e}function $i(){const e=Object.create(null);return(t,n)=>{e[n]?k(`${t} property "${n}" is already defined in ${e[n]}.`):e[n]=t}}let yn=!0;function Li(e){const t=En(e),n=e.proxy,r=e.ctx;yn=!1,t.beforeCreate&&$r(t.beforeCreate,e,"bc");const{data:o,computed:i,methods:s,watch:c,provide:f,inject:N,created:S,beforeMount:a,mounted:h,beforeUpdate:T,updated:$,activated:U,deactivated:te,beforeDestroy:X,beforeUnmount:J,destroyed:ne,unmounted:L,render:v,renderTracked:D,renderTriggered:m,errorCaptured:C,serverPrefetch:d,expose:g,inheritAttrs:l,components:u,directives:O,filters:y}=t,b=process.env.NODE_ENV!=="production"?$i():null;if(process.env.NODE_ENV!=="production"){const[M]=e.propsOptions;if(M)for(const P in M)b("Props",P)}if(N&&Fi(N,r,b),s)for(const M in s){const P=s[M];q(P)?(process.env.NODE_ENV!=="production"?Object.defineProperty(r,M,{value:P.bind(n),configurable:!0,enumerable:!0,writable:!0}):r[M]=P.bind(n),process.env.NODE_ENV!=="production"&&b("Methods",M)):process.env.NODE_ENV!=="production"&&k(`Method "${M}" has type "${typeof P}" in the component definition. Did you reference the function correctly?`)}if(o){process.env.NODE_ENV!=="production"&&!q(o)&&k("The data option must be a function. Plain object usage is no longer supported.");const M=o.call(n,n);if(process.env.NODE_ENV!=="production"&&qn(M)&&k("data() returned a Promise - note data() cannot be async; If you intend to perform data fetching before component renders, use async setup() + <Suspense>."),!Y(M))process.env.NODE_ENV!=="production"&&k("data() should return an object.");else if(e.data=cn(M),process.env.NODE_ENV!=="production")for(const P in M)b("Data",P),wn(P[0])||Object.defineProperty(r,P,{configurable:!0,enumerable:!0,get:()=>M[P],set:ie})}if(yn=!0,i)for(const M in i){const P=i[M],K=q(P)?P.bind(n,n):q(P.get)?P.get.bind(n,n):ie;process.env.NODE_ENV!=="production"&&K===ie&&k(`Computed property "${M}" has no getter.`);const ee=!q(P)&&q(P.set)?P.set.bind(n):process.env.NODE_ENV!=="production"?()=>{k(`Write operation failed: computed property "${M}" is readonly.`)}:ie,he=bs({get:K,set:ee});Object.defineProperty(r,M,{enumerable:!0,configurable:!0,get:()=>he.value,set:be=>he.value=be}),process.env.NODE_ENV!=="production"&&b("Computed",M)}if(c)for(const M in c)Lr(c[M],r,n,M);if(f){const M=q(f)?f.call(n):f;Reflect.ownKeys(M).forEach(P=>{Ki(P,M[P])})}S&&$r(S,e,"c");function R(M,P){H(P)?P.forEach(K=>M(K.bind(n))):P&&M(P.bind(n))}if(R(Si,a),R(Ci,h),R(Mi,T),R(Ii,$),R(Ni,U),R(xi,te),R(Bi,C),R(Ai,D),R(Ri,m),R(Ti,J),R(Br,L),R(Pi,d),H(g))if(g.length){const M=e.exposed||(e.exposed={});g.forEach(P=>{Object.defineProperty(M,P,{get:()=>n[P],set:K=>n[P]=K})})}else e.exposed||(e.exposed={});v&&e.render===ie&&(e.render=v),l!=null&&(e.inheritAttrs=l),u&&(e.components=u),O&&(e.directives=O),d&&Ei(e)}function Fi(e,t,n=ie){H(e)&&(e=On(e));for(const r in e){const o=e[r];let i;Y(o)?"default"in o?i=Bt(o.from||r,o.default,!0):i=Bt(o.from||r):i=Bt(o),re(i)?Object.defineProperty(t,r,{enumerable:!0,configurable:!0,get:()=>i.value,set:s=>i.value=s}):t[r]=i,process.env.NODE_ENV!=="production"&&n("Inject",r)}}function $r(e,t,n){Dt(H(e)?e.map(r=>r.bind(t.proxy)):e.bind(t.proxy),t,n)}function Lr(e,t,n,r){let o=r.includes(".")?Ur(n,r):()=>n[r];if(ue(e)){const i=t[e];q(i)?Nn(o,i):process.env.NODE_ENV!=="production"&&k(`Invalid watch handler specified by key "${e}"`,i)}else if(q(e))Nn(o,e.bind(n));else if(Y(e))if(H(e))e.forEach(i=>Lr(i,t,n,r));else{const i=q(e.handler)?e.handler.bind(n):t[e.handler];q(i)?Nn(o,i,e):process.env.NODE_ENV!=="production"&&k(`Invalid watch handler specified by key "${e.handler}"`,i)}else process.env.NODE_ENV!=="production"&&k(`Invalid watch option: "${r}"`,e)}function En(e){const t=e.type,{mixins:n,extends:r}=t,{mixins:o,optionsCache:i,config:{optionMergeStrategies:s}}=e.appContext,c=i.get(t);let f;return c?f=c:!o.length&&!n&&!r?f=t:(f={},o.length&&o.forEach(N=>At(f,N,s,!0)),At(f,t,s)),Y(t)&&i.set(t,f),f}function At(e,t,n,r=!1){const{mixins:o,extends:i}=t;i&&At(e,i,n,!0),o&&o.forEach(s=>At(e,s,n,!0));for(const s in t)if(r&&s==="expose")process.env.NODE_ENV!=="production"&&k('"expose" option is ignored when declared in mixins or extends. It should only be declared in the base component itself.');else{const c=qi[s]||n&&n[s];e[s]=c?c(e[s],t[s]):t[s]}return e}const qi={data:Fr,props:qr,emits:qr,methods:ft,computed:ft,beforeCreate:ce,created:ce,beforeMount:ce,mounted:ce,beforeUpdate:ce,updated:ce,beforeDestroy:ce,beforeUnmount:ce,destroyed:ce,unmounted:ce,activated:ce,deactivated:ce,errorCaptured:ce,serverPrefetch:ce,components:ft,directives:ft,watch:ji,provide:Fr,inject:Hi};function Fr(e,t){return t?e?function(){return se(q(e)?e.call(this,this):e,q(t)?t.call(this,this):t)}:t:e}function Hi(e,t){return ft(On(e),On(t))}function On(e){if(H(e)){const t={};for(let n=0;n<e.length;n++)t[e[n]]=e[n];return t}return e}function ce(e,t){return e?[...new Set([].concat(e,t))]:t}function ft(e,t){return e?se(Object.create(null),e,t):t}function qr(e,t){return e?H(e)&&H(t)?[...new Set([...e,...t])]:se(Object.create(null),kr(e),kr(t??{})):t}function ji(e,t){if(!e)return t;if(!t)return e;const n=se(Object.create(null),e);for(const r in t)n[r]=ce(e[r],t[r]);return n}let Ui=null;function Ki(e,t){if(!de)process.env.NODE_ENV!=="production"&&k("provide() can only be used inside setup().");else{let n=de.provides;const r=de.parent&&de.parent.provides;r===n&&(n=de.provides=Object.create(r)),n[e]=t}}function Bt(e,t,n=!1){const r=de||pe;if(r||Ui){const o=r?r.parent==null?r.vnode.appContext&&r.vnode.appContext.provides:r.parent.provides:void 0;if(o&&e in o)return o[e];if(arguments.length>1)return n&&q(t)?t.call(r&&r.proxy):t;process.env.NODE_ENV!=="production"&&k(`injection "${String(e)}" not found.`)}else process.env.NODE_ENV!=="production"&&k("inject() can only be used inside setup() or functional components.")}const Wi={},Hr=e=>Object.getPrototypeOf(e)===Wi;let lt,Re;function Qi(e,t){e.appContext.config.performance&&Vt()&&Re.mark(`vue-${t}-${e.uid}`),process.env.NODE_ENV!=="production"&&mi(e,t,Vt()?Re.now():Date.now())}function Ji(e,t){if(e.appContext.config.performance&&Vt()){const n=`vue-${t}-${e.uid}`,r=n+":end";Re.mark(r),Re.measure(`<${An(e,e.type)}> ${t}`,n,r),Re.clearMarks(n),Re.clearMarks(r)}process.env.NODE_ENV!=="production"&&wi(e,t,Vt()?Re.now():Date.now())}function Vt(){return lt!==void 0||(typeof window<"u"&&window.performance?(lt=!0,Re=window.performance):lt=!1),lt}const Yi=is,Gi=Symbol.for("v-scx"),Xi=()=>{{const e=Bt(Gi);return e||process.env.NODE_ENV!=="production"&&k("Server rendering context not provided. Make sure to only call useSSRContext() conditionally in the server build."),e}};function Nn(e,t,n){return process.env.NODE_ENV!=="production"&&!q(t)&&k("`watch(fn, options?)` signature has been moved to a separate API. Use `watchEffect(fn, options?)` instead. `watch` now only supports `watch(source, cb, options?) signature."),jr(e,t,n)}function jr(e,t,n=Q){const{immediate:r,deep:o,flush:i,once:s}=n;process.env.NODE_ENV!=="production"&&!t&&(r!==void 0&&k('watch() "immediate" option is only respected when using the watch(source, callback, options?) signature.'),o!==void 0&&k('watch() "deep" option is only respected when using the watch(source, callback, options?) signature.'),s!==void 0&&k('watch() "once" option is only respected when using the watch(source, callback, options?) signature.'));const c=se({},n);process.env.NODE_ENV!=="production"&&(c.onWarn=k);let f;if(Ft)if(i==="sync"){const h=Xi();f=h.__watcherHandles||(h.__watcherHandles=[])}else if(!t||r)c.once=!0;else return{stop:ie,resume:ie,pause:ie};const N=de;c.call=(h,T,$)=>Dt(h,N,T,$);let S=!1;i==="post"?c.scheduler=h=>{Yi(h,N&&N.suspense)}:i!=="sync"&&(S=!0,c.scheduler=(h,T)=>{T?h():gn(h)}),c.augmentJob=h=>{t&&(h.flags|=4),S&&(h.flags|=2,N&&(h.id=N.uid,h.i=N))};const a=ni(e,t,c);return f&&f.push(a),a}function Zi(e,t,n){const r=this.proxy,o=ue(e)?e.includes(".")?Ur(r,e):()=>r[e]:e.bind(r,r);let i;q(t)?i=t:(i=t.handler,n=t);const s=Rn(this),c=jr(o,i.bind(r),n);return s(),c}function Ur(e,t){const n=t.split(".");return()=>{let r=e;for(let o=0;o<n.length&&r;o++)r=r[n[o]];return r}}function js(){}function Kr(e,t=!0){let n;for(let r=0;r<e.length;r++){const o=e[r];if(Cn(o)){if(o.type!==Xe||o.children==="v-if"){if(n)return;if(n=o,process.env.NODE_ENV!=="production"&&t&&n.patchFlag>0&&n.patchFlag&2048)return Kr(n.children)}}else return}return n}function Wr({vnode:e,parent:t},n){for(;t;){const r=t.subTree;if(r.suspense&&r.suspense.activeBranch===e&&(r.el=e.el),r===e)(e=t.vnode).el=n,t=t.parent;else break}}const zi=e=>e.__isSuspense;let xn=0;const es={name:"Suspense",__isSuspense:!0,process(e,t,n,r,o,i,s,c,f,N){if(e==null)ts(t,n,r,o,i,s,c,f,N);else{if(i&&i.deps>0&&!e.suspense.isInFallback){t.suspense=e.suspense,t.suspense.vnode=t,t.el=e.el;return}ns(e,t,n,r,o,s,c,f,N)}},hydrate:rs,normalize:os};function dt(e,t){const n=e.props&&e.props[t];q(n)&&n()}function ts(e,t,n,r,o,i,s,c,f){const{p:N,o:{createElement:S}}=f,a=S("div"),h=e.suspense=Jr(e,o,r,t,a,n,i,s,c,f);N(null,h.pendingBranch=e.ssContent,a,null,r,h,i,s),h.deps>0?(dt(e,"onPending"),dt(e,"onFallback"),N(null,e.ssFallback,t,n,r,null,i,s),Ge(h,e.ssFallback)):h.resolve(!1,!0)}function ns(e,t,n,r,o,i,s,c,{p:f,um:N,o:{createElement:S}}){const a=t.suspense=e.suspense;a.vnode=t,t.el=e.el;const h=t.ssContent,T=t.ssFallback,{activeBranch:$,pendingBranch:U,isInFallback:te,isHydrating:X}=a;if(U)a.pendingBranch=h,Mn(h,U)?(f(U,h,a.hiddenContainer,null,o,a,i,s,c),a.deps<=0?a.resolve():te&&(X||(f($,T,n,r,o,null,i,s,c),Ge(a,T)))):(a.pendingId=xn++,X?(a.isHydrating=!1,a.activeBranch=U):N(U,o,a),a.deps=0,a.effects.length=0,a.hiddenContainer=S("div"),te?(f(null,h,a.hiddenContainer,null,o,a,i,s,c),a.deps<=0?a.resolve():(f($,T,n,r,o,null,i,s,c),Ge(a,T))):$&&Mn(h,$)?(f($,h,n,r,o,a,i,s,c),a.resolve(!0)):(f(null,h,a.hiddenContainer,null,o,a,i,s,c),a.deps<=0&&a.resolve()));else if($&&Mn(h,$))f($,h,n,r,o,a,i,s,c),Ge(a,h);else if(dt(t,"onPending"),a.pendingBranch=h,h.shapeFlag&512?a.pendingId=h.component.suspenseId:a.pendingId=xn++,f(null,h,a.hiddenContainer,null,o,a,i,s,c),a.deps<=0)a.resolve();else{const{timeout:J,pendingId:ne}=a;J>0?setTimeout(()=>{a.pendingId===ne&&a.fallback(T)},J):J===0&&a.fallback(T)}}let Qr=!1;function Jr(e,t,n,r,o,i,s,c,f,N,S=!1){process.env.NODE_ENV!=="production"&&!Qr&&(Qr=!0,console[console.info?"info":"log"]("<Suspense> is an experimental feature and its API will likely change."));const{p:a,m:h,um:T,n:$,o:{parentNode:U,remove:te}}=N;let X;const J=ss(e);J&&t&&t.pendingBranch&&(X=t.pendingId,t.deps++);const ne=e.props?yo(e.props.timeout):void 0;process.env.NODE_ENV!=="production"&&ai(ne,"Suspense timeout");const L=i,v={vnode:e,parent:t,parentComponent:n,namespace:s,container:r,hiddenContainer:o,deps:0,pendingId:xn++,timeout:typeof ne=="number"?ne:-1,activeBranch:null,pendingBranch:null,isInFallback:!S,isHydrating:S,isUnmounted:!1,effects:[],resolve(D=!1,m=!1){if(process.env.NODE_ENV!=="production"){if(!D&&!v.pendingBranch)throw new Error("suspense.resolve() is called without a pending branch.");if(v.isUnmounted)throw new Error("suspense.resolve() is called on an already unmounted suspense boundary.")}const{vnode:C,activeBranch:d,pendingBranch:g,pendingId:l,effects:u,parentComponent:O,container:y}=v;let b=!1;v.isHydrating?v.isHydrating=!1:D||(b=d&&g.transition&&g.transition.mode==="out-in",b&&(d.transition.afterLeave=()=>{l===v.pendingId&&(h(g,y,i===L?$(d):i,0),Mt(u))}),d&&(U(d.el)===y&&(i=$(d)),T(d,O,v,!0)),b||h(g,y,i,0)),Ge(v,g),v.pendingBranch=null,v.isInFallback=!1;let R=v.parent,M=!1;for(;R;){if(R.pendingBranch){R.effects.push(...u),M=!0;break}R=R.parent}!M&&!b&&Mt(u),v.effects=[],J&&t&&t.pendingBranch&&X===t.pendingId&&(t.deps--,t.deps===0&&!m&&t.resolve()),dt(C,"onResolve")},fallback(D){if(!v.pendingBranch)return;const{vnode:m,activeBranch:C,parentComponent:d,container:g,namespace:l}=v;dt(m,"onFallback");const u=$(C),O=()=>{v.isInFallback&&(a(null,D,g,u,d,null,l,c,f),Ge(v,D))},y=D.transition&&D.transition.mode==="out-in";y&&(C.transition.afterLeave=O),v.isInFallback=!0,T(C,d,null,!0),y||O()},move(D,m,C){v.activeBranch&&h(v.activeBranch,D,m,C),v.container=D},next(){return v.activeBranch&&$(v.activeBranch)},registerDep(D,m,C){const d=!!v.pendingBranch;d&&v.deps++;const g=D.vnode.el;D.asyncDep.catch(l=>{St(l,D,0)}).then(l=>{if(D.isUnmounted||v.isUnmounted||v.pendingId!==D.suspenseId)return;D.asyncResolved=!0;const{vnode:u}=D;process.env.NODE_ENV!=="production"&&wr(u),gs(D,l,!1),g&&(u.el=g);const O=!g&&D.subTree.el;m(D,u,U(g||D.subTree.el),g?null:$(D.subTree),v,s,C),O&&te(O),Wr(D,u.el),process.env.NODE_ENV!=="production"&&br(),d&&--v.deps===0&&v.resolve()})},unmount(D,m){v.isUnmounted=!0,v.activeBranch&&T(v.activeBranch,n,D,m),v.pendingBranch&&T(v.pendingBranch,n,D,m)}};return v}function rs(e,t,n,r,o,i,s,c,f){const N=t.suspense=Jr(t,r,n,e.parentNode,document.createElement("div"),null,o,i,s,c,!0),S=f(e,N.pendingBranch=t.ssContent,n,N,i,s);return N.deps===0&&N.resolve(!1,!0),S}function os(e){const{shapeFlag:t,children:n}=e,r=t&32;e.ssContent=Yr(r?n.default:n),e.ssFallback=r?Yr(n.fallback):Ae(Xe)}function Yr(e){let t;if(q(e)){const n=ze&&e._c;n&&(e._d=!1,Ze()),e=e(),n&&(e._d=!0,t=le,Xr())}if(H(e)){const n=Kr(e);process.env.NODE_ENV!=="production"&&!n&&e.filter(r=>r!==Vr).length>0&&k("<Suspense> slots expect a single root node."),e=n}return e=fs(e),t&&!e.dynamicChildren&&(e.dynamicChildren=t.filter(n=>n!==e)),e}function is(e,t){t&&t.pendingBranch?H(e)?t.effects.push(...e):t.effects.push(e):Mt(e)}function Ge(e,t){e.activeBranch=t;const{vnode:n,parentComponent:r}=e;let o=t.el;for(;!o&&t.component;)t=t.component.subTree,o=t.el;n.el=o,r&&r.subTree===n&&(r.vnode.el=o,Wr(r,o))}function ss(e){const t=e.props&&e.props.suspensible;return t!=null&&t!==!1}const Dn=Symbol.for("v-fgt"),Gr=Symbol.for("v-txt"),Xe=Symbol.for("v-cmt"),kt=[];let le=null;function Ze(e=!1){kt.push(le=e?null:[])}function Xr(){kt.pop(),le=kt[kt.length-1]||null}let ze=1;function Zr(e){ze+=e,e<0&&le&&(le.hasOnce=!0)}function zr(e){return e.dynamicChildren=ze>0?le||Be:null,Xr(),ze>0&&le&&le.push(e),e}function Sn(e,t,n,r,o,i){return zr(Lt(e,t,n,r,o,i,!0))}function eo(e,t,n,r,o){return zr(Ae(e,t,n,r,o,!0))}function Cn(e){return e?e.__v_isVNode===!0:!1}function Mn(e,t){if(process.env.NODE_ENV!=="production"&&t.shapeFlag&6&&e.component){const n=It.get(t.type);if(n&&n.has(e.component))return e.shapeFlag&=-257,t.shapeFlag&=-513,!1}return e.type===t.type&&e.key===t.key}const as=(...e)=>no(...e),to=({key:e})=>e??null,$t=({ref:e,ref_key:t,ref_for:n})=>(typeof e=="number"&&(e=""+e),e!=null?ue(e)||re(e)||q(e)?{i:pe,r:e,k:t,f:!!n}:e:null);function Lt(e,t=null,n=null,r=0,o=null,i=e===Dn?0:1,s=!1,c=!1){const f={__v_isVNode:!0,__v_skip:!0,type:e,props:t,key:t&&to(t),ref:t&&$t(t),scopeId:Ir,slotScopeIds:null,children:n,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetStart:null,targetAnchor:null,staticCount:0,shapeFlag:i,patchFlag:r,dynamicProps:o,dynamicChildren:null,appContext:null,ctx:pe};return c?(Tn(f,n),i&128&&e.normalize(f)):n&&(f.shapeFlag|=ue(n)?8:16),process.env.NODE_ENV!=="production"&&f.key!==f.key&&k("VNode created with invalid key (NaN). VNode type:",f.type),ze>0&&!s&&le&&(f.patchFlag>0||i&6)&&f.patchFlag!==32&&le.push(f),f}const Ae=process.env.NODE_ENV!=="production"?as:no;function no(e,t=null,n=null,r=0,o=null,i=!1){if((!e||e===Vr)&&(process.env.NODE_ENV!=="production"&&!e&&k(`Invalid vnode type when creating vnode: ${e}.`),e=Xe),Cn(e)){const c=pt(e,t,!0);return n&&Tn(c,n),ze>0&&!i&&le&&(c.shapeFlag&6?le[le.indexOf(e)]=c:le.push(c)),c.patchFlag=-2,c}if(so(e)&&(e=e.__vccOpts),t){t=cs(t);let{class:c,style:f}=t;c&&!ue(c)&&(t.class=je(c)),Y(f)&&(Et(f)&&!H(f)&&(f=se({},f)),t.style=Wt(f))}const s=ue(e)?1:zi(e)?128:yi(e)?64:Y(e)?4:q(e)?2:0;return process.env.NODE_ENV!=="production"&&s&4&&Et(e)&&(e=F(e),k("Vue received a Component that was made a reactive object. This can lead to unnecessary performance overhead and should be avoided by marking the component with `markRaw` or using `shallowRef` instead of `ref`.",`
Component that was made reactive: `,e)),Lt(e,t,n,r,o,s,i,!0)}function cs(e){return e?Et(e)||Hr(e)?se({},e):e:null}function pt(e,t,n=!1,r=!1){const{props:o,ref:i,patchFlag:s,children:c,transition:f}=e,N=t?ds(o||{},t):o,S={__v_isVNode:!0,__v_skip:!0,type:e.type,props:N,key:N&&to(N),ref:t&&t.ref?n&&i?H(i)?i.concat($t(t)):[i,$t(t)]:$t(t):i,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:process.env.NODE_ENV!=="production"&&s===-1&&H(c)?c.map(ro):c,target:e.target,targetStart:e.targetStart,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:t&&e.type!==Dn?s===-1?16:s|16:s,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:f,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&pt(e.ssContent),ssFallback:e.ssFallback&&pt(e.ssFallback),el:e.el,anchor:e.anchor,ctx:e.ctx,ce:e.ce};return f&&r&&Pr(S,f.clone(S)),S}function ro(e){const t=pt(e);return H(e.children)&&(t.children=e.children.map(ro)),t}function us(e=" ",t=0){return Ae(Gr,null,e,t)}function In(e="",t=!1){return t?(Ze(),eo(Xe,null,e)):Ae(Xe,null,e)}function fs(e){return e==null||typeof e=="boolean"?Ae(Xe):H(e)?Ae(Dn,null,e.slice()):typeof e=="object"?ls(e):Ae(Gr,null,String(e))}function ls(e){return e.el===null&&e.patchFlag!==-1||e.memo?e:pt(e)}function Tn(e,t){let n=0;const{shapeFlag:r}=e;if(t==null)t=null;else if(H(t))n=16;else if(typeof t=="object")if(r&65){const o=t.default;o&&(o._c&&(o._d=!1),Tn(e,o()),o._c&&(o._d=!0));return}else{n=32;const o=t._;!o&&!Hr(t)?t._ctx=pe:o===3&&pe&&(pe.slots._===1?t._=1:(t._=2,e.patchFlag|=1024))}else q(t)?(t={default:t,_ctx:pe},n=32):(t=String(t),r&64?(n=16,t=[us(t)]):n=8);e.children=t,e.shapeFlag|=n}function ds(...e){const t={};for(let n=0;n<e.length;n++){const r=e[n];for(const o in r)if(o==="class")t.class!==r.class&&(t.class=je([t.class,r.class]));else if(o==="style")t.style=Wt([t.style,r.style]);else if(vo(o)){const i=t[o],s=r[o];s&&i!==s&&!(H(i)&&i.includes(s))&&(t[o]=i?[].concat(i,s):s)}else o!==""&&(t[o]=r[o])}return t}let de=null;const ps=()=>de||pe;let Pn;{const e=Jn(),t=(n,r)=>{let o;return(o=e[n])||(o=e[n]=[]),o.push(r),i=>{o.length>1?o.forEach(s=>s(i)):o[0](i)}};Pn=t("__VUE_INSTANCE_SETTERS__",n=>de=n),t("__VUE_SSR_SETTERS__",n=>Ft=n)}const Rn=e=>{const t=de;return Pn(e),e.scope.on(),()=>{e.scope.off(),Pn(t)}};function hs(e){return e.vnode.shapeFlag&4}let Ft=!1;function gs(e,t,n){q(t)?e.type.__ssrInlineRender?e.ssrRender=t:e.render=t:Y(t)?(process.env.NODE_ENV!=="production"&&Cn(t)&&k("setup() should not return VNodes directly - return a render function instead."),process.env.NODE_ENV!=="production"&&(e.devtoolsRawSetupState=t),e.setupState=mr(t),process.env.NODE_ENV!=="production"&&ki(e)):process.env.NODE_ENV!=="production"&&t!==void 0&&k(`setup() should return an object. Received: ${t===null?"null":typeof t}`),vs(e,n)}let oo;function vs(e,t,n){const r=e.type;if(!e.render){if(!t&&oo&&!r.render){const o=r.template||En(e).template;if(o){process.env.NODE_ENV!=="production"&&Qi(e,"compile");const{isCustomElement:i,compilerOptions:s}=e.appContext.config,{delimiters:c,compilerOptions:f}=r,N=se(se({isCustomElement:i,delimiters:c},s),f);r.render=oo(o,N),process.env.NODE_ENV!=="production"&&Ji(e,"compile")}}e.render=r.render||ie}{const o=Rn(e);Ue();try{Li(e)}finally{Ke(),o()}}process.env.NODE_ENV!=="production"&&!r.render&&e.render===ie&&!t&&(r.template?k('Component provided template option but runtime compilation is not supported in this build of Vue. Configure your bundler to alias "vue" to "vue/dist/vue.esm-bundler.js".'):k("Component is missing template or render function: ",r))}process.env.NODE_ENV;function _s(e){return e.exposed?e.exposeProxy||(e.exposeProxy=new Proxy(mr(Qo(e.exposed)),{get(t,n){if(n in t)return t[n];if(n in ut)return ut[n](e)},has(t,n){return n in t||n in ut}})):e.proxy}const ms=/(?:^|[-_])(\w)/g,ws=e=>e.replace(ms,t=>t.toUpperCase()).replace(/[-_]/g,"");function io(e,t=!0){return q(e)?e.displayName||e.name:e.name||t&&e.__name}function An(e,t,n=!1){let r=io(t);if(!r&&t.__file){const o=t.__file.match(/([^/\\]+)\.\w+$/);o&&(r=o[1])}if(!r&&e&&e.parent){const o=i=>{for(const s in i)if(i[s]===t)return s};r=o(e.components||e.parent.type.components)||o(e.appContext.components)}return r?ws(r):n?"App":"Anonymous"}function so(e){return q(e)&&"__vccOpts"in e}const bs=(e,t)=>{const n=ei(e,t,Ft);if(process.env.NODE_ENV!=="production"){const r=ps();r&&r.appContext.config.warnRecursiveComputed&&(n._warnRecursive=!0)}return n};function ys(){if(process.env.NODE_ENV==="production"||typeof window>"u")return;const e={style:"color:#3ba776"},t={style:"color:#1677ff"},n={style:"color:#f5222d"},r={style:"color:#eb2f96"},o={__vue_custom_formatter:!0,header(a){return Y(a)?a.__isVue?["div",e,"VueInstance"]:re(a)?["div",{},["span",e,S(a)],"<",c("_value"in a?a._value:a),">"]:Qe(a)?["div",{},["span",e,fe(a)?"ShallowReactive":"Reactive"],"<",c(a),`>${Oe(a)?" (readonly)":""}`]:Oe(a)?["div",{},["span",e,fe(a)?"ShallowReadonly":"Readonly"],"<",c(a),">"]:null:null},hasBody(a){return a&&a.__isVue},body(a){if(a&&a.__isVue)return["div",{},...i(a.$)]}};function i(a){const h=[];a.type.props&&a.props&&h.push(s("props",F(a.props))),a.setupState!==Q&&h.push(s("setup",a.setupState)),a.data!==Q&&h.push(s("data",F(a.data)));const T=f(a,"computed");T&&h.push(s("computed",T));const $=f(a,"inject");return $&&h.push(s("injected",$)),h.push(["div",{},["span",{style:r.style+";opacity:0.66"},"$ (internal): "],["object",{object:a}]]),h}function s(a,h){return h=se({},h),Object.keys(h).length?["div",{style:"line-height:1.25em;margin-bottom:0.6em"},["div",{style:"color:#476582"},a],["div",{style:"padding-left:1.25em"},...Object.keys(h).map(T=>["div",{},["span",r,T+": "],c(h[T],!1)])]]:["span",{}]}function c(a,h=!0){return typeof a=="number"?["span",t,a]:typeof a=="string"?["span",n,JSON.stringify(a)]:typeof a=="boolean"?["span",r,a]:Y(a)?["object",{object:h?F(a):a}]:["span",n,String(a)]}function f(a,h){const T=a.type;if(q(T))return;const $={};for(const U in a.ctx)N(T,U,h)&&($[U]=a.ctx[U]);return $}function N(a,h,T){const $=a[T];if(H($)&&$.includes(h)||Y($)&&h in $||a.extends&&N(a.extends,h,T)||a.mixins&&a.mixins.some(U=>N(U,h,T)))return!0}function S(a){return fe(a)?"ShallowRef":a.effect?"ComputedRef":"Ref"}window.devtoolsFormatters?window.devtoolsFormatters.push(o):window.devtoolsFormatters=[o]}process.env.NODE_ENV,process.env.NODE_ENV,process.env.NODE_ENV;/**
* vue v3.5.3
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/function Es(){ys()}process.env.NODE_ENV!=="production"&&Es();const Bn={numeric:"Numeric",alphanumeric:"Alphanumeric",byte:"Byte",kanji:"Kanji"};function Os(e){switch(!0){case/^[0-9]*$/.test(e):return Bn.numeric;case/^[0-9A-Z $%*+\-./:]*$/.test(e):return Bn.alphanumeric;default:return Bn.byte}}const qt=e=>!!e&&typeof e=="object"&&!Array.isArray(e);function Ht(e,...t){if(!t.length)return e;const n=t.shift();return n===void 0||!qt(e)||!qt(n)?e:(e={...e},Object.keys(n).forEach(r=>{const o=e[r],i=n[r];Array.isArray(o)&&Array.isArray(i)?e[r]=i:qt(o)&&qt(i)?e[r]=Ht(Object.assign({},o),i):e[r]=i}),Ht(e,...t))}function Ns(e,t){const n=document.createElement("a");n.download=t,n.href=e,document.body.appendChild(n),n.click(),document.body.removeChild(n)}function xs({originalHeight:e,originalWidth:t,maxHiddenDots:n,maxHiddenAxisDots:r,dotSize:o}){const i={x:0,y:0},s={x:0,y:0};if(e<=0||t<=0||n<=0||o<=0)return{height:0,width:0,hideYDots:0,hideXDots:0};const c=e/t;return i.x=Math.floor(Math.sqrt(n/c)),i.x<=0&&(i.x=1),r&&r<i.x&&(i.x=r),i.x%2===0&&i.x--,s.x=i.x*o,i.y=1+2*Math.ceil((i.x*c-1)/2),s.y=Math.round(s.x*c),(i.y*i.x>n||r&&r<i.y)&&(r&&r<i.y?(i.y=r,i.y%2===0&&i.x--):i.y-=2,s.y=i.y*o,i.x=1+2*Math.ceil((i.y/c-1)/2),s.x=Math.round(s.y/c)),{height:s.y,width:s.x,hideYDots:i.y,hideXDots:i.x}}const Ds={L:.07,M:.15,Q:.25,H:.3},et={dots:"dots",rounded:"rounded",classy:"classy",classyRounded:"classy-rounded",square:"square",extraRounded:"extra-rounded"};class Vn{constructor({context:t,type:n}){oe(this,"_context");oe(this,"_type");this._context=t,this._type=n}draw(t,n,r,o){const i=this._context,s=this._type;let c;switch(s){case et.dots:c=this._drawDot;break;case et.classy:c=this._drawClassy;break;case et.classyRounded:c=this._drawClassyRounded;break;case et.rounded:c=this._drawRounded;break;case et.extraRounded:c=this._drawExtraRounded;break;case et.square:default:c=this._drawSquare}c.call(this,{x:t,y:n,size:r,context:i,getNeighbor:o})}_rotateFigure({x:t,y:n,size:r,context:o,rotation:i,draw:s}){const c=t+r/2,f=n+r/2;o.translate(c,f),i&&o.rotate(i),s(),o.closePath(),i&&o.rotate(-i),o.translate(-c,-f)}_basicDot(t){const{size:n,context:r}=t;this._rotateFigure({...t,draw:()=>{r.moveTo(0,0),r.arc(0,0,n/2,0,Math.PI*2)}})}_basicSquare(t){const{size:n,context:r}=t;this._rotateFigure({...t,draw:()=>{r.moveTo(0,0),r.rect(-n/2,-n/2,n,n)}})}_basicSideRounded(t){const{size:n,context:r}=t;this._rotateFigure({...t,draw:()=>{r.moveTo(0,0),r.arc(0,0,n/2,-Math.PI/2,Math.PI/2),r.lineTo(-n/2,n/2),r.lineTo(-n/2,-n/2),r.lineTo(0,-n/2)}})}_basicCornerRounded(t){const{size:n,context:r}=t;this._rotateFigure({...t,draw:()=>{r.moveTo(0,0),r.arc(0,0,n/2,-Math.PI/2,0),r.lineTo(n/2,n/2),r.lineTo(-n/2,n/2),r.lineTo(-n/2,-n/2),r.lineTo(0,-n/2)}})}_basicCornerExtraRounded(t){const{size:n,context:r}=t;this._rotateFigure({...t,draw:()=>{r.moveTo(0,0),r.arc(-n/2,n/2,n,-Math.PI/2,0),r.lineTo(-n/2,n/2),r.lineTo(-n/2,-n/2)}})}_basicCornersRounded(t){const{size:n,context:r}=t;this._rotateFigure({...t,draw:()=>{r.moveTo(0,0),r.arc(0,0,n/2,-Math.PI/2,0),r.lineTo(n/2,n/2),r.lineTo(0,n/2),r.arc(0,0,n/2,Math.PI/2,Math.PI),r.lineTo(-n/2,-n/2),r.lineTo(0,-n/2)}})}_basicCornersExtraRounded(t){const{size:n,context:r}=t;this._rotateFigure({...t,draw:()=>{r.moveTo(0,0),r.arc(-n/2,n/2,n,-Math.PI/2,0),r.arc(n/2,-n/2,n,Math.PI/2,Math.PI)}})}_drawDot({x:t,y:n,size:r,context:o}){this._basicDot({x:t,y:n,size:r,context:o,rotation:0})}_drawSquare({x:t,y:n,size:r,context:o}){this._basicSquare({x:t,y:n,size:r,context:o,rotation:0})}_drawRounded({x:t,y:n,size:r,context:o,getNeighbor:i}){const s=+i(-1,0),c=+i(1,0),f=+i(0,-1),N=+i(0,1),S=s+c+f+N;if(S===0){this._basicDot({x:t,y:n,size:r,context:o,rotation:0});return}if(S>2||s&&c||f&&N){this._basicSquare({x:t,y:n,size:r,context:o,rotation:0});return}if(S===2){let a=0;s&&f?a=Math.PI/2:f&&c?a=Math.PI:c&&N&&(a=-Math.PI/2),this._basicCornerRounded({x:t,y:n,size:r,context:o,rotation:a});return}if(S===1){let a=0;f?a=Math.PI/2:c?a=Math.PI:N&&(a=-Math.PI/2),this._basicSideRounded({x:t,y:n,size:r,context:o,rotation:a})}}_drawExtraRounded({x:t,y:n,size:r,context:o,getNeighbor:i}){const s=+i(-1,0),c=+i(1,0),f=+i(0,-1),N=+i(0,1),S=s+c+f+N;if(S===0){this._basicDot({x:t,y:n,size:r,context:o,rotation:0});return}if(S>2||s&&c||f&&N){this._basicSquare({x:t,y:n,size:r,context:o,rotation:0});return}if(S===2){let a=0;s&&f?a=Math.PI/2:f&&c?a=Math.PI:c&&N&&(a=-Math.PI/2),this._basicCornerExtraRounded({x:t,y:n,size:r,context:o,rotation:a});return}if(S===1){let a=0;f?a=Math.PI/2:c?a=Math.PI:N&&(a=-Math.PI/2),this._basicSideRounded({x:t,y:n,size:r,context:o,rotation:a})}}_drawClassy({x:t,y:n,size:r,context:o,getNeighbor:i}){const s=+i(-1,0),c=+i(1,0),f=+i(0,-1),N=+i(0,1);if(s+c+f+N===0){this._basicCornersRounded({x:t,y:n,size:r,context:o,rotation:Math.PI/2});return}if(!s&&!f){this._basicCornerRounded({x:t,y:n,size:r,context:o,rotation:-Math.PI/2});return}if(!c&&!N){this._basicCornerRounded({x:t,y:n,size:r,context:o,rotation:Math.PI/2});return}this._basicSquare({x:t,y:n,size:r,context:o,rotation:0})}_drawClassyRounded({x:t,y:n,size:r,context:o,getNeighbor:i}){const s=+i(-1,0),c=+i(1,0),f=+i(0,-1),N=+i(0,1);if(s+c+f+N===0){this._basicCornersRounded({x:t,y:n,size:r,context:o,rotation:Math.PI/2});return}if(!s&&!f){this._basicCornerExtraRounded({x:t,y:n,size:r,context:o,rotation:-Math.PI/2});return}if(!c&&!N){this._basicCornerExtraRounded({x:t,y:n,size:r,context:o,rotation:Math.PI/2});return}this._basicSquare({x:t,y:n,size:r,context:o,rotation:0})}}const kn={dot:"dot",square:"square",extraRounded:"extra-rounded"};class Ss{constructor({context:t,type:n}){oe(this,"_context");oe(this,"_type");this._context=t,this._type=n}draw(t,n,r,o){const i=this._context,s=this._type;let c;switch(s){case kn.square:c=this._drawSquare;break;case kn.extraRounded:c=this._drawExtraRounded;break;case kn.dot:default:c=this._drawDot}c.call(this,{x:t,y:n,size:r,context:i,rotation:o})}_rotateFigure({x:t,y:n,size:r,context:o,rotation:i,draw:s}){const c=t+r/2,f=n+r/2;o.translate(c,f),i&&o.rotate(i),s(),o.closePath(),i&&o.rotate(-i),o.translate(-c,-f)}_basicDot(t){const{size:n,context:r}=t,o=n/7;this._rotateFigure({...t,draw:()=>{r.arc(0,0,n/2,0,Math.PI*2),r.arc(0,0,n/2-o,0,Math.PI*2)}})}_basicSquare(t){const{size:n,context:r}=t,o=n/7;this._rotateFigure({...t,draw:()=>{r.rect(-n/2,-n/2,n,n),r.rect(-n/2+o,-n/2+o,n-2*o,n-2*o)}})}_basicExtraRounded(t){const{size:n,context:r}=t,o=n/7;this._rotateFigure({...t,draw:()=>{r.arc(-o,-o,2.5*o,Math.PI,-Math.PI/2),r.lineTo(o,-3.5*o),r.arc(o,-o,2.5*o,-Math.PI/2,0),r.lineTo(3.5*o,-o),r.arc(o,o,2.5*o,0,Math.PI/2),r.lineTo(-o,3.5*o),r.arc(-o,o,2.5*o,Math.PI/2,Math.PI),r.lineTo(-3.5*o,-o),r.arc(-o,-o,1.5*o,Math.PI,-Math.PI/2),r.lineTo(o,-2.5*o),r.arc(o,-o,1.5*o,-Math.PI/2,0),r.lineTo(2.5*o,-o),r.arc(o,o,1.5*o,0,Math.PI/2),r.lineTo(-o,2.5*o),r.arc(-o,o,1.5*o,Math.PI/2,Math.PI),r.lineTo(-2.5*o,-o)}})}_drawDot({x:t,y:n,size:r,context:o,rotation:i}){this._basicDot({x:t,y:n,size:r,context:o,rotation:i})}_drawSquare({x:t,y:n,size:r,context:o,rotation:i}){this._basicSquare({x:t,y:n,size:r,context:o,rotation:i})}_drawExtraRounded({x:t,y:n,size:r,context:o,rotation:i}){this._basicExtraRounded({x:t,y:n,size:r,context:o,rotation:i})}}const ao={dot:"dot",square:"square"};class Cs{constructor({context:t,type:n}){oe(this,"_context");oe(this,"_type");this._context=t,this._type=n}draw(t,n,r,o){const i=this._context;switch(this._type){case ao.square:this._drawSquare({x:t,y:n,size:r,context:i,rotation:o});break;case ao.dot:default:this._drawDot({x:t,y:n,size:r,context:i,rotation:o})}}_rotateFigure({x:t,y:n,size:r,context:o,rotation:i,draw:s}){const c=t+r/2,f=n+r/2;o.moveTo(0,0),o.translate(c,f),i&&o.rotate(i),s(),o.closePath(),i&&o.rotate(-i),o.translate(-c,-f)}_drawDot(t){const{size:n,context:r}=t;this._rotateFigure({...t,draw:()=>{r.moveTo(0,0),r.arc(0,0,n/2,0,Math.PI*2)}})}_drawSquare(t){const{size:n,context:r}=t;this._rotateFigure({...t,draw:()=>{r.moveTo(0,0),r.rect(-n/2,-n/2,n,n)}})}}const Ms={radial:"radial",linear:"linear"},qe=[[1,1,1,1,1,1,1],[1,0,0,0,0,0,1],[1,0,0,0,0,0,1],[1,0,0,0,0,0,1],[1,0,0,0,0,0,1],[1,0,0,0,0,0,1],[1,1,1,1,1,1,1]],He=[[0,0,0,0,0,0,0],[0,0,0,0,0,0,0],[0,0,1,1,1,0,0],[0,0,1,1,1,0,0],[0,0,1,1,1,0,0],[0,0,0,0,0,0,0],[0,0,0,0,0,0,0]];class Is{constructor(t){oe(this,"_canvas");oe(this,"_options");oe(this,"_qr");oe(this,"_image");this._canvas=document.createElement("canvas"),this._canvas.width=t.width,this._canvas.height=t.height,this._options=t}get context(){return this._canvas.getContext("2d")}get width(){return this._canvas.width}get height(){return this._canvas.height}getCanvas(){return this._canvas}clear(){const t=this.context;t&&t.clearRect(0,0,this._canvas.width,this._canvas.height)}async drawQR(t){const n=t.getModuleCount(),r=Math.min(this._options.width,this._options.height)-this._options.margin*2,o=Math.floor(r/n);let i={hideXDots:0,hideYDots:0,width:0,height:0};if(this._qr=t,this._options.image){if(await this.loadImage(),!this._image)return;const{imageOptions:s,qrOptions:c}=this._options,f=s.imageSize*Ds[c.errorCorrectionLevel],N=Math.floor(f*n*n);i=xs({originalWidth:this._image.width,originalHeight:this._image.height,maxHiddenDots:N,maxHiddenAxisDots:n-14,dotSize:o})}this.clear(),this.drawBackground(),this.drawDots((s,c)=>{var f,N,S,a,h,T;return!(this._options.imageOptions.hideBackgroundDots&&s>=(n-i.hideXDots)/2&&s<(n+i.hideXDots)/2&&c>=(n-i.hideYDots)/2&&c<(n+i.hideYDots)/2||(f=qe[s])!=null&&f[c]||(N=qe[s-n+7])!=null&&N[c]||(S=qe[s])!=null&&S[c-n+7]||(a=He[s])!=null&&a[c]||(h=He[s-n+7])!=null&&h[c]||(T=He[s])!=null&&T[c-n+7])}),this.drawCorners(),this._options.image&&this.drawImage({width:i.width,height:i.height,count:n,dotSize:o})}drawBackground(){const t=this.context,n=this._options;if(t){if(n.backgroundOptions.gradient){const r=n.backgroundOptions.gradient,o=this._createGradient({context:t,options:r,additionalRotation:0,x:0,y:0,size:this._canvas.width>this._canvas.height?this._canvas.width:this._canvas.height});r.colorStops.forEach(({offset:i,color:s})=>{o.addColorStop(i,s)}),t.fillStyle=o}else n.backgroundOptions.color&&(t.fillStyle=n.backgroundOptions.color);t.fillRect(0,0,this._canvas.width,this._canvas.height)}}drawDots(t){if(!this._qr)throw"QR code is not defined";const n=this.context;if(!n)throw"QR code is not defined";const r=this._options,o=this._qr.getModuleCount();if(o>r.width||o>r.height)throw"The canvas is too small.";const i=Math.min(r.width,r.height)-r.margin*2,s=Math.floor(i/o),c=Math.floor((r.width-o*s)/2),f=Math.floor((r.height-o*s)/2),N=new Vn({context:n,type:r.dotsOptions.type});n.beginPath();for(let S=0;S<o;S++)for(let a=0;a<o;a++)t&&!t(S,a)||this._qr.isDark(S,a)&&N.draw(c+S*s,f+a*s,s,(h,T)=>S+h<0||a+T<0||S+h>=o||a+T>=o||t&&!t(S+h,a+T)?!1:!!this._qr&&this._qr.isDark(S+h,a+T));if(r.dotsOptions.gradient){const S=r.dotsOptions.gradient,a=this._createGradient({context:n,options:S,additionalRotation:0,x:c,y:f,size:o*s});S.colorStops.forEach(({offset:h,color:T})=>{a.addColorStop(h,T)}),n.fillStyle=n.strokeStyle=a}else r.dotsOptions.color&&(n.fillStyle=n.strokeStyle=r.dotsOptions.color);n.fill("evenodd")}drawCorners(t){if(!this._qr)throw"QR code is not defined";const n=this.context;if(!n)throw"QR code is not defined";const r=this._options,o=this._qr.getModuleCount(),i=Math.min(r.width,r.height)-r.margin*2,s=Math.floor(i/o),c=s*7,f=s*3,N=Math.floor((r.width-o*s)/2),S=Math.floor((r.height-o*s)/2);[[0,0,0],[1,0,Math.PI/2],[0,1,-Math.PI/2]].forEach(([a,h,T])=>{var te,X,J,ne,L,v,D,m,C,d;if(t&&!t(a,h))return;const $=N+a*s*(o-7),U=S+h*s*(o-7);if((te=r.cornersSquareOptions)!=null&&te.type){const g=new Ss({context:n,type:(X=r.cornersSquareOptions)==null?void 0:X.type});n.beginPath(),g.draw($,U,c,T)}else{const g=new Vn({context:n,type:r.dotsOptions.type});n.beginPath();for(let l=0;l<qe.length;l++)for(let u=0;u<qe[l].length;u++)(J=qe[l])!=null&&J[u]&&g.draw($+l*s,U+u*s,s,(O,y)=>{var b;return!!((b=qe[l+O])!=null&&b[u+y])})}if((ne=r.cornersSquareOptions)!=null&&ne.gradient){const g=r.cornersSquareOptions.gradient,l=this._createGradient({context:n,options:g,additionalRotation:T,x:$,y:U,size:c});g.colorStops.forEach(({offset:u,color:O})=>{l.addColorStop(u,O)}),n.fillStyle=n.strokeStyle=l}else(L=r.cornersSquareOptions)!=null&&L.color&&(n.fillStyle=n.strokeStyle=r.cornersSquareOptions.color);if(n.fill("evenodd"),(v=r.cornersDotOptions)!=null&&v.type){const g=new Cs({context:n,type:(D=r.cornersDotOptions)==null?void 0:D.type});n.beginPath(),g.draw($+s*2,U+s*2,f,T)}else{const g=new Vn({context:n,type:r.dotsOptions.type});n.beginPath();for(let l=0;l<He.length;l++)for(let u=0;u<He[l].length;u++)(m=He[l])!=null&&m[u]&&g.draw($+l*s,U+u*s,s,(O,y)=>{var b;return!!((b=He[l+O])!=null&&b[u+y])})}if((C=r.cornersDotOptions)!=null&&C.gradient){const g=r.cornersDotOptions.gradient,l=this._createGradient({context:n,options:g,additionalRotation:T,x:$+s*2,y:U+s*2,size:f});g.colorStops.forEach(({offset:u,color:O})=>{l.addColorStop(u,O)}),n.fillStyle=n.strokeStyle=l}else(d=r.cornersDotOptions)!=null&&d.color&&(n.fillStyle=n.strokeStyle=r.cornersDotOptions.color);n.fill("evenodd")})}loadImage(){return new Promise((t,n)=>{const r=this._options,o=new Image;if(!r.image)return n("Image is not defined");typeof r.imageOptions.crossOrigin=="string"&&(o.crossOrigin=r.imageOptions.crossOrigin),this._image=o,o.onload=()=>{t()},o.src=r.image})}drawImage({width:t,height:n,count:r,dotSize:o}){const i=this.context;if(!i)throw"canvasContext is not defined";if(!this._image)throw"image is not defined";const s=this._options,c=Math.floor((s.width-r*o)/2),f=Math.floor((s.height-r*o)/2),N=c+s.imageOptions.margin+(r*o-t)/2,S=f+s.imageOptions.margin+(r*o-n)/2,a=t-s.imageOptions.margin*2,h=n-s.imageOptions.margin*2;i.drawImage(this._image,N,S,a<0?0:a,h<0?0:h)}_createGradient({context:t,options:n,additionalRotation:r,x:o,y:i,size:s}){let c;if(n.type===Ms.radial)c=t.createRadialGradient(o+s/2,i+s/2,0,o+s/2,i+s/2,s/2);else{const f=((n.rotation||0)+r)%(2*Math.PI),N=(f+2*Math.PI)%(2*Math.PI);let S=o+s/2,a=i+s/2,h=o+s/2,T=i+s/2;N>=0&&N<=.25*Math.PI||N>1.75*Math.PI&&N<=2*Math.PI?(S=S-s/2,a=a-s/2*Math.tan(f),h=h+s/2,T=T+s/2*Math.tan(f)):N>.25*Math.PI&&N<=.75*Math.PI?(a=a-s/2,S=S-s/2/Math.tan(f),T=T+s/2,h=h+s/2/Math.tan(f)):N>.75*Math.PI&&N<=1.25*Math.PI?(S=S+s/2,a=a+s/2*Math.tan(f),h=h-s/2,T=T-s/2*Math.tan(f)):N>1.25*Math.PI&&N<=1.75*Math.PI&&(a=a+s/2,S=S+s/2/Math.tan(f),T=T-s/2,h=h-s/2/Math.tan(f)),c=t.createLinearGradient(Math.round(S),Math.round(a),Math.round(h),Math.round(T))}return c}}const co={};for(let e=0;e<=40;e++)co[e]=e;const Ts={L:"L",M:"M",Q:"Q",H:"H"},uo={width:300,height:300,data:"",margin:0,qrOptions:{typeNumber:co[0],mode:void 0,errorCorrectionLevel:Ts.Q},imageOptions:{hideBackgroundDots:!0,imageSize:.4,crossOrigin:void 0,margin:0},dotsOptions:{type:"square",color:"#000"},backgroundOptions:{color:"#fff"}};function jt(e){const t={...e};if(!t.colorStops||!t.colorStops.length)throw"Field 'colorStops' is required in gradient";return t.rotation?t.rotation=Number(t.rotation):t.rotation=0,t.colorStops=t.colorStops.map(n=>({...n,offset:Number(n.offset)})),t}function fo(e){const t={...e};return t.width=Number(t.width),t.height=Number(t.height),t.margin=Number(t.margin),t.imageOptions={...t.imageOptions,hideBackgroundDots:!!t.imageOptions.hideBackgroundDots,imageSize:Number(t.imageOptions.imageSize),margin:Number(t.imageOptions.margin)},t.margin>Math.min(t.width,t.height)&&(t.margin=Math.min(t.width,t.height)),t.dotsOptions={...t.dotsOptions},t.dotsOptions.gradient&&(t.dotsOptions.gradient=jt(t.dotsOptions.gradient)),t.cornersSquareOptions&&(t.cornersSquareOptions={...t.cornersSquareOptions},t.cornersSquareOptions.gradient&&(t.cornersSquareOptions.gradient=jt(t.cornersSquareOptions.gradient))),t.cornersDotOptions&&(t.cornersDotOptions={...t.cornersDotOptions},t.cornersDotOptions.gradient&&(t.cornersDotOptions.gradient=jt(t.cornersDotOptions.gradient))),t.backgroundOptions&&(t.backgroundOptions={...t.backgroundOptions},t.backgroundOptions.gradient&&(t.backgroundOptions.gradient=jt(t.backgroundOptions.gradient))),t}function Ps(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var lo={exports:{}};(function(e,t){var n=function(){var r=function(v,D){var m=236,C=17,d=v,g=i[D],l=null,u=0,O=null,y=[],b={},R=function(_,w){u=d*4+17,l=function(p){for(var E=new Array(p),x=0;x<p;x+=1){E[x]=new Array(p);for(var I=0;I<p;I+=1)E[x][I]=null}return E}(u),M(0,0),M(u-7,0),M(0,u-7),ee(),K(),be(_,w),d>=7&&he(_),O==null&&(O=Ls(d,g,y)),De(O,w)},M=function(_,w){for(var p=-1;p<=7;p+=1)if(!(_+p<=-1||u<=_+p))for(var E=-1;E<=7;E+=1)w+E<=-1||u<=w+E||(0<=p&&p<=6&&(E==0||E==6)||0<=E&&E<=6&&(p==0||p==6)||2<=p&&p<=4&&2<=E&&E<=4?l[_+p][w+E]=!0:l[_+p][w+E]=!1)},P=function(){for(var _=0,w=0,p=0;p<8;p+=1){R(!0,p);var E=c.getLostPoint(b);(p==0||_>E)&&(_=E,w=p)}return w},K=function(){for(var _=8;_<u-8;_+=1)l[_][6]==null&&(l[_][6]=_%2==0);for(var w=8;w<u-8;w+=1)l[6][w]==null&&(l[6][w]=w%2==0)},ee=function(){for(var _=c.getPatternPosition(d),w=0;w<_.length;w+=1)for(var p=0;p<_.length;p+=1){var E=_[w],x=_[p];if(l[E][x]==null)for(var I=-2;I<=2;I+=1)for(var B=-2;B<=2;B+=1)I==-2||I==2||B==-2||B==2||I==0&&B==0?l[E+I][x+B]=!0:l[E+I][x+B]=!1}},he=function(_){for(var w=c.getBCHTypeNumber(d),p=0;p<18;p+=1){var E=!_&&(w>>p&1)==1;l[Math.floor(p/3)][p%3+u-8-3]=E}for(var p=0;p<18;p+=1){var E=!_&&(w>>p&1)==1;l[p%3+u-8-3][Math.floor(p/3)]=E}},be=function(_,w){for(var p=g<<3|w,E=c.getBCHTypeInfo(p),x=0;x<15;x+=1){var I=!_&&(E>>x&1)==1;x<6?l[x][8]=I:x<8?l[x+1][8]=I:l[u-15+x][8]=I}for(var x=0;x<15;x+=1){var I=!_&&(E>>x&1)==1;x<8?l[8][u-x-1]=I:x<9?l[8][15-x-1+1]=I:l[8][15-x-1]=I}l[u-8][8]=!_},De=function(_,w){for(var p=-1,E=u-1,x=7,I=0,B=c.getMaskFunction(w),A=u-1;A>0;A-=2)for(A==6&&(A-=1);;){for(var W=0;W<2;W+=1)if(l[E][A-W]==null){var Z=!1;I<_.length&&(Z=(_[I]>>>x&1)==1);var V=B(E,A-W);V&&(Z=!Z),l[E][A-W]=Z,x-=1,x==-1&&(I+=1,x=7)}if(E+=p,E<0||u<=E){E-=p,p=-p;break}}},tt=function(_,w){for(var p=0,E=0,x=0,I=new Array(w.length),B=new Array(w.length),A=0;A<w.length;A+=1){var W=w[A].dataCount,Z=w[A].totalCount-W;E=Math.max(E,W),x=Math.max(x,Z),I[A]=new Array(W);for(var V=0;V<I[A].length;V+=1)I[A][V]=255&_.getBuffer()[V+p];p+=W;var ve=c.getErrorCorrectPolynomial(Z),_e=N(I[A],ve.getLength()-1),po=_e.mod(ve);B[A]=new Array(ve.getLength()-1);for(var V=0;V<B[A].length;V+=1){var ho=V+po.getLength()-B[A].length;B[A][V]=ho>=0?po.getAt(ho):0}}for(var go=0,V=0;V<w.length;V+=1)go+=w[V].totalCount;for(var Ln=new Array(go),Ut=0,V=0;V<E;V+=1)for(var A=0;A<w.length;A+=1)V<I[A].length&&(Ln[Ut]=I[A][V],Ut+=1);for(var V=0;V<x;V+=1)for(var A=0;A<w.length;A+=1)V<B[A].length&&(Ln[Ut]=B[A][V],Ut+=1);return Ln},Ls=function(_,w,p){for(var E=S.getRSBlocks(_,w),x=a(),I=0;I<p.length;I+=1){var B=p[I];x.put(B.getMode(),4),x.put(B.getLength(),c.getLengthInBits(B.getMode(),_)),B.write(x)}for(var A=0,I=0;I<E.length;I+=1)A+=E[I].dataCount;if(x.getLengthInBits()>A*8)throw"code length overflow. ("+x.getLengthInBits()+">"+A*8+")";for(x.getLengthInBits()+4<=A*8&&x.put(0,4);x.getLengthInBits()%8!=0;)x.putBit(!1);for(;!(x.getLengthInBits()>=A*8||(x.put(m,8),x.getLengthInBits()>=A*8));)x.put(C,8);return tt(x,E)};b.addData=function(_,w){w=w||"Byte";var p=null;switch(w){case"Numeric":p=h(_);break;case"Alphanumeric":p=T(_);break;case"Byte":p=$(_);break;case"Kanji":p=U(_);break;default:throw"mode:"+w}y.push(p),O=null},b.isDark=function(_,w){if(_<0||u<=_||w<0||u<=w)throw _+","+w;return l[_][w]},b.getModuleCount=function(){return u},b.make=function(){if(d<1){for(var _=1;_<40;_++){for(var w=S.getRSBlocks(_,g),p=a(),E=0;E<y.length;E++){var x=y[E];p.put(x.getMode(),4),p.put(x.getLength(),c.getLengthInBits(x.getMode(),_)),x.write(p)}for(var I=0,E=0;E<w.length;E++)I+=w[E].dataCount;if(p.getLengthInBits()<=I*8)break}d=_}R(!1,P())},b.createTableTag=function(_,w){_=_||2,w=typeof w>"u"?_*4:w;var p="";p+='<table style="',p+=" border-width: 0px; border-style: none;",p+=" border-collapse: collapse;",p+=" padding: 0px; margin: "+w+"px;",p+='">',p+="<tbody>";for(var E=0;E<b.getModuleCount();E+=1){p+="<tr>";for(var x=0;x<b.getModuleCount();x+=1)p+='<td style="',p+=" border-width: 0px; border-style: none;",p+=" border-collapse: collapse;",p+=" padding: 0px; margin: 0px;",p+=" width: "+_+"px;",p+=" height: "+_+"px;",p+=" background-color: ",p+=b.isDark(E,x)?"#000000":"#ffffff",p+=";",p+='"/>';p+="</tr>"}return p+="</tbody>",p+="</table>",p},b.createSvgTag=function(_,w,p,E){var x={};typeof arguments[0]=="object"&&(x=arguments[0],_=x.cellSize,w=x.margin,p=x.alt,E=x.title),_=_||2,w=typeof w>"u"?_*4:w,p=typeof p=="string"?{text:p}:p||{},p.text=p.text||null,p.id=p.text?p.id||"qrcode-description":null,E=typeof E=="string"?{text:E}:E||{},E.text=E.text||null,E.id=E.text?E.id||"qrcode-title":null;var I=b.getModuleCount()*_+w*2,B,A,W,Z,V="",ve;for(ve="l"+_+",0 0,"+_+" -"+_+",0 0,-"+_+"z ",V+='<svg version="1.1" xmlns="http://www.w3.org/2000/svg"',V+=x.scalable?"":' width="'+I+'px" height="'+I+'px"',V+=' viewBox="0 0 '+I+" "+I+'" ',V+=' preserveAspectRatio="xMinYMin meet"',V+=E.text||p.text?' role="img" aria-labelledby="'+nt([E.id,p.id].join(" ").trim())+'"':"",V+=">",V+=E.text?'<title id="'+nt(E.id)+'">'+nt(E.text)+"</title>":"",V+=p.text?'<description id="'+nt(p.id)+'">'+nt(p.text)+"</description>":"",V+='<rect width="100%" height="100%" fill="white" cx="0" cy="0"/>',V+='<path d="',W=0;W<b.getModuleCount();W+=1)for(Z=W*_+w,B=0;B<b.getModuleCount();B+=1)b.isDark(W,B)&&(A=B*_+w,V+="M"+A+","+Z+ve);return V+='" stroke="transparent" fill="black"/>',V+="</svg>",V},b.createDataURL=function(_,w){_=_||2,w=typeof w>"u"?_*4:w;var p=b.getModuleCount()*_+w*2,E=w,x=p-w;return L(p,p,function(I,B){if(E<=I&&I<x&&E<=B&&B<x){var A=Math.floor((I-E)/_),W=Math.floor((B-E)/_);return b.isDark(W,A)?0:1}else return 1})},b.createImgTag=function(_,w,p){_=_||2,w=typeof w>"u"?_*4:w;var E=b.getModuleCount()*_+w*2,x="";return x+="<img",x+=' src="',x+=b.createDataURL(_,w),x+='"',x+=' width="',x+=E,x+='"',x+=' height="',x+=E,x+='"',p&&(x+=' alt="',x+=nt(p),x+='"'),x+="/>",x};var nt=function(_){for(var w="",p=0;p<_.length;p+=1){var E=_.charAt(p);switch(E){case"<":w+="&lt;";break;case">":w+="&gt;";break;case"&":w+="&amp;";break;case'"':w+="&quot;";break;default:w+=E;break}}return w},Fs=function(_){var w=1;_=typeof _>"u"?w*2:_;var p=b.getModuleCount()*w+_*2,E=_,x=p-_,I,B,A,W,Z,V={"██":"█","█ ":"▀"," █":"▄","  ":" "},ve={"██":"▀","█ ":"▀"," █":" ","  ":" "},_e="";for(I=0;I<p;I+=2){for(A=Math.floor((I-E)/w),W=Math.floor((I+1-E)/w),B=0;B<p;B+=1)Z="█",E<=B&&B<x&&E<=I&&I<x&&b.isDark(A,Math.floor((B-E)/w))&&(Z=" "),E<=B&&B<x&&E<=I+1&&I+1<x&&b.isDark(W,Math.floor((B-E)/w))?Z+=" ":Z+="█",_e+=_<1&&I+1>=x?ve[Z]:V[Z];_e+=`
`}return p%2&&_>0?_e.substring(0,_e.length-p-1)+Array(p+1).join("▀"):_e.substring(0,_e.length-1)};return b.createASCII=function(_,w){if(_=_||1,_<2)return Fs(w);_-=1,w=typeof w>"u"?_*2:w;var p=b.getModuleCount()*_+w*2,E=w,x=p-w,I,B,A,W,Z=Array(_+1).join("██"),V=Array(_+1).join("  "),ve="",_e="";for(I=0;I<p;I+=1){for(A=Math.floor((I-E)/_),_e="",B=0;B<p;B+=1)W=1,E<=B&&B<x&&E<=I&&I<x&&b.isDark(A,Math.floor((B-E)/_))&&(W=0),_e+=W?Z:V;for(A=0;A<_;A+=1)ve+=_e+`
`}return ve.substring(0,ve.length-1)},b.renderTo2dContext=function(_,w){w=w||2;for(var p=b.getModuleCount(),E=0;E<p;E++)for(var x=0;x<p;x++)_.fillStyle=b.isDark(E,x)?"black":"white",_.fillRect(E*w,x*w,w,w)},b};r.stringToBytesFuncs={default:function(v){for(var D=[],m=0;m<v.length;m+=1){var C=v.charCodeAt(m);D.push(C&255)}return D}},r.stringToBytes=r.stringToBytesFuncs.default,r.createStringToBytes=function(v,D){var m=function(){for(var d=J(v),g=function(){var K=d.read();if(K==-1)throw"eof";return K},l=0,u={};;){var O=d.read();if(O==-1)break;var y=g(),b=g(),R=g(),M=String.fromCharCode(O<<8|y),P=b<<8|R;u[M]=P,l+=1}if(l!=D)throw l+" != "+D;return u}(),C="?".charCodeAt(0);return function(d){for(var g=[],l=0;l<d.length;l+=1){var u=d.charCodeAt(l);if(u<128)g.push(u);else{var O=m[d.charAt(l)];typeof O=="number"?(O&255)==O?g.push(O):(g.push(O>>>8),g.push(O&255)):g.push(C)}}return g}};var o={MODE_NUMBER:1,MODE_ALPHA_NUM:2,MODE_8BIT_BYTE:4,MODE_KANJI:8},i={L:1,M:0,Q:3,H:2},s={PATTERN000:0,PATTERN001:1,PATTERN010:2,PATTERN011:3,PATTERN100:4,PATTERN101:5,PATTERN110:6,PATTERN111:7},c=function(){var v=[[],[6,18],[6,22],[6,26],[6,30],[6,34],[6,22,38],[6,24,42],[6,26,46],[6,28,50],[6,30,54],[6,32,58],[6,34,62],[6,26,46,66],[6,26,48,70],[6,26,50,74],[6,30,54,78],[6,30,56,82],[6,30,58,86],[6,34,62,90],[6,28,50,72,94],[6,26,50,74,98],[6,30,54,78,102],[6,28,54,80,106],[6,32,58,84,110],[6,30,58,86,114],[6,34,62,90,118],[6,26,50,74,98,122],[6,30,54,78,102,126],[6,26,52,78,104,130],[6,30,56,82,108,134],[6,34,60,86,112,138],[6,30,58,86,114,142],[6,34,62,90,118,146],[6,30,54,78,102,126,150],[6,24,50,76,102,128,154],[6,28,54,80,106,132,158],[6,32,58,84,110,136,162],[6,26,54,82,110,138,166],[6,30,58,86,114,142,170]],D=1335,m=7973,C=21522,d={},g=function(l){for(var u=0;l!=0;)u+=1,l>>>=1;return u};return d.getBCHTypeInfo=function(l){for(var u=l<<10;g(u)-g(D)>=0;)u^=D<<g(u)-g(D);return(l<<10|u)^C},d.getBCHTypeNumber=function(l){for(var u=l<<12;g(u)-g(m)>=0;)u^=m<<g(u)-g(m);return l<<12|u},d.getPatternPosition=function(l){return v[l-1]},d.getMaskFunction=function(l){switch(l){case s.PATTERN000:return function(u,O){return(u+O)%2==0};case s.PATTERN001:return function(u,O){return u%2==0};case s.PATTERN010:return function(u,O){return O%3==0};case s.PATTERN011:return function(u,O){return(u+O)%3==0};case s.PATTERN100:return function(u,O){return(Math.floor(u/2)+Math.floor(O/3))%2==0};case s.PATTERN101:return function(u,O){return u*O%2+u*O%3==0};case s.PATTERN110:return function(u,O){return(u*O%2+u*O%3)%2==0};case s.PATTERN111:return function(u,O){return(u*O%3+(u+O)%2)%2==0};default:throw"bad maskPattern:"+l}},d.getErrorCorrectPolynomial=function(l){for(var u=N([1],0),O=0;O<l;O+=1)u=u.multiply(N([1,f.gexp(O)],0));return u},d.getLengthInBits=function(l,u){if(1<=u&&u<10)switch(l){case o.MODE_NUMBER:return 10;case o.MODE_ALPHA_NUM:return 9;case o.MODE_8BIT_BYTE:return 8;case o.MODE_KANJI:return 8;default:throw"mode:"+l}else if(u<27)switch(l){case o.MODE_NUMBER:return 12;case o.MODE_ALPHA_NUM:return 11;case o.MODE_8BIT_BYTE:return 16;case o.MODE_KANJI:return 10;default:throw"mode:"+l}else if(u<41)switch(l){case o.MODE_NUMBER:return 14;case o.MODE_ALPHA_NUM:return 13;case o.MODE_8BIT_BYTE:return 16;case o.MODE_KANJI:return 12;default:throw"mode:"+l}else throw"type:"+u},d.getLostPoint=function(l){for(var u=l.getModuleCount(),O=0,y=0;y<u;y+=1)for(var b=0;b<u;b+=1){for(var R=0,M=l.isDark(y,b),P=-1;P<=1;P+=1)if(!(y+P<0||u<=y+P))for(var K=-1;K<=1;K+=1)b+K<0||u<=b+K||P==0&&K==0||M==l.isDark(y+P,b+K)&&(R+=1);R>5&&(O+=3+R-5)}for(var y=0;y<u-1;y+=1)for(var b=0;b<u-1;b+=1){var ee=0;l.isDark(y,b)&&(ee+=1),l.isDark(y+1,b)&&(ee+=1),l.isDark(y,b+1)&&(ee+=1),l.isDark(y+1,b+1)&&(ee+=1),(ee==0||ee==4)&&(O+=3)}for(var y=0;y<u;y+=1)for(var b=0;b<u-6;b+=1)l.isDark(y,b)&&!l.isDark(y,b+1)&&l.isDark(y,b+2)&&l.isDark(y,b+3)&&l.isDark(y,b+4)&&!l.isDark(y,b+5)&&l.isDark(y,b+6)&&(O+=40);for(var b=0;b<u;b+=1)for(var y=0;y<u-6;y+=1)l.isDark(y,b)&&!l.isDark(y+1,b)&&l.isDark(y+2,b)&&l.isDark(y+3,b)&&l.isDark(y+4,b)&&!l.isDark(y+5,b)&&l.isDark(y+6,b)&&(O+=40);for(var he=0,b=0;b<u;b+=1)for(var y=0;y<u;y+=1)l.isDark(y,b)&&(he+=1);var be=Math.abs(100*he/u/u-50)/5;return O+=be*10,O},d}(),f=function(){for(var v=new Array(256),D=new Array(256),m=0;m<8;m+=1)v[m]=1<<m;for(var m=8;m<256;m+=1)v[m]=v[m-4]^v[m-5]^v[m-6]^v[m-8];for(var m=0;m<255;m+=1)D[v[m]]=m;var C={};return C.glog=function(d){if(d<1)throw"glog("+d+")";return D[d]},C.gexp=function(d){for(;d<0;)d+=255;for(;d>=256;)d-=255;return v[d]},C}();function N(v,D){if(typeof v.length>"u")throw v.length+"/"+D;var m=function(){for(var d=0;d<v.length&&v[d]==0;)d+=1;for(var g=new Array(v.length-d+D),l=0;l<v.length-d;l+=1)g[l]=v[l+d];return g}(),C={};return C.getAt=function(d){return m[d]},C.getLength=function(){return m.length},C.multiply=function(d){for(var g=new Array(C.getLength()+d.getLength()-1),l=0;l<C.getLength();l+=1)for(var u=0;u<d.getLength();u+=1)g[l+u]^=f.gexp(f.glog(C.getAt(l))+f.glog(d.getAt(u)));return N(g,0)},C.mod=function(d){if(C.getLength()-d.getLength()<0)return C;for(var g=f.glog(C.getAt(0))-f.glog(d.getAt(0)),l=new Array(C.getLength()),u=0;u<C.getLength();u+=1)l[u]=C.getAt(u);for(var u=0;u<d.getLength();u+=1)l[u]^=f.gexp(f.glog(d.getAt(u))+g);return N(l,0).mod(d)},C}var S=function(){var v=[[1,26,19],[1,26,16],[1,26,13],[1,26,9],[1,44,34],[1,44,28],[1,44,22],[1,44,16],[1,70,55],[1,70,44],[2,35,17],[2,35,13],[1,100,80],[2,50,32],[2,50,24],[4,25,9],[1,134,108],[2,67,43],[2,33,15,2,34,16],[2,33,11,2,34,12],[2,86,68],[4,43,27],[4,43,19],[4,43,15],[2,98,78],[4,49,31],[2,32,14,4,33,15],[4,39,13,1,40,14],[2,121,97],[2,60,38,2,61,39],[4,40,18,2,41,19],[4,40,14,2,41,15],[2,146,116],[3,58,36,2,59,37],[4,36,16,4,37,17],[4,36,12,4,37,13],[2,86,68,2,87,69],[4,69,43,1,70,44],[6,43,19,2,44,20],[6,43,15,2,44,16],[4,101,81],[1,80,50,4,81,51],[4,50,22,4,51,23],[3,36,12,8,37,13],[2,116,92,2,117,93],[6,58,36,2,59,37],[4,46,20,6,47,21],[7,42,14,4,43,15],[4,133,107],[8,59,37,1,60,38],[8,44,20,4,45,21],[12,33,11,4,34,12],[3,145,115,1,146,116],[4,64,40,5,65,41],[11,36,16,5,37,17],[11,36,12,5,37,13],[5,109,87,1,110,88],[5,65,41,5,66,42],[5,54,24,7,55,25],[11,36,12,7,37,13],[5,122,98,1,123,99],[7,73,45,3,74,46],[15,43,19,2,44,20],[3,45,15,13,46,16],[1,135,107,5,136,108],[10,74,46,1,75,47],[1,50,22,15,51,23],[2,42,14,17,43,15],[5,150,120,1,151,121],[9,69,43,4,70,44],[17,50,22,1,51,23],[2,42,14,19,43,15],[3,141,113,4,142,114],[3,70,44,11,71,45],[17,47,21,4,48,22],[9,39,13,16,40,14],[3,135,107,5,136,108],[3,67,41,13,68,42],[15,54,24,5,55,25],[15,43,15,10,44,16],[4,144,116,4,145,117],[17,68,42],[17,50,22,6,51,23],[19,46,16,6,47,17],[2,139,111,7,140,112],[17,74,46],[7,54,24,16,55,25],[34,37,13],[4,151,121,5,152,122],[4,75,47,14,76,48],[11,54,24,14,55,25],[16,45,15,14,46,16],[6,147,117,4,148,118],[6,73,45,14,74,46],[11,54,24,16,55,25],[30,46,16,2,47,17],[8,132,106,4,133,107],[8,75,47,13,76,48],[7,54,24,22,55,25],[22,45,15,13,46,16],[10,142,114,2,143,115],[19,74,46,4,75,47],[28,50,22,6,51,23],[33,46,16,4,47,17],[8,152,122,4,153,123],[22,73,45,3,74,46],[8,53,23,26,54,24],[12,45,15,28,46,16],[3,147,117,10,148,118],[3,73,45,23,74,46],[4,54,24,31,55,25],[11,45,15,31,46,16],[7,146,116,7,147,117],[21,73,45,7,74,46],[1,53,23,37,54,24],[19,45,15,26,46,16],[5,145,115,10,146,116],[19,75,47,10,76,48],[15,54,24,25,55,25],[23,45,15,25,46,16],[13,145,115,3,146,116],[2,74,46,29,75,47],[42,54,24,1,55,25],[23,45,15,28,46,16],[17,145,115],[10,74,46,23,75,47],[10,54,24,35,55,25],[19,45,15,35,46,16],[17,145,115,1,146,116],[14,74,46,21,75,47],[29,54,24,19,55,25],[11,45,15,46,46,16],[13,145,115,6,146,116],[14,74,46,23,75,47],[44,54,24,7,55,25],[59,46,16,1,47,17],[12,151,121,7,152,122],[12,75,47,26,76,48],[39,54,24,14,55,25],[22,45,15,41,46,16],[6,151,121,14,152,122],[6,75,47,34,76,48],[46,54,24,10,55,25],[2,45,15,64,46,16],[17,152,122,4,153,123],[29,74,46,14,75,47],[49,54,24,10,55,25],[24,45,15,46,46,16],[4,152,122,18,153,123],[13,74,46,32,75,47],[48,54,24,14,55,25],[42,45,15,32,46,16],[20,147,117,4,148,118],[40,75,47,7,76,48],[43,54,24,22,55,25],[10,45,15,67,46,16],[19,148,118,6,149,119],[18,75,47,31,76,48],[34,54,24,34,55,25],[20,45,15,61,46,16]],D=function(d,g){var l={};return l.totalCount=d,l.dataCount=g,l},m={},C=function(d,g){switch(g){case i.L:return v[(d-1)*4+0];case i.M:return v[(d-1)*4+1];case i.Q:return v[(d-1)*4+2];case i.H:return v[(d-1)*4+3];default:return}};return m.getRSBlocks=function(d,g){var l=C(d,g);if(typeof l>"u")throw"bad rs block @ typeNumber:"+d+"/errorCorrectionLevel:"+g;for(var u=l.length/3,O=[],y=0;y<u;y+=1)for(var b=l[y*3+0],R=l[y*3+1],M=l[y*3+2],P=0;P<b;P+=1)O.push(D(R,M));return O},m}(),a=function(){var v=[],D=0,m={};return m.getBuffer=function(){return v},m.getAt=function(C){var d=Math.floor(C/8);return(v[d]>>>7-C%8&1)==1},m.put=function(C,d){for(var g=0;g<d;g+=1)m.putBit((C>>>d-g-1&1)==1)},m.getLengthInBits=function(){return D},m.putBit=function(C){var d=Math.floor(D/8);v.length<=d&&v.push(0),C&&(v[d]|=128>>>D%8),D+=1},m},h=function(v){var D=o.MODE_NUMBER,m=v,C={};C.getMode=function(){return D},C.getLength=function(l){return m.length},C.write=function(l){for(var u=m,O=0;O+2<u.length;)l.put(d(u.substring(O,O+3)),10),O+=3;O<u.length&&(u.length-O==1?l.put(d(u.substring(O,O+1)),4):u.length-O==2&&l.put(d(u.substring(O,O+2)),7))};var d=function(l){for(var u=0,O=0;O<l.length;O+=1)u=u*10+g(l.charAt(O));return u},g=function(l){if("0"<=l&&l<="9")return l.charCodeAt(0)-"0".charCodeAt(0);throw"illegal char :"+l};return C},T=function(v){var D=o.MODE_ALPHA_NUM,m=v,C={};C.getMode=function(){return D},C.getLength=function(g){return m.length},C.write=function(g){for(var l=m,u=0;u+1<l.length;)g.put(d(l.charAt(u))*45+d(l.charAt(u+1)),11),u+=2;u<l.length&&g.put(d(l.charAt(u)),6)};var d=function(g){if("0"<=g&&g<="9")return g.charCodeAt(0)-"0".charCodeAt(0);if("A"<=g&&g<="Z")return g.charCodeAt(0)-"A".charCodeAt(0)+10;switch(g){case" ":return 36;case"$":return 37;case"%":return 38;case"*":return 39;case"+":return 40;case"-":return 41;case".":return 42;case"/":return 43;case":":return 44;default:throw"illegal char :"+g}};return C},$=function(v){var D=o.MODE_8BIT_BYTE,m=r.stringToBytes(v),C={};return C.getMode=function(){return D},C.getLength=function(d){return m.length},C.write=function(d){for(var g=0;g<m.length;g+=1)d.put(m[g],8)},C},U=function(v){var D=o.MODE_KANJI,m=r.stringToBytesFuncs.SJIS;if(!m)throw"sjis not supported.";(function(g,l){var u=m(g);if(u.length!=2||(u[0]<<8|u[1])!=l)throw"sjis not supported."})("友",38726);var C=m(v),d={};return d.getMode=function(){return D},d.getLength=function(g){return~~(C.length/2)},d.write=function(g){for(var l=C,u=0;u+1<l.length;){var O=(255&l[u])<<8|255&l[u+1];if(33088<=O&&O<=40956)O-=33088;else if(57408<=O&&O<=60351)O-=49472;else throw"illegal char at "+(u+1)+"/"+O;O=(O>>>8&255)*192+(O&255),g.put(O,13),u+=2}if(u<l.length)throw"illegal char at "+(u+1)},d},te=function(){var v=[],D={};return D.writeByte=function(m){v.push(m&255)},D.writeShort=function(m){D.writeByte(m),D.writeByte(m>>>8)},D.writeBytes=function(m,C,d){C=C||0,d=d||m.length;for(var g=0;g<d;g+=1)D.writeByte(m[g+C])},D.writeString=function(m){for(var C=0;C<m.length;C+=1)D.writeByte(m.charCodeAt(C))},D.toByteArray=function(){return v},D.toString=function(){var m="";m+="[";for(var C=0;C<v.length;C+=1)C>0&&(m+=","),m+=v[C];return m+="]",m},D},X=function(){var v=0,D=0,m=0,C="",d={},g=function(u){C+=String.fromCharCode(l(u&63))},l=function(u){if(!(u<0)){if(u<26)return 65+u;if(u<52)return 97+(u-26);if(u<62)return 48+(u-52);if(u==62)return 43;if(u==63)return 47}throw"n:"+u};return d.writeByte=function(u){for(v=v<<8|u&255,D+=8,m+=1;D>=6;)g(v>>>D-6),D-=6},d.flush=function(){if(D>0&&(g(v<<6-D),v=0,D=0),m%3!=0)for(var u=3-m%3,O=0;O<u;O+=1)C+="="},d.toString=function(){return C},d},J=function(v){var D=v,m=0,C=0,d=0,g={};g.read=function(){for(;d<8;){if(m>=D.length){if(d==0)return-1;throw"unexpected end of file./"+d}var u=D.charAt(m);if(m+=1,u=="=")return d=0,-1;if(u.match(/^\s$/))continue;C=C<<6|l(u.charCodeAt(0)),d+=6}var O=C>>>d-8&255;return d-=8,O};var l=function(u){if(65<=u&&u<=90)return u-65;if(97<=u&&u<=122)return u-97+26;if(48<=u&&u<=57)return u-48+52;if(u==43)return 62;if(u==47)return 63;throw"c:"+u};return g},ne=function(v,D){var m=v,C=D,d=new Array(v*D),g={};g.setPixel=function(y,b,R){d[b*m+y]=R},g.write=function(y){y.writeString("GIF87a"),y.writeShort(m),y.writeShort(C),y.writeByte(128),y.writeByte(0),y.writeByte(0),y.writeByte(0),y.writeByte(0),y.writeByte(0),y.writeByte(255),y.writeByte(255),y.writeByte(255),y.writeString(","),y.writeShort(0),y.writeShort(0),y.writeShort(m),y.writeShort(C),y.writeByte(0);var b=2,R=u(b);y.writeByte(b);for(var M=0;R.length-M>255;)y.writeByte(255),y.writeBytes(R,M,255),M+=255;y.writeByte(R.length-M),y.writeBytes(R,M,R.length-M),y.writeByte(0),y.writeString(";")};var l=function(y){var b=y,R=0,M=0,P={};return P.write=function(K,ee){if(K>>>ee)throw"length over";for(;R+ee>=8;)b.writeByte(255&(K<<R|M)),ee-=8-R,K>>>=8-R,M=0,R=0;M=K<<R|M,R=R+ee},P.flush=function(){R>0&&b.writeByte(M)},P},u=function(y){for(var b=1<<y,R=(1<<y)+1,M=y+1,P=O(),K=0;K<b;K+=1)P.add(String.fromCharCode(K));P.add(String.fromCharCode(b)),P.add(String.fromCharCode(R));var ee=te(),he=l(ee);he.write(b,M);var be=0,De=String.fromCharCode(d[be]);for(be+=1;be<d.length;){var tt=String.fromCharCode(d[be]);be+=1,P.contains(De+tt)?De=De+tt:(he.write(P.indexOf(De),M),P.size()<4095&&(P.size()==1<<M&&(M+=1),P.add(De+tt)),De=tt)}return he.write(P.indexOf(De),M),he.write(R,M),he.flush(),ee.toByteArray()},O=function(){var y={},b=0,R={};return R.add=function(M){if(R.contains(M))throw"dup key:"+M;y[M]=b,b+=1},R.size=function(){return b},R.indexOf=function(M){return y[M]},R.contains=function(M){return typeof y[M]<"u"},R};return g},L=function(v,D,m){for(var C=ne(v,D),d=0;d<D;d+=1)for(var g=0;g<v;g+=1)C.setPixel(g,d,m(g,d));var l=te();C.write(l);for(var u=X(),O=l.toByteArray(),y=0;y<O.length;y+=1)u.writeByte(O[y]);return u.flush(),"data:image/gif;base64,"+u};return r}();(function(){n.stringToBytesFuncs["UTF-8"]=function(r){function o(i){for(var s=[],c=0;c<i.length;c++){var f=i.charCodeAt(c);f<128?s.push(f):f<2048?s.push(192|f>>6,128|f&63):f<55296||f>=57344?s.push(224|f>>12,128|f>>6&63,128|f&63):(c++,f=65536+((f&1023)<<10|i.charCodeAt(c)&1023),s.push(240|f>>18,128|f>>12&63,128|f>>6&63,128|f&63))}return s}return o(r)}})(),function(r){e.exports=r()}(function(){return n})})(lo);var Rs=lo.exports;const As=Ps(Rs);class $n{constructor(t){oe(this,"_options");oe(this,"_container");oe(this,"_canvas");oe(this,"_qr");oe(this,"_drawingPromise");this._options=t?fo(Ht(uo,t)):uo,this.update()}static _clearContainer(t){t&&(t.innerHTML="")}update(t){$n._clearContainer(this._container),this._options=t?fo(Ht(this._options,t)):this._options,this._options.data&&(this._qr=As(this._options.qrOptions.typeNumber,this._options.qrOptions.errorCorrectionLevel),this._qr.addData(this._options.data,this._options.qrOptions.mode||Os(this._options.data)),this._qr.make(),this._canvas=new Is(this._options),this._drawingPromise=this._canvas.drawQR(this._qr),this.append(this._container))}append(t){if(t){if(typeof t.appendChild!="function")throw"Container should be a single DOM node";this._canvas&&t.appendChild(this._canvas.getCanvas()),this._container=t}}async getImageUrl(t){return this._drawingPromise&&await this._drawingPromise===void 0&&this._canvas?this._canvas.getCanvas().toDataURL(`image/${t}`):""}download(t){this._drawingPromise&&this._drawingPromise.then(()=>{if(!this._canvas)return;const n=t,r=n.extension||"png",o=n.name||"qr",i=this._canvas.getCanvas().toDataURL(`image/${r}`);Ns(i,`${o}.${r}`)})}}const Bs={key:0},Vs=["src"],ks={key:1},$s=Rr({__name:"QRCodeVue3Async",props:{value:{default:""},width:{default:300},height:{default:300},margin:{default:0},imgclass:{default:""},myclass:{default:""},downloadButton:{default:""},buttonName:{default:"Download"},qrOptions:{default:{typeNumber:0,mode:"Byte",errorCorrectionLevel:"Q"}},imageOptions:{default:{hideBackgroundDots:!0,imageSize:.4,margin:0}},dotsOptions:{default:{type:"dots",color:"#6a1a4c",gradient:{type:"linear",rotation:0,colorStops:[{offset:0,color:"#6a1a4c"},{offset:1,color:"#6a1a4c"}]}}},backgroundOptions:{default:{color:"#ffffff"}},cornersSquareOptions:{default:{type:"dot",color:"#000000"}},cornersDotOptions:{default:{type:void 0,color:"#000000"}},fileExt:{default:"png"},image:{default:""},download:{type:Boolean,default:!1},downloadOptions:{default:{name:"vqr",extension:"png"}},isHide:{type:Boolean}},emits:["imageLoaded"],setup(e,{emit:t}){const n=t,r=e,o=new $n({data:r.value,width:r.width,height:r.height,margin:r.margin,qrOptions:r.qrOptions,imageOptions:r.imageOptions,dotsOptions:r.dotsOptions,backgroundOptions:r.backgroundOptions,image:r.image,cornersSquareOptions:r.cornersSquareOptions,cornersDotOptions:r.cornersDotOptions}),i=Jo("");async function s(){i.value=await o.getImageUrl(r.fileExt),n("imageLoaded",i.value)}s();function c(){o.download(r.downloadOptions)}return(f,N)=>f.isHide?In("",!0):(Ze(),Sn("div",Bs,[i.value?(Ze(),Sn("div",{key:0,class:je(f.myclass)},[Lt("img",{src:i.value,class:je(f.imgclass),crossorigin:"anonymous"},null,10,Vs)],2)):In("",!0),i.value&&f.download?(Ze(),Sn("div",ks,[Lt("button",{onClick:c,class:je(f.downloadButton)},Gn(f.buttonName),3)])):In("",!0)]))}});return Rr({__name:"QRCodeVue3",props:{value:{default:""},width:{default:300},height:{default:300},margin:{default:0},imgclass:{default:""},myclass:{default:""},downloadButton:{default:""},buttonName:{default:"Download"},qrOptions:{default:{typeNumber:0,mode:"Byte",errorCorrectionLevel:"Q"}},imageOptions:{default:{hideBackgroundDots:!0,imageSize:.4,margin:0}},dotsOptions:{default:{type:"dots",color:"#6a1a4c",gradient:{type:"linear",rotation:0,colorStops:[{offset:0,color:"#6a1a4c"},{offset:1,color:"#6a1a4c"}]}}},backgroundOptions:{default:{color:"#ffffff"}},cornersSquareOptions:{default:{type:"dot",color:"#000000"}},cornersDotOptions:{default:{type:void 0,color:"#000000"}},fileExt:{default:"png"},image:{default:""},download:{type:Boolean,default:!1},downloadOptions:{default:{name:"vqr",extension:"png"}},isHide:{type:Boolean}},emits:["imageLoaded"],setup(e,{emit:t}){const n=t,r=e;function o(i){n("imageLoaded",i)}return(i,s)=>(Ze(),eo(es,null,{default:bi(()=>[Ae($s,{"background-options":r.backgroundOptions,"button-name":r.buttonName,"corners-dot-options":r.cornersDotOptions,"corners-square-options":r.cornersSquareOptions,"dots-options":r.dotsOptions,download:r.download,"download-button":r.downloadButton,"download-options":r.downloadOptions,"file-ext":r.fileExt,height:r.height,image:r.image,"image-options":r.imageOptions,imgclass:r.imgclass,margin:r.margin,value:r.value,myclass:r.myclass,"qr-options":r.qrOptions,width:r.width,"is-hide":r.isHide,onImageLoaded:o},null,8,["background-options","button-name","corners-dot-options","corners-square-options","dots-options","download","download-button","download-options","file-ext","height","image","image-options","imgclass","margin","value","myclass","qr-options","width","is-hide"])]),_:1}))}})});
