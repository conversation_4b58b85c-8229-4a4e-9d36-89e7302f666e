import type { PropType as __PropType } from 'vue';
export interface Props {
  value: string;
  width?: number;
  height?: number;
  margin?: number;
  imgclass?: string;
  myclass?: string;
  downloadButton?: string;
  buttonName?: string;
  qrOptions?: any;
  imageOptions?: any;
  dotsOptions?: any;
  backgroundOptions?: any;
  cornersSquareOptions?: any;
  cornersDotOptions?: any;
  fileExt?: string;
  image?: string;
  download?: boolean;
  downloadOptions?: any;
  isHide?: boolean;
}
declare const _sfc_main: import('vue').DefineComponent<
  import('vue').ExtractPropTypes<{
    value: {
      type: __PropType<string>;
      required: true;
      default: string;
    };
    width: {
      type: __PropType<number | undefined>;
      required: false;
      default: number;
    };
    height: {
      type: __PropType<number | undefined>;
      required: false;
      default: number;
    };
    margin: {
      type: __PropType<number | undefined>;
      required: false;
      default: number;
    };
    imgclass: {
      type: __PropType<string | undefined>;
      required: false;
      default: string;
    };
    myclass: {
      type: __PropType<string | undefined>;
      required: false;
      default: string;
    };
    downloadButton: {
      type: __PropType<string | undefined>;
      required: false;
      default: string;
    };
    buttonName: {
      type: __PropType<string | undefined>;
      required: false;
      default: string;
    };
    qrOptions: {
      type: __PropType<any>;
      required: false;
      default: {
        typeNumber: number;
        mode: string;
        errorCorrectionLevel: string;
      };
    };
    imageOptions: {
      type: __PropType<any>;
      required: false;
      default: {
        hideBackgroundDots: boolean;
        imageSize: number;
        margin: number;
      };
    };
    dotsOptions: {
      type: __PropType<any>;
      required: false;
      default: {
        type: string;
        color: string;
        gradient: {
          type: string;
          rotation: number;
          colorStops: {
            offset: number;
            color: string;
          }[];
        };
      };
    };
    backgroundOptions: {
      type: __PropType<any>;
      required: false;
      default: {
        color: string;
      };
    };
    cornersSquareOptions: {
      type: __PropType<any>;
      required: false;
      default: {
        type: string;
        color: string;
      };
    };
    cornersDotOptions: {
      type: __PropType<any>;
      required: false;
      default: {
        type: undefined;
        color: string;
      };
    };
    fileExt: {
      type: __PropType<string | undefined>;
      required: false;
      default: string;
    };
    image: {
      type: __PropType<string | undefined>;
      required: false;
      default: string;
    };
    download: {
      type: __PropType<boolean | undefined>;
      required: false;
      default: boolean;
    };
    downloadOptions: {
      type: __PropType<any>;
      required: false;
      default: {
        name: string;
        extension: string;
      };
    };
    isHide: {
      type: __PropType<boolean | undefined>;
      required: false;
    };
  }>,
  {},
  {},
  {},
  {},
  import('vue').ComponentOptionsMixin,
  import('vue').ComponentOptionsMixin,
  'imageLoaded'[],
  'imageLoaded',
  import('vue').PublicProps,
  Readonly<
    import('vue').ExtractPropTypes<{
      value: {
        type: __PropType<string>;
        required: true;
        default: string;
      };
      width: {
        type: __PropType<number | undefined>;
        required: false;
        default: number;
      };
      height: {
        type: __PropType<number | undefined>;
        required: false;
        default: number;
      };
      margin: {
        type: __PropType<number | undefined>;
        required: false;
        default: number;
      };
      imgclass: {
        type: __PropType<string | undefined>;
        required: false;
        default: string;
      };
      myclass: {
        type: __PropType<string | undefined>;
        required: false;
        default: string;
      };
      downloadButton: {
        type: __PropType<string | undefined>;
        required: false;
        default: string;
      };
      buttonName: {
        type: __PropType<string | undefined>;
        required: false;
        default: string;
      };
      qrOptions: {
        type: __PropType<any>;
        required: false;
        default: {
          typeNumber: number;
          mode: string;
          errorCorrectionLevel: string;
        };
      };
      imageOptions: {
        type: __PropType<any>;
        required: false;
        default: {
          hideBackgroundDots: boolean;
          imageSize: number;
          margin: number;
        };
      };
      dotsOptions: {
        type: __PropType<any>;
        required: false;
        default: {
          type: string;
          color: string;
          gradient: {
            type: string;
            rotation: number;
            colorStops: {
              offset: number;
              color: string;
            }[];
          };
        };
      };
      backgroundOptions: {
        type: __PropType<any>;
        required: false;
        default: {
          color: string;
        };
      };
      cornersSquareOptions: {
        type: __PropType<any>;
        required: false;
        default: {
          type: string;
          color: string;
        };
      };
      cornersDotOptions: {
        type: __PropType<any>;
        required: false;
        default: {
          type: undefined;
          color: string;
        };
      };
      fileExt: {
        type: __PropType<string | undefined>;
        required: false;
        default: string;
      };
      image: {
        type: __PropType<string | undefined>;
        required: false;
        default: string;
      };
      download: {
        type: __PropType<boolean | undefined>;
        required: false;
        default: boolean;
      };
      downloadOptions: {
        type: __PropType<any>;
        required: false;
        default: {
          name: string;
          extension: string;
        };
      };
      isHide: {
        type: __PropType<boolean | undefined>;
        required: false;
      };
    }>
  > &
    Readonly<{
      onImageLoaded?: ((...args: any[]) => any) | undefined;
    }>,
  {
    value: string;
    width: number | undefined;
    height: number | undefined;
    margin: number | undefined;
    imgclass: string | undefined;
    myclass: string | undefined;
    downloadButton: string | undefined;
    buttonName: string | undefined;
    qrOptions: any;
    imageOptions: any;
    dotsOptions: any;
    backgroundOptions: any;
    cornersSquareOptions: any;
    cornersDotOptions: any;
    fileExt: string | undefined;
    image: string | undefined;
    download: boolean | undefined;
    downloadOptions: any;
  },
  {},
  {},
  {},
  string,
  import('vue').ComponentProvideOptions,
  true,
  {},
  any
>;
export default _sfc_main;
