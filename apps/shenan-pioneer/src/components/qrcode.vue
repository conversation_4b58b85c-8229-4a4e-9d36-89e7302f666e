<script setup lang="ts">
import { Loading, utilsOss } from '@/utils';
// eslint-disable-next-line import/no-relative-packages
import VueQrcode from './qrcode-vue3';
import VueCanvasPoster from 'vue3-canvas-poster';

const props = defineProps<{
  value: string;
  stallName: string;
  areaName: string;
}>();

const imgs = {
  stall1: `${utilsOss.imgPath}/register/stall_icon1.svg`,
  qrcode_bg: `${utilsOss.imgPath}/register/qrcode_bg.png`,
};

const qrcodeRef = ref();
// function handleImg() {
//   handleHtml2Img('canvasId').then((res) => {
//     imgUrl.value = res;
//     count.value++;
//     if (count.value > 5) {
//       clearInterval(timer as ReturnType<typeof setInterval>);
//       Loading.hide();
//     }
//   });
// }

// onMounted(() => {
//   Loading.show('生成中');
//   setTimeout(
//     () => {
//       console.log(
//         '%c [qrcodeRef.value]-40',
//         'font-size:13px; background:#336699; color:#fff;',
//         qrcodeRef.value,
//       );
//       nextTick(() => {
//         if (props.loop) {
//           timer = setInterval(() => {
//             handleImg();
//           }, 300);
//         } else {
//           handleHtml2Img('canvasId').then((res) => {
//             imgUrl.value = res;
//           });
//         }
//       });
//     },
//     props.loop ? 1200 : 2500,
//   );
// });

const posterUrl = ref('');

const painting = ref<any>();

function handleImageLoaded(img: string) {
  uni.getImageInfo({
    src: imgs.qrcode_bg,
    success: (res) => {
      uni.getImageInfo({
        src: img,
        success: (res2) => {
          painting.value = {
            width: `${res.width}px`,
            height: `${res.height}px`,

            background: 'transparent',
            views: [
              {
                type: 'image',
                url: imgs.qrcode_bg,
                css: {
                  borderRadius: '5px',
                  width: `${res.width}px`,
                  height: `${res.height}px`,
                },
              },
              {
                type: 'text',
                text: props.stallName,
                css: {
                  fontSize: '28px',
                  fontWeight: 'bold',
                  textAlign: 'center',
                  width: `${res.width}px`,
                  top: `${res.height / 3.5}px`,
                },
              },
              {
                type: 'image',
                url: img,
                css: {
                  left: `${(res.width - res2.width) / 2 + 20}px`,
                  top: `${res.height / 2.5}px`,
                  width: `${res.width / 2 - 15}px`,
                  height: `${res.width / 2 - 15}px`,
                  borderRadius: '5px',
                },
              },
              {
                type: 'text',
                text: props.areaName,
                css: {
                  fontSize: '28px',
                  fontWeight: 'bold',
                  textAlign: 'center',
                  width: `${res.width}px`,
                  top: `${res.height / 1.3}px`,
                },
              },
            ],
          };
        },
      });
    },
  });
}

function success(url: string) {
  posterUrl.value = url;
}

function fail(err: any) {
  console.log('%c [err]-127', 'font-size:13px; background:#336699; color:#fff;', err);
}
</script>

<template>
  <img class="w-[80vw]" mode="widthFix" :src="posterUrl" alt="" />
  <vue-canvas-poster
    v-if="painting"
    :poster-url="posterUrl"
    :width-pixels="1000"
    :painting="painting"
    @success="success"
    @fail="fail" />
  <vue-qrcode
    ref="qrcodeRef"
    type="canvas"
    shape="square"
    :margin="0"
    :is-hide="false"
    imgclass="imgInfo"
    :value="value"
    :qr-options="{ typeNumber: '0', mode: 'Byte', errorCorrectionLevel: 'Q' }"
    :dots-options="{ type: 'classy', color: '#121212', roundSize: true, gradient: null }"
    :background-options="{ round: 0, color: '#ffffff' }"
    :dots-options-helper="{
      colorType: { single: true, gradient: false },
      gradient: {
        linear: true,
        radial: false,
        color1: '#6a1a4c',
        color2: '#6a1a4c',
        rotation: '0',
      },
    }"
    :corners-square-options="{ type: '', color: '#000000', gradient: null }"
    :corners-square-options-helper="{
      colorType: { single: true, gradient: false },
      gradient: {
        linear: true,
        radial: false,
        color1: '#000000',
        color2: '#000000',
        rotation: '0',
      },
    }"
    :corners-dot-options="{ type: '', color: '#000000' }"
    :corners-dot-options-helper="{
      colorType: { single: true, gradient: false },
      gradient: {
        linear: true,
        radial: false,
        color1: '#000000',
        color2: '#000000',
        rotation: '0',
      },
    }"
    :background-options-helper="{
      colorType: { single: true, gradient: false },
      gradient: {
        linear: true,
        radial: false,
        color1: '#ffffff',
        color2: '#ffffff',
        rotation: '0',
      },
    }"
    @image-loaded="handleImageLoaded" />
</template>

<style lang="scss"></style>
