<script setup lang="ts">
import useRoleStore, { RoleInfo } from '@/hooks/useRole';
import { Navigator, UserInfo, utilsOss } from '@/utils';

const emits = defineEmits(['init']);

const imgs = {
  tab1_1: `${utilsOss.imgPath}/tabs/icon_df1_1.png`,
  tab1_2: `${utilsOss.imgPath}/tabs/icon_df1_2.png`,
  tab2_1: `${utilsOss.imgPath}/tabs/icon_df2_1.png`,
  tab2_2: `${utilsOss.imgPath}/tabs/icon_df2_2.png`,
  tab3_1: `${utilsOss.imgPath}/tabs/icon_df3_1.png`,
  tab3_2: `${utilsOss.imgPath}/tabs/icon_df3_2.png`,
  tab4_1: `${utilsOss.imgPath}/tabs/icon_df4_1.png`,
  tab4_2: `${utilsOss.imgPath}/tabs/icon_df4_2.png`,
};

const pages = [
  '/pages/index/index',
  '/pages/message/index',
  '/pages/work/index',
  '/pages/user/index',
];

const page = getCurrentPages()[getCurrentPages().length - 1];

const index = pages.findIndex((p) => p.includes(page?.route || '')) || 0;

const current = ref(index);

const roleStore = useRoleStore();

const roleInfo = ref<RoleInfo>();

onBeforeMount(async () => {
  await roleStore.initOrganization(!roleStore.roleInfo.isInit);
  roleInfo.value = roleStore.roleInfo;
  roleStore.roleInfo.isInit = true;
  emits('init');
});

const onTabChange = () => {
  Navigator.replace(pages[current.value]);
};

onPullDownRefresh(async () => {
  await roleStore.initOrganization(true);
  roleInfo.value = roleStore.roleInfo;
  emits('init');
  uni.stopPullDownRefresh();
});
</script>

<template>
  <wd-tabbar
    v-if="roleInfo"
    v-model="current"
    fixed
    bordered
    safe-area-inset-bottom
    placeholder
    custom-class="tabs py-3"
    @change="onTabChange">
    <wd-tabbar-item :name="0" title="首页" icon="home">
      <template #icon="{ active }">
        <img :src="active ? imgs.tab1_2 : imgs.tab1_1" class="w-[18px] h-[20px] mb-1" />
      </template>
    </wd-tabbar-item>
    <wd-tabbar-item v-if="roleInfo.roleName !== '市民'" :name="1" title="消息" icon="cart">
      <template #icon="{ active }">
        <img :src="active ? imgs.tab2_2 : imgs.tab2_1" class="w-[18px] h-[20px] mb-1" />
      </template>
    </wd-tabbar-item>
    <wd-tabbar-item v-if="roleInfo.roleName !== '市民'" :name="2" title="工作台" icon="cart">
      <template #icon="{ active }">
        <img :src="active ? imgs.tab3_2 : imgs.tab3_1" class="w-[18px] h-[20px] mb-1" />
      </template>
    </wd-tabbar-item>
    <wd-tabbar-item :name="3" title="我的" icon="user">
      <template #icon="{ active }">
        <img :src="active ? imgs.tab4_2 : imgs.tab4_1" class="w-[18px] h-[20px] mb-1" />
      </template>
    </wd-tabbar-item>
  </wd-tabbar>
</template>

<style lang="scss" scoped></style>
