<script setup lang="ts">
import { ref, onMounted, computed, nextTick } from 'vue';
import { ReqArticlesProps, ReqCategoryProps, ServiceGetCategoryOrArticles } from '@rms/service';
import { exceptionHandler } from '@rms/utils';
import Register from '@rms/register';
import { utilsOss } from '@/utils';

interface Emits {
  (e: 'latest', data: ReqArticlesProps): void;
}
interface IProps {
  /** 过滤回调函数 */
  filter?: (data: ReqCategoryProps, type?: string) => boolean;
  /** type */
  type: string;
  /** activeTab 颜色 */
  activeColor?: string;
  /** 是否自动初始化 */
  autoInit?: boolean;
  /** 是否显示 tabs */
  isTabs?: boolean;
  /** handleTabs */
  handleTabs?: (tabs: TabProps[]) => TabProps[];
}

interface TabProps {
  id: string;
  displayName: string;
  list: ReqArticlesProps[] | null;
}

const props = withDefaults(defineProps<IProps>(), {
  filter: (data: ReqCategoryProps, type?: string) => data.description?.includes(type || ''),
  activeColor: '#387ef5',
  autoInit: true,
});

const imgs = {
  img_wx: `${utilsOss.imgPath}/home/<USER>
  icon_zs: `${utilsOss.imgPath}/home/<USER>
};

const current = ref(0);

const list = ref<ReqCategoryProps[]>([]);

const tabs = ref<TabProps[]>([]);

const emits = defineEmits<Emits>();

const ids = computed(() => tabs.value.map((tab) => tab.id));

function handleClickItem(index: number) {
  current.value = index;
}

function handleMoreArticles() {
  const register = new Register();

  console.log('%c []-54', 'font-size:13px; background:#336699; color:#fff;', register.RmsNavigator);
  register.RmsNavigator.push('/pages/pages-information/list', {
    id: ids.value[current.value],
  });
}

function toInfomationDetail(item: ReqArticlesProps) {
  const register = new Register();
  console.log(
    '%c []-54',
    'font-size:13px; background:#336699; color:#fff;',
    register.RmsNavigator.push,
  );
  register.RmsNavigator.push('/pages/pages-information/detail', {
    id: item.id,
  });
}

async function categoryInit(isInit = false) {
  const { filter, type } = props;

  if (!isInit && tabs.value.length) {
    return;
  }

  list.value = (await ServiceGetCategoryOrArticles('category', null)) as ReqCategoryProps[];

  tabs.value = [];

  const tree = list.value.filter((item) => filter(item, type));

  tree.forEach((v) => {
    const item = v.children && v.children.length ? v.children : [v];
    const tabArr = item.map((e) => ({
      id: e.id,
      displayName: e.displayName,
      list: null,
    }));

    tabs.value = [...tabs.value, ...tabArr];

    if (typeof props.handleTabs === 'function') {
      tabs.value = props.handleTabs(tabs.value);
    }
    console.log(
      '%c [tabs.value]-90',
      'font-size:13px; background:#336699; color:#fff;',
      tabs.value,
    );
  });
}

async function AaticlesInit(num = 0, isInit = false) {
  try {
    if (!ids.value[num]) return;

    if (!isInit && tabs.value[num]?.list?.length) {
      AaticlesInit(num + 1, isInit);
      return;
    }

    const id = ids.value[num];
    const index = tabs.value.findIndex((v) => v.id === id);
    const data = (await ServiceGetCategoryOrArticles('articles', { id })) as ReqArticlesProps[];

    if (!num && data && data.length) {
      emits('latest', data[0]);
    }

    tabs.value[index].list = data;
    if (!props.isTabs) return;
    await AaticlesInit(num + 1, isInit);
  } catch (error) {
    exceptionHandler(error);
  }
}

const showMore = computed(() => {
  const length = tabs.value[current.value]?.list?.length || 0;
  return length > 5;
});

function handleSwiperChange(e: { detail: { current: number } }) {
  current.value = e.detail.current;
}

const wdTabRef = ref();

const getLineWidth = computed(() => {
  if (!props.isTabs) return 0;

  const length = tabs.value?.[current.value]?.displayName?.length || 0;

  nextTick(() => {
    wdTabRef.value?.updateLineStyle();
  });

  return length * 15;
});

async function init(isInit = false) {
  await categoryInit(isInit);
  await AaticlesInit(0, isInit);
}

onMounted(() => {
  if (props.autoInit) {
    init();
  }
});

defineExpose({ init });
</script>

<template>
  <div v-if="tabs[current]?.list?.length">
    <div class="flex items-center gap-1 mb-3">
      <img :src="imgs.icon_zs" class="w-4 h-4" alt="" srcset="" />
      <div class="text-17 font-bold">{{ tabs[current]?.displayName }}</div>
    </div>
    <view
      v-for="item of tabs[current]?.list?.slice(0, 5)"
      :key="item.id"
      class="flex p-3 bg-transparent relative mb-3 min-h-[81px]"
      @click="toInfomationDetail(item)">
      <img class="absolute top-0 left-0 w-full h-full" :src="imgs.img_wx" alt="" srcset="" />
      <view class="flex flex-col gap-1 justify-between flex-1 relative z-10">
        <view class="pr-2 font-bold ellipsis-2">{{ item.title }}</view>
        <view class="text-placeholder text-13">{{ item.author }}</view>
        <view class="text-placeholder text-13">发布时间：{{ item.createdAt }}</view>
      </view>
    </view>
    <wd-button type="text" custom-class="w-full mb-3" @click="handleMoreArticles">
      查看更多
    </wd-button>
  </div>
</template>

<style lang="scss"></style>
