import { ApiFindRegionByLnglat, ApiGetRoleRegion } from '@/api';
import { Loading, UserInfo } from '@/utils';
import { ServiceRegion } from '@rms/service/src';
import { ReturnPromiseType } from '@rms/types/utils';
import useRole from './useRole';
import { ApiAMapGeocodeRegeo } from '@shencom/api';
import { ResRegionList } from '@rms/api/src/region';

export function useRoleRegion() {
  const roleRegions = ref<{
    regionPids: string[];
    regionIds: string[];
    regionCids: string[];
  }>({
    regionPids: [],
    regionIds: [],
    regionCids: [],
  });

  const roleLevel = ref<number>();

  const roleStore = useRole();

  const isAdmin2 = computed(() => {
    return ['Construction:party', 'Construction:unitleader', 'Installation:Manager'].includes(
      roleStore.roleInfo.roleId,
    );
  });

  onMounted(async () => {
    if (!UserInfo.isLogin()) return;

    await roleStore.initOrganization(true);

    if (isAdmin2.value) {
      roleLevel.value = 0;
      return;
    }

    const roleData = await ApiGetRoleRegion();
    if (!roleData) return;

    roleLevel.value = roleData.level;
    roleRegions.value.regionPids = roleData.regionIds.map((item) => item.regionPid);
    roleRegions.value.regionIds = roleData.regionIds.map((item) => item.regionId);
    roleRegions.value.regionCids = roleData.regionIds.map((item) => item.regionCid);
    roleRegions.value.regionPids = [...new Set(roleRegions.value.regionPids)];
    roleRegions.value.regionIds = [...new Set(roleRegions.value.regionIds)];
    roleRegions.value.regionCids = [...new Set(roleRegions.value.regionCids)];
  });

  type ResServiceRegion = ReturnPromiseType<typeof ServiceRegion>;

  function handleData(data: ResServiceRegion) {
    if (roleLevel.value === undefined) return [];

    if (roleLevel.value === 0) {
      return data;
    }

    let res: ResServiceRegion = [];

    if (roleRegions.value.regionPids.length) {
      res = data.filter(
        (item) => roleRegions.value.regionPids.includes(item.id) || item.id === '-1',
      );
    }

    if (roleLevel.value === 1) {
      return res;
    }

    if (roleRegions.value.regionIds.length) {
      res.forEach((item) => {
        item.children = item.children?.filter(
          (i) => roleRegions.value.regionIds.includes(i.id) || i.id === '-1',
        );
      });
    }

    if (roleLevel.value === 2) {
      return res;
    }

    if (roleRegions.value.regionCids.length) {
      res.forEach((item) => {
        item.children?.forEach((c) => {
          c.children = c.children?.filter(
            (i) => roleRegions.value.regionCids.includes(i.id) || i.id === '-1',
          );
        });
      });
    }

    return res;
  }

  function checkRegionIds(regionIds: string[]) {
    if (roleLevel.value === undefined) return false;

    if (roleLevel.value === 0) {
      return true;
    }

    if (roleLevel.value === 1) {
      return roleRegions.value.regionPids.some((item) => regionIds[0] === item);
    }

    if (roleLevel.value === 2) {
      return roleRegions.value.regionIds.some((item) => regionIds[1] === item);
    }

    return roleRegions.value.regionCids.some((item) => regionIds[2] === item);
  }

  async function getRegionByLatlng(lnglat: [number, number], needCid = false) {
    Loading.show('获取区域中');

    let regionRes: string[] = [];

    try {
      const data = await ApiAMapGeocodeRegeo({
        location: `${lnglat[0]},${lnglat[1]}`,
      });
      // 获取省市区街道
      const province = data.regeocode.addressComponent.province;
      const city = data.regeocode.addressComponent.city;
      const district = data.regeocode.addressComponent.district;
      const street = data.regeocode.addressComponent.township;
      console.log(
        '%c [data.regeocode.addressComponent]-20',
        'font-size:13px; background:#336699; color:#fff;',
        data.regeocode.addressComponent,
      );

      console.log(
        '%c [region]-21',
        'font-size:13px; background:#336699; color:#fff;',
        `${province}${city}${district}${street}`,
      );

      // 龙岗区横岗街道
      // const district = '龙岗区';
      // const street = '横岗街道';

      const regions = await ApiFindRegionByLnglat({
        lng: lnglat[0],
        lat: lnglat[1],
      });

      const regionArr = await ServiceRegion();
      const districtObj = regionArr.find((r) => r.title.includes(district));
      if (!districtObj) return [];

      let streetObj: ResRegionList | null = null;
      if (regions.length) {
        const [r1, r2] = regions;
        streetObj =
          districtObj.children?.find(
            (r) => r.title.includes(r1.name) || r.title.includes(r2.name),
          ) || null;
      }
      if (!streetObj) {
        streetObj = districtObj.children?.find((r) => r.title.includes(street)) || null;
      }

      if (!streetObj) return [];

      if (needCid) {
        if (regions.length) {
          const [r1, r2] = regions;
          const communityObj =
            streetObj.children?.find(
              (r) => r.title.includes(r1.name) || r.title.includes(r2.name),
            ) || null;
          if (communityObj) {
            regionRes = [districtObj.id, streetObj.id, communityObj.id];
            return regionRes;
          }
        }
        regionRes = [districtObj.id, streetObj.id, streetObj.children?.[0]?.id || ''];
      } else {
        regionRes = [districtObj.id, streetObj.id];
      }
    } catch (error) {
      console.log('%c [error]-86', 'font-size:13px; background:#336699; color:#fff;', error);
    } finally {
      Loading.hide();
    }

    return regionRes;
  }

  return { roleLevel, handleData, roleRegions, getRegionByLatlng, checkRegionIds };
}
