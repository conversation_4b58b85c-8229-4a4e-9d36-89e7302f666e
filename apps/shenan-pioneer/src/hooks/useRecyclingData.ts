import { cloneDeep } from 'lodash-es';
import { defineStore } from 'pinia';

export const useRecyclingData = defineStore(
  'recyclingData',
  () => {
    const rowForm1 = {
      // 工程
      projectId: '',
      // 工程名称
      projectTitle: '',
      // 联系人
      contact: '',
      // 联系方式
      phone: '',
      // 期望上门时间
      expectTime: new Date().valueOf(),
    };
    const form = ref(cloneDeep(rowForm1));

    function initForm() {
      form.value = cloneDeep(rowForm1);
    }

    return {
      form,
      initForm,
    };
  },
  {
    unistorage: {},
  },
);
