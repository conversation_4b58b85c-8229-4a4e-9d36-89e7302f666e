import { ref } from 'vue';
import { defineStore } from 'pinia';
import {
  ApiGetAllPermission,
  ApiGetAllRoles,
  ApiGetOrganization,
  ApiGetOrganizationList,
  Organization,
  Role,
} from '@/api';
import { IsH5, Storage, UserInfo } from '@/utils';

export interface RoleInfo {
  isInit: boolean;
  roleName: string;
  roleId: string;
  organizationId: string;
  organizationName: string;
  roles: Role[];
}

function GetOrganizationStorageAdapter(): Organization | null {
  if (IsH5) {
    const organization = window.sessionStorage.getItem('organization');
    return organization ? JSON.parse(organization) : null;
  }
  return Storage.get<Organization>('organization');
}

function SetOrganizationStorageAdapter(organization: Organization) {
  if (IsH5) {
    window.sessionStorage.setItem('organization', JSON.stringify(organization));
  } else {
    Storage.set('organization', organization);
  }
}

const useRoleStore = defineStore('sysRole', () => {
  const roleInfo = reactive<RoleInfo>({
    isInit: false,
    roleName: '市民',
    roleId: '',
    organizationId: '',
    organizationName: '',
    roles: [],
  });

  const organizationList = ref<Organization[]>([]);

  async function ServiceGetOrganization(isInit = false) {
    if (!UserInfo.isLogin()) return;

    const { id: oid, name: oname } = GetOrganizationStorageAdapter() || {};

    if (!isInit && oid) {
      roleInfo.organizationId = oid;
      roleInfo.organizationName = oname || '';
      return;
    }

    const current = await ApiGetOrganization();
    if (current) {
      SetOrganizationStorageAdapter(current);
      roleInfo.organizationId = current.id;
      roleInfo.organizationName = current.name;
    }
  }

  async function ServiceGetOrganizationList() {
    if (!UserInfo.isLogin()) return;

    const data = await ApiGetOrganizationList();
    organizationList.value = data || [];
  }

  async function ServiceSwitchOrganization(organizationId: string) {
    if (!UserInfo.isLogin()) return;

    const current = await ApiGetOrganization(organizationId);
    if (current) {
      SetOrganizationStorageAdapter(current);
      roleInfo.organizationId = current.id;
      roleInfo.organizationName = current.name;
    }
  }

  async function initOrganization(isInit = false) {
    if (!UserInfo.isLogin()) return roleInfo;

    if (!isInit && roleInfo.isInit) {
      return roleInfo;
    }

    roleInfo.organizationId = '';
    roleInfo.organizationName = '';

    await ServiceGetOrganization(isInit);
    await ServiceGetOrganizationList();
    if (!roleInfo.organizationId && organizationList.value.length) {
      roleInfo.organizationId = organizationList.value[0].id;
      roleInfo.organizationName = organizationList.value[0].name;
      await ServiceSwitchOrganization(organizationList.value[0].id);
    }

    await initRole(isInit);

    return roleInfo;
  }

  async function initRole(isInit = false) {
    if (!isInit) return;

    roleInfo.roleName = '市民';
    roleInfo.roleId = '';
    roleInfo.roles = [];

    if (!roleInfo.organizationId) {
      const sysRole2 = [
        {
          name: '安装人员',
          id: 'Installation:Manager',
        },
        {
          name: '销售人员',
          id: 'Head:Sales',
        },
        {
          name: '技术人员',
          id: 'technical:director',
        },
        {
          name: '商务人员',
          id: 'commercial:affairs',
        },
        {
          name: '建设单位法人',
          id: 'Enterprise:legalperson',
        },
        {
          name: '物业负责人',
          id: 'Property:Manager',
        },
        {
          name: '施工单位工人',
          id: 'Construction:workers',
        },
        {
          name: '业主/建设方',
          id: 'Construction:party',
        },
        {
          name: '施工单位负责人',
          id: 'Construction:unitleader',
        },
        {
          name: '施工监理人',
          id: 'Construction:supervisor',
        },

        // 未认证
        {
          name: '施工监理人',
          id: 'Construction:supervisor:notauth',
        },
        {
          name: '施工单位工人',
          id: 'Construction:workers:notauth',
        },
        {
          name: '业主/建设方',
          id: 'Construction:party:notauth',
        },
        {
          name: '施工单位负责人',
          id: 'Construction:unitleader:notauth',
        },
      ];
      const data = await ApiGetAllRoles();
      roleInfo.roles = data;
      const role = sysRole2.find((item) => data.some((r) => r.roleName === item.id));
      roleInfo.roleName = role?.name || '市民';
      roleInfo.roleId = role?.id || '';
      return;
    }

    const sysRole1 = [
      {
        name: '社区管理员',
        id: 'Senior:community',
      },

      {
        name: '巡查人员',
        id: 'Senior:patrol',
      },
      {
        name: '市级管理员',
        id: 'Senior:citylevel',
      },
      {
        name: '区级管理员',
        id: 'Senior:districtlevel',
      },
      {
        name: '街道管理员',
        id: 'Senior:street',
      },
      {
        name: '社区管理员',
        id: 'Senior:community',
      },
      {
        name: '市级管理员',
        id: 'Ordinary:citylevel',
      },
      {
        name: '区级管理员',
        id: 'Ordinary:districtlevel',
      },
      {
        name: '街道管理员',
        id: 'Ordinary:street',
      },
      {
        name: '社区管理员',
        id: 'Ordinary:community',
      },
    ];

    // 市级管理员ALLevel:admin,街道管理员DistrictLevel:admin,区级管理员Citylevel:admin,社区管理员Community:admin
    // 巡查员
    // 施工单位负责人：Constructionunit:leader
    const data = await ApiGetAllRoles();
    roleInfo.roles = data;
    const role = sysRole1.find((item) => data.some((r) => r.roleName === item.id));
    roleInfo.roleName = role?.name || '市民';
    roleInfo.roleId = role?.id || '';
  }

  async function switchOrganization(organizationId: string) {
    await ServiceSwitchOrganization(organizationId);
    await initRole(true);
  }

  return {
    initOrganization,
    switchOrganization,
    roleInfo,
    organizationList,
  };
});

export default useRoleStore;
