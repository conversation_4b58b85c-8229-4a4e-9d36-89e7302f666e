import { createSSRApp } from 'vue';
import * as <PERSON><PERSON> from 'pinia';
import App from './App.vue';
import '@/register';
import globalLogin from '@rms/login/src/mixin/init';
import { createUnistorage } from 'pinia-plugin-unistorage';

export function createApp() {
  const app = createSSRApp(App);
  const pinia = Pinia.createPinia();

  // 数据持久化
  pinia.use(createUnistorage());

  app.use(pinia);
  app.mixin(globalLogin);

  return {
    app,
    Pinia,
  };
}
