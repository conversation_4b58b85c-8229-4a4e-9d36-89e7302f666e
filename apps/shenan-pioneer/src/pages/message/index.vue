<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue';
import { useList } from '@rms/components/sc-list/sc-list';
import { Dayjs, FormatDateTime } from '@shencom/utils';
import { ApiGetMessageList, ResMessageItem } from '@/api/modules/message';
import { ApiQueryHandler } from '@shencom/api';
import { StorageGetMessageRefresh, StorageRemoveMessageRefresh } from '@/storage/message';
import { toPageFormat } from '@/utils/navigation';
import { ApiGetProjectShow } from '@/api';
import { Toast } from '@/utils';
import { patrolResultStatusList } from '@/config/patrolRecord';

interface RelateObj1 {
  content: string;
  createdAt: number;
  id: string;
  isDeleted: number;
  organizationId: string;
  projectId: string;
  relateId: string;
  resolveFlag: number;
  status: number;
  type: number;
  updatedAt: number;
}
interface RelateObj2 {
  content: string;
  createdAt: 1752577417000;
  id: string;
  isDeleted: 0;
  memo: string;
  organizationId: string;
  projectId: string;
  relateId: string;
  resolveFlag: number;
  status: number;
  type: number;
  updatedAt: number;
  userId: string;
}
interface RelateObj3 {
  content: string;
  createdAt: number;
  defectiveNumber: number;
  id: string;
  isDeleted: number;
  organizationId: string;
  projectId: string;
  recordId: string;
  resultStatus: number;
  state: number;
  status: number;
  updatedAt: number;
  userId: string;
}
interface MessageItem extends ResMessageItem {
  event: {
    typeName: string;
    projectName: string;
  };
  projectName: string;
  relateObj1: RelateObj1;
  relateObj2: RelateObj2;
  relateObj3: RelateObj3;
  buttonText: string;
  buttonUrl: string;
  typeText: string;
  text: string;
  pics: string;
  msgId: string;
}

const today = [
  Dayjs().startOf('d').format('YYYY-MM-DD HH:mm:ss'),
  Dayjs().endOf('d').format('YYYY-MM-DD HH:mm:ss'),
];
const yesterday = [
  Dayjs().subtract(1, 'day').startOf('d').format('YYYY-MM-DD HH:mm:ss'),
  Dayjs().subtract(1, 'day').endOf('d').format('YYYY-MM-DD HH:mm:ss'),
];
// Tab 选项
const filterOptions = [
  {
    label: '今日',
    value: today.join(','),
  },
  {
    label: '昨日',
    value: yesterday.join(','),
  },
  { label: '历史全部', value: '' },
];

// 已读/未读筛选
const readStatusOptions = [
  { label: '未读', value: 0 },
  { label: '已读', value: 1 },
];

// eslint-disable-next-line no-shadow
enum MsgTypeMap {
  '预约安装提醒' = 1,
  '预约回收提醒',
  '现场勘查提醒',
  '上门安装提醒',
  '上门回收提醒',
  '已接入监管提醒',
  '结束监管提醒',
  '违规告警提醒',
}

const curTab = ref(today.join(','));
const curReadStatus = ref(0);

// 监听 tab 变化，重新获取数据
watch(curTab, () => {
  refresh();
});

// 监听已读状态变化，重新获取数据
watch(curReadStatus, () => {
  refresh();
});

function refresh() {
  getData(true, params.value);
}

const params = computed(() => ({
  // 1-查询监管流程消息 ，2-查询告警提醒消息
  // queryType: 1,
  query: ApiQueryHandler(
    [
      [curReadStatus.value, 'status', 'number'],
      curTab.value && ([curTab.value, 'createdAt', 'rangeDateTime'] as any),
    ].filter(Boolean),
  ),
  size: 10,
}));

const { list, getData, listProps } = useList<MessageItem[]>({
  request: ApiGetMessageList,
  params: params.value,
  immediate: true,
  handle: (content) => {
    const button: Record<string, { text: string; path: string }> = {
      预约安装提醒: { text: '预约安装', path: '/pages/engineering/appoint' },
      预约回收提醒: { text: '预约回收', path: '/pages/recycling/create' },
      现场勘查提醒: { text: '上门勘查', path: '/pages/engineering/survey' },
      上门安装提醒: { text: '上门安装', path: '/pages/engineering/installation' },
      上门回收提醒: { text: '上门回收', path: '/pages/recycling/recycle' },
      已接入监管提醒: { text: '查看监控', path: '/pages/monitor/index' },
      结束监管提醒: { text: '查看详情', path: '/pages/engineering/detail' },
      巡查结果通知: { text: '查看详情', path: '/pages/patrolRecord/detail' },
    };
    return content.map((item) => {
      const obj = item[`relateObj${item.type}` as keyof MessageItem] as RelateObj1;
      let typeText = '';
      let buttonText = '';
      let buttonUrl = '';
      if ([1, 2].includes(item.type)) {
        typeText = MsgTypeMap[obj.type];
        buttonText = button[typeText]?.text || '';
        buttonUrl = button[typeText]?.path || '';
      }
      if (item.type === 3) {
        typeText = obj.content;
        buttonText = '查看详情';
        buttonUrl = '/pages/patrolRecord/detail';
      }
      const event = item.memo ? JSON.parse(item.memo) : null;
      return {
        ...item,
        ...obj,
        msgId: item.id,
        type: item.type,
        status: item.status,
        event,
        pics: event?.pics ? event.pics.split(',')[0] : '',
        typeText,
        text: `${obj.content}，${
          typeText?.includes('监管') ? '我要 ' : typeText?.includes('预约') ? '请先 ' : ''
        }`,
        buttonText,
        buttonUrl,
      };
    });
  },
});

function handleCardClick(item: MessageItem) {
  if (item.type === 2) {
    toPageFormat('/pages/monitor/warning/detail', {
      id: item.relateId,
      msgId: item.msgId,
    });
    return;
  }
  if (item.type === 3) {
    toPageFormat('/pages/patrolRecord/detail', {
      id: item.relateObj3.recordId,
      msgId: item.msgId,
      isShowButton: true,
    });
    return;
  }
  toPageFormat('/pages/engineering/detail', {
    id: item.projectId,
    msgId: item.msgId,
  });
}

async function handleButtonClick(item: MessageItem) {
  const data = await ApiGetProjectShow(item.projectId);
  if (!data) {
    Toast.warning('该工程已被删除');
    return;
  }

  if (item.buttonUrl) {
    toPageFormat(item.buttonUrl, {
      id: ['查看监控', '查看详情'].includes(item.buttonText) ? item.projectId : item.relateId,
      orderId: item.orderId,
    });
  }
}

onMounted(() => {
  refresh();
});

onShow(() => {
  const needRefresh = StorageGetMessageRefresh();
  if (needRefresh) {
    refresh();
    StorageRemoveMessageRefresh();
  }
});
</script>

<template>
  <view class="min-h-screen bg-base flex flex-col relative">
    <!-- Tab 切换 -->
    <view class="px-3 pt-3 pb-3 bg-white absolute w-full top-0 z-10">
      <wd-radio-group
        v-model="curTab"
        shape="button"
        class="flex justify-between -mr-2"
        :style="{ '--wot-radio-button-min-width': '100%' }">
        <wd-radio v-for="opt in filterOptions" :key="opt.value" :value="opt.value" class="flex-1">
          {{ opt.label }}
        </wd-radio>
      </wd-radio-group>

      <!-- 已读/未读筛选 -->
      <view class="flex justify-end gap-2 mt-4">
        <text
          v-for="status in readStatusOptions"
          :key="status.value"
          :class="`text-sm cursor-pointer ${
            curReadStatus === status.value ? 'text-blue-500 font-medium' : 'text-gray-400'
          }`"
          @click="curReadStatus = status.value">
          {{ status.label }}
        </text>
      </view>
    </view>

    <!-- 消息列表 -->
    <sc-list
      v-bind="listProps"
      class="!px-0"
      height-class="h-[calc(100vh-50px)]"
      :empty-text="curReadStatus === 0 ? '暂无未读消息' : '暂无已读消息'"
      local-refresh
      @on-handle-upper-fn="getData(true)">
      <view class="h-24" />
      <view
        v-for="item in list"
        :key="item.id"
        class="rounded-lg shadow-md shadow-slate-200 bg-white border border-gray-200 border-solid mx-3 mt-3 overflow-hidden">
        <view class="flex justify-between items-center flex-wrap py-2 px-3 bg-slate-100">
          <view class="flex items-center gap-2">
            <text class="text-base font-semibold text-gray-700">
              {{ item.typeText || '提醒' }}
            </text>
            <wd-tag size="small" :type="item.status ? 'default' : 'primary'">
              {{ item.status ? '已读' : '未读' }}
            </wd-tag>
          </view>
          <text class="text-xs text-gray-500">{{ FormatDateTime(item.createdAt) }}</text>
        </view>

        <view class="px-4 py-3 flex justify-between items-center">
          <wd-img
            v-if="item.pics"
            :src="item.pics"
            class="w-20 h-20 rounded-lg mr-2"
            mode="aspectFill"
            error-icon="photo-fail" />
          <view class="flex-1 text-sm text-gray-700 leading-relaxed">
            <template v-if="item.type === 2">
              <view class="mb-2 font-medium text-red">
                {{ item.relateObj2.memo && JSON.parse(item.relateObj2.memo)?.typeName }}
              </view>
              <view>
                所属工程【{{
                  item.relateObj2.memo && JSON.parse(item.relateObj2.memo).projectName
                }}】
              </view>
            </template>
            <template v-else-if="item.type === 3">
              <view class="grid grid-cols-2 gap-2 py-3">
                <view>
                  处理结果：
                  {{
                    patrolResultStatusList.find((i) => i.id === +item.relateObj3.resultStatus)
                      ?.value
                  }}
                </view>
                <view>问题项：{{ item.relateObj3.defectiveNumber }}个</view>
              </view>
              <view>所属工程：【{{ item.projectName }}】</view>
            </template>
            <template v-else>
              <text>{{ item.text }}</text>
              <text
                v-if="item.buttonText"
                class="pl-0.5 font-medium text-red"
                @click="handleButtonClick(item)">
                {{ item.buttonText }} >>
              </text>
            </template>
          </view>
        </view>

        <view class="px-4 py-2 flex justify-end border-t border-solid border-gray-100">
          <wd-button plain size="small" type="info" @click="handleCardClick(item)">
            查看详情
          </wd-button>
        </view>
      </view>
    </sc-list>

    <Tabbar />
  </view>
</template>

<style lang="scss" scoped></style>
