<script setup lang="ts">
import { ref } from 'vue';
import { onLoad } from '@dcloudio/uni-app';
import { FormatDate } from '@shencom/utils';
import { useList } from '@rms/components/sc-list/sc-list';
import { requestArticlesData, ReqArticlesProps, ArticlesProps } from '@rms/service';
import { IndexBodyInterface } from '@rms/types';
import { utilsOss, Navigator } from '@/utils';

const title = ref('');

const props = defineProps<{
  id: string;
}>();

const imgs = {
  img_02: `${utilsOss.imgPath}/home/<USER>
  icon_7: `${utilsOss.imgPath}/home/<USER>
};

const { list, getData, listProps } = useList<ReqArticlesProps[]>({
  request: getArticlesList,
  params: {
    categoryId: props.id,
  },
  handle: (content: any[]) => {
    title.value = content[0].displayName;
    return content.map((item) => ({
      digest: item.digest || '',
      title: item.title,
      id: item.id,
      author: item.author,
      referUrl: item.referUrl || '',
      remoteUrl: item.pictureUrl ? item.pictureUrl + utilsOss.zoomCustom() : `${utilsOss}logo.png`,
      createdAt: FormatDate(item.createdAt),
    }));
  },
});

async function getArticlesList(body?: IndexBodyInterface) {
  const res = await requestArticlesData({
    categoryId: props.id,
    size: 10,
    ...body,
  });

  return res;
}

function onHandleUpper() {
  getData(true);
}

function toInfomationDetail(item: ReqArticlesProps) {
  Navigator.push('/pages/pages-information/detail', {
    id: item.id,
  });
}
</script>

<template>
  <view class="p-3">
    <div class="flex items-center gap-2 mb-3">
      <img :src="imgs.icon_7" class="w-3.5 h-3.5" alt="" srcset="" />
      <div class="text-17 font-bold">{{ title }}</div>
    </div>
    <sc-list
      class="w-full h-screen !px-0"
      class-names="w-full h-screen !px-0"
      v-bind="listProps"
      @on-handle-upper-fn="onHandleUpper">
      <view
        v-for="item of list"
        :key="item.id"
        class="flex p-3 bg-transparent relative mb-3 min-h-[81px]"
        @click="toInfomationDetail(item)">
        <img class="absolute top-0 left-0 w-full h-full" :src="imgs.img_02" alt="" srcset="" />
        <view class="flex flex-col gap-1 justify-between flex-1 relative z-10">
          <view class="pr-2 font-bold ellipsis-2">{{ item.title }}</view>
          <view class="text-placeholder text-13">{{ item.author }}</view>
          <view class="text-placeholder text-13">发布时间：{{ item.createdAt }}</view>
        </view>
      </view>
    </sc-list>
  </view>
</template>
