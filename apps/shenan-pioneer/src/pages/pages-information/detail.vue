<script setup lang="ts">
import { onLoad } from '@dcloudio/uni-app';
import { ref } from 'vue';
import { FormatDateTime } from '@shencom/utils';
import { exceptionHandler, Loading, reviewOrCopyUrl } from '@rms/utils';
import { ArticlesProps } from '@rms/service';
import { ServiceArticleDetail } from '@/api/pages-information/information';
import { Navigator } from '@/utils';

const isShow = ref(false);

const showTitle = ref(true); // 是否展示标题、发布时间和来源

const showAttachment = ref(false); // 是否展示附件

const navTitle = ref(''); // 头部导航标题

const articleInfo = ref<ArticlesProps | null>(null);

async function getArticleDetail(id: string) {
  articleInfo.value = await ServiceArticleDetail(id);
  const type = articleInfo.value?.type;
  // 链接图文
  if (type === 1) {
    Navigator.replace('/pages/webview/index', {
      url: articleInfo.value?.referUrl,
    });
    return;
  }
  uni.setNavigationBarTitle({
    title: navTitle.value || articleInfo.value?.title || '',
  });
  isShow.value = true;
}

onLoad((option) => {
  try {
    const { id, hideTitle, attachment, title } = option || {};
    if (!id) return;
    if (hideTitle) {
      showTitle.value = false;
    }
    if (attachment) {
      showAttachment.value = true;
    }
    // 设置头部导航标题
    if (title) {
      navTitle.value = title;
      uni.setNavigationBarTitle({
        title: navTitle.value,
      });
    }
    Loading.show('加载中...');

    getArticleDetail(id);
  } catch (error) {
    exceptionHandler(error);
  } finally {
    Loading.hide();
  }
});
</script>

<template>
  <view v-show="isShow && articleInfo" class="info-detail p-[30rpx] box-border">
    <!-- 新闻-详情 -->
    <view class="box-border">
      <view v-if="showTitle">
        <view class="font-bold text-16">{{ articleInfo?.title }}</view>
        <view class="text-placeholder pt-[10rpx] mb-[40rpx]">
          {{ FormatDateTime(articleInfo?.createdAt) }}
          <text class="ml-1">来源：{{ articleInfo?.author }}</text>
        </view>
      </view>
      <!-- <view v-html="articleInfo?.content"></view> -->

      <sc-parse :content="articleInfo?.content" />
      <view v-if="showAttachment" class="text-16 pb-[60rpx]">
        <view class="font-bold mb-1">附件</view>
        <view
          v-for="item in articleInfo?.ncmsAttachments"
          :key="item.id"
          class="text-[#3390ff]"
          @click="reviewOrCopyUrl(item.fileUrl)">
          {{ item.fileName }}
        </view>
        <view v-if="!articleInfo?.ncmsAttachments?.length">无</view>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.info-detail {
  ::v-deep .lazyload {
    width: 100% !important;
  }
}
</style>
