<script setup lang="ts">
import { ApiGetPatrolList, ApiGetPatrolTabList, ApiGetRoleRegion, ReqPatrolListItem } from '@/api';
import { patrolRecordStatusList, patrolResultStatusList } from '@/config/patrolRecord';
import useRoleStore from '@/hooks/useRole';
import { monitorFlagOptions, projectStatusOptions } from '@/state';
import { StorageGetMonitorFlowRefresh, StorageRemoveMonitorFlowRefresh } from '@/storage/monitor';
import {
  FormatDate,
  FormatDateTime,
  IsMiniProgram,
  IsWeixinH5,
  Navigator,
  Toast,
  utilsOss,
} from '@/utils';
import { useList } from '@rms/components/sc-list/sc-list';
import { ServiceGisByIds, ServiceRegion } from '@rms/service/src';
import { watchThrottled } from '@vueuse/core';
import { useRoleRegion } from '@/hooks/useRoleRegion';
import {
  StorageGetPatrolRecordRefresh,
  StorageRemovePatrolRecordRefresh,
} from '@/storage/patrolRecord';

const props = defineProps<{
  projectId: string;
  state: string;
  resultStatusList: string;
}>();

const currentRecordStatusList = ref(patrolRecordStatusList);
const currentpatrolResultStatusList = computed(() => {
  return patrolResultStatusList.filter((i) => i.id !== 0);
});

const search = reactive({
  name: '',
  state: 4,
  size: 10,
  createdAtBtw: '',
  resultStatus: '',
  projectId: '',
  regions: [] as string[],
});

const resultColumns = computed(() => {
  return patrolResultStatusList.map((i) => ({
    label: i.value,
    value: i.id,
  }));
});
const resultPickerRef = ref();
const { list, listProps, getData } = useList<ReqPatrolListItem[]>({
  request: [1, 2].includes(+props.state) ? ApiGetPatrolTabList : ApiGetPatrolList,
  params: search,
  immediate: false,
});

async function setRegionName(region: string[]) {
  let res = await ServiceRegion();

  filterData.regionName =
    region
      .filter(Boolean)
      .map((r) => {
        const item = res.find((i) => i.id === r);
        res = item?.children || [];
        return item?.title;
      })
      .pop() || '';
}

watch(
  () => search.regions,
  () => {
    setRegionName(search.regions);
  },
  { deep: true },
);

watchThrottled(
  search,
  () => {
    refresh();
  },
  { throttle: 200, immediate: true, deep: true },
);

function refresh() {
  const [regionPid, regionId, regionCid] = search.regions;
  if (props.state) {
    search.state = Number(props.state);
  }
  let params = {};
  if ([1, 2].includes(Number(props.state))) {
    params = { tab: Number(props.state) };
  } else {
    params = { state: search.state === 4 ? '' : search.state };
  }

  getData(true, {
    resultStatus: +search.resultStatus === 4 ? '' : search.resultStatus,
    projectId: props.projectId,
    resultStatusList:
      props.resultStatusList && props.resultStatusList.split(',').map((item) => Number(item)),
    regionPid,
    ...params,
    regionId,
    regionCid,
    createdAtBtw:
      search.createdAtBtw.length === 2
        ? [`${search.createdAtBtw[0]} 00:00:00`, `${search.createdAtBtw[1]} 23:59:59`].join(',')
        : '',
  });
}

function getTitle() {
  let title = '';

  console.log('props.state', props.state);
  switch (props.state) {
    case '1':
      title = '整改清单';
      break;
    case '2':
      title = '复查清单';
      break;
    default:
      title = '巡查记录';
  }
  return title;
}

onShow(() => {
  uni.setNavigationBarTitle({
    title: getTitle(),
  });
  const needRefresh = StorageGetPatrolRecordRefresh();
  if (needRefresh) {
    refresh();
    StorageRemovePatrolRecordRefresh();
  }
});

const roleStore = useRoleStore();
roleStore.initOrganization(true);

onMounted(() => {
  refresh();
});

function toDetail(item: ReqPatrolListItem) {
  Navigator.push('/pages/patrolRecord/detail', {
    id: item.id,
    isShowButton: !!props.state,
  });
}

async function toLocation(item: ReqPatrolListItem) {
  const gisInfo = await ServiceGisByIds(item.projectPoiId);
  const options = {
    longitude: +gisInfo[0].lng,
    latitude: +gisInfo[0].lat,
    name: item.projectName,
    address: gisInfo[0].addr,
  };

  if (IsWeixinH5 || IsMiniProgram) {
    uni.$wx.openLocation(options);
  } else {
    uni.openLocation(options);
  }
}

const scPickerRef = ref();
function onTimeOpen() {
  scPickerRef.value.show();
}

function onTimeConfirm(p: any) {
  filterData.createdAtBtw = p.join('-');
}

function onResultPickerShow() {
  resultPickerRef.value.open();
}

const filterData = reactive({
  regionName: '',
  createdAtBtw: '',
  resultStatus: '',
});
const { roleLevel, handleData } = useRoleRegion();
const scRegionRef = ref();
function onRegionShow() {
  if (typeof roleLevel.value !== 'number') {
    Toast.error('暂无权限');
    return;
  }

  scRegionRef.value.show();
}

function onConfirmResult(e: any) {
  filterData.resultStatus = e.selectedItems.label;
  search.resultStatus = e.value;
}

onPullDownRefresh(async () => {
  search.state = 4;
  search.createdAtBtw = '';
  search.resultStatus = '';
  filterData.createdAtBtw = '';
  filterData.resultStatus = '';
  filterData.regionName = '';
  search.regions = [];

  await refresh();
  uni.stopPullDownRefresh();
});
onReachBottom(() => null);
</script>

<template>
  <div v-if="!props.projectId" class="w-full bg-[#F6F8FF] py-3">
    <div class="flex justify-around items-center text-gray-500 text-12 px-2 mb-2">
      <div
        class="rounded-2xl text-center bg-gray-100 h-[30px] ellipsis-1 w-[100px] leading-[30px] px-1"
        @click="onRegionShow">
        <span class="mr-1">{{ filterData.regionName || '选择区域' }}</span>
        <wd-icon name="arrow-down" size="15px"></wd-icon>
      </div>
      <div
        :class="[
          'rounded-2xl text-center bg-gray-100 h-[30px] ellipsis-1 w-[230px] leading-[30px] px-1',
        ]"
        @click="onTimeOpen">
        <span class="mr-1">{{ filterData.createdAtBtw || '巡查时间' }}</span>
        <wd-icon name="arrow-down" size="15px"></wd-icon>
      </div>
      <div
        v-if="!props.state"
        class="rounded-2xl text-center bg-gray-100 h-[30px] ellipsis-1 w-[150px] leading-[30px] px-1"
        @click="onResultPickerShow">
        <span class="mr-1">{{ filterData.resultStatus || '处理结果' }}</span>
        <wd-icon name="arrow-down" size="15px"></wd-icon>
      </div>
    </div>
    <wd-radio-group
      v-if="!props.state"
      v-model="search.state"
      class="!bg-[#F6F8FF]"
      custom-class="flex justify-around"
      shape="button">
      <wd-radio :value="4">全部</wd-radio>
      <wd-radio v-for="item in currentRecordStatusList" :key="item.id" :value="item.id">
        {{ item.value }}
      </wd-radio>
    </wd-radio-group>
    <wd-radio-group
      v-if="props.state"
      v-model="search.resultStatus"
      class="!bg-[#F6F8FF]"
      custom-class="flex justify-around"
      shape="button">
      <wd-radio :value="4">全部</wd-radio>
      <wd-radio v-for="item in currentpatrolResultStatusList" :key="item.id" :value="item.id">
        {{ item.value }}
      </wd-radio>
    </wd-radio-group>
  </div>

  <sc-list v-bind="listProps" class="!p-0 !h-auto !min-h-[calc(100vh-100px)]">
    <div class="w-full p-3">
      <div
        v-for="item in list"
        :key="item.id"
        class="bg-[#F6F8FF] rounded-xl p-3 pt-8 mb-3 text-12 relative"
        @click="toDetail(item)">
        <div
          class="absolute w-[140px] top-0 rounded-tr-xl rounded-bl-xl text-center ellipsis-1 bg2 p-1 text-13 right-0 z-10">
          {{ item.projectNumber || 'sadasd124213123w' }}
        </div>

        <div class="flex items-center gap-2 mb-2">
          <div class="max-w-[200px] text-16 font-bold">{{ item.projectName }}</div>
          <wd-tag
            :type="projectStatusOptions.find((i) => i.value === +item.projectStatus)?.type"
            custom-class="!text-12">
            {{ monitorFlagOptions.find((i) => i.value === +item.projectMonitorFlag)?.label }}-{{
              projectStatusOptions.find((i) => i.value === +item.projectStatus)?.label || '已结束'
            }}
          </wd-tag>
        </div>
        <div class="flex items-center gap-2 mb-1">
          <div>小散工程地址: {{ item.projectAddress }}</div>
          <wd-icon
            color="#1338D5"
            name="location"
            size="18px"
            @click.stop="toLocation(item)"></wd-icon>
        </div>

        <div class="mb-1">巡查时间: {{ FormatDateTime(item.createdAt) }}</div>
        <div class="grid grid-cols-2 gap-2 py-3">
          <div>
            <uni-text>处理结果：</uni-text>
            <uni-text
              :class="patrolResultStatusList.find((i) => i.id === +item.resultStatus)?.class">
              {{ patrolResultStatusList.find((i) => i.id === +item.resultStatus)?.value }}
            </uni-text>
          </div>
          <div>
            <uni-text>巡查人员：</uni-text>
            <uni-text>{{ item.patrolUserNames }}</uni-text>
          </div>
          <div>
            <uni-text>问题项：</uni-text>
            <uni-text>{{ item.defectiveNumber }}</uni-text>
            个
          </div>
          <div v-show="item.resultStatus !== 0">
            <uni-text>限整改期限：</uni-text>
            <uni-text :class="item.isTimeout ? 'text-[#dd524d]' : ''">
              {{ item.overtime && FormatDate(item.overtime) }}
            </uni-text>
          </div>
          <div>
            <uni-text>当前状态：</uni-text>
            <uni-text :class="currentRecordStatusList.find((i) => i.id === +item.state)?.class">
              {{ currentRecordStatusList.find((i) => i.id === +item.state)?.value }}
            </uni-text>
          </div>
        </div>
      </div>
    </div>
  </sc-list>
  <sc-region
    v-if="typeof roleLevel === 'number'"
    ref="scRegionRef"
    v-model="search.regions"
    :handle-data="handleData"
    is-hide></sc-region>
  <wd-select-picker
    ref="resultPickerRef"
    v-model="search.resultStatus"
    :z-index="9999"
    label="输入摊位名称"
    :columns="resultColumns"
    type="radio"
    use-default-slot
    filterable
    @confirm="onConfirmResult">
    <div></div>
  </wd-select-picker>

  <sc-date-picker
    ref="scPickerRef"
    v-model="search.createdAtBtw"
    type="daterange"
    is-hide
    @change="onTimeConfirm" />
</template>

<style lang="scss" scoped>
:deep(.wd-radio__label) {
  text-align: center !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  padding: 8px !important;
}
:deep(.wd-radio) {
  margin: 1px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}
:deep(.inspectBtn.is-disabled) {
  background: #666 !important;
}

.bg-sky1 {
  // 天空蓝渐变色
  background-image: linear-gradient(120deg, #a1c4fd 0%, #c2e9fb 100%);
}

.bg2 {
  background-image: linear-gradient(to top, #cfd9df 0%, #e2ebf0 100%);
}

.border-b {
  border-bottom: 1rpx solid #e2ebf0;
}
</style>
