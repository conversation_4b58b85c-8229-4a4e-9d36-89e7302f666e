<script setup lang="ts">
import { exceptionHandler, FormatDate, Loading, Navigator, Toast, UserInfo } from '@/utils';
import BasicCheckItemDetail from './components/BasicCheckItemDetail.vue';
import {
  ApiGetCurrentUser,
  ApiGetPatrolDetail,
  ApiSubmitRectificationResult,
  DataItem,
  DetailData,
  RecordTableItem,
  TableItem,
} from '@/api';
import { ServiceImgByIds } from '@rms/service/src';
import { patrolResultStatusList } from '@/config/patrolRecord';
import { addWatermarkAfterRead } from '../recycling/canvas';
import { Dictionary } from '@rms/types';
import { userInfo } from 'os';
import { e } from 'unocss';
import { StorageSetPatrolRecordRefresh } from '@/storage/patrolRecord';
import { cloneDeep } from 'lodash-es';

const props = defineProps<{
  id: string;
}>();

const detailData = ref<DetailData>();

const userName = ref('');

const projectId = ref('');

// 处理流程数据
const processSteps = ref([
  {
    title: '现场巡查',
    time: '2025-01-11 10:00:00',
    description: '巡查人员: 张三',
    status: 'finished' as const,
  },
  {
    title: '整改反馈',
    time: '2025-01-11 15:30:00',
    description: '等待整改结果',
    status: 'finished' as const,
  },
]);

function onGotoDetail() {
  Navigator.push('/pages/engineering/detail', { id: projectId.value });
}

function onGotoHistory() {
  Navigator.push('/pages/patrolRecord/list', {
    projectId: detailData.value?.projectId,
  });
}

// 切换巡查表展开状态
function toggleForm(form: DataItem) {
  form.isExpanded = !form.isExpanded;
}

const basicCheckItemDetailRefs = ref<any[]>([]);

const dataList = ref<DataItem[]>([]);
async function getTableList(list: RecordTableItem[]) {
  const datas: DataItem[] = [];
  const imgIds: string[] = [];
  list.forEach((item) => {
    const { name, patrolItemSnapshots } = item.patrolTableSnapshot;
    const dataItem: DataItem = {
      name,
      isExpanded: false,
      tableList: [],
    };

    if (patrolItemSnapshots && patrolItemSnapshots.length) {
      patrolItemSnapshots.forEach((items) => {
        const { name: itemName, id, patrolItemInfoSnapshotList } = items;

        if (patrolItemInfoSnapshotList && patrolItemInfoSnapshotList.length) {
          patrolItemInfoSnapshotList.forEach((checkItem) => {
            if (checkItem.data.qualified === 0) {
              let tableItem: Partial<TableItem> = {};
              tableItem.name = itemName;
              tableItem.itemId = id;
              tableItem.score = checkItem.score;
              tableItem.colspanNum = patrolItemInfoSnapshotList.length;
              const { content, score } = checkItem;
              tableItem = {
                ...tableItem,
                ...checkItem.data,
              };
              tableItem.content = `${content}`;
              tableItem.id = checkItem.data.id;
              dataItem.tableList.push(tableItem);

              if (tableItem.picture) {
                imgIds.push(tableItem.picture);
              }
              // if (tableItem.rectifiedPicture) {
              //   imgIds.push(tableItem.rectifiedPicture);
              // }
            }
          });
        }
      });
    }
    datas.push(dataItem);
  });
  if (imgIds.length) {
    const imgs = await ServiceImgByIds(imgIds);
    datas.forEach((item) => {
      if (item.tableList && item.tableList.length) {
        item.tableList.forEach((tableItem) => {
          if (tableItem.picture) {
            tableItem.pictureUrl = imgs[tableItem.picture]?.remoteUrl;
          }
          // if (tableItem.rectifiedPicture) {
          //   tableItem.rectifiedPictureUrl = imgs[tableItem.rectifiedPicture]?.remoteUrl;
          // }
        });
      }
    });
  }
  dataList.value = datas;
}

function handleSuccess() {
  // 设置刷新标记，通知相关页面刷新数据
  StorageSetPatrolRecordRefresh();

  Toast.success('提交成功', {
    complete: () => {
      Navigator.replace('/pages/patrolRecord/list?state=1');
    },
  });
}

async function submitForm() {
  try {
    const recordTableDataList: any[] = [];
    dataList.value.forEach((item) => {
      const { tableList } = item;
      recordTableDataList.push(...tableList);
    });
    const formParams = {
      id: props.id,
      recordTableDataList,
    };
    const res = await ApiSubmitRectificationResult(formParams);
    handleSuccess();
  } catch (error) {
    exceptionHandler(error);
  }
}
// 上传整改结果
const isSubmit = ref(false);
async function onSubmit() {
  Loading.show();

  // 第一次遍历：检查是否所有整改照片都已上传
  for (let index = 0; index < dataList.value.length; index++) {
    const form = dataList.value[index];
    for (let itemIndex = 0; itemIndex < form.tableList.length; itemIndex++) {
      const item = form.tableList[itemIndex];
      if (!item.rectifiedPictureList || !item.rectifiedPictureList.length) {
        Toast.error('请上传整改后照片');
        return;
      }
    }
  }

  if (isSubmit.value) {
    return;
  }
  isSubmit.value = true;

  try {
    // 第二次遍历：为所有整改照片添加水印

    for (let index = 0; index < dataList.value.length; index++) {
      const form = dataList.value[index];
      for (let itemIndex = 0; itemIndex < form.tableList.length; itemIndex++) {
        const item = form.tableList[itemIndex];
        const componentRef = basicCheckItemDetailRefs.value[index * 1000 + itemIndex];
        if (componentRef && item.rectifiedPictureList) {
          // eslint-disable-next-line no-await-in-loop
          await addWatermarkAfterRead(item.rectifiedPictureList, {
            title: detailData.value?.projectName || '',
            patrolRecordText1: `整改人员：${userName.value}`,
            patrolRecordText2: `问题项：【${item.name}】${item.content}不合格`,
          });
          // eslint-disable-next-line no-await-in-loop
          await componentRef.scFormRef.startRef.rectifiedPictureList.onUpload();

          item.rectifiedPicture = item.rectifiedPictureList
            .map((picItem: any) => picItem.id)
            .join(',');
        }
      }
    }
    await submitForm();
  } catch (error) {
    exceptionHandler(error);
  } finally {
    isSubmit.value = false;
    Loading.hide();
  }
}
async function init() {
  const res = await ApiGetPatrolDetail(props.id);
  detailData.value = res;
  getTableList(res.recordTables);
}

// 处理基础检查项数据变化
function onCheckItemUpdate(index: number, itemIndex: number, itemData: Partial<TableItem>) {
  dataList.value[index].tableList[itemIndex] = itemData;
}

async function getCurrentUser() {
  const res = await ApiGetCurrentUser();
  userName.value = res.realname;
}

onLoad(() => {
  init();
  getCurrentUser();
});
</script>

<template>
  <div v-if="detailData" class="min-h-screen patrolRecord-detail-page bg-gray-50 pb-10">
    <div class="p-4">
      <div class="text-18">整改反馈</div>
    </div>
    <!-- 主要内容区域 -->
    <div v-if="dataList.length" class="p-4 space-y-4">
      <!-- 本次巡查表 -->
      <div
        v-for="(form, index) in dataList"
        :key="form.name"
        class="bg-white rounded-lg transition-all">
        <div class="p-4 border-b border-gray-100">
          <div class="flex justify-between items-center">
            <div class="font-semibold text-gray-900 mr-3">本次巡查表</div>
            <div class="font-semibold text-blue-400 flex-1">{{ form.name }}</div>
          </div>
        </div>

        <!-- 展开的内容 -->
        <div class="p-4">
          <div class="check-items space-y-3">
            <BasicCheckItemDetail
              v-for="(item, itemIndex) in form.tableList"
              :ref="
                (el) => {
                  const uniqueIndex = index * 1000 + itemIndex;
                  basicCheckItemDetailRefs[uniqueIndex] = el;
                }
              "
              :key="item.id"
              :item="item"
              :is-rectify="true"
              :disabled="true"
              :index="itemIndex"
              @update="onCheckItemUpdate(index, itemIndex, $event)" />
          </div>
        </div>
      </div>
    </div>

    <!-- 底部按钮 -->
    <sc-button-fixed class="pt-5 box-border z-10">
      <div class="w-full flex items-center justify-center">
        <wd-button
          custom-class="!rounded-md !w-full"
          size="large"
          :style="{ opacity: !isSubmit ? 1 : 0.5 }"
          type="primary"
          @click="onSubmit">
          提交整改结果
        </wd-button>
      </div>
    </sc-button-fixed>
  </div>
</template>

<style lang="scss" scoped>
.patrolRecord-detail-page {
  :deep(.wd-step__icon-outer) {
    color: #fff !important;
    background-color: #22b75a !important;
    border-color: #22b75a !important;
  }
  :deep(.wd-step__line) {
    background-color: #22b75a !important;
  }
}
</style>
