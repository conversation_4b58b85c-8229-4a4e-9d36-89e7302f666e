<script setup lang="ts">
import { Dialog, Toast } from '@rms/utils/src';
import { ref, watch } from 'vue';

const recordRef = ref<UniApp.UI.Popup | null>(null);

interface IProps {
  modelValue: boolean;
}

interface IEmits {
  (event: 'update:modelValue', e: boolean): void;
  (event: 'end', e: string): void;
}

const textContent = ref('');

const props = defineProps<IProps>();

const emits = defineEmits<IEmits>();

watch(
  () => props.modelValue,
  (val) => {
    if (val) {
      audioStart();
      recordRef.value?.open();
    }
  },
);

const close = () => {
  recordRef.value?.close();
  emits('update:modelValue', false);
  emits('end', textContent.value);
};

const startTime = ref(0);
const isStarting = ref(false);
const isRecording = ref(false);
const audioStart = () => {
  isStarting.value = true;
  uni.$wx.startRecord({
    success: () => {
      setStatus('ing');
      startTime.value = new Date().getTime();
      if (!isStarting.value) {
        uni.$wx.stopRecord({
          complete: () => {
            isRecording.value = false;
            Toast.warning('停止录音');
          },
        });
      } else {
        isRecording.value = true;
        audioEnd();
      }
    },
    fail: (error: any) => {
      console.log(error, '开启录音失败');
    },
    cancel: () => {
      Toast.warning('用户拒绝授权录音');
    },
  });
};

const confirm = () => {
  close();
};

const reload = () => {
  textContent.value = '';
  audioStart();
};

const isCancel = ref(false);

const status = ref('init');
// const status = ref('end');

const setStatus = (val: string) => {
  status.value = val;
};
const localId = ref('');

const audioEnd = () => {
  uni.$wx.onVoiceRecordEnd({
    // 录音时间超过一分钟没有停止的时候会执行 complete 回调
    complete: (res: any) => {
      setStatus('end');
      Toast.warning('录音时间超过一分钟');
      localId.value = res.localId;
      setTimeout(() => {
        upVoice();
      }, 1000);
    },
  });
};

const upVoice = () => {
  uni.$wx.translateVoice({
    localId: localId.value, // 需要识别的音频的本地Id，由录音相关接口获得
    isShowProgressTips: 1, // 默认为1，显示进度提示
    success: (res: any) => {
      if (!res.translateResult) {
        isRecording.value = false;
        textContent.value = '';
      } else {
        textContent.value = res.translateResult.replace(/。/, '');
      }
    },
    fail: async (error) => {
      textContent.value = '';
      if (error.errMsg.includes('missing arguments')) {
        const isconfirm = await Dialog('没有检测到你的声音，大声一点，再试一次吧。', {
          title: '提示',
          showCancel: false,
        });
        if (isconfirm) {
          reload();
        }
      }
    },
  });
};

const cancel = () => {
  uni.$wx.stopRecord({
    success: () => {
      isRecording.value = false;
      localId.value = '';
    },
  });
};

const stop = () => {
  isStarting.value = false;
  const t = new Date().getTime();
  if (t - startTime.value < 600 || t - startTime.value > 80000) {
    cancel();
  } else {
    uni.$wx.stopRecord({
      success: (res: any) => {
        setStatus('end');
        console.log('停止录音 :>> ', res);
        localId.value = res.localId;
        upVoice();
      },
    });
  }
};
</script>

<template>
  <uni-popup ref="recordRef" class="w-screen h-screen recordCom" :mask-click="false">
    <view class="recordBox rounded px-5 pt-3 pb-5 bg-white relative">
      <view class="record_title text-center text-18">语音输入</view>

      <view class="absolute z-10 right-3 top-3.5" @click="close">
        <uni-icons type="closeempty" color="#333" size="20"></uni-icons>
      </view>
      <view v-if="status === 'ing'">
        <view class="text-center">
          <text class="text-16 block mt-5 mb-2">我在聆听...</text>
          <view class="flex items-center justify-center pt-8">
            <view class="bar hr hr1"></view>
            <view class="bar hr hr2"></view>
            <view class="bar hr hr3"></view>
            <view class="bar hr hr4"></view>
            <view class="bar hr hr5"></view>
            <view class="bar hr hr6"></view>
            <view class="bar hr hr7"></view>
            <view class="bar hr hr8"></view>
            <view class="bar hr hr9"></view>
            <view class="bar hr hr10"></view>
          </view>
        </view>
        <view class="w-full flex justify-center mt-12">
          <view class="w-1/2">
            <sc-button type="primary" :round="true" @click="stop">结束说话</sc-button>
          </view>
        </view>
      </view>
      <view v-if="status === 'end'">
        <view class="textbox">
          <text v-if="textContent" class="text-16 block mt-5 mb-2">{{ textContent }}</text>
        </view>
        <view class="w-full flex justify-center mt-12">
          <view class="w-1/2 mr-5">
            <sc-button type="primary" :round="true" @click="reload">再说一遍</sc-button>
          </view>
          <view class="w-1/2 ml-5">
            <sc-button type="primary" :round="true" @click="confirm">确认</sc-button>
          </view>
        </view>
      </view>
    </view>
  </uni-popup>
</template>

<style lang="scss" scoped>
.recordCom {
  .recordBox {
    width: 80vw;
  }
  .textbox {
    @apply overflow-y-scroll;
    max-height: 30vh;
  }
  .bar {
    animation: scaleAni 0.2s ease-in-out;
    -webkit-animation-iteration-count: infinite;
    animation-iteration-count: infinite;
    -webkit-animation-direction: alternate;
    animation-direction: alternate;
    background: #187cff;
    width: 3px;
    height: 8px;
    margin: 0 5px;
    display: inline-block;
    border: none;
    &.hr1 {
      animation-delay: -1s;
    }
    &.hr2 {
      animation-delay: -0.9s;
    }
    &.hr3 {
      animation-delay: -0.7s;
    }
    &.hr4 {
      animation-delay: -0.6s;
    }
    &.hr5 {
      animation-delay: -0.5s;
    }
    &.hr6 {
      animation-delay: -0.4s;
    }
    &.hr7 {
      animation-delay: -0.3s;
    }
    &.hr8 {
      animation-delay: -0.2s;
    }
    &.hr9 {
      animation-delay: -0.1s;
    }
  }

  @keyframes scaleAni {
    from {
      -webkit-transform: scaleY(1);
      transform: scaleY(1);
    }
    to {
      -webkit-transform: scaleY(4);
      transform: scaleY(4);
    }
  }
}
</style>
