<script setup lang="ts">
import { PatrolRecordFormItem } from '@/api';
import { addWatermarkAfterRead } from '@/pages/recycling/canvas';
import { IsPro, Toast } from '@/utils';
import { FormConfig } from '@rms/components/sc-form/sc-form.vue';
import { ChooseFile } from '@rms/components/sc-upload/type';
import { ref, watch } from 'vue';
import Recording from './Recording.vue';

const scFormRef = ref();

const props = defineProps<{
  item: PatrolRecordFormItem;
  index: number;
  projectName: string;
  disabled: boolean;
}>();

const emit = defineEmits<{
  update: [data: PatrolRecordFormItem];
  remove: [];
}>();

const itemData = ref<PatrolRecordFormItem>({ ...props.item });

const formConfig = ref<FormConfig['config']>({
  formAttrs: {
    labelWidth: '220rpx',
    fontSize: '15px',
  },
  data: [
    {
      prop: 'qualified',
      label: '',
      tag: {
        tagType: 'custom',
        attr: {
          className: 'w-full flex mb-2 py-2',
          required: true,
          disabled: props.disabled,
        },
      },
    },
    {
      prop: 'picture',
      label: '问题照片',
      isHide: computed(() => itemData.value.qualified === 1),
      tag: {
        tagType: 'upload',
        attr: {
          required: true,
          maxCount: 1,
          disabled: props.disabled,
          maxSize: 10 * 1024 * 1024,
          uploadType: IsPro ? 'oss' : 'server',
          sizeType: ['original', 'compressed'],
          autoUpload: false,
          afterRead: async (files: ChooseFile[]) => {
            // itemData.value.picture = files;
            // await addWatermarkAfterRead(files, {
            //   title: props.projectName,
            //   inspectName: '蒸桑拿',
            //   problemName: itemData.value.content,
            // });
            // console.log('2323', 2323);
            // itemData.value.picture.forEach((item) => {
            //   if (item.tempUrl) return;
            //   item.tempUrl = item.watermark;
            //   item.url = item.watermark;
            //   item.path = item.watermark;
            // });
            // const uploadComponent = scFormRef.value.startRef.picture;
            // console.log('uploadComponent', uploadComponent);
            // await uploadComponent.onUpload();
          },
        },
      },
      listeners: {
        oversize: () => {
          Toast.warning('上传文件大小不能超过10M！', {
            duration: 2000,
          });
        },
      },
    },
    {
      prop: 'memo',
      label: '问题说明',
      isHide: computed(() => itemData.value.qualified === 1),
      itemAttrs: {
        labelPosition: 'top',
      },
      tag: {
        tagType: 'component',
        attr: {
          disabled: props.disabled,
          required: true,
          rows: 4,
        },
      },
    },
  ],
  form: itemData,
  rules: [
    {
      name: 'picture',
      rules: [
        {
          required: true,
          errorMessage: '请选择问题照片',
        },
      ],
    },
    {
      name: 'memo',
      rules: [
        {
          required: true,
          errorMessage: '请输入问题说明',
        },
        {
          validateFunction: (_, value) => {
            return value?.every((item: any) => !!item);
          },
          errorMessage: '请输入问题说明',
        },
      ],
    },
  ],
});

// 监听数据变化，向父组件发送更新
watch(
  itemData,
  (newVal) => {
    emit('update', newVal);
  },
  { deep: true },
);

const recordShow = ref(false);
function openRecording() {
  recordShow.value = true;
}

const getContext = (str: string) => {
  itemData.value.memo += str;
};

defineExpose({
  scFormRef,
});
</script>

<template>
  <div class="basic-check-item mb-4 p-2 bg-white rounded-lg border border-gray-200">
    <div class="item-header mb-4">
      <sc-form ref="scFormRef" :config="formConfig" class="mt-3">
        <template #qualified>
          <div>
            <div class="flex items-center justify-between">
              <div class="font-bold">{{ itemData.name }}</div>
              <div v-show="!disabled">
                <wd-radio-group v-model="itemData.qualified" class="flex" shape="button">
                  <wd-radio :value="1">合格</wd-radio>
                  <wd-radio :value="0">不合格</wd-radio>
                </wd-radio-group>
              </div>
              <div
                v-show="disabled"
                :class="itemData.qualified === 1 ? 'text-green-500' : 'text-red-500'">
                {{ itemData.qualified === 1 ? '合格' : '不合格' }}
              </div>
            </div>
            <div class="text-gray-600 mt-1 text-sm leading-relaxed">
              {{ itemData.content }}({{ itemData.score }}分)
            </div>
          </div>
        </template>
        <template #memo>
          <div class="w-full border border-solid relative border-gray-200 rounded-lg">
            <wd-textarea v-model="itemData.memo" placeholder="请输入问题说明" :rows="4" />
            <div class="absolute right-2 bottom-0">
              <wd-button type="text" @click="openRecording">
                <uni-icons type="mic" size="20"></uni-icons>
              </wd-button>
            </div>
          </div>
        </template>
      </sc-form>
    </div>
    <Recording v-model="recordShow" @end="getContext"></Recording>
  </div>
</template>

<style lang="scss" scoped>
.basic-check-item {
  :deep(.wd-radio-group) {
    .wd-radio {
      display: block !important;
    }
    .wd-radio__label {
      height: 32px;
      padding-top: 0;
      padding-bottom: 0;
      display: flex;
      align-items: center;
    }
  }
}
</style>
