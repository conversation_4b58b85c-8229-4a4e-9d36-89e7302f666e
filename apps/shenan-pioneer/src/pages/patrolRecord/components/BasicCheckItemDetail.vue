<script setup lang="ts">
import { PatrolRecordFormItem, TableItem } from '@/api';
import { IsPro, previewImg, Toast } from '@/utils';
import { FormConfig } from '@rms/components/sc-form/sc-form.vue';
import { ref } from 'vue';

const props = defineProps<{
  item: Partial<TableItem>;
  isRectify?: boolean;
}>();
const emit = defineEmits<{
  update: [data: Partial<TableItem>];
  remove: [];
}>();
const itemData = ref<Partial<TableItem>>({ ...props.item });

watch(
  itemData,
  (newVal) => {
    console.log('newVal', newVal);
    emit('update', newVal);
  },
  { deep: true },
);

const scFormRef = ref();

const formConfig = ref<FormConfig['config']>({
  formAttrs: {
    labelWidth: '220rpx',
    fontSize: '15px',
  },
  data: [
    {
      prop: 'rectifiedPictureList',
      label: '整改后照片',
      tag: {
        tagType: 'upload',
        attr: {
          required: true,
          maxCount: 1,
          maxSize: 10 * 1024 * 1024,
          uploadType: IsPro ? 'oss' : 'server',
          sizeType: ['original', 'compressed'],
          autoUpload: false,
        },
      },
      listeners: {
        oversize: () => {
          Toast.warning('上传文件大小不能超过10M！', {
            duration: 2000,
          });
        },
      },
    },
  ],
  form: itemData,
  rules: [
    {
      name: 'rectifiedPictureList',
      rules: [
        {
          required: true,
          errorMessage: '请选择问题照片',
        },
      ],
    },
  ],
});

defineExpose({
  scFormRef,
});
</script>

<template>
  <div
    class="basic-check-item mb-4 p-4 pb-1 bg-white rounded-lg border border-solid border-gray-200">
    <div class="item-header mb-4">
      <div class="flex items-center justify-between">
        <div class="font-bold">{{ itemData.name }}</div>
        <div :class="itemData.qualified === 1 ? 'text-green-500' : 'text-red-500'">
          {{ itemData.qualified === 1 ? '合格' : '不合格' }}
        </div>
      </div>
      <div>
        <div class="text-gray-600 mt-1 text-sm leading-relaxed">
          {{ itemData.content }}({{ itemData.score }}分)
        </div>
      </div>
      <div v-if="itemData.pictureUrl && itemData.qualified === 0" class="flex mt-2">
        <div class="mr-2">问题照片</div>
        <img
          class="w-[100px] h-[100px] rounded-lg drop-shadow-lg mt-2"
          mode="aspectFit"
          :src="itemData.pictureUrl"
          alt=""
          @click="previewImg([itemData.pictureUrl])" />
      </div>

      <div v-if="itemData.qualified === 0" class="flex mt-2">
        <div class="mr-2">问题说明</div>
        <div class="text-gray-400">{{ itemData.memo || '无' }}</div>
      </div>
      <div v-if="itemData.rectifiedPictureUrl && itemData.qualified === 0" class="flex mt-2">
        <div class="mr-2">整改后照片</div>
        <img
          class="w-[100px] h-[100px] rounded-lg drop-shadow-lg mt-2"
          mode="aspectFit"
          :src="itemData.rectifiedPictureUrl"
          alt=""
          @click="previewImg([itemData.rectifiedPictureUrl])" />
      </div>
      <sc-form v-if="isRectify" ref="scFormRef" :config="formConfig" class="mt-3"></sc-form>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.basic-check-item {
}
</style>
