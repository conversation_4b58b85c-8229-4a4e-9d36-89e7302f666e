<script setup lang="ts">
import { ref, watch } from 'vue';
import BasicCheckItem from './BasicCheckItem.vue';
import { PatrolRecordFormItem } from '@/api';

interface PatrolRecordFormData {
  id: string;
  name: string;
  checkItems: PatrolRecordFormItem[];
}

const props = defineProps<{
  form: PatrolRecordFormData;
  projectName: string;
}>();

const emit = defineEmits<{
  update: [data: PatrolRecordFormData];
}>();

const formData = ref<PatrolRecordFormData>({ ...props.form });

// 添加BasicCheckItem的ref数组
const basicCheckItemRefs = ref<any[]>([]);

// 监听表单数据变化，向父组件发送更新
watch(
  formData,
  (newVal) => {
    console.log('newVal', newVal);
    emit('update', newVal);
  },
  { deep: true },
);

// 处理基础检查项数据变化
function onCheckItemUpdate(index: number, itemData: PatrolRecordFormItem) {
  formData.value.checkItems[index] = itemData;
}

defineExpose({
  basicCheckItemRefs,
});
</script>

<template>
  <div class="patrolRecord-form mb-4 p-4 bg-gray-50 rounded-lg">
    <div class="form-header mb-2">
      <div class="text-lg font-semibold text-gray-800">
        {{ formData.name }}
      </div>
    </div>

    <div class="check-items">
      <BasicCheckItem
        v-for="(item, index) in formData.checkItems"
        :key="item.id"
        :ref="
          (el) => {
            basicCheckItemRefs[index] = el;
          }
        "
        :item="item"
        :index="index"
        :project-name="projectName"
        :disabled="false"
        @update="onCheckItemUpdate(index, $event)" />
    </div>
  </div>
</template>

<style lang="scss" scoped>
.patrolRecord-form {
  border: 1px solid #e5e7eb;

  .form-header {
    border-bottom: 1px solid #f3f4f6;
  }
}
</style>
