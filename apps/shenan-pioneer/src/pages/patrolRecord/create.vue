<script setup lang="ts">
import {
  ApiGetPatrolCreate,
  ApiGetPatrolInfoList,
  ApiGetPatrolItemList,
  ApiGetPatrolTable,
  ApiGetRelateMember,
  ApiUpdateProjectStatus,
  PatrolRecordFormItem,
  RelateMember,
  ResPatrolTableItem,
} from '@/api';
import { Dialog, exceptionToast, Loading, Navigator, Toast, UserInfo, utilsOss } from '@/utils';
import { FormConfig } from '@rms/components/sc-form/sc-form.vue';
import { Dictionary } from '@rms/types';
import {
  StorageGetPatrolRecordMembers,
  StorageSetPatrolRecordMembers,
  StorageSetPatrolRecordRefresh,
} from '@/storage/patrolRecord';
import PatrolRecordForm from './components/PatrolRecordForm.vue';
import { patrolResultStatusList } from '@/config/patrolRecord';
import { addWatermarkAfterRead } from '../recycling/canvas';

interface SelectItem {
  id: string;
  name: string;
  disabled?: boolean;
}

interface ConfirmItem {
  value: string[];
  selectedItems: SelectItem[];
}

const props = defineProps<{
  title: string;
  id: string;
}>();

const showPopup = ref(false);

const currentpatrolResultStatusList = ref([...patrolResultStatusList]);

// 巡查表选项
const PatrolRecordForms = ref<ResPatrolTableItem[]>([]);

const selectedForms = ref<string[]>([]);
const selectedFormsOld = ref<string[]>([]); // 保存旧值
const memberList = ref<SelectItem[]>([]);

const formData = ref<any>({
  id: '',
  state: 1,
  memberId: '',
  tableId: '',
  tableIdTexts: '',
  defectiveNumber: 0,
  resultStatus: '',
  results: [],
  overtime: '',
});

// 表单列表数据 - 使用新的数据结构
const formListData = ref<
  Array<{
    id: string;
    name: string;
    checkItems: Array<PatrolRecordFormItem>;
  }>
>([]);

// 监听formListData变化，更新formData.results
watch(
  formListData,
  (newVal) => {
    formData.value.results = newVal;
  },
  { deep: true },
);

const isSubmit = ref(false);

const scFormRef = ref();
const patrolRecordFormRefs = ref<any[]>([]);

const selectRef = ref();
const selectMemberRef = ref();
const formConfig = ref<FormConfig['config']>({
  formAttrs: {
    labelWidth: '220rpx',
    fontSize: '15px',
    border: true,
  },
  data: [
    {
      prop: 'state',
      label: '工程状态',
      tag: {
        tagType: 'custom',
        attr: {
          className: 'w-full flex py-2',
          required: true,
        },
      },
    },
    {
      label: '本次巡查员',
      prop: 'memberIdTexts',
      isHide: computed(() => +formData.value.state !== 1),
      tag: {
        tagType: 'select',
        options: [],
        attr: {
          required: true,
          placeholder: '请选择本次巡查人员',
        },
      },
      listeners: {
        click: () => {
          selectMemberRef.value?.open();
        },
      },
    },
    {
      label: '本次巡查表',
      prop: 'tableIdTexts',
      isHide: computed(() => +formData.value.state !== 1),
      tag: {
        tagType: 'select',
        options: [],
        attr: {
          className: 'w-full flex py-2',
          iconName: 'i-ion-chevron-forward-outline',
          required: true,
          placeholder: '请选择巡查表',
        },
      },
      listeners: {
        click: () => {
          selectRef.value?.open();
        },
      },
    },
    {
      label: '',
      prop: 'results',
      isHide: computed(() => +formData.value.state !== 1 && formData.value.tableId),
      tag: {
        tagType: 'custom',
        options: [],
        attr: {
          className: 'w-full flex py-2',
          iconName: 'i-ion-chevron-forward-outline',
          required: true,
          placeholder: '请选择巡查表',
        },
      },
      listeners: {
        click: () => {
          showPopup.value = true;
        },
      },
    },
    {
      label: '本次问题项共',
      prop: 'defectiveNumber',
      isHide: computed(() => +formData.value.state !== 1),
      tag: {
        tagType: 'component',
        attr: {
          className: 'border-b border-solid border-gray-200',
          disabled: true,
          required: true,
          placeholder: '',
        },
      },
    },
    {
      label: '本次处理结果',
      prop: 'resultStatus',
      isHide: computed(() => +formData.value.state !== 1),
      tag: {
        tagType: 'picker',
        options: currentpatrolResultStatusList.value,
        attr: {
          required: true,
          disabled: computed(() => formData.value.defectiveNumber === 0),
          placeholder: '请输入本次处理结果',
        },
      },
    },
    {
      label: '限整改期限',
      prop: 'overtime',
      isHide: computed(
        () => +formData.value.state !== 1 || [0, 2].includes(formData.value.resultStatus),
      ),
      tag: {
        tagType: 'date-picker',
        attr: {
          type: 'date',
          required: true,
          start: new Date().getTime(),
          placeholder: '请选择限整改期限',
        },
      },
    },
  ],
  form: formData,
  rules: [
    {
      name: 'state',
      rules: [
        {
          required: true,
          errorMessage: '请选择工程状态',
        },
      ],
    },
    {
      name: 'memberIdTexts',
      rules: [
        {
          required: true,
          errorMessage: '请选择本次巡查员',
        },
      ],
    },
    {
      name: 'tableIdTexts',
      rules: [
        {
          required: true,
          errorMessage: '请选择本次巡查表',
        },
      ],
    },
    {
      name: 'resultStatus',
      rules: [
        {
          required: true,
          errorMessage: '请选择本次处理结果',
        },
      ],
    },
    {
      name: 'overtime',
      rules: [
        {
          required: true,
          errorMessage: '请选择限整改期限',
        },
      ],
    },
  ],
});

// 处理巡查表数据更新
function onFormUpdate(index: number, formDataItem: any) {
  formListData.value[index] = formDataItem;
  formData.value.results = formListData.value;
}

watch(
  () => formListData.value,
  () => {
    const cnt = formListData.value.flatMap((item) =>
      item.checkItems.filter((checkItem) => checkItem.qualified !== 1),
    );
    formData.value.defectiveNumber = cnt.length;
    if (formData.value.defectiveNumber === 0) {
      formData.value.resultStatus = 0;
    } else if (formData.value.resultStatus === 0) {
      formData.value.resultStatus = '';
    }
    if (formData.value.defectiveNumber === 0) {
      currentpatrolResultStatusList.value = [...patrolResultStatusList];
    } else {
      currentpatrolResultStatusList.value = [...patrolResultStatusList].filter(
        (item) => item.id !== 0,
      );
    }
    const resultStatusItem = formConfig.value.data.find((item) => item.prop === 'resultStatus');
    if (resultStatusItem) {
      resultStatusItem.tag.options = currentpatrolResultStatusList.value;
    }
  },
  {
    immediate: true,
    deep: true,
  },
);

async function getPatrolInfo(formValue: string): Promise<PatrolRecordFormItem[]> {
  const form = {
    qualified: 1,
    picture: [],
    memo: '',
  };
  const info = await ApiGetPatrolInfoList(formValue);
  if (info.length) {
    const list = await ApiGetPatrolItemList(formValue);
    return info
      .map((item) => {
        const matchedItem = list.find((el) => el.id === item.itemId);
        return {
          ...item,
          ...form,
          name: matchedItem ? matchedItem.name : undefined, // 如果匹配到就加 name，否则 undefined
          createdAt: item.createdAt,
          matchedItemAt: matchedItem && matchedItem.createdAt,
        } as unknown as PatrolRecordFormItem;
      })
      .filter((item) => item.matchedItemAt)
      .sort((b, a) => b.createdAt - a.createdAt)
      .sort((b, a) => b.matchedItemAt - a.matchedItemAt);
  }
  return info as unknown as PatrolRecordFormItem[];
}

function onConfirmMember(e: ConfirmItem) {
  formData.value.memberId = e.value;
  formData.value.memberIdTexts = e.selectedItems.map((item) => item.name).join(',');
}

// 确认选择巡查表
async function onConfirmForms(e: ConfirmItem) {
  formData.value.tableId = e.value;
  formData.value.tableIdTexts = e.selectedItems.map((item) => item.name).join(',');
  selectedForms.value = e.value;
  // 找出新增的值（新值中存在但旧值中不存在的）
  const newValues = selectedForms.value.filter((value) => !selectedFormsOld.value.includes(value));

  const deleteValues = selectedFormsOld.value.filter(
    (value) => !selectedForms.value.includes(value),
  );
  if (deleteValues.length) {
    for (let i = formListData.value.length - 1; i >= 0; i--) {
      if (deleteValues.includes(formListData.value[i].id)) {
        formListData.value.splice(i, 1);
      }
    }
  }
  // 只对新值调用getPatrolInfo
  for await (const formValue of newValues) {
    const infoList = await getPatrolInfo(formValue);
    const form = PatrolRecordForms.value.find((f) => f.id === formValue);
    formListData.value.push({
      id: formValue,
      name: form?.name || '',
      checkItems: infoList,
    });
  }

  // 更新旧值
  selectedFormsOld.value = [...selectedForms.value];

  showPopup.value = false;
}

function handleSuccess() {
  // 设置刷新标记，通知相关页面刷新数据
  StorageSetPatrolRecordRefresh();
  StorageSetPatrolRecordMembers({
    memberId: formData.value.memberId,
    memberTexts: formData.value.memberIdTexts,
  });
  Toast.success('提交成功', {
    complete: () => {
      Navigator.replace('/pages/index/index');
    },
  });
}

async function toFinish() {
  const flag = await Dialog('未到工程结束时间，是否提前施工完成?');
  if (!flag) return;

  try {
    await ApiUpdateProjectStatus({ id: props.id, status: 2 });
    handleSuccess();
  } catch (error) {
    exceptionToast(error, '');
  }
}

async function onSubmit() {
  if (isSubmit.value) return;

  if (formData.value.state !== 1) {
    isSubmit.value = true;
    await toFinish();
    return;
  }

  const flag = await scFormRef.value.validate();
  if (!flag) return;
  const results: Dictionary = {};

  // 验证表单列表
  for (let i = 0; i < formListData.value.length; i++) {
    const form = formListData.value[i];
    for (let j = 0; j < form.checkItems.length; j++) {
      const item = form.checkItems[j];
      if (item.qualified !== 1) {
        if (!item.picture || item.picture.length === 0) {
          Toast.error('请上传问题照片');
          return;
        }
        if (!item.memo.trim()) {
          Toast.error('请填写问题说明');
          return;
        }
      }
    }
  }
  isSubmit.value = true;

  // 遍历results对象，获取qualified为1的项目的upload实例
  for (let i = 0; i < formListData.value.length; i++) {
    const form = formListData.value[i];
    if (!results[form.id]) {
      results[form.id] = [];
    }
    for (let j = 0; j < form.checkItems.length; j++) {
      const item = form.checkItems[j];
      const resultItem: Dictionary = {
        id: item.id,
        qualified: item.qualified,
        score: item.score,
      };

      // 如果qualified为1，获取upload实例
      if (item.qualified !== 1) {
        const patrolRecordFormRef = patrolRecordFormRefs.value[i];

        if (
          patrolRecordFormRef.basicCheckItemRefs[j] &&
          patrolRecordFormRef.basicCheckItemRefs[j].scFormRef.startRef.picture
        ) {
          // eslint-disable-next-line no-await-in-loop
          await addWatermarkAfterRead(item.picture, {
            title: props.title,
            patrolRecordText1: `巡查人员：${formData.value.memberIdTexts}`,
            patrolRecordText2: `问题项：【${item.name}】${item.content}不合格`,
          });
          // eslint-disable-next-line no-await-in-loop
          await patrolRecordFormRef.basicCheckItemRefs[j].scFormRef.startRef.picture.onUpload();
        }
      }

      if (item.qualified !== 1) {
        resultItem.picture = item.picture.map((picItem: any) => picItem.id).join(',');
        resultItem.memo = item.memo;
      }
      results[form.id].push(resultItem);
    }
  }

  submitForm(results);
}

async function submitForm(results: Dictionary) {
  try {
    const formParams = {
      memberId: formData.value.memberId.join(','),
      projectId: props.id,
      results,
      resultStatus: formData.value.resultStatus,
      tableId: formData.value.tableId.join(','),
      overtime: ![0, 2].includes(formData.value.resultStatus) ? formData.value.overtime : '',
    };
    await ApiGetPatrolCreate(formParams);
    handleSuccess();
  } catch (error) {
    exceptionToast(error, '提交失败');
    isSubmit.value = false;
  }
}

async function getMemberList() {
  const list: RelateMember[] = await ApiGetRelateMember({
    typeId: '6',
    projectId: props.id,
  });
  const userInfo = UserInfo.getUserInfo()!;
  memberList.value = list.map((item) => ({
    id: item.memberId,
    name: item.realname,
    disabled: item.userId === userInfo.id,
  }));

  const selectItem = memberList.value.find((item) => item.disabled);
  if (selectItem) {
    onConfirmMember({
      value: [selectItem.id],
      selectedItems: memberList.value.filter((item) => item.disabled),
    });
  }
  const storageMembers = StorageGetPatrolRecordMembers();
  if (storageMembers) {
    formData.value.memberId = storageMembers.memberId;
    formData.value.memberIdTexts = storageMembers.memberTexts;
  }
}
onMounted(async () => {
  try {
    Loading.show();
    await getMemberList();
    const data = await ApiGetPatrolTable();
    PatrolRecordForms.value = data;
  } catch (error) {
    exceptionToast(error, '');
  } finally {
    Loading.hide();
  }
});

function onGotoDetail() {
  Navigator.push('/pages/engineering/detail', { id: props.id });
}

function onGotoHistory() {
  Navigator.push('/pages/patrolRecord/list', { projectId: props.id });
}

onLoad(async (options) => {
  if (!options?.id) {
    Toast.warning('当前没有工程项目');
    Navigator.back();
    return;
  }
  formData.value.id = options?.id || '';
});
</script>

<template>
  <div class="w-full min-h-screen bg-base box-border relative pb-16">
    <div class="p-3 title-bg-blue items-center">
      <div class="flex border-b pb-2 border-solid border-white border-t-0 border-l-0 border-r-0">
        <wd-icon name="app" color="#fff" size="22px" class="mr-2"></wd-icon>
        <div class="text-16 text-white">{{ title }}</div>
      </div>
      <div class="flex justify-around mt-3 text-white">
        <div class="flex items-center" @click="onGotoDetail">
          <wd-icon name="search" class="mr-2" size="18px"></wd-icon>
          工程详情
        </div>
        <div class="flex items-center" @click="onGotoHistory">
          <wd-icon name="search" class="mr-2" size="18px"></wd-icon>
          历史巡查记录
        </div>
      </div>
    </div>
    <div class="mt-3 p-3 bg-white px-3 pt-4 rounded-md mb-4">
      <sc-form ref="scFormRef" :config="formConfig" class="mt-3">
        <template #state>
          <div class="w-full flex flex-row items-center mb-5">
            <div class="uni-forms-item__label flex items-center">
              <uni-text>
                <span class="text-[#dd524d] font-bold">*</span>
              </uni-text>
              <uni-text>
                <span>工程状态</span>
              </uni-text>
            </div>
            <div>
              <wd-radio-group v-model="formData.state" shape="dot" inline>
                <wd-radio :value="1">施工中</wd-radio>
                <wd-radio :value="2">已结束</wd-radio>
              </wd-radio-group>
            </div>
          </div>
        </template>
        <template #installCnt>
          <sc-number-box v-model="formData.installCnt" :min="1" class="pr-1" />
        </template>
        <template #defectiveNumber>
          <div class="flex items-center">
            <wd-input
              v-model="formData.defectiveNumber"
              disabled
              placeholder=""
              :min="1"
              class="pr-1" />
            个
          </div>
        </template>
        <template #results>
          <div class="form-list-container">
            <PatrolRecordForm
              v-for="(form, index) in formListData"
              :key="form.id"
              :ref="
                (el) => {
                  patrolRecordFormRefs[index] = el;
                }
              "
              :form="form"
              :project-name="title"
              @update="(data) => onFormUpdate(index, data)" />
          </div>
        </template>
      </sc-form>
    </div>

    <sc-button-fixed class="pt-5 box-border z-10">
      <div class="w-full flex items-center justify-center">
        <wd-button
          :style="{ opacity: !isSubmit ? 1 : 0.5 }"
          custom-class="!rounded-md !w-full"
          size="large"
          type="primary"
          @click="onSubmit">
          提交
        </wd-button>
      </div>
    </sc-button-fixed>
    <wd-select-picker
      ref="selectRef"
      :model-value="formData.tableId"
      label=""
      :z-index="9999999999"
      type="checkbox"
      value-key="id"
      label-key="name"
      title="选择巡查表"
      use-default-slot
      safe-area-inset-bottom
      confirm-button-text="确定"
      :columns="PatrolRecordForms"
      @confirm="onConfirmForms">
      <div></div>
    </wd-select-picker>
    <wd-select-picker
      ref="selectMemberRef"
      :model-value="formData.memberId"
      label=""
      :z-index="9999999999"
      type="checkbox"
      value-key="id"
      label-key="name"
      title="选择巡查员"
      use-default-slot
      safe-area-inset-bottom
      confirm-button-text="确定"
      :columns="memberList"
      @confirm="onConfirmMember">
      <div></div>
    </wd-select-picker>
  </div>
</template>

<style lang="scss" scoped>
.title-bg-blue {
  background: #057cb4;
}

.form-list-container {
  padding: 16px 0;
}
:deep(.wd-checkbox) {
  display: flex !important;
  align-items: center !important;
  .wd-checkbox__txt {
    white-space: normal !important;
  }
  .wd-checkbox__shape {
    display: block !important;
    margin-top: 0px;
  }
  .wd-checkbox__label {
    flex: 1;
  }
}
.popup-content {
  background: white;
  border-radius: 12px 12px 0 0;

  .popup-header {
    border-bottom: 1px solid #e5e7eb;
    padding-bottom: 12px;
  }

  .popup-body {
    padding: 16px 0;
  }
}
</style>

<style lang="scss"></style>
