<script setup lang="ts">
import { exception<PERSON><PERSON><PERSON>, Navigator, Toast } from '@/utils';
import { ApiReCheckRectify } from '@/api';
import { StorageSetPatrolRecordRefresh } from '@/storage/patrolRecord';

const props = defineProps<{
  id: string;
}>();

const form = ref({
  isPassRecheck: '',
  recheckMemo: '',
});

const isSubmit = ref(false);

// 上传整改结果
async function onSubmit() {
  if (!form.value.isPassRecheck) {
    Toast.error('请选择复查结果');
    return;
  }
  if (isSubmit.value) {
    return;
  }
  isSubmit.value = true;
  try {
    const params =
      Number(form.value.isPassRecheck) === 0
        ? {
            recheckMemo: form.value.recheckMemo,
          }
        : {};
    await ApiReCheckRectify({
      id: props.id,
      isPassRecheck: Number(form.value.isPassRecheck),
      ...params,
    });
    // 设置刷新标记，通知相关页面刷新数据
    StorageSetPatrolRecordRefresh();

    Toast.success('提交成功', {
      complete: () => {
        Navigator.replace('/pages/patrolRecord/list?state=2');
      },
    });
  } catch (error) {
    exceptionHandler(error);
  } finally {
    isSubmit.value = false;
  }
}
</script>

<template>
  <div class="min-h-screen patrolRecord-detail-page bg-white pb-10">
    <div class="p-4">
      <div class="text-18">现场复查</div>
    </div>

    <div class="px-10">
      <wd-radio-group v-model="form.isPassRecheck" shape="dot">
        <wd-radio value="1">已整改，复查合格</wd-radio>
        <wd-radio value="0">未整改，复查不合格驳回重新整改</wd-radio>
      </wd-radio-group>
      <div v-if="form.isPassRecheck === '0'" class="mt-3">
        <wd-textarea
          v-model="form.recheckMemo"
          class="border border-solid"
          placeholder="复查不合格原因说明"
          show-word-limit />
      </div>
    </div>
    <!-- 底部按钮 -->
    <sc-button-fixed class="pt-5 box-border z-10">
      <div class="w-full flex items-center justify-center">
        <wd-button custom-class="!rounded-md !w-full" size="large" type="primary" @click="onSubmit">
          提交复查结果
        </wd-button>
      </div>
    </sc-button-fixed>
  </div>
</template>

<style lang="scss" scoped></style>
