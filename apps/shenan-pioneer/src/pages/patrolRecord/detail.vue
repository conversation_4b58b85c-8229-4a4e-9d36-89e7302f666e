<script setup lang="ts">
import { exceptionHandler, FormatDate, FormatDateTime, Navigator, Toast } from '@/utils';
import BasicCheckItemDetail from './components/BasicCheckItemDetail.vue';
import {
  ApiGetPatrolDetail,
  ApiGetPatrolFlow,
  DataItem,
  DetailData,
  RecordTableItem,
  ResPatrolFlow,
  TableItem,
} from '@/api';
import { ServiceImgByIds } from '@rms/service/src';
import { patrolResultStatusList, patrolRecordFlowList } from '@/config/patrolRecord';
import useRoleStore, { RoleInfo } from '@/hooks/useRole';
import { ApiUpdateMessageStatus } from '@/api/modules/message';
import { StorageSetMessageRefresh } from '@/storage/message';

interface ProcessStepItem {
  title: string;
  time?: string;
  description?: string;
  result?: string;
  memo?: string;
  status?: 'finished' | 'process' | 'error';
}

const props = defineProps<{
  id: string;
  isShowButton: string;
}>();

const detailData = ref<DetailData>();

const projectId = ref('');

const resultIsExpanded = ref(false);

// 处理流程数据
const processSteps = ref<ProcessStepItem[]>([]);

function onGotoDetail() {
  Navigator.push('/pages/engineering/detail', { id: detailData.value?.projectId });
}

function onGotoHistory() {
  Navigator.push('/pages/patrolRecord/list', {
    projectId: detailData.value?.projectId,
  });
}

// 切换巡查表展开状态
function toggleForm(form: DataItem) {
  form.isExpanded = !form.isExpanded;
}

// 上传整改结果
function gotoRectify() {
  Navigator.replace('/pages/patrolRecord/rectify', {
    id: props.id,
  });
}

// 填写整改结果
function gotoRecheck() {
  Navigator.replace('/pages/patrolRecord/recheck', {
    id: props.id,
  });
}

const dataList = ref<DataItem[]>([]);
const resultDataList = ref<Partial<TableItem>[]>([]);
async function getTableList(list: RecordTableItem[]) {
  const datas: DataItem[] = [];
  const imgIds: string[] = [];
  resultDataList.value = [];
  dataList.value = [];
  list.forEach((item) => {
    const { name, patrolItemSnapshots } = item.patrolTableSnapshot;
    const dataItem: DataItem = {
      name,
      isExpanded: false,
      tableList: [],
    };

    if (patrolItemSnapshots && patrolItemSnapshots.length) {
      patrolItemSnapshots.forEach((items) => {
        const { name: itemName, id, patrolItemInfoSnapshotList } = items;

        if (patrolItemInfoSnapshotList && patrolItemInfoSnapshotList.length) {
          patrolItemInfoSnapshotList.forEach((checkItem) => {
            const tableItem: Partial<TableItem> = {};
            tableItem.name = itemName;
            tableItem.itemId = id;
            tableItem.score = checkItem.score;
            tableItem.colspanNum = patrolItemInfoSnapshotList.length;
            const { content, score } = checkItem;
            const { qualified, picture, rectifiedPicture, memo } = checkItem.data;
            tableItem.content = content;
            tableItem.qualifiedText = checkItem.data.qualified === 1 ? '合格' : '不合格';
            tableItem.memo = memo;
            tableItem.picture = picture;
            tableItem.rectifiedPicture = rectifiedPicture;
            tableItem.qualified = qualified;
            dataItem.tableList.push(tableItem);

            if (picture) {
              imgIds.push(picture);
            }
            if (rectifiedPicture) {
              imgIds.push(rectifiedPicture);
            }
          });
        }
      });
    }
    datas.push(dataItem);
  });
  if (imgIds.length) {
    const imgs = await ServiceImgByIds(imgIds);
    datas.forEach((item) => {
      if (item.tableList && item.tableList.length) {
        item.tableList.forEach((tableItem) => {
          if (tableItem.picture) {
            tableItem.pictureUrl = imgs[tableItem.picture]?.remoteUrl;
          }
          if (tableItem.rectifiedPicture) {
            tableItem.rectifiedPictureUrl = imgs[tableItem.rectifiedPicture]?.remoteUrl;
          }
          if (!tableItem.qualified && tableItem.rectifiedPicture) {
            resultDataList.value.push(tableItem);
          }
        });
      }
    });
  }
  dataList.value = datas;
}

const { roleInfo } = useRoleStore();

function getDescription(flow: number, name: string) {
  if (flow === 1) {
    return `巡查人员：${name}`;
  }
  if (flow === 2) {
    return `整改人员：${name}`;
  }
  if (flow === 3 || flow === 4) {
    return `巡查人员：${name}`;
  }
  return '';
}

async function init() {
  const res = await ApiGetPatrolDetail(props.id);
  const flowRes = await ApiGetPatrolFlow(props.id);
  processSteps.value = [];
  if (flowRes.length) {
    flowRes.forEach((item) => {
      const { flow, createdAt } = item;
      const { value, id } = patrolRecordFlowList.find((i) => i.id === flow) || {};
      const description = getDescription(flow, item.realname);

      processSteps.value.push({
        ...item,
        title: value || '',
        time: FormatDateTime(createdAt),
        description,
        status: 'finished',
        result:
          flow === 3 ? `已整改，复查合格` : flow === 4 ? `未整改，复查不合格驳回重新整改` : '',
      });
    });
    if (res.state === 1) {
      processSteps.value.push({
        title: '整改反馈',
        status: 'process',
      });
    }
    if (res.state === 2) {
      processSteps.value.push({
        title: '整改复查',
        status: 'process',
      });
    }
  }
  detailData.value = res;
  getTableList(res.recordTables);
}

onShow(async () => {
  init();
});

onLoad((options) => {
  if (options?.msgId) {
    try {
      ApiUpdateMessageStatus({ id: options.msgId, status: 1 });
      StorageSetMessageRefresh();
    } catch (error) {
      exceptionHandler(error);
    }
  }
});
</script>

<template>
  <div v-if="detailData" class="min-h-screen patrolRecord-detail-page bg-gray-50 pb-10">
    <!-- 头部 -->
    <div class="p-3 title-bg-blue items-center">
      <div class="flex border-b pb-2 border-solid border-white border-t-0 border-l-0 border-r-0">
        <wd-icon name="app" color="#fff" size="22px" class="mr-2"></wd-icon>
        <div class="text-16 text-white">{{ detailData.projectName }}</div>
      </div>
      <div class="flex justify-around mt-3 text-white">
        <div class="flex items-center" @click="onGotoDetail">
          <wd-icon name="search" class="mr-2" size="18px"></wd-icon>
          工程详情
        </div>
        <div class="flex items-center" @click="onGotoHistory">
          <wd-icon name="search" class="mr-2" size="18px"></wd-icon>
          历史巡查记录
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div v-if="dataList.length" class="p-4 space-y-4">
      <!-- 本次巡查表 -->
      <div v-for="form in dataList" :key="form.name" class="bg-white rounded-lg transition-all">
        <div class="p-4 border-b border-gray-100">
          <div class="flex justify-between items-center">
            <div class="font-semibold text-gray-900 mr-3">本次巡查表</div>
            <div class="font-semibold text-blue-400 flex-1">{{ form.name }}</div>
          </div>
        </div>

        <!-- 展开的内容 -->
        <div v-show="form.isExpanded" class="p-4">
          <div class="check-items space-y-3">
            <BasicCheckItemDetail
              v-for="(item, itemIndex) in form.tableList"
              :key="item.id"
              :item="item"
              :disabled="true"
              :index="itemIndex"
              project-name="工程巡查" />
          </div>
        </div>

        <!-- 展开/收起按钮 -->
        <div
          class="flex items-center justify-center py-3 cursor-pointer border-t border-gray-100"
          @click="toggleForm(form)">
          <wd-icon
            :name="form.isExpanded ? 'chevron-up' : 'chevron-down'"
            size="22px"
            class="text-blue-500" />
        </div>
      </div>

      <div v-if="resultDataList.length" class="bg-white rounded-lg px-4 pt-4 pb-2">
        <div class="text-lg font-semibold text-gray-900 border-b pb-2">整改结果</div>
        <div
          v-for="item in resultDataList"
          v-show="resultIsExpanded"
          :key="item.id"
          class="bg-white rounded-lg transition-all">
          <!-- 展开的内容 -->
          <div class="check-items space-y-3">
            <BasicCheckItemDetail
              :key="item.id"
              :item="item"
              :disabled="true"
              project-name="工程巡查" />
          </div>
        </div>

        <!-- 展开/收起按钮 -->
        <div
          class="flex items-center justify-center py-3 cursor-pointer border-t border-gray-100"
          @click="resultIsExpanded = !resultIsExpanded">
          <wd-icon
            :name="resultIsExpanded ? 'chevron-up' : 'chevron-down'"
            size="22px"
            class="text-blue-500" />
        </div>
      </div>

      <!-- 问题统计 -->
      <div v-if="detailData" class="bg-white rounded-lg p-4">
        <wd-cell title="本次问题项共" :value="`${detailData.defectiveNumber} 个`" />
        <wd-cell title="本次处理结果">
          <div
            :class="patrolResultStatusList.find((i) => i.id === +detailData.resultStatus)?.class">
            {{ patrolResultStatusList.find((i) => i.id === +detailData!.resultStatus)?.value }}
          </div>
        </wd-cell>
        <wd-cell v-if="[1, 3].includes(detailData.resultStatus)" title="限整改期限">
          <uni-text :class="detailData.isTimeout ? 'text-[#dd524d]' : ''">
            {{ FormatDate(detailData.overtime) }}
          </uni-text>
        </wd-cell>
      </div>

      <!-- 处理流程 -->
      <div class="bg-white rounded-lg p-4">
        <div class="text-lg font-semibold text-gray-900 mb-4 border-b pb-2">处理流程</div>
        <wd-steps :active="processSteps.length" :vertical="true" class="mt-4">
          <wd-step v-for="(step, index) in processSteps" :key="index" :status="step.status">
            <template #title>
              <div class="flex items-center">
                <div class="text-sm text-gray-500 mr-3 font-medium">{{ step.title }}</div>
                <div class="text-xs text-gray-500">{{ step.time }}</div>
              </div>
            </template>
            <template #description>
              <div>
                <div v-if="step.result" class="text-gray-500 mb-1">{{ step.result }}</div>
                <div v-if="step.memo" class="text-gray-500">{{ step.memo }}</div>
                <div class="text-gray-500 font-bold">{{ step.description }}</div>
              </div>
            </template>
          </wd-step>
        </wd-steps>
      </div>
    </div>

    <!-- 底部按钮 -->
    <sc-button-fixed v-if="props.isShowButton === 'true'" class="pt-5 box-border z-10">
      <div class="w-full flex items-center justify-center">
        <wd-button
          v-if="
            detailData.state === 1 && ['业主/建设方', '施工单位负责人'].includes(roleInfo?.roleName)
          "
          custom-class="!rounded-md !w-full"
          size="large"
          type="primary"
          @click="gotoRectify">
          上传整改结果
        </wd-button>
        <wd-button
          v-if="detailData.state === 2 && roleInfo?.roleName.includes('巡查人员')"
          custom-class="!rounded-md !w-full"
          size="large"
          type="primary"
          @click="gotoRecheck">
          填写复查结果
        </wd-button>
      </div>
    </sc-button-fixed>
  </div>
</template>

<style lang="scss" scoped>
.patrolRecord-detail-page {
  .title-bg-blue {
    background: #057cb4;
  }
  :deep(.wd-step__icon-outer) {
    color: #fff !important;
    background-color: #22b75a !important;
    border-color: #22b75a !important;
  }
  :deep(.wd-step__line) {
    background-color: #22b75a !important;
  }
}
</style>
