<script setup lang="ts">
import { ref } from 'vue';
import RecycleService from './components/RecycleService.vue';
import InstallService from './components/InstallService.vue';
import { StorageGetMonitorFlowRefresh } from '@/storage/monitor';

const activeTab = ref(0);
const tabs = [
  { name: '安装服务', value: 0 },
  { name: '回收服务', value: 1 },
];

const serviceRef =
  ref<(InstanceType<typeof InstallService> | InstanceType<typeof RecycleService>)[]>();

onLoad((options) => {
  activeTab.value = +(options?.type ?? 0);
});

onShow(() => {
  const needRefresh = StorageGetMonitorFlowRefresh();
  if (needRefresh) {
    serviceRef.value?.forEach((item) => {
      item.refresh();
    });
  }
});
</script>

<template>
  <wd-tabs v-model="activeTab" auto-line-width class="h-screen overflow-hidden">
    <wd-tab v-for="tab in tabs" :key="tab.value" :title="tab.name">
      <component :is="tab.value === 0 ? InstallService : RecycleService" ref="serviceRef" />
    </wd-tab>
  </wd-tabs>
</template>

<style lang="scss" scoped></style>
