<script setup lang="ts">
import { FormConfig } from '@rms/components/sc-form/sc-form.vue';
import { ref } from 'vue';
import {
  exceptionHandler,
  exceptionToast,
  Loading,
  Toast,
  Navigator,
  ValidatePhone,
  utilsOss,
} from '@/utils';
import Tab from './components/tab.vue';
import { previewFile } from '@/utils/file';
import { ApiQueryHandler } from '@shencom/api';
import {
  ApiGetMonitorOrderList,
  MonitorAppointBody,
  ApiMonitorOrderUpdate,
  ApiGetProjectShow,
  ApiGetProjectList,
} from '@/api';
import { Dictionary } from '@rms/types';
import { StorageSetMonitorFlowRefresh } from '@/storage/monitor';
import useRoleStore from '@/hooks/useRole';

const { roleInfo } = useRoleStore();

const showTab = ref(false);

const formRef = ref();

const formData = ref<MonitorAppointBody>({
  id: '',
  state: 1,
  contactName: '',
  contactMobile: '',
  reservationTime: '',
});

const rules = ref<FormConfig['config']['rules']>([
  {
    name: 'id',
    rules: [{ required: true, errorMessage: '请选择工程' }],
  },
  {
    name: 'contactName',
    rules: [{ required: true, errorMessage: '请输入联系人姓名' }],
  },
  {
    name: 'contactMobile',
    rules: [
      {
        required: true,
        errorMessage: '请输入手机号码',
      },
      {
        errorMessage: '请输入正确的联系方式',
        validateFunction: (_, value) => ValidatePhone(value),
      },
    ],
  },
  {
    name: 'reservationTime',
    rules: [{ required: true, errorMessage: '请输入期望上门时间' }],
  },
]);

const config = ref<FormConfig['config']>({
  form: formData,
  rules: rules.value,
  formAttrs: {
    labelWidth: '220rpx',
    fontSize: '15px',
    border: true,
  },
  data: [
    {
      prop: 'id',
      label: '预约工程',
      tag: {
        options: [],
        tagType: 'picker',
        attr: {
          clearable: true,
          required: true,
          placeholder: '请选择预约工程',
        },
      },
    },
    {
      prop: 'contactName',
      label: '联系人',
      tag: {
        tagType: 'text',
        attr: {
          required: true,
          placeholder: '请输入联系人姓名',
        },
      },
    },
    {
      prop: 'contactMobile',
      label: '联系方式',
      tag: {
        tagType: 'text',
        attr: {
          type: 'number',
          required: true,
          placeholder: '请输入联系方式',
        },
      },
    },
    {
      prop: 'reservationTime',
      label: '期望上门时间',
      tag: {
        tagType: 'date-picker',
        attr: {
          placeholder: '请选择期望上门时间',
          required: true,
          type: 'datetime',
          'hide-second': true,
          minDate: new Date().valueOf(),
        },
      },
    },
  ],
});

onLoad((options) => {
  formData.value.id = options?.id;
  showTab.value = !options?.id;
  getProjectOptions(options);
});

const projectList = ref<Dictionary[]>([]);

watch(
  () => formData.value.id,
  () => {
    getProjectDetail();
  },
);

async function getProjectOptions(options: Dictionary = {}) {
  try {
    const formItem = config.value.data.find((item) => item.prop === 'id');
    if (!formItem) return;

    const isLimitUser = ['施工单位负责人', '业主/建设方'].includes(roleInfo.roleName);

    formItem.tag.options = isLimitUser
      ? await getLimitUserProjectList(options.id)
      : await getMonitorProjectList(options.id);

    if (options.id) {
      getProjectDetail();
    }
  } catch (error) {
    exceptionHandler(error);
  }
}

async function getMonitorProjectList(projectId: string) {
  const query = projectId
    ? ApiQueryHandler(projectId, 'flowId', 'select')
    : ApiQueryHandler(6, 'flow', 'select');
  const res = await ApiGetMonitorOrderList({ query });

  projectList.value = res.content;

  return res.content.map((item) => ({
    id: item.flowId,
    value: item.projectName,
  }));
}

async function getLimitUserProjectList(projectId: string) {
  const query = projectId ? { flowId: projectId } : { flow: 6 };
  const res = await ApiGetProjectList({ ...query });

  projectList.value = res.content.map((v) => ({
    ...v,
    projectId: v.id,
  }));

  return res.content.map((item) => ({
    id: item.flowId,
    value: item.name,
  }));
}

async function getProjectDetail() {
  const { projectId } = projectList.value.find((item) => item.flowId === formData.value.id) || {};
  if (!projectId) {
    formData.value.contactName = '';
    formData.value.contactMobile = '';
    return;
  }

  const data = await ApiGetProjectShow(projectId);
  formData.value.contactName = data?.contractorCharger || '';
  formData.value.contactMobile = data?.contractorChargerMobile || '';
}

let isSubmit = false;

async function onSubmit() {
  if (isSubmit) return;

  const flag = await formRef.value.validate();
  if (!flag) return;

  isSubmit = true;

  try {
    Loading.show('提交中');

    await ApiMonitorOrderUpdate({
      ...formData.value,
      state: 1,
    });

    // 设置刷新标记，通知相关页面刷新数据
    StorageSetMonitorFlowRefresh();

    Toast.success('预约成功', {
      complete: () => {
        Navigator.replace('/pages/recycling/create-result', {
          time: formData.value.reservationTime,
          type: '回收',
        });
      },
    });
  } catch (error) {
    Loading.hide();
    exceptionToast(error, '预约失败，请重试');
  } finally {
    isSubmit = false;
  }
}

function onGotoHistory() {
  Navigator.push('/pages/recycling/list', { type: 1, tab: 1 });
}

function onPreview() {
  previewFile(`${utilsOss.ossPath}/files/小散工程监管解决方案（含报价）.pdf`);
}
</script>
<template>
  <view class="w-full min-h-screen p-3 bg-base box-border relative pb-16">
    <tab v-if="showTab" active="回收服务" />

    <view class="flex justify-end">
      <text class="text-blue underline" @click="onPreview">回收服务介绍</text>
    </view>

    <view class="mt-3 bg-white px-3 pt-4 rounded-md">
      <wd-steps align-center>
        <wd-step title="回收预约"></wd-step>
        <wd-step title="上门回收"></wd-step>
      </wd-steps>

      <sc-form ref="formRef" :config="config" class="mt-3"></sc-form>
    </view>

    <sc-button-fixed class="pt-5 box-border">
      <view class="w-full flex items-center justify-between">
        <!-- <wd-button custom-class="!mr-6" type="text" icon="list" @click="onGotoHistory">
          进度查询
        </wd-button> -->
        <wd-button
          :disabled="isSubmit"
          custom-class="!rounded-md !w-full"
          size="large"
          type="primary"
          @click="onSubmit">
          提交
        </wd-button>
      </view>
    </sc-button-fixed>
  </view>
</template>
