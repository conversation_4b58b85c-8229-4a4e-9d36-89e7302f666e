<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { Navigator } from '@/utils';
import { useList } from '@rms/components/sc-list/sc-list';
import { ApiGetMonitorOrderList, MonitorOrderListRes } from '@/api';
import { recycleStatus } from '@/config/engineering';
import { onLocation } from '@/utils/location';
import useRoleStore from '@/hooks/useRole';

const { roleInfo } = useRoleStore();

const filterOptions = [
  { label: '全部', value: 0 },
  { label: '待回收', value: 1 },
  { label: '已完成', value: 2 },
];
const curTab = ref(0);

onLoad((options) => {
  curTab.value = +(options?.tab || 0);
});

// 监听 tab 变化，重新获取数据
watch(curTab, () => {
  refresh();
});

function refresh() {
  getData(true, params.value);
}

const params = computed(() => ({
  recycleType: curTab.value,
  size: 10,
  // 安装工人/商务人员-1 ，  建设方（业主）/施工负责人-2
  queryType: ['施工单位负责人', '业主/建设方'].includes(roleInfo.roleName) ? 2 : 1,
}));

const { list, getData, listProps } = useList<MonitorOrderListRes[]>({
  request: ApiGetMonitorOrderList,
  params: params.value,
  immediate: false,
});

function onLogionSuccess() {
  refresh();
}

const handleCreate = () => {
  Navigator.push('/pages/recycling/create');
};

function handleCardClick(item: MonitorOrderListRes) {
  Navigator.push('/pages/recycling/detail', {
    id: item.id,
    flow: item.flow,
  });
}

function getStatusClass(status?: number): string {
  const classMap: { [key: number]: string } = {
    1: 'bg-orange-50 text-orange-600 border-orange-200',
    2: 'bg-green-50 text-green-600 border-green-200',
  };
  return (status && classMap[status]) || 'bg-gray-50 text-gray-600 border-gray-200';
}

defineExpose({
  refresh,
});
</script>

<template>
  <view class="h-full bg-base relative">
    <view class="absolute top-0 left-0 w-full z-10 px-5 pr-1 pt-3 pb-4 bg-white">
      <wd-radio-group
        v-model="curTab"
        shape="button"
        class="w-full flex justify-between"
        :style="{ '--wot-radio-button-min-width': '100%' }">
        <wd-radio v-for="opt in filterOptions" :key="opt.value" :value="opt.value" class="flex-1">
          {{ opt.label }}
        </wd-radio>
      </wd-radio-group>
    </view>

    <sc-list
      v-bind="listProps"
      class="!px-0"
      :height-class="
        roleInfo.roleName === '安装人员' ? 'h-[calc(100vh_-_100px)]' : 'h-[calc(100vh_-_40px)]'
      "
      empty-text="暂无此类工程"
      :local-refresh="true"
      @touchmove.stop
      @on-handle-upper-fn="getData(true)">
      <view class="h-16"></view>
      <view
        v-for="item in list"
        :key="item.id"
        class="bg-white rounded-lg shadow-sm mt-3 mx-3 p-3"
        @click="handleCardClick(item)">
        <view class="flex justify-between items-center font-semibold mb-3">
          <text class="text-gray-800">{{ item.projectName }}</text>
          <view class="bg-gray-100 px-2 rounded-md border border-gray-200">
            <text class="text-xs text-gray-500">{{ item.projectNumber }}</text>
          </view>
        </view>

        <view class="flex items-start justify-between text-gray-600 text-sm mb-3">
          <text>{{ item.projectAddress }}</text>
          <wd-icon
            name="location"
            size="16"
            class="text-blue-500 ml-1"
            @click.stop="onLocation(item)" />
        </view>

        <view class="text-sm">
          <text class="text-gray-700">当前进度：</text>
          <text
            :class="`inline-block px-3 py-0.5 rounded text-xs border ml-1.5 ${getStatusClass(
              item.recycleStatus,
            )}`">
            {{ recycleStatus[item.recycleStatus as keyof typeof recycleStatus] || '' }}
          </text>
        </view>
      </view>
    </sc-list>

    <sc-button-fixed v-if="roleInfo.roleName === '安装人员'" class="p-0">
      <view class="w-full">
        <wd-button block type="primary" custom-class="!rounded-sm !py-6" @click="handleCreate">
          发起预约
        </wd-button>
      </view>
    </sc-button-fixed>

    <sc-auth @success-once="onLogionSuccess"></sc-auth>
  </view>
</template>

<style lang="scss" scoped>
::v-deep {
  .sc-button-fixed-container {
    padding: 0;
  }
}
</style>
