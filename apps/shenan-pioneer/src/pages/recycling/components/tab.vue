<script setup lang="ts">
import { utilsOss, Navigator } from '@/utils';

const props = withDefaults(defineProps<{ active: string }>(), {
  active: '安装服务',
});

const tabs = {
  安装服务: {
    active: `${utilsOss.imgPath}/empty/img_install_active.png`,
    inactive: `${utilsOss.imgPath}/empty/img_install.png`,
    path: '/pages/engineering/appoint',
  },
  回收服务: {
    active: `${utilsOss.imgPath}/empty/img_recycle_active.png`,
    inactive: `${utilsOss.imgPath}/empty/img_recycle.png`,
    path: '/pages/recycling/create',
  },
};

function onTabClick(key: string, path: string) {
  if (key === props.active) return;

  uni.showModal({
    title: '提示',
    content: '跳转后已填写内容会清空，是否继续？',
    success: (res) => {
      if (res.confirm) {
        Navigator.replace(path);
      }
    },
  });
}
</script>

<template>
  <div class="grid grid-cols-2 gap-3 py-3">
    <div
      v-for="(item, key) in tabs"
      :key="key"
      class="flex-1 flex flex-col justify-center items-center px-3 py-4 rounded-md"
      :class="[props.active === key ? 'bg-white text-blue' : 'bg-gray-200 text-gray-400']"
      @click="onTabClick(key, item.path)">
      <img
        :src="props.active === key ? item.active : item.inactive"
        :alt="key"
        class="w-14 h-14 object-contain" />
      <div class="mt-2 text-base font-medium">{{ key }}</div>
    </div>
  </div>
</template>
