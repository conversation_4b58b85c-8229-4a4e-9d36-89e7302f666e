<script setup lang="ts">
import { Navigator, utilsOss } from '@/utils';
import { ref } from 'vue';

const imgPath = `${utilsOss.imgPath}/empty/img_appoint_success.png`;

const time = ref('');
const type = ref('');

onLoad((option) => {
  time.value = option?.time || '';
  type.value = option?.type || '';
});

function onGotoHistory() {
  Navigator.replace('/pages/recycling/list', { tab: 1, type: type.value === '安装' ? 0 : 1 });
}

function onGotoHome() {
  Navigator.replace('/pages/index/index');
}
</script>
<template>
  <view class="w-full h-screen p-5 box-border flex flex-col justify-center items-center">
    <image :src="imgPath" alt="success" class="w-25 h-25" mode="aspectFit" />
    <text class="text-3xl font-bold text-blue mt-3">预约成功</text>

    <view class="w-full text-sm text-gray-500 mt-10 text-center">
      <text>您已预约</text>
      <text class="text-blue px-1">{{ time }}</text>
      <text>上门{{ type }}监控，稍后{{ type }}人员会电话联系您，请保持电话畅通。</text>
    </view>

    <view class="mt-20 pb-10 w-full space-y-4">
      <wd-button
        custom-class="w-full !rounded-md"
        size="large"
        type="primary"
        @click="onGotoHistory">
        进度查询
      </wd-button>
      <wd-button custom-class="w-full !rounded-md" size="large" type="info" @click="onGotoHome">
        返回首页
      </wd-button>
    </view>
  </view>
</template>
<style lang="scss" scoped></style>
