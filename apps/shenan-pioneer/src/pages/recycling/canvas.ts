import Toast from '@rms/utils/src/toast';
import { ApiAMapGeocodeRegeo } from '@shencom/api';
import { Dictionary } from '@rms/types';
import { <PERSON>js, exceptionHandler, getLocationAdapter } from '@/utils';

// 处理图片选择后加水印
export async function addWatermarkAfterRead(
  files: Dictionary[],
  data: Dictionary,
): Promise<Dictionary[]> {
  const successFiles: Dictionary[] = [];
  const failedFiles: Dictionary[] = [];

  // 串行处理每个文件的水印添加
  /* eslint-disable no-await-in-loop */
  for await (const file of files) {
    try {
      if (file.file) {
        const watermarkedBase64 = await addWatermark(file.file, data);
        if (watermarkedBase64) {
          // file.tempUrl = watermarkedBase64;
          // file.url = watermarkedBase64;
          // file.path = watermarkedBase64;
          file.watermark = watermarkedBase64;
          file.file = new File([file.file.blob], file.file.name, {
            type: file.file.type,
            lastModified: file.file.lastModified,
            endings: file.file.endings,
          });
          file.blob = base64ToBlob(watermarkedBase64);
          file.file.blob = base64ToBlob(watermarkedBase64);
          successFiles.push(file);
        } else {
          failedFiles.push(file);
        }
      } else {
        failedFiles.push(file);
      }
    } catch (error) {
      console.error('水印添加失败:', error);
      failedFiles.push(file);
    }
  }
  /* eslint-enable no-await-in-loop */

  // 如果有失败的图片，提示用户
  if (failedFiles.length > 0) {
    Toast.warning(`${failedFiles.length} 张图片水印添加失败`, {
      duration: 2000,
    });
  }

  return successFiles;
}

async function getAddress() {
  try {
    const [lng, lat] = await getLocationAdapter({ timeout: 30000 });
    const { regeocode } = await ApiAMapGeocodeRegeo({
      location: `${lng},${lat}`,
    });
    return regeocode.formatted_address;
  } catch (error) {
    exceptionHandler(error);
    return '';
  }
}

/**
 * 获取图片信息，网络图片需先配置download域名才能生效
 *
 * @param {string} url 图片链接（网络链接 / 临时链接）
 * @returns {object} 图片宽高等
 */
export const getImageInfo = (url: string) =>
  new Promise<UniApp.GetImageInfoSuccessData>((resolve, reject) => {
    uni.getImageInfo({
      src: url,
      success: (res) => {
        if (!url.includes('http')) {
          res.path = process.env.NODE_ENV === 'production' ? res.path : `/${res.path}`;
        }
        resolve(res);
      },
      fail: (err) => {
        reject(err);
        Toast.error('网络错误请重试');
      },
    });
  });

/**
 * canvas 导出图片
 *
 * @param {string} canvasId
 * @returns {string} 临时图片链接
 */
export const canvasToTempFilePath = (canvasId: string) =>
  new Promise<string>((resolve, reject) => {
    uni.canvasToTempFilePath({
      canvasId,
      success: (res) => {
        resolve(res.tempFilePath);
      },
      fail: reject,
    });
  });

/**
 * base64转为二进制流
 */
const toBlob = (baseStr: string, type: string) => {
  const text = window.atob(baseStr.split(',')[1]);
  const buffer = new ArrayBuffer(text.length);
  const ubuffer = new Uint8Array(buffer);

  for (let i = 0; i < text.length; i++) {
    ubuffer[i] = text.charCodeAt(i);
  }

  // eslint-disable-next-line @typescript-eslint/ban-ts-comment
  // @ts-ignore
  const Builder = window.WebKitBlobBuilder || window.MozBlobBuilder;
  let blob;
  if (Builder) {
    const builder = new Builder();
    builder.append(buffer);
    blob = builder.getBlob(type);
  } else {
    blob = new window.Blob([buffer], {
      type,
    });
  }
  return blob;
};

/**
 * 图片压缩
 */
export const compressPic = (file: File & { path: string }) => {
  if (!file) {
    Toast.error('图片不存在');
  }
  const canvas = document.createElement('canvas');
  const ctx = canvas.getContext('2d');

  const tCanvas = document.createElement('canvas');
  const tCtx = tCanvas.getContext('2d');

  return new Promise<Blob>((resolve) => {
    const initSize = file.size;
    const image = new Image();
    image.src = file.path;
    image.onload = () => {
      if (!ctx || !tCtx) return;
      let width = image.width;
      let height = image.height;

      let ratio = (width * height) / 4000000;
      if (ratio > 1) {
        width /= ratio;
        height /= ratio;
      } else {
        ratio = 1;
      }

      canvas.width = width;
      canvas.height = height;

      // 铺底色
      ctx.fillStyle = '#fff';
      ctx.fillRect(0, 0, canvas.width, canvas.height);

      let count = (width * height) / 1000000;
      if (count > 1) {
        count = Math.ceil(count);
        const nw = Math.floor(width / count);
        const nh = Math.floor(height / count);
        tCanvas.width = nw;
        tCanvas.height = nh;
        tCtx.drawImage(image, 0, 0, nw * ratio, nh * ratio, 0, 0, nw, nh);
        ctx.drawImage(tCanvas, 0, 0);
        tCtx.drawImage(image, nw * ratio, 0, nw * ratio, nh * ratio, 0, 0, nw, nh);
        ctx.drawImage(tCanvas, nw, 0);
        for (let i = 0; i < count; i++) {
          for (let j = 0; j < count; j++) {
            tCtx.drawImage(
              image,
              i * nw * ratio,
              j * nh * ratio,
              nw * ratio,
              nh * ratio,
              0,
              0,
              nw,
              nh,
            );

            ctx.drawImage(tCanvas, i * nw, j * nh);
          }
        }
      } else {
        ctx.drawImage(image, 0, 0, width, height);
      }

      // 进行压缩
      const vData = canvas.toDataURL('image/jpeg', 0.7);
      console.log(`压缩前: ${(initSize / 1024 / 1024).toFixed(1)}Mb`);
      console.log(`压缩前: ${(vData.length / 1024).toFixed(1)}Kb`);
      tCanvas.width = 0;
      tCanvas.height = 0;
      canvas.width = 0;
      canvas.height = 0;
      resolve(toBlob(vData, file.type));
    };
  });
};

/**
 * 文件转base64
 */
export const fileToBase64 = (file: File | any): Promise<string> => {
  return new Promise((resolve, reject) => {
    // 检查是否是标准的File对象
    if (file instanceof File || file instanceof Blob) {
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = () => resolve(reader.result as string);
      reader.onerror = (error) => reject(error);
    } else if (file && (file.tempFilePath || file.path)) {
      // 处理uni-app的文件对象
      const filePath = file.tempFilePath || file.path;

      // #ifdef H5
      // H5环境下，如果有blob属性，使用blob
      if (file.blob) {
        const reader = new FileReader();
        reader.readAsDataURL(file.blob);
        reader.onload = () => resolve(reader.result as string);
        reader.onerror = (error) => reject(error);
        return;
      }
      // #endif

      // 使用uni-app的文件系统API读取文件
      uni.getFileSystemManager().readFile({
        filePath,
        encoding: 'base64',
        success: (res) => {
          // 根据文件扩展名判断MIME类型
          const extension = filePath.split('.').pop()?.toLowerCase();
          let mimeType = 'image/jpeg'; // 默认

          if (extension === 'png') {
            mimeType = 'image/png';
          } else if (extension === 'gif') {
            mimeType = 'image/gif';
          } else if (extension === 'webp') {
            mimeType = 'image/webp';
          }

          const base64String = `data:${mimeType};base64,${res.data}`;
          resolve(base64String);
        },
        fail: (error) => {
          console.error('读取文件失败:', error);
          reject(error);
        },
      });
    } else {
      reject(new Error('Invalid file object'));
    }
  });
};

interface WatermarkOptions {
  fontSize?: number;
  textColor?: string;
  textAlign?: CanvasTextAlign;
  textBaseline?: CanvasTextBaseline;
  opacity?: number;
  padding?: [number, number];
  backgroundColor?: string;
  position?: 'top' | 'bottom';
}

/**
 * 给图片添加文字水印
 */
export const addTextWatermark = (
  base64Image: string,
  text: string[],
  options: WatermarkOptions = {},
): Promise<string> => {
  const {
    fontSize = 28,
    textColor = '#ffffff',
    textAlign = 'left',
    textBaseline = 'bottom',
    opacity = 0.8,
    padding = [20, 20],
    backgroundColor = 'rgba(0,0,0,0.3)',
    position = 'bottom',
  } = options;

  return new Promise((resolve, reject) => {
    const image = new Image();
    image.src = base64Image;
    image.onload = () => {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      if (!ctx) {
        reject(new Error('Failed to get canvas context'));
        return;
      }

      // 设置画布尺寸
      canvas.width = image.width;
      canvas.height = image.height;

      // 绘制原图
      ctx.drawImage(image, 0, 0);

      // 计算水印区域高度
      const lineHeight = fontSize * 1.5;
      const watermarkHeight = text.length * lineHeight + padding[1] * 2;

      // 绘制水印背景
      ctx.fillStyle = backgroundColor;
      ctx.globalAlpha = opacity;
      if (position === 'bottom') {
        ctx.fillRect(0, canvas.height - watermarkHeight, canvas.width, watermarkHeight);
      } else {
        ctx.fillRect(0, 0, canvas.width, watermarkHeight);
      }

      // 绘制水印文字
      ctx.fillStyle = textColor;
      ctx.font = `${fontSize}px sans-serif`;
      ctx.textAlign = textAlign;
      ctx.textBaseline = textBaseline;
      ctx.globalAlpha = 1;

      text.forEach((line, index) => {
        const y =
          position === 'bottom'
            ? canvas.height - watermarkHeight + padding[1] + lineHeight * (index + 1)
            : padding[1] + lineHeight * (index + 1);
        ctx.fillText(line, padding[0], y);
      });

      // 导出新图片
      resolve(canvas.toDataURL('image/jpeg', 0.9));
    };
    image.onerror = () => reject(new Error('Failed to load image'));
  });
};

interface EngineeringWatermarkOptions {
  title?: string;
  time?: string;
  address?: string;
  flowName?: string;
  patrolRecordText1?: string;
  patrolRecordText2?: string;
  fontSize?: number;
  titleFontSize?: number;
  textColor?: string;
  backgroundColor?: string;
  padding?: [number, number];
  borderRadius?: number;
  opacity?: number;
}

// 添加水印到图片
export async function addWatermark(file: File, data: EngineeringWatermarkOptions): Promise<string> {
  try {
    const base64 = await fileToBase64(file);
    return await addEngineeringWatermark(base64, {
      ...data,
      fontSize: 14,
      titleFontSize: 16,
      textColor: '#ffffff',
      backgroundColor: '#4d80f0',
      padding: [12, 8],
      borderRadius: 6,
      opacity: 0.9,
    });
  } catch (error) {
    exceptionHandler(error);
    return '';
  }
}

/**
 * 给工程图片添加专用水印（蓝色背景矩形框样式）
 */
export const addEngineeringWatermark = async (
  base64Image: string,
  options: EngineeringWatermarkOptions = {},
): Promise<string> => {
  const {
    title = '',
    time = Dayjs().format('YYYY-MM-DD HH:mm'),
    flowName = '',
    patrolRecordText1 = '',
    patrolRecordText2 = '',
    fontSize = 14,
    titleFontSize = 16,
    backgroundColor = '#4A90E2',
    padding = [12, 8],
    borderRadius = 6,
    opacity = 0.9,
  } = options;

  const address = (await getAddress()) || options.address || '';

  return new Promise((resolve, reject) => {
    const image = new Image();
    image.src = base64Image;
    image.onload = () => {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      if (!ctx) {
        reject(new Error('Failed to get canvas context'));
        return;
      }

      // 设置画布尺寸
      canvas.width = image.width;
      canvas.height = image.height;

      // 绘制原图
      ctx.drawImage(image, 0, 0);

      // 判断图片方向
      const isPortrait = canvas.height > canvas.width;

      // 九宫格计算：每个格子占图片的1/3
      const gridWidth = canvas.width / 3;
      const gridHeight = canvas.height / 3;

      // 水印目标尺寸：根据图片方向调整基准尺寸
      let baseWatermarkWidth;
      let baseWatermarkHeight;
      if (isPortrait) {
        // 竖向图片：使用较大的宽度基准，确保水印不会太小
        baseWatermarkWidth = Math.max(gridWidth, canvas.width * 0.5);
        baseWatermarkHeight = gridHeight;
      } else {
        // 横向图片：使用更大的宽度基准，确保有足够空间显示文字和换行
        baseWatermarkWidth = Math.max(gridWidth, canvas.width * 0.7);
        baseWatermarkHeight = gridHeight;
      }

      // 设置水印最大尺寸限制 - 横屏图片使用更大的宽度限制，确保标题有足够空间换行
      const maxWatermarkWidth = isPortrait ? canvas.width * 0.95 : canvas.width * 0.85;
      const maxWatermarkHeight = canvas.height / 2;

      // 根据水印区域计算合适的字体大小
      // 基于水印区域的宽度来计算字体大小，确保文字能合适地填充
      const baseFontSize = Math.max(Math.min(baseWatermarkWidth / 15, baseWatermarkHeight / 8), 14);
      const adaptiveFontSize = Math.max(baseFontSize, fontSize);
      const adaptiveTitleFontSize = Math.max(adaptiveFontSize * 1.2, titleFontSize);

      // 根据字体大小计算内边距
      const adaptivePadding: [number, number] = [
        Math.max(adaptiveFontSize * 0.8, padding[0]),
        Math.max(adaptiveFontSize * 0.6, padding[1]),
      ];

      // 通用换行处理函数 - 优化长文本处理
      const wrapText = (text: string, maxWidth: number, font: string): string[] => {
        const lines = [];
        let currentLine = '';

        // 设置字体用于测量
        ctx.font = font;

        // 计算可用宽度（减去内边距）
        const availableWidth = maxWidth - adaptivePadding[0] * 2;

        // 对于横屏图片，允许更长的行
        const effectiveWidth = isPortrait
          ? availableWidth
          : Math.max(availableWidth, canvas.width * 0.6);

        for (let i = 0; i < text.length; i++) {
          const testLine = currentLine + text[i];
          const testWidth = ctx.measureText(testLine).width;

          if (testWidth > effectiveWidth && currentLine) {
            // 当前行已经有内容且加上新字符会超出，换行
            lines.push(currentLine);
            currentLine = text[i];
          } else {
            currentLine = testLine;
          }
        }

        if (currentLine) {
          lines.push(currentLine);
        }

        return lines;
      };

      // 标题换行处理函数 - 针对横屏图片优化
      const wrapTitleText = (titleText: string, maxWidth: number, font: string): string[] => {
        const lines = [];
        let currentLine = '';

        // 设置字体用于测量
        ctx.font = font;

        // 计算可用宽度（减去内边距）
        const availableWidth = maxWidth - adaptivePadding[0] * 2;

        // 对于横屏图片，确保标题能够正确换行
        const effectiveWidth = isPortrait
          ? availableWidth
          : Math.min(availableWidth, canvas.width * 0.8);

        for (let i = 0; i < titleText.length; i++) {
          const testLine = currentLine + titleText[i];
          const testWidth = ctx.measureText(testLine).width;

          if (testWidth > effectiveWidth && currentLine) {
            // 当前行已经有内容且加上新字符会超出，换行
            lines.push(currentLine);
            currentLine = titleText[i];
          } else {
            currentLine = testLine;
          }
        }

        if (currentLine) {
          lines.push(currentLine);
        }

        return lines;
      };

      // 特殊处理地址换行 - 需要考虑对齐
      const wrapAddressText = (
        addressText: string,
        maxWidth: number,
        font: string,
      ): { text: string; isAddressContinuation: boolean }[] => {
        const prefix = '拍摄地址：';
        const fullText = prefix + addressText;

        ctx.font = font;
        const availableWidth = maxWidth - adaptivePadding[0] * 2;

        // 如果整行都能放下，直接返回
        if (ctx.measureText(fullText).width <= availableWidth) {
          return [{ text: fullText, isAddressContinuation: false }];
        }

        // 否则需要换行处理
        const lines = [];
        const currentLine = prefix;

        // 先检查前缀是否就超出了宽度
        if (ctx.measureText(prefix).width > availableWidth) {
          // 前缀本身就超出，特殊处理
          return [{ text: prefix, isAddressContinuation: false }];
        }

        // 计算第一行能放下多少地址字符
        let firstLineContent = '';
        for (let i = 0; i < addressText.length; i++) {
          const testLine = prefix + firstLineContent + addressText[i];
          const testWidth = ctx.measureText(testLine).width;

          if (testWidth > availableWidth) {
            // 第一行放不下更多字符，结束第一行
            break;
          } else {
            firstLineContent += addressText[i];
          }
        }

        // 添加第一行
        if (firstLineContent) {
          lines.push({ text: prefix + firstLineContent, isAddressContinuation: false });
        } else {
          // 如果第一行连一个字符都放不下，至少放前缀
          lines.push({ text: prefix, isAddressContinuation: false });
        }

        // 处理剩余的地址内容
        const remainingText = addressText.substring(firstLineContent.length);
        if (remainingText) {
          // 计算后续行的可用宽度（需要减去前缀宽度，因为后续行要与第一行地址内容对齐）
          const prefixWidth = ctx.measureText(prefix).width;
          const remainingLineAvailableWidth = availableWidth - prefixWidth;

          // 对剩余内容进行换行处理
          let remainingLine = '';
          for (let i = 0; i < remainingText.length; i++) {
            const testLine = remainingLine + remainingText[i];
            const testWidth = ctx.measureText(testLine).width;

            if (testWidth > remainingLineAvailableWidth && remainingLine) {
              // 当前行已经有内容且加上新字符会超出，换行
              lines.push({ text: remainingLine, isAddressContinuation: true });
              remainingLine = remainingText[i];
            } else {
              remainingLine = testLine;
            }
          }

          // 添加最后一行
          if (remainingLine) {
            lines.push({ text: remainingLine, isAddressContinuation: true });
          }
        }

        return lines;
      };

      // 首先计算标题所需的尺寸
      ctx.font = `bold ${adaptiveTitleFontSize}px sans-serif`;

      // 先计算标题单行的宽度，用于确定水印基准宽度
      const titleSingleLineWidth = ctx.measureText(title).width;
      const titleRequiredWidth = titleSingleLineWidth + adaptivePadding[0] * 2;

      // 计算拍摄时间文本的宽度，确保不换行
      const timeText = `拍摄时间：${time}`;
      ctx.font = `${adaptiveFontSize}px sans-serif`;
      const timeTextWidth = ctx.measureText(timeText).width;
      const timeRequiredWidth = timeTextWidth + adaptivePadding[0] * 2;

      // 计算水印的基准宽度（至少要能容纳标题）
      let watermarkWidth = Math.max(baseWatermarkWidth, titleRequiredWidth);

      // 对于横屏图片，优先考虑文字内容的宽度和标题换行需求
      if (!isPortrait) {
        // 预计算所有可能的长文本宽度
        const longTexts = [timeText, patrolRecordText1, patrolRecordText2].filter(Boolean);
        let maxTextWidth = 0;
        ctx.font = `${adaptiveFontSize}px sans-serif`;
        longTexts.forEach((text) => {
          const textWidth = ctx.measureText(text).width + adaptivePadding[0] * 2;
          maxTextWidth = Math.max(maxTextWidth, textWidth);
        });

        // 对于横屏图片，确保有足够空间进行标题换行
        // 先尝试用当前宽度进行标题换行，如果换行后仍然超出，则调整宽度
        const testTitleLines = wrapTitleText(
          title,
          watermarkWidth,
          `bold ${adaptiveTitleFontSize}px sans-serif`,
        );

        let maxTitleLineWidth = 0;
        ctx.font = `bold ${adaptiveTitleFontSize}px sans-serif`;
        testTitleLines.forEach((line) => {
          const lineWidth = ctx.measureText(line).width;
          maxTitleLineWidth = Math.max(maxTitleLineWidth, lineWidth);
        });

        const titleRequiredWidthAfterWrap = maxTitleLineWidth + adaptivePadding[0] * 2;

        // 取最大值：标题换行后需要的宽度、其他文本宽度、基准宽度
        watermarkWidth = Math.max(watermarkWidth, maxTextWidth, titleRequiredWidthAfterWrap);
        // 确保不超过最大宽度限制
        watermarkWidth = Math.min(watermarkWidth, maxWatermarkWidth);
      } else {
        watermarkWidth = Math.min(watermarkWidth, maxWatermarkWidth);
      }

      // 确保水印宽度能容纳时间文本（不换行）
      if (timeRequiredWidth > watermarkWidth) {
        watermarkWidth = Math.min(timeRequiredWidth, maxWatermarkWidth);
      }

      // 使用确定的宽度进行文字换行
      const timeLines = [timeText]; // 时间文本不换行，直接作为单行

      // 基于确定的水印宽度计算标题换行，确保标题不会超出水印边界
      const titleLines = wrapTitleText(
        title,
        watermarkWidth,
        `bold ${adaptiveTitleFontSize}px sans-serif`,
      );

      // 验证标题换行后的宽度是否超出水印宽度
      let maxTitleLineWidth = 0;
      titleLines.forEach((line) => {
        const lineWidth = ctx.measureText(line).width;
        maxTitleLineWidth = Math.max(maxTitleLineWidth, lineWidth);
      });

      const updatedTitleRequiredWidth = maxTitleLineWidth + adaptivePadding[0] * 2;

      // 如果标题换行后仍然超出水印宽度，则调整水印宽度（但不超过最大限制）
      if (updatedTitleRequiredWidth > watermarkWidth) {
        watermarkWidth = Math.min(updatedTitleRequiredWidth, maxWatermarkWidth);
      }

      const addressLines = wrapAddressText(
        address,
        watermarkWidth,
        `${adaptiveFontSize}px sans-serif`,
      );

      // 构建动态内容行
      const dynamicContentLines: { text: string; isAddressContinuation: boolean }[] = [];

      // 如果有 flowName，添加当前进度
      if (flowName) {
        const progressLines = wrapText(
          `当前进度：${flowName}`,
          watermarkWidth,
          `${adaptiveFontSize}px sans-serif`,
        );
        dynamicContentLines.push(
          ...progressLines.map((line) => ({ text: line, isAddressContinuation: false })),
        );
      }

      // 如果有 patrolRecordText1，添加巡查人员
      if (patrolRecordText1) {
        const inspectLines = wrapText(
          patrolRecordText1,
          watermarkWidth,
          `${adaptiveFontSize}px sans-serif`,
        );
        dynamicContentLines.push(
          ...inspectLines.map((line) => ({ text: line, isAddressContinuation: false })),
        );
      }

      // 如果有 patrolRecordText2，添加问题项
      if (patrolRecordText2) {
        const problemLines = wrapText(
          patrolRecordText2,
          watermarkWidth,
          `${adaptiveFontSize}px sans-serif`,
        );
        dynamicContentLines.push(
          ...problemLines.map((line) => ({ text: line, isAddressContinuation: false })),
        );
      }

      // 构建所有内容行
      const contentLines = [
        ...timeLines.map((line) => ({ text: line, isAddressContinuation: false })),
        ...addressLines,
        ...dynamicContentLines,
      ];

      // 计算实际需要的宽度
      let actualMaxWidth = titleRequiredWidth;

      // 检查所有内容行的宽度
      ctx.font = `${adaptiveFontSize}px sans-serif`;
      contentLines.forEach((line) => {
        const lineWidth = ctx.measureText(line.text).width + adaptivePadding[0] * 2;
        actualMaxWidth = Math.max(actualMaxWidth, lineWidth);
      });

      // 如果实际需要的宽度超过当前水印宽度，则调整
      if (actualMaxWidth > watermarkWidth) {
        watermarkWidth = Math.min(actualMaxWidth, maxWatermarkWidth);

        // 重新换行（如果宽度改变了）
        const newTimeLines = wrapText(
          `拍摄时间：${time}`,
          watermarkWidth,
          `${adaptiveFontSize}px sans-serif`,
        );
        const newAddressLines = wrapAddressText(
          address,
          watermarkWidth,
          `${adaptiveFontSize}px sans-serif`,
        );

        // 重新构建动态内容行
        const newDynamicContentLines: { text: string; isAddressContinuation: boolean }[] = [];

        if (flowName) {
          const newProgressLines = wrapText(
            `当前进度：${flowName}`,
            watermarkWidth,
            `${adaptiveFontSize}px sans-serif`,
          );
          newDynamicContentLines.push(
            ...newProgressLines.map((line) => ({ text: line, isAddressContinuation: false })),
          );
        }

        if (patrolRecordText1) {
          const newInspectLines = wrapText(
            patrolRecordText1,
            watermarkWidth,
            `${adaptiveFontSize}px sans-serif`,
          );
          newDynamicContentLines.push(
            ...newInspectLines.map((line) => ({ text: line, isAddressContinuation: false })),
          );
        }

        if (patrolRecordText2) {
          const newProblemLines = wrapText(
            patrolRecordText2,
            watermarkWidth,
            `${adaptiveFontSize}px sans-serif`,
          );
          newDynamicContentLines.push(
            ...newProblemLines.map((line) => ({ text: line, isAddressContinuation: false })),
          );
        }

        contentLines.length = 0;
        contentLines.push(
          ...newTimeLines.map((line) => ({ text: line, isAddressContinuation: false })),
          ...newAddressLines,
          ...newDynamicContentLines,
        );
      }

      // 计算尺寸
      const lineHeight = adaptiveFontSize * 1.4;
      const titleLineHeight = adaptiveTitleFontSize * 1.4;
      const titleSpacing = adaptivePadding[1] * 0.8; // 标题上下间距

      // 水印整体高度 - 根据实际内容行数计算
      const titleSectionHeight = titleLines.length * titleLineHeight + titleSpacing * 2;
      const contentSectionHeight = contentLines.length * lineHeight + adaptivePadding[1] * 2;
      const totalRequiredHeight = titleSectionHeight + contentSectionHeight;

      // 水印高度：使用实际内容需要的高度
      let watermarkHeight = totalRequiredHeight;
      watermarkHeight = Math.min(watermarkHeight, maxWatermarkHeight);

      // 水印位置 - 卡片左下角紧贴图片左下角
      const watermarkX = 0;
      const watermarkY = canvas.height - watermarkHeight;

      // 确保水印不会超出图片边界
      if (watermarkWidth > canvas.width) {
        watermarkWidth = canvas.width;
      }

      if (watermarkHeight > canvas.height) {
        watermarkHeight = canvas.height;
      }

      // 绘制标题部分（蓝色背景）
      ctx.fillStyle = backgroundColor;
      ctx.globalAlpha = opacity;
      ctx.beginPath();

      // 绘制标题圆角矩形（上半部分）
      const radius = borderRadius;
      const x = watermarkX;
      const y = watermarkY;
      const width = watermarkWidth;
      const height = titleSectionHeight;

      ctx.moveTo(x + radius, y);
      ctx.lineTo(x + width - radius, y);
      ctx.quadraticCurveTo(x + width, y, x + width, y + radius);
      ctx.lineTo(x + width, y + height);
      ctx.lineTo(x, y + height);
      ctx.lineTo(x, y + radius);
      ctx.quadraticCurveTo(x, y, x + radius, y);
      ctx.closePath();
      ctx.fill();

      // 绘制内容部分（白色背景）
      ctx.fillStyle = 'rgba(255, 255, 255, 0.95)';
      ctx.globalAlpha = opacity;
      ctx.beginPath();

      // 绘制内容圆角矩形（下半部分）
      const contentY = watermarkY + titleSectionHeight;
      const contentHeight = contentSectionHeight;

      ctx.moveTo(x, contentY);
      ctx.lineTo(x + width, contentY);
      ctx.lineTo(x + width, contentY + contentHeight - radius);
      ctx.quadraticCurveTo(
        x + width,
        contentY + contentHeight,
        x + width - radius,
        contentY + contentHeight,
      );
      ctx.lineTo(x + radius, contentY + contentHeight);
      ctx.quadraticCurveTo(x, contentY + contentHeight, x, contentY + contentHeight - radius);
      ctx.lineTo(x, contentY);
      ctx.closePath();
      ctx.fill();

      // 绘制标题文字（白色，居中）
      ctx.fillStyle = '#ffffff';
      ctx.globalAlpha = 1;
      ctx.font = `bold ${adaptiveTitleFontSize}px sans-serif`;
      ctx.textAlign = 'center';
      ctx.textBaseline = 'middle';

      // 计算标题在标题区域内的垂直居中位置
      const titleAreaCenterY = watermarkY + titleSectionHeight / 2;
      const totalTitleHeight = titleLines.length * titleLineHeight;
      const titleStartY = titleAreaCenterY - totalTitleHeight / 2;

      // 绘制每一行标题
      titleLines.forEach((line, index) => {
        const titleX = watermarkX + watermarkWidth / 2;
        const titleY = titleStartY + index * titleLineHeight + titleLineHeight / 2;
        ctx.fillText(line, titleX, titleY);
      });

      // 绘制内容文字（黑色，左对齐）
      ctx.fillStyle = '#333333';
      ctx.font = `${adaptiveFontSize}px sans-serif`;
      ctx.textAlign = 'left';
      ctx.textBaseline = 'top';

      // 计算"拍摄地址："前缀的宽度用于对齐
      const addressPrefixWidth = ctx.measureText('拍摄地址：').width;

      // 绘制内容行
      let currentY = contentY + adaptivePadding[1];
      contentLines.forEach((line) => {
        const xPosition = line.isAddressContinuation
          ? watermarkX + adaptivePadding[0] + addressPrefixWidth
          : watermarkX + adaptivePadding[0];

        ctx.fillText(line.text, xPosition, currentY);
        currentY += lineHeight;
      });

      // 导出新图片
      resolve(canvas.toDataURL('image/jpeg', 0.9));
    };
    image.onerror = () => reject(new Error('Failed to load image'));
  });
};

// 将 base64 转换为 Blob 对象
export function base64ToBlob(base64: string, mimeType = 'image/jpeg'): Blob {
  // 移除 data:image/xxx;base64, 前缀
  const base64Data = base64.split(',')[1] || base64;

  // 解码 base64
  const byteCharacters = atob(base64Data);
  const byteNumbers = new Array(byteCharacters.length);

  for (let i = 0; i < byteCharacters.length; i++) {
    byteNumbers[i] = byteCharacters.charCodeAt(i);
  }

  const byteArray = new Uint8Array(byteNumbers);
  return new Blob([byteArray], { type: mimeType });
}
