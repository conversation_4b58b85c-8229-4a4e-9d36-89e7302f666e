<script setup lang="ts">
import { ref, computed } from 'vue';
import { onLoad, onShow } from '@dcloudio/uni-app';
import { Navigator } from '@/utils';
import {
  ApiGetMonitorOrderDetail,
  ApiGetMonitorFlowList,
  MonitorAppointBody,
  MonitorOrderDetailRes,
  MonitorOrderFlowListItem,
  MonitorRecycleBody,
} from '@/api';
import { ApiFileShow, ApiQueryHandler } from '@shencom/api';
import { FormatDateTime } from '@shencom/utils';
import { Dictionary } from '@rms/types';
import { onLocation } from '@/utils/location';
import { StorageGetMonitorFlowRefresh } from '@/storage/monitor';
import useRoleStore from '@/hooks/useRole';

const { roleInfo } = useRoleStore();

interface DetailParams {
  id: string;
  status: string;
}

const params = ref<DetailParams>({
  id: '',
  status: '1',
});

const detailData = ref<MonitorOrderDetailRes>();
const flowList = ref<MonitorOrderFlowListItem[]>([]);

// 根据状态获取步骤信息
const steps = ref<Dictionary[]>([]);

// 获取当前激活的步骤索引
const activeStep = computed(() => {
  const status = Number(detailData.value?.flow);
  return Math.max(0, status - 1);
});

const flowDetail = ref();

async function getStepDetails() {
  const status = detailData.value?.flow || 0;
  const stepData = [];
  const details = [];

  // 回收预约信息（状态 >= 6 时显示）
  if (status >= 6) {
    const item = flowList.value.find((v) => v.flow === 6 && v.state === 1) as MonitorAppointBody;
    const reservationTime = item?.reservationTime
      ? FormatDateTime(item.reservationTime, 'YYYY-MM-DD HH:mm')
      : ' ';
    stepData.push({
      title: '回收预约',
      description: item?.updatedAt ? FormatDateTime(item?.updatedAt, 'YYYY-MM-DD HH:mm') : ' ',
      active: status >= 6,
      completed: status > 6,
    });
    details.push({
      title: '回收预约',
      content: {
        ...item,
        reservationTime,
      },
    });
  }

  // 上门回收信息（状态 >= 7 时显示）
  if (status >= 7) {
    const item = flowList.value.find((v) => v.flow === 7 && v.state === 1) as MonitorRecycleBody;
    const imgs = item?.pic ? (await ApiFileShow({ ids: item?.pic.split(',') })).data || [] : [];
    const recycleTime = item?.recycleTime
      ? FormatDateTime(item.recycleTime, 'YYYY-MM-DD HH:mm')
      : ' ';
    stepData.push({
      title: '上门回收',
      description: item?.updatedAt ? FormatDateTime(item?.updatedAt, 'YYYY-MM-DD HH:mm') : ' ',
      active: status >= 7,
      completed: status > 7,
    });
    details.push({
      title: '上门回收',
      content: item && {
        ...item,
        recycleTime,
        pic: imgs.map((v) => v.remoteUrl) || [],
      },
    });
  }

  steps.value = stepData;
  flowDetail.value = details;
}

onLoad(async (options) => {
  params.value.id = options?.id || '';
  init();
});

onShow(() => {
  const needRefresh = StorageGetMonitorFlowRefresh();
  if (needRefresh) {
    init();
  }
});

async function init() {
  await getDetail();
  await getFlowList();
  getStepDetails();
}

async function getDetail() {
  const res = await ApiGetMonitorOrderDetail(params.value.id);
  detailData.value = res;
}

async function getFlowList() {
  const res = await ApiGetMonitorFlowList({
    query: ApiQueryHandler(params.value.id, 'orderId', 'select'),
  });
  flowList.value = res.content || [];
}
// 图片预览
const previewImage = (images: string[] | undefined, current: string) => {
  if (!images || images.length === 0) return;
  uni.previewImage({
    urls: images,
    current,
  });
};

function onCall(phone?: string) {
  if (!phone) return;
  uni.makePhoneCall({
    phoneNumber: phone,
  });
}

function toRecycle() {
  Navigator.push('/pages/recycling/recycle', {
    id: detailData.value?.flowId,
    orderId: detailData.value?.id,
  });
}
</script>

<template>
  <view class="min-h-screen bg-gray-50 pb-16">
    <view class="text-gray-700 p-5">
      <view class="flex justify-between items-center">
        <text class="block text-base font-bold">{{ detailData?.projectName || '工程详情' }}</text>
        <view class="bg-gray-200 px-2 rounded-md border border-gray-200">
          <text class="text-xs text-gray-500">{{ detailData?.projectNumber }}</text>
        </view>
      </view>
      <view class="mt-2">
        <text class="text-sm text-gray-500">{{ detailData?.projectAddress }}</text>
        <wd-icon
          v-if="detailData?.projectPoiId"
          name="location"
          size="14"
          class="ml-1"
          @click="onLocation(detailData)" />
      </view>
    </view>

    <view class="bg-white py-5 mx-2.5 rounded-lg shadow-sm">
      <wd-steps :active="activeStep" align-center>
        <wd-step
          v-for="(step, index) in steps"
          :key="index"
          :title="step.title"
          :description="step.description" />
      </wd-steps>
    </view>

    <view class="mt-4 px-2.5">
      <view
        v-for="(detail, index) in flowDetail"
        :key="index"
        class="bg-white rounded-lg mb-4 overflow-hidden shadow-sm">
        <view class="bg-blue-600 text-center py-2 border-b border-gray-100">
          <text class="text-base font-medium text-white">{{ detail.title }}</text>
        </view>

        <view>
          <template v-if="detail.title === '回收预约'">
            <wd-cell-group v-if="detail.content">
              <wd-cell title="预约工程" :value="detailData?.projectName" />
              <wd-cell title="联系人" :value="detail.content.contactName" />
              <wd-cell title="联系方式">
                <view class="flex justify-end items-center">
                  <text>{{ detail.content.contactMobile }}</text>
                  <i
                    class="i-ion-call ml-1 text-blue"
                    @click="onCall(detail.content.contactMobile)"></i>
                </view>
              </wd-cell>
              <wd-cell title="期望上门时间" :value="detail.content.reservationTime" />
            </wd-cell-group>
            <sc-empty v-else class="my-8" text="待预约" />
          </template>

          <template v-if="detail.title === '上门回收'">
            <wd-cell-group v-if="detail.content">
              <wd-cell title="回收照片">
                <view class="flex justify-end flex-wrap gap-3">
                  <image
                    v-for="(img, imgIndex) in detail.content.pic"
                    :key="imgIndex"
                    :src="img"
                    mode="aspectFit"
                    class="w-16 h-16 rounded-md border border-solid border-gray-200"
                    @click="previewImage(detail.content.pic, img)" />
                </view>
              </wd-cell>
              <wd-cell title="回收监控数量" :value="detail.content.recycleCnt" />
              <wd-cell title="回收说明" :value="detail.content.memo" />
              <wd-cell title="回收人员" :value="detail.content.createdUserName" />
              <wd-cell title="联系方式">
                <view class="flex justify-end items-center">
                  <text>{{ detail.content.createdUserPhone }}</text>
                  <i
                    class="i-ion-call ml-1 text-blue"
                    @click="onCall(detail.content.createdUserPhone)"></i>
                </view>
              </wd-cell>
              <!-- <wd-cell title="回收时间" :value="detail.content.recycleTime" /> -->
            </wd-cell-group>
            <sc-empty v-else class="my-8" text="待回收" />
          </template>
        </view>
      </view>
    </view>

    <sc-button-fixed v-if="roleInfo.roleName === '安装人员'" class="pt-5 box-border">
      <wd-button
        v-if="detailData?.flow === 7"
        block
        custom-class="!bg-gradient-to-br !from-violet-600 !to-blue-600"
        size="large"
        @click="toRecycle">
        去回收
      </wd-button>
    </sc-button-fixed>
  </view>
</template>

<style lang="scss" scoped></style>
