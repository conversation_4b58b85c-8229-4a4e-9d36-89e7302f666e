<script setup lang="ts">
import { exceptionToast, FormatDate, FormatDateTime, IsPro, Navigator, Toast } from '@/utils';
import { FormConfig } from '@rms/components/sc-form/sc-form.vue';
import { addWatermarkAfterRead } from '@/pages/recycling/canvas';
import { ref } from 'vue';
import { ChooseFile } from '@rms/components/sc-upload/type';
import { Dictionary } from '@rms/types';
import {
  ApiGetMonitorFlowList,
  ApiGetMonitorOrderDetail,
  ApiGetProjectShow,
  ApiMonitorOrderUpdate,
  MonitorRecycleBody,
} from '@/api';
import { StorageSetMonitorFlowRefresh } from '@/storage/monitor';
import { ApiQueryHandler } from '@shencom/api';

const project = ref<Dictionary>({
  title: '',
  address: '',
  reservationTime: '',
  flowName: '上门回收',
});

async function getFlowList(id: string) {
  const data = await ApiGetMonitorFlowList({
    query: ApiQueryHand<PERSON>(id, 'orderId', 'select'),
  });
  return data.content;
}

const formData = ref<Omit<MonitorRecycleBody, 'pic'> & { pic: Dictionary[] }>({
  id: '',
  state: 1,
  recycleTime: '',
  memo: '',
  pic: [],
  recycleCnt: 1,
  cameraDeviceIds: [],
  cameraSerialNos: [],
});

onLoad(async (options) => {
  formData.value.id = options?.id || '';

  const data = await ApiGetMonitorOrderDetail(options?.orderId);
  const flowList = await getFlowList(options?.orderId);
  const flow6 = flowList.find((item) => item.flow === 6);

  formData.value.cameraDeviceIds = data.cameraDeviceIds;
  formData.value.cameraSerialNos = data.cameraSerialNos;
  project.value.title = data?.projectName || '';
  project.value.address = data?.projectAddress || '';
  project.value.reservationTime = FormatDate(flow6?.finishTime, 'YYYY-MM-DD HH:mm') || '';
});

const scFormRef = ref();

const formConfig = ref<FormConfig['config']>({
  formAttrs: {
    labelWidth: '220rpx',
    fontSize: '15px',
    border: true,
  },
  data: [
    {
      prop: 'pic',
      label: '回收照片',
      tag: {
        tagType: 'upload',
        attr: {
          required: true,
          multiple: true,
          maxCount: 10,
          maxSize: 10 * 1024 * 1024,
          uploadType: IsPro ? 'oss' : 'server',
          sizeType: ['original', 'compressed'],
          autoUpload: false,
          afterRead: async (files: ChooseFile[]) => {
            await addWatermarkAfterRead(files, project.value);
            formData.value.pic.forEach((item) => {
              if (item.tempUrl) return;
              item.tempUrl = item.watermark;
              item.url = item.watermark;
              item.path = item.watermark;
            });
          },
        },
      },
      listeners: {
        oversize: () => {
          Toast.warning('上传文件大小不能超过10M！', {
            duration: 2000,
          });
        },
      },
    },
    {
      prop: 'recycleCnt',
      label: '回收监控数量',
      tag: {
        tagType: 'component',
        attr: {
          placeholder: '请回收监控数量',
          required: true,
        },
      },
    },
    {
      prop: 'cameraDeviceIds',
      label: '移除监控设备',
      tag: {
        tagType: 'component',
        attr: {
          required: true,
        },
      },
    },
    {
      prop: 'memo',
      label: '回收说明',
      tag: {
        tagType: 'textarea',
        attr: {
          placeholder: '请输入回收说明',
          rows: 3,
          maxlength: 300,
          showCount: true,
        },
      },
    },
  ],
  form: formData,
  rules: [
    {
      name: 'pic',
      rules: [
        {
          required: true,
          errorMessage: '请选择回收照片',
        },
      ],
    },
    {
      name: 'recycleCnt',
      rules: [
        {
          required: true,
          errorMessage: '请回收监控数量',
        },
      ],
    },
  ],
});

const isSubmit = ref(false);

async function onSubmit() {
  if (isSubmit.value) return;

  const flag = await scFormRef.value.validate();
  if (!flag) return;

  isSubmit.value = true;

  try {
    const uploadComponent = scFormRef.value.startRef.pic;
    await uploadComponent.onUpload();
  } catch (error) {
    exceptionToast(error, '图片上传失败');
    isSubmit.value = false;
    return;
  }

  submitForm();
}

async function submitForm() {
  try {
    const formPayload = {
      ...formData.value,
      pic: formData.value.pic.map((item) => item.id).join(','),
    };

    if (!formPayload.pic) {
      isSubmit.value = false;
      return;
    }

    await ApiMonitorOrderUpdate(formPayload);

    // 设置刷新标记，通知相关页面刷新数据
    StorageSetMonitorFlowRefresh();

    Toast.success('提交成功', {
      complete: () => {
        Navigator.back();
      },
    });
  } catch (error) {
    exceptionToast(error, '提交失败');
    isSubmit.value = false;
  }
}

function onGotoHistory() {
  Navigator.push('/pages/recycling/list', { type: 1, tab: 0 });
}
</script>

<template>
  <div class="w-full min-h-screen p-3 bg-base box-border relative pb-16">
    <div class="mt-3 bg-white px-3 pt-4 rounded-md mb-4">
      <wd-steps :active="2" align-center>
        <wd-step
          title="回收预约"
          :description="FormatDateTime(project.reservationTime, 'YYYY-MM-DD HH:mm')"></wd-step>
        <wd-step title="上门回收" description=" "></wd-step>
      </wd-steps>

      <sc-form ref="scFormRef" :config="formConfig" class="mt-3">
        <template #recycleCnt>
          <sc-number-box v-model="formData.recycleCnt" :min="1" class="pr-1" />
        </template>

        <template #cameraDeviceIds>
          <div class="flex flex-col gap-2 w-full h-full justify-center">
            <div v-for="(item, index) in formData.cameraSerialNos" :key="index" class="w-full">
              {{ item }}
            </div>
          </div>
        </template>
      </sc-form>
    </div>

    <sc-button-fixed class="pt-5 box-border">
      <div class="w-full flex items-center justify-between">
        <div></div>
        <wd-button custom-class="!mr-6" type="text" icon="list" @click="onGotoHistory">
          进度查询
        </wd-button>
        <wd-button
          :disabled="isSubmit"
          custom-class="!rounded-md !w-1/2"
          size="large"
          type="primary"
          @click="onSubmit">
          提交
        </wd-button>
      </div>
    </sc-button-fixed>
  </div>
</template>
