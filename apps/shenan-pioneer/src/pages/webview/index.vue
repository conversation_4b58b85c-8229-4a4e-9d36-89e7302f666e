<script setup lang="ts">
import { ref } from 'vue';
import { ApiGetScCode } from '@shencom/api';
import { onLoad, onShareAppMessage, onShow } from '@dcloudio/uni-app';
import ScAuth from '@rms/components/sc-auth/sc-auth.vue';
import {
  exceptionHandler,
  parseUrlParams,
  objectToUrlParams,
  Toast,
  UserInfo,
  Navigator,
} from '@/utils';
import { scid } from '@/config/base';

interface Config {
  title: string;
  link: string;
  imgUrl: string;
}

const webViewSrc = ref('');

const shareConfig = ref<Config | null>(null);

const errorText = ref('');

const isLoading = ref(true);

const config = ref<Record<string, any>>({});

onLoad(async (option) => {
  config.value = getQuery(option || {});
  if (!config.value) return;

  isLoading.value = true;
  uni.showLoading({ title: '加载中...' });

  /** 处理 webview 加载事件未正常触发的情况 */
  setTimeout(() => {
    if (isLoading.value) hideLoading();
  }, 5000);
});

onShow(() => {
  setConfig();
});

// TODO: 添加分享配置
onShareAppMessage(() => {
  const { title: routeTitle, is_share } = config.value;

  if (+is_share && shareConfig.value) {
    const { title = routeTitle || '深分类', link, imgUrl } = shareConfig.value;
    const params = Object.entries(config.value)
      .map(([key, value]) => {
        if (key === 'url') {
          const url = link
            ? encodeURIComponent(link.replace(/(\??sccode=[0-9]*)?(&?uid=[0-9]*)?/g, ''))
            : value;
          return `${key}=${url}`;
        }
        return `${key}=${value}`;
      })
      .join('&');
    return { title, path: `/pages/webview/index?${params}`, imageUrl: imgUrl };
  }
  return {};
});

/** 解析路由参数 */
function getQuery(scene: Record<string, string | undefined>) {
  // 从小程序内部进入
  if (!scene.q) return scene;

  // 从普通二维码识别进入
  const { t, ...query } = parseUrlParams(scene) || {};

  // 二维码 scid 与当前环境是否一致，不一致则无法查看页面
  if (t !== scid) {
    errorText.value = '租户不匹配';
    return {};
  }

  errorText.value = '';
  return query;
}

/** 设置页面标题、分享配置 */
function setConfig() {
  const { is_share, title } = config.value;
  if (!+is_share) {
    uni.hideShareMenu({ hideShareItems: ['qq', 'qzone'] });
  }

  if (title) {
    /** 自定义页面标题 */
    uni.setNavigationBarTitle({ title });
  }
}

async function init() {
  /** 不重复加载 */
  if (webViewSrc.value) {
    hideLoading();
    return;
  }

  const { isToken } = config.value;

  const token = UserInfo.isLogin() && UserInfo.getToken();
  if (isToken && !token) {
    errorText.value = '请登录后访问';
    uni.scLogin();
    return;
  }

  /** 设置页面 url */
  const src = await getWebviewUrl();
  if (src) {
    webViewSrc.value = src;
    console.log('webViewSrc :>> ', webViewSrc.value);
  }
  hideLoading();
}

/** 获取页面 url 并处理参数 */
async function getWebviewUrl() {
  const { isToken, url } = config.value;

  const src = decodeURIComponent(url);
  if (!isToken) return src;

  const params = await getUrlParams();
  if (!params) return '';

  // 截取 #/ 及后面内容
  const routeReg = /#\/(.*)/;
  const route = src.match(routeReg);

  let query = (route && route[0]) || '';
  // 获取拼接分隔符
  const hasQuery = query.match(/(?:\?)(.*)/);
  const separator = hasQuery ? (hasQuery[1] ? '&' : '') : '?';

  // 拼接授权字段
  query = `${query}${separator}${params}`;

  // 活动的授权字段需编码
  const formatQuery = src.includes('isActivity') ? `&router=${encodeURIComponent(query)}` : query;
  return route ? src.replace(routeReg, formatQuery) : src + formatQuery;
}

async function getUrlParams() {
  try {
    const { data } = await ApiGetScCode();
    const sccode = data.code;
    if (sccode) {
      const { uid, openid } = UserInfo.getUserInfo() || {};
      const params = { sccode, uid, openid };
      return objectToUrlParams(params);
    }

    throw new Error('加载出错');
  } catch (error) {
    handleError(error, '网络异常');
  }

  return '';
}

function handleError(error: any, tips: string) {
  exceptionHandler(error);
  Toast.error(tips, {
    success: () => {
      onBack();
    },
  });
}

function onBack() {
  setTimeout(() => {
    Navigator.back();
  }, 1000);
}

function hideLoading() {
  try {
    uni.hideLoading();
  } catch (error) {
    exceptionHandler(error);
  } finally {
    isLoading.value = false;
  }
}

function onWebLoad(e: UniApp.Event) {
  console.log('onWebLoad :>> ', e.detail);
  hideLoading();
}

function onWebError(e: UniApp.Event) {
  console.log('onWebError :>> ', e);
  handleError(e, '加载出错');
  hideLoading();
}

function onPostMessage(e: UniApp.Event) {
  console.log('onPostMessage :>> ', e);
  hideLoading();
  // TODO: 设置转发分享配置
  const [data] = (e.detail.data || []).slice(-1);
  const { shareConfig: sConfig = {} } = data || {};
  shareConfig.value = sConfig;
}
</script>

<template>
  <sc-empty :show="!!errorText" :text="errorText" />
  <web-view
    v-if="webViewSrc"
    :src="webViewSrc"
    @load="onWebLoad"
    @error="onWebError"
    @message="onPostMessage"></web-view>
  <sc-auth @success-every="init" @cancel="onBack"></sc-auth>
</template>
