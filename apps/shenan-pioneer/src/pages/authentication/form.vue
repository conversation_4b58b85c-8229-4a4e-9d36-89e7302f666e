<script setup lang="ts">
import { ref, computed } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { ApiMemberAuthentication, getWorkTypeOptions } from '@/api';
import { Toast, ValidateIdCard } from '@rms/utils';
import { FormConfig } from '@rms/components/sc-form/sc-form.vue';
import { type MemberAuthenticationDTO, MemberAuthenticationFormDTO } from '@/@types/authentication';
import { UserInfo } from '@/utils';
import { EngineeringRoleEnum } from '@/config/engineering';

const router = useRouter();
const route = useRoute();
const loading = ref(false);

const type = computed(() => Number(route.query.type));

// 表单数据
const formData = ref<MemberAuthenticationFormDTO>({
  realname: '',
  mobile: UserInfo.getPhone() || '',
  type: type.value,
  idCard: UserInfo.getUserInfo()?.pid || '',
});

// 工种类型相关
const workTypeOptions = getWorkTypeOptions().map((item) => item.label);

// 计算属性 - 根据人员类型显示不同字段
const formTitle = computed(() => `${route.query.typeName || '人员'}认证`);

// 本人照片：施工负责人/施工工人/施工监理必填
const showPersonalPhoto = computed(() =>
  [
    EngineeringRoleEnum.MANAGER_NOT_AUTH,
    EngineeringRoleEnum.WORKER_NOT_AUTH,
    EngineeringRoleEnum.SUPERVISOR_NOT_AUTH,
  ].includes(type.value),
);

// 工种信息：施工负责人/施工工人选填
const showWorkInfo = computed(() =>
  [EngineeringRoleEnum.MANAGER_NOT_AUTH, EngineeringRoleEnum.WORKER_NOT_AUTH].includes(type.value),
);

// 监理信息：施工监理必填
const showSupervisorInfo = computed(() =>
  [EngineeringRoleEnum.SUPERVISOR_NOT_AUTH].includes(type.value),
);

const scFormRef = ref();

const formConfig = computed<FormConfig['config']>(() => {
  return {
    formAttrs: {
      labelWidth: '220rpx',
      fontSize: '15px',
      border: true,
    },
    data: [
      // 基本信息 - 所有角色必填
      {
        prop: 'realname',
        label: '姓名',
        itemAttrs: {
          required: true,
        },
        tag: {
          tagType: 'text',
          attr: {
            placeholder: '请输入姓名',
            required: true,
          },
        },
      },
      {
        prop: 'mobile',
        label: '手机号',
        itemAttrs: {
          required: true,
        },
        tag: {
          tagType: 'text',
          attr: {
            disabled: !!UserInfo.getPhone(),
            placeholder: '请输入手机号',
            required: true,
          },
        },
      },
      {
        prop: 'idCard',
        label: '身份证号码',
        itemAttrs: {
          required: true,
        },
        tag: {
          tagType: 'text',
          attr: {
            disabled: !!UserInfo.getUserInfo()?.pid,
            placeholder: '请输入身份证号码',
            required: true,
          },
        },
      },
      // 本人照片 - 施工负责人/施工工人/施工监理必填
      {
        prop: 'personalPhoto',
        label: '本人照片',
        isHide: !showPersonalPhoto.value,
        itemAttrs: {
          required: showPersonalPhoto.value,
        },
        tag: {
          tagType: 'upload',
          attr: {
            placeholder: '请上传高清正脸照',
            placeholderTop: true,
            placeholderClass: 'pl-3',
            maxCount: 1,
            required: showPersonalPhoto.value,
          },
        },
      },
      // 工种信息 - 施工负责人/施工工人选填
      {
        prop: 'workTypeName',
        label: '技术工种名称',
        isHide: !showWorkInfo.value,
        tag: {
          tagType: 'picker',
          options: workTypeOptions.map((item) => ({ id: item, value: item })),
          attr: {
            placeholder: '请选择工种类型',
          },
        },
      },
      {
        prop: 'certificateNumber',
        label: '证书编号',
        isHide: !showWorkInfo.value,
        tag: {
          tagType: 'text',
          attr: {
            placeholder: '请输入证书编号',
          },
        },
      },
      {
        prop: 'certificateStartDate',
        label: '证件有效期开始',
        isHide: !showWorkInfo.value,
        tag: {
          tagType: 'date-picker',
          attr: {
            placeholder: '请选择开始日期',
            type: 'date',
          },
        },
      },
      {
        prop: 'certificateEndDate',
        label: '证件有效期结束',
        isHide: !showWorkInfo.value,
        tag: {
          tagType: 'date-picker',
          attr: {
            placeholder: '请选择结束日期',
            type: 'date',
          },
        },
      },
      {
        prop: 'certificatePic',
        label: '证书照片',
        isHide: !showWorkInfo.value,
        tag: {
          tagType: 'upload',
          attr: {
            placeholder: '含人像面',
            placeholderTop: true,
            placeholderClass: 'pl-3',
            maxCount: 1,
          },
        },
      },
      // 监理信息 - 施工监理必填
      {
        prop: 'supervisorCertificateNumber',
        label: '监理员证件编号',
        isHide: !showSupervisorInfo.value,
        itemAttrs: {
          required: showSupervisorInfo.value,
        },
        tag: {
          tagType: 'text',
          attr: {
            placeholder: '请输入监理员证件编号',
            required: showSupervisorInfo.value,
          },
        },
      },
      {
        prop: 'supervisorCertificateStartDate',
        label: '证件有效期开始',
        isHide: !showSupervisorInfo.value,
        itemAttrs: {
          required: showSupervisorInfo.value,
        },
        tag: {
          tagType: 'date-picker',
          attr: {
            placeholder: '请选择开始日期',
            type: 'date',
            required: showSupervisorInfo.value,
          },
        },
      },
      {
        prop: 'supervisorCertificateEndDate',
        label: '证件有效期结束',
        isHide: !showSupervisorInfo.value,
        itemAttrs: {
          required: showSupervisorInfo.value,
        },
        tag: {
          tagType: 'date-picker',
          attr: {
            placeholder: '请选择结束日期',
            type: 'date',
            required: showSupervisorInfo.value,
          },
        },
      },
      {
        prop: 'supervisorCertificatePic',
        label: '监理员证件照片',
        isHide: !showSupervisorInfo.value,
        itemAttrs: {
          required: showSupervisorInfo.value,
        },
        tag: {
          tagType: 'upload',
          attr: {
            maxCount: 1,
            required: showSupervisorInfo.value,
          },
        },
      },
    ],
    form: formData.value,
    rules: [
      {
        name: 'realname',
        rules: [
          {
            required: true,
            errorMessage: '请输入姓名',
          },
        ],
      },
      {
        name: 'mobile',
        rules: [
          {
            required: true,
            errorMessage: '请输入手机号',
          },
        ],
      },
      {
        name: 'idCard',
        rules: [
          {
            required: true,
            errorMessage: '请输入身份证号码',
          },
          {
            validateFunction: (_, value) => {
              if (UserInfo.getUserInfo()?.pid) {
                return true;
              }
              return ValidateIdCard(value);
            },
            errorMessage: '请输入正确的身份证号码',
          },
        ],
      },
      ...(showPersonalPhoto.value
        ? [
            {
              name: 'personalPhoto',
              rules: [
                {
                  required: true,
                  errorMessage: '请上传本人照片',
                },
              ],
            },
          ]
        : []),
      ...(showSupervisorInfo.value
        ? [
            {
              name: 'supervisorCertificateNumber',
              rules: [
                {
                  required: true,
                  errorMessage: '请输入监理员证件编号',
                },
              ],
            },
            {
              name: 'supervisorCertificateStartDate',
              rules: [
                {
                  required: true,
                  errorMessage: '请选择监理员证件有效期开始时间',
                },
              ],
            },
            {
              name: 'supervisorCertificateEndDate',
              rules: [
                {
                  required: true,
                  errorMessage: '请选择监理员证件有效期结束时间',
                },
              ],
            },
            {
              name: 'supervisorCertificatePic',
              rules: [
                {
                  required: true,
                  errorMessage: '请上传监理员证件照片',
                },
              ],
            },
          ]
        : []),
    ],
  };
});

/**
 * 处理表单提交
 */
const handleSubmit = async () => {
  const flag = await scFormRef.value.validate();
  if (!flag) return;

  loading.value = true;

  const body: MemberAuthenticationDTO = {
    ...formData.value,
    type: type.value,
    personalPhoto: undefined,
    certificatePic: undefined,
    supervisorCertificatePic: undefined,
  };
  if (formData.value.personalPhoto?.length) {
    body.personalPhoto = formData.value.personalPhoto[0].remoteUrl;
  }
  if (formData.value.certificatePic?.length) {
    body.certificatePic = formData.value.certificatePic[0].remoteUrl;
  }
  if (formData.value.supervisorCertificatePic?.length) {
    body.supervisorCertificatePic = formData.value.supervisorCertificatePic[0].remoteUrl;
  }

  try {
    // 提交认证
    await ApiMemberAuthentication(body);

    Toast.success('认证提交成功');
    // 跳转到首页
    uni.$wx.miniProgram.redirectTo({
      url: '/pages/index/index',
    });
  } catch (e) {
    Toast.error('认证提交失败，请重试');
  } finally {
    loading.value = false;
  }
};
</script>

<template>
  <view class="min-h-screen bg-gray-50">
    <div class="text-xl font-bold bg-white text-center py-3">
      {{ formTitle }}
    </div>
    <div class="h-0.5"></div>
    <!-- 表单内容 -->
    <view class="pb-20">
      <view class="bg-white px-4">
        <sc-form ref="scFormRef" :config="formConfig" />
      </view>

      <!-- 提交按钮 -->
      <view class="fixed bottom-0 left-0 right-0 bg-white p-4 mt-1">
        <wd-button
          :disabled="loading"
          custom-class="!w-full !rounded-md"
          size="large"
          type="primary"
          @click="handleSubmit">
          {{ loading ? '提交中...' : '提交认证' }}
        </wd-button>
      </view>
    </view>
  </view>
</template>
