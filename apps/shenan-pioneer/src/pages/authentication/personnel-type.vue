<template>
  <view class="personnel-type-container">
    <!-- 头部导航 -->
    <sc-nav-bar title="人员认证" />

    <!-- 选择提示 -->
    <view class="select-tip">
      <text class="tip-text">请选择您的身份类型</text>
    </view>

    <!-- 人员类型选项 -->
    <view class="personnel-options">
      <view
        v-for="option in personnelTypeOptions"
        :key="option.value"
        class="option-item"
        @tap="handleSelectType(option)">
        <view class="option-content">
          <view class="option-info">
            <text class="option-title">{{ option.label }}</text>
            <text class="option-desc">{{ option.description }}</text>
          </view>
          <view class="option-arrow">
            <text class="arrow-icon">›</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 底部说明 -->
    <view class="footer-info">
      <text class="info-text">
        请根据您的实际身份选择正确的类型，系统将根据您的选择显示相应的认证表单
      </text>
    </view>
  </view>
</template>

<script setup lang="ts">
import { getPersonnelTypeOptions } from '@/api';
import { Navigator } from '@/utils';

// 获取人员类型选项
const personnelTypeOptions = getPersonnelTypeOptions();

/**
 * 处理选择人员类型
 */
const handleSelectType = (option: any) => {
  // 跳转到认证表单页面，传递选择的类型
  Navigator.push('/pages/authentication/form', {
    type: option.value,
    typeName: option.label,
  });
};
</script>

<style scoped>
.personnel-type-container {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.select-tip {
  padding: 32rpx 32rpx 16rpx;
  background-color: #fff;
}

.tip-text {
  font-size: 28rpx;
  color: #666;
  text-align: center;
}

.personnel-options {
  background-color: #fff;
  margin-top: 16rpx;
}

.option-item {
  border-bottom: 1rpx solid #eee;
}

.option-item:last-child {
  border-bottom: none;
}

.option-content {
  display: flex;
  align-items: center;
  padding: 32rpx;
  min-height: 120rpx;
}

.option-icon {
  width: 80rpx;
  height: 80rpx;
  margin-right: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f0f0f0;
  border-radius: 8rpx;
}

.option-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.option-title {
  font-size: 32rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 8rpx;
}

.option-desc {
  font-size: 26rpx;
  color: #999;
  line-height: 1.4;
}

.option-arrow {
  margin-left: 16rpx;
}

.arrow-icon {
  font-size: 36rpx;
  color: #ccc;
  font-weight: bold;
}

.footer-info {
  padding: 32rpx;
  background-color: #fff;
  margin-top: 16rpx;
}

.info-text {
  font-size: 26rpx;
  color: #999;
  line-height: 1.5;
  text-align: center;
}
</style>
