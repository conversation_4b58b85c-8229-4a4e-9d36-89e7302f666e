import { url } from '@/api/url';
import { IsDev, utilsOss } from '@/utils';

const jessibucaQueue: { success: any[]; fail: any[] } = { success: [], fail: [] };

export function loadJessibuca() {
  const id = 'jessibucaScript';
  const jessibuca = document.querySelector(`#${id}`);
  return new Promise<true | void>((resolve, reject) => {
    if (jessibuca) {
      if (jessibuca.getAttribute('loading')) {
        jessibucaQueue.success.push(resolve);
        jessibucaQueue.fail.push(reject);
      } else {
        resolve(true);
      }
      return;
    }
    const script = document.createElement('script');
    script.type = 'text/javascript';
    script.async = true;
    script.src = `https://scplugins.oss-cn-shenzhen.aliyuncs.com/plugins/cdn/jessibuca/jessibuca.js`;
    script.id = id;
    script.setAttribute('loading', 'true');

    script.onload = () => {
      script.removeAttribute('loading');
      resolve(true);
      jessibucaQueue.success.forEach((cb) => {
        cb(true);
      });
    };
    script.onerror = () => {
      document.body.removeChild(script);
      reject();
      jessibucaQueue.fail.forEach((cb) => {
        cb();
      });
      throw new Error('加载jessibuca视频插件失败');
    };

    document.body.appendChild(script);
  });
}
