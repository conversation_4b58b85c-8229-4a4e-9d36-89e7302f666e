<script setup lang="ts">
import { exceptionToast, Loading, utilsOss } from '@/utils';
import { loadJessibuca } from './utils';
import { ApiGetCameraLive, EventCameraPointRes } from '@/api';

const props = defineProps<{
  camera: EventCameraPointRes;
  id: string;
}>();

const player = ref<any>(null);
const showMask = ref(true); // 控制遮罩层显示状态

onMounted(async () => {
  const isLoad = await loadJessibuca();

  if (isLoad) {
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    player.value = new window.Jessibuca({
      container: `#flv_${props.id}`,
      isResize: false,
      decoder: `https://scplugins.oss-cn-shenzhen.aliyuncs.com/plugins/cdn/jessibuca/decoder.js`,
      operateBtns: {
        loadingText: '加载中...',
        fullscreen: true,
        useWebFullScreen: true,
        autoUseSystemFullScreen: true,
        screenshot: true,
        play: true,
        audio: true,
        record: true,
      },
    });

    player.value.on('play', () => {
      console.log('%c [play]-28', 'font-size:13px; background:#336699; color:#fff;', 'play');
    });
    player.value.on('pause', () => {
      console.log('%c [pause]-32', 'font-size:13px; background:#336699; color:#fff;', 'pause');
    });
    player.value.on('load', () => {
      // play();
      console.log('%c [load]-36', 'font-size:13px; background:#336699; color:#fff;', 'load');
    });
  }
});

function pause() {
  if (player.value) {
    player.value.pause();
  }
}

async function play() {
  if (player.value) {
    try {
      Loading.show('获取视频链接中...');
      const { channel, serialNo, type } = props.camera;
      const data = await ApiGetCameraLive({ channelNo: channel, serialNo, type });

      player.value.play(data.flvAddress);
      showMask.value = false; // 播放时隐藏遮罩层
    } catch (error) {
      exceptionToast(error, '');
    } finally {
      Loading.hide();
    }
  }
}

// 处理播放按钮点击
function handlePlayClick() {
  play();
}

onBeforeUnmount(() => {
  player.value.pause();
  player.value = null;
});

defineExpose({
  play,
  pause,
});
</script>

<template>
  <div class="video-wrapper">
    <!-- 视频容器 -->
    <div :id="`flv_${id}`" class="video-container"></div>

    <!-- 遮罩层 -->
    <div v-if="showMask" class="video-mask" @click="handlePlayClick">
      <div class="play-button">
        <svg class="play-icon" viewBox="0 0 24 24" fill="currentColor">
          <path d="M8 5v14l11-7z" />
        </svg>
      </div>
      <div class="play-text">点击播放</div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.video-wrapper {
  position: relative;
  width: 100%;
  height: 300px;
}

.video-container {
  width: 100%;
  height: 100%;
}

.video-mask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    background: rgba(0, 0, 0, 0.7);

    .play-button {
      transform: scale(1.1);
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
    }
  }
}

.play-button {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 16px;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.play-icon {
  width: 32px;
  height: 32px;
  color: #333;
  margin-left: 4px; // 稍微向右偏移，让播放图标看起来更居中
}

.play-text {
  color: white;
  font-size: 16px;
  font-weight: 500;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}
</style>
