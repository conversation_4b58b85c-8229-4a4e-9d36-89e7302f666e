<script setup lang="ts">
import { previewImg } from '@/utils';
import { nameMapColor } from '@/utils/monitor';

interface Option {
  value: string | number;
  label: string;
  children?: Option[];
  disabled?: boolean;
}

const props = withDefaults(
  defineProps<{
    width?: number;
    height?: number;
    imgs: {
      pics: string[];
      boxs: string[];
    };
    labels?: Option[];
  }>(),
  {
    labels: () => [],
  },
);

const canvasPics = ref<string[]>([]);

async function initCanvas() {
  const { pics, boxs } = props.imgs;
  pics.forEach((pic: string, idx: number) => {
    if (boxs[idx] && boxs[idx] !== '{}' && boxs[idx] !== '[]') {
      const img = new Image();
      img.crossOrigin = '';
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      img.src = pic || '';
      img.onload = () => {
        const { naturalWidth, naturalHeight } = img;
        if (ctx) {
          canvas.width = naturalWidth;
          canvas.height = naturalHeight;
          ctx.lineWidth = 3;
          ctx.drawImage(img, 0, 0, naturalWidth, naturalHeight);
          let data = JSON.parse(boxs[idx]);
          // 需要兼容单个json
          if (Object.prototype.toString.call(data) === '[object Object]') {
            data = [data];
          }
          data.forEach((area: any) => {
            ctx.fillStyle = 'rgba(249, 189, 75, 0.2)';
            ctx.strokeStyle = '#fc8631';
            if (area.type) {
              const points = JSON.parse(area.newTagCoord);
              // 这类数据有标签颜色
              if (area.labelId && props.labels?.length) {
                const tag = props.labels.find((t) => t.value === area.labelId);
                if (tag) {
                  const { darkA, dark } = nameMapColor(tag.label.trim(), { opacity: 0.4 });
                  ctx.fillStyle = darkA;
                  ctx.strokeStyle = dark;
                }
              }
              if (area.type === 'rectangle') {
                const xmin = points[0][0];
                const ymin = points[0][1];
                const xmax = points[1][0];
                const ymax = points[1][1];
                const w = xmax - xmin;
                const h = ymax - ymin;
                const x = +xmin;
                const y = +ymin;
                ctx.fillRect(x, y, w, h);
                ctx.strokeRect(x, y, w, h);
              }
              if (area.type === 'polygon') {
                ctx.beginPath();
                points.forEach(([pointX, pointY]: [number, number], i: number) => {
                  if (i === 0) {
                    ctx.moveTo(pointX, pointY);
                  }
                  if (i !== 0) {
                    ctx.lineTo(pointX, pointY);
                  }
                });
                ctx.closePath();
                ctx.fill();
                ctx.stroke();
              }
            } else {
              const { xmax, xmin, ymax, ymin } = area;
              const w = xmax - xmin;
              const h = ymax - ymin;
              const x = +xmin;
              const y = +ymin;
              ctx.fillRect(x, y, w, h);
              ctx.strokeRect(x, y, w, h);
            }
          });
          canvasPics.value[idx] = canvas.toDataURL('image/jpeg', 0.4);
        }
      };
    } else {
      canvasPics.value[idx] = pic;
    }
  });
}

onMounted(() => {
  initCanvas();
});

function toPreview() {
  previewImg(canvasPics.value);
}
</script>

<template>
  <img
    v-if="canvasPics.length"
    class="rounded-lg w-full h-full"
    :mode="height ? undefined : 'widthFix'"
    :src="canvasPics[0]"
    @click.stop="toPreview" />
  <div v-else :style="{ width: `${width}px`, height: `${height}px` }"></div>
</template>

<style lang="scss" scoped></style>
