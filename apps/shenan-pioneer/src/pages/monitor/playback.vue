<script setup lang="ts">
import { ApiGetMonitorHistory, ApiGetMonitorHistoryList, ApiGetMonitorHistoryListRes } from '@/api';
import { FormatDateTime } from '@shencom/utils';
import { dayjs } from 'wot-design-uni';
import flv3 from './components/flv3.vue';
import { exceptionToast, Loading } from '@/utils';

const props = defineProps<{
  serialNo: string;
  channelNo: string;
}>();

const today = dayjs().format('YYYY-MM-DD');
const end = dayjs().format('YYYY-MM-DD HH:mm:ss');

const date = ref<string[]>([`${today} 00:00:00`, `${today} 23:59:59`]);

function changeLog(e: any) {
  getList();
}

const showPopup = ref(false);
const url = ref<string>('');

async function handlePlay(item: ApiGetMonitorHistoryListRes) {
  try {
    Loading.show('加载中...');

    const data = await ApiGetMonitorHistory({
      serialNo: props.serialNo,
      channelNo: props.channelNo,
      startTime: item.startTime,
      endTime: item.endTime,
    });

    url.value = data.flvAddress;
    showPopup.value = true;
  } catch (error) {
    exceptionToast(error, '');
  } finally {
    Loading.hide();
  }
}

function handleClose() {
  url.value = '';
  showPopup.value = false;
}

const list = ref<ApiGetMonitorHistoryListRes[]>([]);

async function getList() {
  try {
    Loading.show('加载中...');

    list.value = await ApiGetMonitorHistoryList({
      serialNo: props.serialNo,
      channelNo: props.channelNo,
      startTime: date.value[0],
      endTime: date.value[1],
    });
  } catch (error) {
    exceptionToast(error, '');
  } finally {
    Loading.hide();
  }
}

onMounted(async () => {
  getList();
});
</script>

<template>
  <view class="p-3">
    <uni-datetime-picker
      v-model="date"
      :clear-icon="false"
      type="datetimerange"
      :end="end"
      @change="changeLog" />
  </view>

  <view class="p-3">
    <view
      v-for="item in list"
      :key="item.name"
      class="border border-gray-200 rounded-md px-2 py-3 mb-3 relative">
      <view class="text-16 mb-2">监控名称：{{ item.name }}</view>
      <view class="flex text-12 text-gray-500">
        时间段：{{ FormatDateTime(item.startTime) }}-{{ FormatDateTime(item.endTime) }}
      </view>

      <view class="absolute top-5 right-1" @click="handlePlay(item)">
        <sc-icon name="i-solar:play-circle-linear" size="30" class="text-blue-400"></sc-icon>
      </view>
    </view>
  </view>

  <wd-popup v-model="showPopup" title="监控回放" @close="handleClose">
    <div class="w-screen">
      <flv3 v-if="url" :url="url"></flv3>
    </div>
  </wd-popup>
</template>

<style lang="scss" scoped></style>
