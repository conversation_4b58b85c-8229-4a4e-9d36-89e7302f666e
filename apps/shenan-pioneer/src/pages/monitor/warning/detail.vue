<script setup lang="ts">
import { ApiGetWarningInfo, ApiGetWarningListRes } from '@/api';
import { FormatDateTime } from '@shencom/utils';
import Picture from '../components/picture.vue';
import { ApiUpdateMessageStatus } from '@/api/modules/message';
import { StorageSetMessageRefresh } from '@/storage/message';
import { exceptionHandler } from '@/utils';

const props = defineProps<{
  id: string;
  msgId: string;
}>();

const eventInfo = ref<ApiGetWarningListRes>();
onMounted(async () => {
  const data = await ApiGetWarningInfo(props.id);
  eventInfo.value = data;
  eventInfo.value.regionName = [data.districtName, data.streetName, data.villageName].join('');
});

onLoad((options) => {
  if (options?.msgId) {
    try {
      ApiUpdateMessageStatus({ id: options.msgId, status: 1 });
      StorageSetMessageRefresh();
    } catch (error) {
      exceptionHandler(error);
    }
  }
});
</script>

<template>
  <view class="bg2 min-h-screen p-5">
    <wd-cell-group title="事件详情" custom-class="overflow-hidden rounded-lg mb-5 !text-13">
      <wd-cell title="事件编号" :value="eventInfo?.eventNo" />
      <wd-cell title="场景类别" :value="eventInfo?.sceneCategoryName" />
      <wd-cell title="违规类型" :value="eventInfo?.sceneName" />
      <wd-cell title="违规问题" :value="eventInfo?.typeName" />
      <wd-cell title="工程名称" :value="eventInfo?.projectName" />
      <wd-cell title="所属区域" :value="eventInfo?.regionName" />
      <wd-cell title="工程类别" :value="eventInfo?.projectCateName" />
      <wd-cell title="详细地址" :value="eventInfo?.projectAddress" />
      <wd-cell title="监控设备号">
        <template #default>
          <div class="text-wrap w-[200px] text-left warpText">{{ eventInfo?.monitorNo }}</div>
        </template>
      </wd-cell>
      <wd-cell title="报警时间" :value="FormatDateTime(eventInfo?.eventAt)" />
    </wd-cell-group>

    <wd-cell-group v-if="eventInfo" title="事件捕获" custom-class="overflow-hidden rounded-lg mb-5">
      <wd-cell title="违规图片" />
      <div class="w-full flex justify-center pb-3">
        <Picture
          :width="330"
          :imgs="{ pics: [eventInfo.pics], boxs: [eventInfo.violationBox] }"></Picture>
      </div>
    </wd-cell-group>

    <wd-cell-group
      v-if="eventInfo"
      title="事件流转"
      custom-class="overflow-hidden rounded-lg mb-5"></wd-cell-group>
  </view>
</template>

<style lang="scss" scoped>
.bg2 {
  --wot-cell-group-title-fs: 16px;
  --wot-cell-title-fs: 14px;
  --wot-cell-value-fs: 14px;
  background: #f6f8ff;
}

.warpText {
  word-wrap: break-word;
  word-break: normal;
}
</style>
