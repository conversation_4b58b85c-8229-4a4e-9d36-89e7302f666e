<script setup lang="ts">
import {
  ApiGetMonitorInfo,
  ApiGetMonitorListMobile,
  ApiGetProjectList,
  ApiGetWarningList,
  ApiGetWarningListRes,
  EventCameraPointRes,
} from '@/api';
import { useList } from '@rms/components/sc-list/sc-list';
import { FormatDateTime } from '@shencom/utils';
import Picture from '../components/picture.vue';
import { ServiceRegion } from '@rms/service/src';
import { exceptionHandler, Navigator } from '@/utils';
import { ApiQueryHandler } from '@shencom/api';
import { watchThrottled } from '@vueuse/core';
import { useRoleRegion } from '@/hooks/useRoleRegion';

const props = defineProps<{
  id?: string;
  eid?: string;
  projectId?: string;
}>();

const filterData = reactive({
  regionName: '',
  projectName: '',
  monitorName: '',
});

const isInit = ref(false);
const { roleLevel, handleData } = useRoleRegion();

const scRegionRef = ref();
const searchInfo = reactive({
  regions: [] as string[],
});

const searchVal = ref('');
const monitorId = ref('');

const eventCameraPointList = ref<EventCameraPointRes[]>([]);
const monitorChannel = ref('');

const paramsComp = computed(() => {
  const [regionPid, regionId, regionCid] = searchInfo.regions;
  const q = [
    [projectId.value, 'projectId', 'number'],
    [monitorChannel.value, 'deviceCode', 'string'],
  ].filter((i) => !!i[0]) as any;

  const query = q.length ? ApiQueryHandler(q) : null;
  if (props.projectId) {
    projectId.value = props.projectId;
  }

  console.log('%c []-43', 'font-size:13px; background:#336699; color:#fff;', projectId.value);
  return {
    keyword: searchVal.value,
    projectId: projectId.value,
    regionPid,
    regionId,
    regionCid,
  };
});

const { list, listProps, getData } = useList<ApiGetWarningListRes[]>({
  request: ApiGetWarningList,
  params: {},
  immediate: false,
  handle: async (res) => {
    return res;
  },
});

async function setRegionName(region: string[]) {
  let res = await ServiceRegion();

  filterData.regionName =
    region
      .filter(Boolean)
      .map((r) => {
        const item = res.find((i) => i.id === r);
        res = item?.children || [];
        return item?.title;
      })
      .pop() || '';
}

async function onClear() {
  filterData.projectName = '';
  projectId.value = '';
  searchInfo.regions = [];
  filterData.monitorName = '';
  monitorChannel.value = '';
  monitorId.value = '';
  searchVal.value = '';

  await getData(true, paramsComp.value);
}

function onRegionShow() {
  scRegionRef.value.show();
}

const stallPickerRef = ref();
const stallPickerRef2 = ref();
const projectId = ref('');
const projectColumns = ref<any[]>([]);

function onMonitorShow() {
  stallPickerRef2.value.open();
}

function onProjectShow() {
  stallPickerRef.value.open();
}

watch(
  () => searchInfo.regions,
  () => {
    const [regionPid, regionId, regionCid] = searchInfo.regions;
    setRegionName(searchInfo.regions);
    projectId.value = '';
    projectColumns.value = [];
    filterData.projectName = '';
    getProjectList({
      regionPid,
      regionId,
      regionCid,
    });
    if (isInit.value) {
      getData(true, paramsComp.value);
    }
  },
  { immediate: true, deep: true },
);

watch(
  () => projectId.value,
  () => {
    monitorChannel.value = '';
    monitorId.value = '';
    filterData.monitorName = '';
    getMonitorList(projectId.value);
  },
  { immediate: true },
);

function onConfirmProject(e: any) {
  console.log('%c [e]-136', 'font-size:13px; background:#336699; color:#fff;', e);
  filterData.projectName = e.selectedItems.label;
  projectId.value = e.value;
  nextTick(() => {
    getData(true, paramsComp.value);
  });
}

function onConfirmMonitor(e: any) {
  monitorId.value = e.value;
  monitorChannel.value = e.selectedItems.channel;
  filterData.monitorName = e.selectedItems.label;
  getData(e.value, paramsComp.value);
}

async function getProjectList(params: any = {}) {
  try {
    const data = await ApiGetProjectList({
      size: 999,
      ...params,
    });
    projectColumns.value = data.content.map((item) => ({
      label: item.name,
      value: item.id,
      ...item,
    }));
  } catch (error) {
    exceptionHandler(error);
  }
}

async function getMonitorList(id: string) {
  try {
    eventCameraPointList.value = [];
    let arr: EventCameraPointRes[] = [];
    const data = await ApiGetMonitorListMobile(id);
    data.content.forEach((info) => {
      arr = [...arr, ...info.eventCameraPointList];
    });

    eventCameraPointList.value = arr.map((item) => ({
      label: item.channel,
      value: item.id,
      ...item,
    }));
  } catch (error) {
    exceptionHandler(error);
  }
}

function toDetail(item: ApiGetWarningListRes) {
  Navigator.push('/pages/monitor/warning/detail', {
    id: item.id,
  });
}

onMounted(async () => {
  if (props.id) {
    const data = await ApiGetMonitorInfo(props.id);
    const target = data.eventCameraPointList.find((item) => item.id === props.eid);
    if (target) {
      searchInfo.regions = [data.regionPid, data.regionId, data.regionCid];

      nextTick(() => {
        projectId.value = data.projectId;
        filterData.projectName = data.projectName;
        nextTick(() => {
          monitorId.value = target.id;
          monitorChannel.value = target.channel;
          filterData.monitorName = target.monitorNo;
          isInit.value = true;
          getData(true, paramsComp.value);
        });
      });
    } else {
      isInit.value = true;
      getData(true, {});
    }
  } else if (!props.projectId) {
    isInit.value = true;
    await getData(true, {});
  } else {
    isInit.value = true;
  }
});

onPullDownRefresh(async () => {
  await onClear();
  uni.stopPullDownRefresh();
});

watchThrottled(
  () => searchVal.value,
  () => {
    getData(true, paramsComp.value);
  },
);

onReachBottom(() => null);
</script>

<template>
  <div v-if="isInit" class="bg2 min-h-screen">
    <div v-if="!props.projectId" class="w-full bg-white py-3 rounded-b-xl mb-3">
      <wd-search
        v-if="!id"
        v-model="searchVal"
        placeholder="请输入工程名称"
        hide-cancel
        custom-class="mb-3" />
      <div class="flex justify-around items-center text-gray-500 text-12 px-2 gap-1">
        <div
          class="rounded-2xl text-center bg-gray-100 h-[30px] ellipsis-1 w-[100px] leading-[30px] px-1"
          @click="onRegionShow">
          <span class="mr-1">{{ filterData.regionName || '所属区域' }}</span>
          <wd-icon name="arrow-down" size="15px"></wd-icon>
        </div>

        <div
          class="rounded-2xl text-center bg-gray-100 h-[30px] ellipsis-1 w-[150px] leading-[30px] px-1"
          @click="onProjectShow">
          <span class="mr-1">{{ filterData.projectName || '选择工程' }}</span>
          <wd-icon name="arrow-down" size="15px"></wd-icon>
        </div>

        <div
          v-show="projectId"
          class="rounded-2xl text-center bg-gray-100 h-[30px] ellipsis-1 w-[100px] leading-[30px] px-1"
          @click="onMonitorShow">
          <span class="mr-1">{{ filterData.monitorName || '监控设备' }}</span>
          <wd-icon name="arrow-down" size="15px"></wd-icon>
        </div>

        <wd-button type="error" plain size="small" @click="onClear">清空</wd-button>
      </div>
    </div>

    <sc-list v-bind="listProps" class="!p-0 !h-auto !min-h-[calc(100vh-38px)]">
      <div class="w-full p-3">
        <div
          v-for="item of list"
          :key="item.id"
          class="bg-white rounded-lg p-3 mb-3 flex gap-2"
          @click="toDetail(item)">
          <div class="w-[100px] h-[100px] pt-1">
            <Picture
              :width="100"
              :height="100"
              :imgs="{ pics: [item.pics], boxs: [item.violationBox] }"></Picture>
          </div>
          <div class="flex-1 flex flex-col gap-1 text-13">
            <div class="text-15 font-bold">{{ item.typeName }}</div>
            <div>报警时间：{{ FormatDateTime(item.eventAt) }}</div>
            <div>小散工程名称：{{ item.projectName }}</div>
            <div>小散工程地址：{{ item.projectAddress }}</div>
          </div>
        </div>
      </div>
    </sc-list>
  </div>

  <wd-select-picker
    ref="stallPickerRef"
    v-model="projectId"
    :z-index="9999"
    label=""
    :columns="projectColumns"
    type="radio"
    use-default-slot
    filterable
    @confirm="onConfirmProject">
    <div></div>
  </wd-select-picker>

  <wd-select-picker
    ref="stallPickerRef2"
    v-model="monitorId"
    :z-index="9999"
    label=""
    :columns="eventCameraPointList"
    type="radio"
    use-default-slot
    filterable
    @confirm="onConfirmMonitor">
    <div></div>
  </wd-select-picker>

  <sc-region
    v-if="typeof roleLevel === 'number'"
    ref="scRegionRef"
    v-model="searchInfo.regions"
    :handle-data="handleData"
    is-hide></sc-region>
</template>

<style lang="scss" scoped>
:deep(.wd-radio__label) {
  display: flex;
  align-items: center;
}
:deep(.wd-radio) {
  margin: 0 !important;
}

.bg-sky1 {
  // 天空蓝渐变色
  background-image: linear-gradient(120deg, #a1c4fd 0%, #c2e9fb 100%);
}

.bg2 {
  background: #f6f8ff;
}
</style>
