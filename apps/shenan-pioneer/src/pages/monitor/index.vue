<script setup lang="ts">
import {
  ApiGetMonitorListMobile,
  ApiGetProjectList,
  ApiGetProjectShow,
  EventCameraPointRes,
} from '@/api';
import xpMuiplayer from './components/xpMuiplayer.vue';
import flv2 from './components/flv2.vue';
import { exception<PERSON><PERSON><PERSON>, Navigator, Toast } from '@/utils';
import { ServiceRegion } from '@rms/service/src';
import { useRoleRegion } from '@/hooks/useRoleRegion';

const dataInfo = ref<any>();

const props = defineProps<{
  projectId?: string;
}>();

const eventCameraPointList = ref<EventCameraPointRes[]>([]);

const filterData = reactive({
  regionName: '',
  projectName: '',
});

async function setRegionName(region: string[]) {
  let res = await ServiceRegion();

  filterData.regionName =
    region
      .filter(Boolean)
      .map((r) => {
        const item = res.find((i) => i.id === r);
        res = item?.children || [];
        return item?.title;
      })
      .pop() || '';
}

const scRegionRef = ref();
const searchInfo = reactive({
  regions: [] as string[],
  projectId: '',
  monitorId: '',
});

function onRegionShow() {
  if (typeof roleLevel.value !== 'number') {
    Toast.error('暂无权限');
    return;
  }

  scRegionRef.value.show();
}

const stallPickerRef = ref();
const projectId = ref('');
const projectColumns = ref<any[]>([]);

function onProjectShow() {
  stallPickerRef.value.open();
}

const { roleLevel, handleData } = useRoleRegion();

watch(
  () => searchInfo.regions,
  () => {
    const [regionPid, regionId, regionCid] = searchInfo.regions;
    setRegionName(searchInfo.regions);
    projectId.value = '';
    eventCameraPointList.value = [];
    projectColumns.value = [];
    filterData.projectName = '';
    getProjectList({
      regionPid,
      regionId,
      regionCid,
    });
  },
  { deep: true },
);

async function initProject(id: string) {
  try {
    const res = await ApiGetProjectShow(id);
    if (!res) {
      throw new Error('项目不存在');
    }
    projectColumns.value = [
      {
        label: res.name,
        value: res.id,
        ...res,
      },
    ];
    onConfirmProject({
      selectedItems: { label: res.name },
      value: res.id,
    });
  } catch (error) {
    exceptionHandler(error);
    getProjectList();
  }
}

function onConfirmProject(e: any) {
  filterData.projectName = e.selectedItems.label;
  projectId.value = e.value;
  getMonitorList(e.value);
}

async function getProjectList(params: any = {}) {
  try {
    const data = await ApiGetProjectList({
      size: 999,
      ...params,
    });
    projectColumns.value = data.content.map((item) => ({
      label: item.name,
      value: item.id,
      ...item,
    }));
  } catch (error) {
    exceptionHandler(error);
  }
}

const projectName = ref('');

async function getMonitorList(id: string) {
  const data = await ApiGetMonitorListMobile(id);
  projectName.value = data.content[0]?.projectName || '';
  eventCameraPointList.value = [];
  data.content.forEach((info) => {
    eventCameraPointList.value = [...eventCameraPointList.value, ...info.eventCameraPointList];
  });
}

function onClear() {
  filterData.projectName = '';
  projectId.value = '';
  searchInfo.regions = [];
  eventCameraPointList.value = [];
}

function onMonitorPlayback(item: EventCameraPointRes) {
  Navigator.push(`/pages/monitor/playback`, {
    serialNo: item.serialNo,
    channelNo: item.channel,
  });
}

function onAIAlarm(item: EventCameraPointRes) {
  Navigator.push(`/pages/monitor/warning/list`, {
    id: item.deviceId,
    eid: item.id,
  });
}

function onDeviceRepair() {
  console.log('设备保修');
}

watch(
  () => projectColumns.value,
  () => {
    if (props.projectId) {
      filterData.projectName = projectColumns.value.find((i) => i.value === props.projectId)?.label;
    }
  },
  { immediate: true, deep: true },
);

onMounted(() => {
  // getMonitorList();
  if (props.projectId) {
    projectId.value = props.projectId;
    getMonitorList(props.projectId);
  }
});

onLoad((options) => {
  initProject(options?.id);
});

onPullDownRefresh(() => {
  filterData.projectName = '';
  projectId.value = '';
  searchInfo.regions = [];
  eventCameraPointList.value = [];
  uni.stopPullDownRefresh();
});
</script>

<template>
  <view class="bg-[#F6F8FF] min-h-screen p-3">
    <div class="w-full bg-white py-3 rounded-xl mb-3">
      <div class="flex justify-around items-center text-gray-500 text-12 px-2">
        <div
          class="rounded-2xl text-center bg-gray-100 h-[30px] ellipsis-1 w-[100px] leading-[30px] px-1"
          @click="onRegionShow">
          <span class="mr-1">{{ filterData.regionName || '所属区域' }}</span>
          <wd-icon name="arrow-down" size="15px"></wd-icon>
        </div>

        <div
          class="rounded-2xl text-center bg-gray-100 h-[30px] ellipsis-1 w-[150px] leading-[30px] px-1"
          @click="onProjectShow">
          <span class="mr-1">{{ filterData.projectName || '选择工程' }}</span>
          <wd-icon name="arrow-down" size="15px"></wd-icon>
        </div>

        <wd-button type="error" plain size="small" @click="onClear">清空</wd-button>
      </div>
    </div>

    <!-- <xp-muiplayer
      v-if="eventCameraPointList.length"
      title="123"
      src="https://aiot.hifenlei.com/rtp/44030700091328130791_44030700091328130791_0.live.flv"
      type="flv"
      :live="true"></xp-muiplayer> -->

    <div
      v-for="item of eventCameraPointList"
      :key="item.id"
      class="relative bg-white rounded-xl py-2 mb-3">
      <div class="flex items-center justify-between p-2">
        <div class="flex-1">{{ projectName }} - {{ item.channel }}</div>
        <div :class="item.status === 1 ? 'text-green-500' : 'text-red-500'" class="w-[30px]">
          {{ item.status === 1 ? '在线' : '离线' }}
        </div>
      </div>
      <flv2 :id="item.id" :camera="item"></flv2>

      <div class="py-3 flex justify-around">
        <!-- <wd-radio-group v-model="current" custom-class="flex justify-around" shape="button">
          <wd-radio :value="1">实时监控</wd-radio>
          <wd-radio :value="2">监控回放</wd-radio>
          <wd-radio :value="3">AI告警</wd-radio>
          <wd-radio :value="4">设备保修</wd-radio>
        </wd-radio-group> -->
        <wd-button type="primary" plain size="small">实时监控</wd-button>
        <wd-button type="info" plain size="small" @click="onMonitorPlayback(item)">
          监控回放
        </wd-button>
        <wd-button type="info" plain size="small" @click="onAIAlarm(item)">AI告警</wd-button>
        <wd-button type="info" plain size="small">设备保修</wd-button>
      </div>
    </div>

    <div v-if="!eventCameraPointList.length" class="flex justify-center items-center h-[50vh]">
      <wd-status-tip
        image="search"
        :tip="filterData.projectName ? '暂无监控设备' : '请先选择工程'" />
    </div>
  </view>

  <wd-select-picker
    ref="stallPickerRef"
    v-model="projectId"
    :z-index="9999"
    label="输入摊位名称"
    :columns="projectColumns"
    type="radio"
    use-default-slot
    filterable
    @confirm="onConfirmProject">
    <div></div>
  </wd-select-picker>

  <sc-region
    v-if="typeof roleLevel === 'number'"
    ref="scRegionRef"
    v-model="searchInfo.regions"
    :handle-data="handleData"
    is-hide></sc-region>
</template>

<style lang="scss" scoped>
:deep(.wd-radio) {
  margin: 0 !important;
}
</style>
