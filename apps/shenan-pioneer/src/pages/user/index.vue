<script setup lang="ts">
import { computed, onMounted, ref } from 'vue';
import { ApiSysUpdateInfo, ApiWechatUpdateInfo } from '@shencom/api';
import ScForm from '@rms/components/sc-form/sc-form.vue';
import { ServiceLogin, getUserProfile } from '@rms/service';
import { createImgFile, getFileName } from '@rms/components/sc-upload/utils';
import { ChooseFile, TempFile } from '@rms/components/sc-upload/type';
import { ossSign, ossUpload } from '@rms/components/sc-upload/file';
import {
  UserInfo,
  utilsOss,
  Loading,
  exceptionHandler,
  exceptionToast,
  Toast,
  Navigator,
  compareVersion,
} from '@/utils';
import { ApiResourceMenuJava } from '@rms/api/src';
import useRoleStore from '@/hooks/useRole';

interface FormProps {
  realname: string;
  nickname: string;
  phone: string;
  avatar: (SC.File | null)[];
  sex?: number;
}

const imgPath = `${utilsOss.imgPath}`;

const scFormRef = ref<InstanceType<typeof ScForm> | null>(null);

const isEdit = ref(false);

const isNewVersion = ref(false);

const roleStore = useRoleStore();

const form = ref<FormProps>({
  realname: '',
  nickname: '',
  phone: '',
  avatar: [],
  sex: undefined,
});

const formConfig = computed<any>(() => ({
  form: form.value,
  rules: [
    {
      name: 'realname',
      rules: [
        {
          required: true,
          errorMessage: '请输入真实姓名',
        },
      ],
    },
    {
      name: 'avatar',
      rules: [
        {
          validateFunction: (_, value) => {
            return !!value.length;
          },
          errorMessage: '请上传头像',
        },
      ],
    },
  ],
  formAttrs: {
    labelWidth: 100,
  },
  data: [
    {
      prop: 'realname',
      label: '真实姓名',
      tag: {
        tagType: 'text',
        attr: {
          placeholder: '请输入真实姓名',
          disabled: !isEdit.value,
          required: isEdit.value,
        },
      },
    },
    {
      prop: 'nickname',
      label: '昵称',
      tag: {
        tagType: 'text',
        attr: {
          type: 'nickname',
          placeholder: '请输入昵称',
          disabled: !isEdit.value,
          required: isEdit.value,
        },
      },
    },
    {
      prop: 'phone',
      label: '手机号码',
      tag: {
        tagType: 'text',
        attr: {
          placeholder: '请输入手机号码',
          required: isEdit.value,
          disabled: true,
        },
      },
    },
    {
      prop: 'avatar',
      label: '头像',
      tag: {
        tagType: 'upload',
        attr: {
          disabled: !isEdit.value,
          required: isEdit.value,
          'max-count': '1',
          deletable: isEdit.value,
        },
      },
    },
    {
      prop: 'component1',
      label: '',
      isHide: !isEdit.value,
      tag: {
        tagType: 'component',
      },
    },
  ],
}));

let rawForm: FormProps = JSON.parse(JSON.stringify(form.value));

// 设置表单数据
function settingForm(formData: Partial<FormProps> = {}) {
  form.value.avatar = formData.avatar || [
    createImgFile(UserInfo.getAvatar() || `${imgPath}/default_avatar.svg`),
  ];
  form.value.nickname = formData.nickname || UserInfo.getNickname() || '微信用户';
  form.value.phone = formData.phone || UserInfo.getPhone() || '';
  form.value.realname = formData.realname || UserInfo.getRealname() || '未设置';
}

/**
 * 更新微信用户信息
 */
async function onHandleUpdateWxInfo() {
  try {
    const userInfo = UserInfo.getUserInfo();

    if (!userInfo) {
      Toast.error('登录信息过期', {
        complete: () => {
          Navigator.back(1);
        },
      });
      return;
    }

    const initWxData = {
      nickname: form.value.nickname,
      avatar: form.value?.avatar[0]?.remoteUrl || '',
      openid: userInfo.openid || '',
    };
    const initSysData = {
      id: userInfo.uid || '',
      ...form.value,
      avatar: form.value?.avatar[0]?.remoteUrl || '',
      phone: '',
    };

    await ApiWechatUpdateInfo(initWxData);
    try {
      await ApiSysUpdateInfo(initSysData);
    } catch (error) {
      exceptionToast(error, '');
    }
    await ServiceLogin.autoLogin();

    Toast.success('更新成功', {
      complete: () => {
        UserInfo.setUserInfo({
          ...userInfo,
          ...form.value,
          avatar: form.value?.avatar[0]?.remoteUrl || '',
        });
        Navigator.back(1);
      },
    });
  } catch (error) {
    exceptionHandler(error);
  }
}

async function uploadAvatar(file: TempFile) {
  const sign = await ossSign();

  if (!sign) return null;

  const res = (await ossUpload({ sign, file })) as SC.File;

  return { status: 'success', progress: 100, ...file, ...res };
}

function formatImage(res: UniApp.Event) {
  const { avatarUrl } = res.detail;

  const file = { name: getFileName(avatarUrl), path: avatarUrl, size: 0, type: 'image' };

  return [
    { name: getFileName(avatarUrl), path: avatarUrl, size: 0, type: 'image', file },
  ] as ChooseFile[];
}

/**
 * 获取微信头像
 */
async function getWxAvatar(e: UniApp.Event) {
  console.log('%c [e]-213', 'font-size:13px; background:#336699; color:#fff;', e);
  try {
    Loading.show('上传中...');
    const file = formatImage(e);
    console.log('%c [file]-216', 'font-size:13px; background:#336699; color:#fff;', file);

    const avatarImg = await uploadAvatar(file[0]);

    if (avatarImg) {
      form.value.avatar = [avatarImg];
      Toast.success('获取成功');
    } else {
      Toast.error('获取头像失败');
    }
  } catch (error) {
    exceptionToast(error, '获取头像失败');
  }
}

/**
 * 同步微信信息
 */
async function onHandleAsyncWxInfo() {
  try {
    Loading.show('加载中...');
    const { userInfo } = await getUserProfile();

    settingForm({
      realname: form.value.realname || userInfo.nickName,
      nickname: userInfo.nickName,
      sex: userInfo.gender,
      avatar: [createImgFile(userInfo.avatarUrl)],
    });

    await onHandleUpdateWxInfo();
  } catch (error) {
    exceptionHandler(error);
  } finally {
    Loading.hide();
  }
}

function resetFormData() {
  settingForm(rawForm);
}

async function onHandleEdit() {
  isEdit.value = true;
}

async function onHandleSave() {
  const flag = await scFormRef.value?.validate();
  if (!flag) return;
  onHandleUpdateWxInfo();
  isEdit.value = false;
}

function onHandleCancel() {
  resetFormData();
  isEdit.value = false;
}

const btnShow = reactive({
  logout: false,
  edit: false,
});

async function getBtnShow() {
  try {
    const { data: logout } = await ApiResourceMenuJava({
      group: ['userinfo-logout'],
      size: 1,
    });

    const { data: edit } = await ApiResourceMenuJava({
      group: ['userinfo-edit'],
      size: 1,
    });

    btnShow.logout = !!logout.length;
    btnShow.edit = !!edit.length;
  } catch (error) {
    exceptionHandler(error);
  }
}

onMounted(() => {
  settingForm();
  rawForm = JSON.parse(JSON.stringify(form.value));
  getBtnShow();
});

async function onLogout() {
  roleStore.roleInfo.isInit = false;
  await ServiceLogin.logout();
  UserInfo.clearAllUser();
  uni.reLaunch({
    url: '/pages/index/index',
  });
}
</script>

<template>
  <view class="bg-[#f5f5f5] w-screen h-screen" :class="{ fixPadding: !isEdit }">
    <sc-group>
      <sc-form ref="scFormRef" :config="formConfig">
        <template #component1>
          <sc-button
            class="flex-1 !rounded !mb-2 !w-[160rpx] !h-[60rpx] !px-0 !top-[-40rpx]"
            open-type="chooseAvatar"
            @chooseavatar="getWxAvatar">
            <text class="text-11 flex items-center">获取微信头像</text>
          </sc-button>
        </template>
      </sc-form>
    </sc-group>

    <view v-if="!isEdit" class="w-full px-5 pt-10 flex flex-col gap-3">
      <sc-button v-if="btnShow.edit" block type="primary" @click="onHandleEdit">
        修改个人信息
      </sc-button>
      <sc-button v-if="btnShow.logout" block type="default" @click="onLogout">退出登录</sc-button>
    </view>

    <sc-group v-else class="bg-transparent pt-5">
      <sc-button class="flex-1 !rounded !mb-2" @click="onHandleSave">保存</sc-button>
      <sc-button type="default" class="flex-1 !rounded" @click="onHandleCancel">取消</sc-button>
    </sc-group>
  </view>

  <Tabbar />
</template>

<style lang="scss" scoped>
.fixPadding {
  :deep(.avatar) {
    .uni-forms-item__label {
      padding-left: 10px !important;
    }
  }
}
</style>
