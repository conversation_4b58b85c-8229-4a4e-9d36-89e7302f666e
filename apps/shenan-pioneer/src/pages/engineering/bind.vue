<script setup lang="ts">
import {
  ApiSearchProject,
  ApiBindProject,
  type ProjectSearchResponse,
  ApiGetAllRoles,
} from '@/api';
import { Toast, scanCode } from '@/utils';
import ProjectInfoModal from './components/project-info-modal.vue';
import AuthenticationPromptModal from './components/authentication-prompt-modal.vue';
import { useRoute } from 'vue-router';

const route = useRoute();

// 表单数据
const formData = reactive({
  projectNumber: '',
  inviteCode: route.query.code as string,
});

// 弹框状态
const showProjectModal = ref(false);
const showAuthModal = ref(false);
const projectInfo = ref<ProjectSearchResponse | null>(null);
const loading = ref(false);
const isAuthenticated = ref(false);
const searchError = ref(''); // 添加错误信息状态

// 表单验证规则
const rules = {
  projectNumber: [{ required: true, message: '请输入备案号', trigger: 'blur' }],
  inviteCode: [{ required: true, message: '请输入邀请码', trigger: 'blur' }],
};

const formRef = ref();

// 检查用户认证状态
async function checkUserAuthentication() {
  try {
    const roles = await ApiGetAllRoles();
    isAuthenticated.value = roles && roles.length > 0;
    return isAuthenticated.value;
  } catch (error) {
    isAuthenticated.value = false;
    return false;
  }
}

// 扫码功能
async function handleScanCode() {
  try {
    const result = await scanCode();
    if (result.result) {
      // 解析二维码内容，提取邀请码
      const qrContent = result.result;
      // 假设二维码格式为: https://xxx.xxx.xx/invite-code?code=邀请码
      const codeMatch = qrContent.match(/code=([^&]+)/);
      if (codeMatch && codeMatch[1]) {
        formData.inviteCode = codeMatch[1];
        Toast.success('扫码成功');
      } else {
        Toast.error('二维码格式不正确');
      }
    }
  } catch (error) {
    Toast.error('扫码失败，请重试');
  }
}

function handleBack() {
  uni.$wx.miniProgram.redirectTo({ url: '/pages/index/index' });
}

// 查找工程
async function handleSearch() {
  try {
    await formRef.value?.validate();
    // 清除之前的错误信息
    searchError.value = '';
  } catch (error) {
    return;
  }

  loading.value = true;
  try {
    const data = await ApiSearchProject({
      projectNumber: formData.projectNumber,
      inviteCode: formData.inviteCode,
    });

    projectInfo.value = data;
    showProjectModal.value = true;
    // 成功时清除错误信息
    searchError.value = '';
  } catch (error) {
    // 设置错误信息而不是显示toast
    searchError.value = '查找工程失败，请检查备案号和邀请码是否正确';
  } finally {
    loading.value = false;
  }
}

// 确认绑定
async function handleConfirmBind() {
  if (!projectInfo.value) return;

  const authenticated = await checkUserAuthentication();
  if (!authenticated) {
    showAuthModal.value = true;
    return;
  }

  loading.value = true;
  try {
    await ApiBindProject({
      inviteCode: formData.inviteCode,
      projectNumber: formData.projectNumber,
    });

    showProjectModal.value = false;

    // 绑定成功后跳转到成功页面，传递工程信息
    const projectInfoParam = encodeURIComponent(JSON.stringify(projectInfo.value));
    uni.redirectTo({
      url: `/pages/engineering/bind-success?projectInfo=${projectInfoParam}`,
    });
  } catch (error: any) {
    const msg = error?.data?.errmsg || '绑定失败，请重试';
    Toast.error(msg);
  } finally {
    loading.value = false;
  }
}

// 关闭弹框
function handleCloseModal() {
  showProjectModal.value = false;
  projectInfo.value = null;
}

// 处理认证确认
function handleAuthConfirm() {
  showAuthModal.value = false;
  // 跳转到认证页面
  uni.redirectTo({
    url: '/pages/authentication/personnel-type',
  });
}

// 处理认证弹框关闭
function handleAuthClose() {
  showAuthModal.value = false;
  uni.navigateBack();
}

onShow(async () => {
  // 先检查用户认证状态
  const authenticated = await checkUserAuthentication();
  if (!authenticated) {
    showAuthModal.value = true;
  }
});
</script>

<template>
  <div class="min-h-screen bg-content">
    <div class="px-4 py-6">
      <!-- 页面标题 -->
      <div class="mb-6">
        <h1 class="text-xl font-bold text-gray-800 text-center">绑定工程</h1>
        <p class="text-sm text-gray-600 text-center mt-2">请输入备案号和邀请码来绑定工程</p>
      </div>

      <!-- 表单卡片 -->
      <div class="bg-card p-6 mb-6">
        <uni-forms ref="formRef" :model="formData" :rules="rules" label-width="80px">
          <!-- 备案号 -->
          <uni-forms-item label="备案号" name="projectNumber" required>
            <uni-easyinput
              v-model="formData.projectNumber"
              placeholder="请输入备案号"
              :clearable="true"
              :maxlength="50" />
          </uni-forms-item>

          <!-- 邀请码 -->
          <uni-forms-item label="邀请码" name="inviteCode" required>
            <div class="flex items-center gap-2">
              <uni-easyinput
                v-model="formData.inviteCode"
                placeholder="请输入邀请码"
                :clearable="true"
                :maxlength="20"
                class="flex-1" />
              <wd-button type="info" size="small" @click="handleScanCode">
                <sc-icon name="i-carbon:scan-alt" size="16" />
              </wd-button>
            </div>
          </uni-forms-item>
        </uni-forms>

        <!-- 查找按钮 -->
        <div class="mt-8">
          <wd-button
            type="primary"
            icon="search"
            size="large"
            block
            :loading="loading"
            @click="handleSearch">
            查找工程
          </wd-button>
        </div>
        <div class="mt-4">
          <wd-button type="info" size="large" block icon="home" @click="handleBack">
            返回首页
          </wd-button>
        </div>

        <!-- 错误信息显示 -->
        <div v-if="searchError" class="mt-4 p-3 bg-red-50 border border-red-200 rounded-lg">
          <div class="flex items-center gap-2">
            <sc-icon name="i-carbon:warning" size="16" class="text-red-500" />
            <span class="text-sm text-red-700">{{ searchError }}</span>
          </div>
        </div>
      </div>

      <!-- 使用说明 -->
      <div class="bg-card p-4">
        <h3 class="text-sm font-medium text-gray-700 mb-2">使用说明：</h3>
        <div class="text-xs text-gray-600 space-y-1">
          <p>1. 请输入正确的工程备案号</p>
          <p>2. 输入邀请码或点击扫码按钮扫描二维码</p>
          <p>3. 点击查找工程按钮查看工程信息</p>
          <p>4. 确认信息无误后点击确定绑定</p>
        </div>
      </div>
    </div>

    <!-- 工程信息弹框 -->
    <ProjectInfoModal
      :show="showProjectModal"
      :project-info="projectInfo"
      :loading="loading"
      @confirm="handleConfirmBind"
      @close="handleCloseModal" />

    <!-- 认证提示弹框 -->
    <AuthenticationPromptModal
      :show="showAuthModal"
      @confirm="handleAuthConfirm"
      @close="handleAuthClose" />
  </div>
</template>

<style lang="scss" scoped>
.bg-content {
  background: linear-gradient(180deg, #f8fafc 0%, #f1f5f9 100%);
}

:deep(.uni-forms-item__content) {
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  padding: 6px 8px;
}

:deep(.uni-easyinput__content) {
  border: none;
  background: transparent;
}

:deep(.uni-easyinput__content-input) {
  font-size: 14px;
}
</style>
