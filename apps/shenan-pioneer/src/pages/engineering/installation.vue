<script setup lang="ts">
import {
  ApiGetCameraDeviceNotRelevance,
  ApiGetMonitorFlowList,
  ApiGetMonitorOrderDetail,
  ApiMonitorOrderUpdate,
  CameraDeviceNotRelevanceRes,
  MonitorInstallBody,
} from '@/api';
import { Dayjs, exceptionToast, FormatDate, IsPro, Loading, Navigator, Toast } from '@/utils';
import { addWatermarkAfterRead } from '@/pages/recycling/canvas';
import { FormConfig } from '@rms/components/sc-form/sc-form.vue';
import { Dictionary } from '@rms/types';
import { ChooseFile } from '@rms/components/sc-upload/type';
import { StorageSetMonitorFlowRefresh } from '@/storage/monitor';
import { ApiQueryHandler } from '@shencom/api';

const project = ref<Dictionary>({
  title: '',
  address: '',
  flowName: '上门安装',
  inspectTime: '',
});

const formData = ref<Omit<MonitorInstallBody, 'pic'> & { pic: Dictionary[] }>({
  id: '',
  state: 1,
  installTime: '',
  memo: '',
  pic: [],
  installCnt: 1,
  cameraDeviceIds: [],
});

const cameraDeviceList = ref<{ value: string; options: any[] }[]>([]);
const cameraDeviceNotRelevanceList = ref<CameraDeviceNotRelevanceRes[]>([]);

watch(
  () => formData.value.installCnt,
  (newVal) => {
    const currentList = cameraDeviceList.value;
    const length = newVal - currentList.length;
    if (length < 0) {
      cameraDeviceList.value.splice(newVal, currentList.length - newVal);
      return;
    }
    const newList = Array.from({ length }, () => {
      return {
        value: '',
        options: cameraDeviceNotRelevanceList.value,
      };
    });

    cameraDeviceList.value = [...currentList, ...newList];
  },
  {
    immediate: true,
  },
);

const cameraDeviceIds = computed(() => {
  return cameraDeviceList.value.map((item) => item.value);
});

watch(
  () => cameraDeviceIds.value,
  (newVal) => {
    cameraDeviceList.value.forEach((item) => {
      const list = cameraDeviceNotRelevanceList.value.filter(
        (c) => !newVal.includes(c.id) || item.value === c.id,
      );
      item.options = list;
    });

    formData.value.cameraDeviceIds = newVal;
  },
  {
    deep: true,
  },
);

const isSubmit = ref(false);

const scFormRef = ref();

const formConfig = ref<FormConfig['config']>({
  formAttrs: {
    labelWidth: '220rpx',
    fontSize: '15px',
    border: true,
  },
  data: [
    {
      prop: 'pic',
      label: '安装照片',
      tag: {
        tagType: 'upload',
        attr: {
          required: true,
          multiple: true,
          maxCount: 10,
          maxSize: 10 * 1024 * 1024,
          uploadType: IsPro ? 'oss' : 'server',
          sizeType: ['original', 'compressed'],
          autoUpload: false,
          afterRead: async (files: ChooseFile[]) => {
            await addWatermarkAfterRead(files, project.value);
            formData.value.pic.forEach((item) => {
              if (item.tempUrl) return;
              item.tempUrl = item.watermark;
              item.url = item.watermark;
              item.path = item.watermark;
            });
          },
        },
      },
      listeners: {
        oversize: () => {
          Toast.warning('上传文件大小不能超过10M！', {
            duration: 2000,
          });
        },
      },
    },
    {
      prop: 'installCnt',
      label: '安装监控数量',
      tag: {
        tagType: 'component',
        attr: {
          placeholder: '请安装监控数量',
          required: true,
        },
      },
    },
    {
      prop: 'cameraDeviceIds',
      label: '监控序列号',
      tag: {
        tagType: 'component',
        attr: {
          required: true,
        },
      },
    },
    {
      prop: 'memo',
      label: '安装说明',
      tag: {
        tagType: 'textarea',
        attr: {
          placeholder: '请输入安装说明',
          rows: 3,
          maxlength: 300,
          showCount: true,
        },
      },
    },
  ],
  form: formData,
  rules: [
    {
      name: 'pic',
      rules: [
        {
          required: true,
          errorMessage: '请选择安装照片',
        },
      ],
    },
    {
      name: 'installCnt',
      rules: [
        {
          required: true,
          errorMessage: '请安装监控数量',
        },
      ],
    },
    {
      name: 'cameraDeviceIds',
      rules: [
        {
          required: true,
          errorMessage: '请选择监控序列号',
        },
        {
          validateFunction: (_, value) => {
            return value?.every((item: any) => !!item);
          },
          errorMessage: '请选择监控序列号',
        },
      ],
    },
  ],
});

onLoad(async (options) => {
  formData.value.id = options?.id || '';

  const data = await ApiGetMonitorOrderDetail(options?.orderId);
  const flowList = await getFlowList(options?.orderId);
  const flow1 = flowList.find((item) => item.flow === 1);
  const flow2 = flowList.find((item) => item.flow === 2);

  project.value.title = data?.projectName || '';
  project.value.address = data?.projectAddress || '';
  project.value.reservationTime = FormatDate(flow1?.finishTime, 'YYYY-MM-DD HH:mm') || '';
  project.value.inspectTime = FormatDate(flow2?.finishTime, 'YYYY-MM-DD HH:mm') || '';
});

async function onSubmit() {
  if (isSubmit.value) return;

  const flag = await scFormRef.value.validate();
  if (!flag) return;

  isSubmit.value = true;

  try {
    const uploadComponent = scFormRef.value.startRef.pic;
    await uploadComponent.onUpload();
  } catch (error) {
    exceptionToast(error, '图片上传失败');
    isSubmit.value = false;
    return;
  }

  submitForm();
}

async function submitForm() {
  try {
    const formPayload = {
      ...formData.value,
      installTime: Dayjs().format('YYYY-MM-DD HH:mm'),
      pic: formData.value.pic.map((item) => item.id).join(','),
    };

    if (!formPayload.pic) {
      isSubmit.value = false;
      return;
    }

    await ApiMonitorOrderUpdate(formPayload);

    // 设置刷新标记，通知相关页面刷新数据
    StorageSetMonitorFlowRefresh();

    Toast.success('提交成功', {
      complete: () => {
        Navigator.back();
      },
    });
  } catch (error) {
    exceptionToast(error, '提交失败');
    isSubmit.value = false;
  }
}

async function getFlowList(id: string) {
  const data = await ApiGetMonitorFlowList({
    query: ApiQueryHandler(id, 'orderId', 'select'),
  });
  return data.content;
}

onMounted(async () => {
  try {
    Loading.show();
    const data = await ApiGetCameraDeviceNotRelevance({
      size: 999,
    });
    cameraDeviceNotRelevanceList.value = data.content;

    cameraDeviceList.value.forEach((item) => {
      item.options = cameraDeviceNotRelevanceList.value;
    });
  } catch (error) {
    exceptionToast(error, '');
  } finally {
    Loading.hide();
  }
});

function onGotoHistory() {
  Navigator.push('/pages/recycling/list', { type: 0, tab: 3 });
}
</script>

<template>
  <div class="w-full min-h-screen p-3 bg-base box-border relative pb-16">
    <div class="mt-3 bg-white px-3 pt-4 rounded-md mb-4">
      <wd-steps :active="2" align-center>
        <wd-step title="安装预约" :description="project.reservationTime"></wd-step>
        <wd-step title="现场勘查" :description="project.inspectTime"></wd-step>
        <wd-step title="上门安装" description=" "></wd-step>
      </wd-steps>

      <sc-form ref="scFormRef" :config="formConfig" class="mt-3">
        <template #installCnt>
          <sc-number-box v-model="formData.installCnt" :min="1" class="pr-1" />
        </template>

        <template #cameraDeviceIds>
          <div
            v-for="(item, index) in cameraDeviceList"
            :key="index"
            class="border border-solid rounded border-gray-200 w-full mb-2">
            <sc-picker
              v-model="item.value"
              :ranges="item.options"
              placeholder="请选择监控序列号"
              :range-map="['id', 'serialNo']" />
          </div>
        </template>
      </sc-form>
    </div>

    <sc-button-fixed class="pt-5 box-border">
      <div class="w-full flex items-center justify-between">
        <div></div>
        <wd-button custom-class="!mr-6" type="text" icon="list" @click="onGotoHistory">
          进度查询
        </wd-button>
        <wd-button
          :disabled="isSubmit"
          custom-class="!rounded-md !w-1/2"
          size="large"
          type="primary"
          @click="onSubmit">
          提交
        </wd-button>
      </div>
    </sc-button-fixed>
  </div>
</template>
