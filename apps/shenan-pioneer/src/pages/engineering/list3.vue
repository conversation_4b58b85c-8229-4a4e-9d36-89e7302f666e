<script setup lang="ts">
import { ApiGetPatrolProjectList, ApiGetRoleRegion, ProjectInfoRes } from '@/api';
import useRoleStore from '@/hooks/useRole';
import { monitorFlagOptions, projectStatusOptions } from '@/state';
import { Dayjs, getLocationAdapter, IsMiniProgram, IsWeixinH5, Navigator, Toast } from '@/utils';
import { useList } from '@rms/components/sc-list/sc-list';
import { ServiceGisByIds, ServiceRegion } from '@rms/service/src';
import { watchThrottled } from '@vueuse/core';
import { useRoleRegion } from '@/hooks/useRoleRegion';
import {
  StorageGetPatrolRecordRefresh,
  StorageRemovePatrolRecordRefresh,
} from '@/storage/patrolRecord';
import { Dictionary } from '@rms/types';

interface SearchParams {
  regions: string[];
  keyword: string;
  status: number;
  size: number;
  distance: number;
  time: string;
  lat: number | string; // Can be number or string initially
  lng: number | string;
}
const search = reactive<SearchParams>({
  regions: [] as string[],
  keyword: '',
  status: 2,
  size: 10,
  distance: 0,
  time: '',
  lat: '',
  lng: '',
});

const distanceColumns = ref([
  {
    label: '100米内',
    value: 100,
  },
  {
    label: '200米内',
    value: 200,
  },
  {
    label: '500米内',
    value: 500,
  },
  {
    label: '1km内',
    value: 1000,
  },
  {
    label: '3km内',
    value: 3000,
  },
]);

const timeColumns = ref([
  {
    label: '未巡查',
    value: 0,
  },
  {
    label: '一天内',
    value: 1,
  },
  {
    label: '一周内',
    value: 7,
  },
  {
    label: '一个月内',
    value: 30,
  },
  {
    label: '三个月内',
    value: 90,
  },
]);
const today = Dayjs().format('YYYY-MM-DD 23:59:59');
const timeObject = {
  1: {
    lastPatrolAtStart: Dayjs(today).subtract(1, 'day').format('YYYY-MM-DD HH:mm:ss'),
    lastPatrolAtEnd: today,
  },
  7: {
    lastPatrolAtStart: Dayjs(today).subtract(7, 'day').format('YYYY-MM-DD HH:mm:ss'),
    lastPatrolAtEnd: today,
  },
  30: {
    lastPatrolAtStart: Dayjs(today).subtract(30, 'day').format('YYYY-MM-DD HH:mm:ss'),
    lastPatrolAtEnd: today,
  },
  90: {
    lastPatrolAtStart: Dayjs(today).subtract(90, 'day').format('YYYY-MM-DD HH:mm:ss'),
    lastPatrolAtEnd: today,
  },
};

const distancePickerRef = ref();
const timePickerRef = ref();
const { list, listProps, getData } = useList<ProjectInfoRes[]>({
  request: ApiGetPatrolProjectList,
  params: search,
  immediate: false,
  handle: async (res) => {
    return res;
  },
});

async function setRegionName(region: string[]) {
  let res = await ServiceRegion();

  filterData.regionName =
    region
      .filter(Boolean)
      .map((r) => {
        const item = res.find((i) => i.id === r);
        res = item?.children || [];
        return item?.title;
      })
      .pop() || '';
}

watch(
  () => search.regions,
  () => {
    setRegionName(search.regions);
  },
  { deep: true },
);

watchThrottled(
  search,
  () => {
    refresh();
  },
  { throttle: 200, immediate: true, deep: true },
);

function refresh() {
  const [regionPid, regionId, regionCid] = search.regions;

  const params: Dictionary = {
    ...search,
    status: search.status === 0 ? '' : search.status - 1,
    distance: search.distance === 0 ? '' : search.distance,
    regionPid,
    regionId,
    regionCid,
  };
  if (params.time) {
    params.lastPatrolAtStart = timeObject[params.time as keyof typeof timeObject].lastPatrolAtStart;
    params.lastPatrolAtEnd = timeObject[params.time as keyof typeof timeObject].lastPatrolAtEnd;
  }
  if (typeof params.time === 'number') {
    params.isPatrol = params.time === 0 ? 2 : 1;
    delete params.time;
  }
  delete params.regions;
  getData(true, params);
}

onShow(() => {
  const needRefresh = StorageGetPatrolRecordRefresh();
  if (needRefresh) {
    refresh();
    StorageRemovePatrolRecordRefresh();
  }
});

const roleRegion = ref();

const roleStore = useRoleStore();
roleStore.initOrganization(true);

async function getRegionList() {
  roleRegion.value = await ApiGetRoleRegion();
}

onMounted(async () => {
  const res = await getLocationAdapter();
  if (res) {
    search.lng = res[0];
    search.lat = res[1];
  }
  getRegionList();
  refresh();
});

function toDetail(item: ProjectInfoRes) {
  Navigator.push('/pages/engineering/detail', {
    id: item.id,
  });
}

async function toLocation(item: ProjectInfoRes) {
  const gisInfo = await ServiceGisByIds(item.poiId);
  const options = {
    longitude: +gisInfo[0].lng,
    latitude: +gisInfo[0].lat,
    name: item.name,
    address: gisInfo[0].addr,
  };

  if (IsWeixinH5 || IsMiniProgram) {
    uni.$wx.openLocation(options);
  } else {
    uni.openLocation(options);
  }
}

async function toPatrolRecord(item: ProjectInfoRes) {
  if (item.status === 2) {
    Toast.warning('该工程已结束，不可进行巡查');
    return;
  }
  Navigator.replace('/pages/patrolRecord/create', {
    id: item.id,
    title: item.name,
  });
}

function onReport() {
  Navigator.push('/pages/filedEngineering/create');
}
const { roleLevel, handleData } = useRoleRegion();
const scRegionRef = ref();
function onRegionShow() {
  if (typeof roleLevel.value !== 'number') {
    Toast.error('暂无权限');
    return;
  }

  scRegionRef.value.show();
}

function onPickerDistanceShow() {
  distancePickerRef.value.open();
}

function onTimeShow() {
  timePickerRef.value.open();
}

const filterData = reactive({
  regionName: '',
  distanceText: '',
  time: '',
});

function onClear() {
  filterData.distanceText = '';
  search.distance = 0;
  filterData.time = '';
  search.time = '';
  filterData.regionName = '';
  search.regions = [];
}

function onConfirmDistance(e: any) {
  filterData.distanceText = e.selectedItems.label;
  search.distance = e.value;
}

function onConfirmTime(e: any) {
  filterData.time = e.selectedItems.label;
  search.time = e.value;
}

onPullDownRefresh(async () => {
  search.status = 0;

  await refresh();
  uni.stopPullDownRefresh();
});
onReachBottom(() => null);
</script>

<template>
  <div class="">
    <div class="w-full bg-[#F6F8FF] py-3">
      <wd-input
        v-model="search.keyword"
        class="mx-3 mb-2 !bg-[#F6F8FF]"
        placeholder="请输入工程名称/地址" />
      <div class="flex justify-around items-center text-gray-500 text-12 px-2 mb-2">
        <div
          class="rounded-2xl text-center bg-gray-100 h-[30px] ellipsis-1 w-[100px] leading-[30px] px-1"
          @click="onRegionShow">
          <span class="mr-1">{{ filterData.regionName || '所属区域' }}</span>
          <wd-icon name="arrow-down" size="15px"></wd-icon>
        </div>

        <div
          class="rounded-2xl text-center bg-gray-100 h-[30px] ellipsis-1 w-[150px] leading-[30px] px-1"
          @click="onPickerDistanceShow">
          <span class="mr-1">{{ filterData.distanceText || '筛选距离' }}</span>
          <wd-icon name="arrow-down" size="15px"></wd-icon>
        </div>
        <div
          class="rounded-2xl text-center bg-gray-100 h-[30px] ellipsis-1 w-[150px] leading-[30px] px-1"
          @click="onTimeShow">
          <span class="mr-1">{{ filterData.time || '最后巡查时间' }}</span>
          <wd-icon name="arrow-down" size="15px"></wd-icon>
        </div>

        <wd-button type="error" plain size="small" @click="onClear">重置</wd-button>
      </div>
      <wd-radio-group
        v-model="search.status"
        class="!bg-[#F6F8FF]"
        custom-class="flex justify-around"
        shape="button">
        <wd-radio :value="0">全部</wd-radio>
        <wd-radio :value="2">施工中</wd-radio>
        <wd-radio :value="1">未开始</wd-radio>
        <wd-radio :value="3">已结束</wd-radio>
      </wd-radio-group>
    </div>

    <sc-list v-bind="listProps" class="!p-0 !h-auto !min-h-[calc(100vh-38px)]">
      <div class="w-full p-3">
        <div
          v-for="item in list"
          :key="item.id"
          class="bg-[#F6F8FF] rounded-xl p-3 pt-8 mb-3 text-12 relative"
          @click="toDetail(item)">
          <div
            class="absolute w-[140px] top-0 rounded-tr-xl rounded-bl-xl text-center ellipsis-1 bg2 p-1 text-13 right-0">
            {{ item.projectNumber || 'sadasd124213123w' }}
          </div>

          <div class="flex items-center gap-2 mb-2">
            <div class="max-w-[200px] text-16 font-bold">{{ item.name }}</div>
            <wd-tag
              :type="projectStatusOptions.find((i) => i.value === item.status)?.type as any"
              custom-class="!text-12">
              {{ monitorFlagOptions.find((i) => i.value === item.monitorFlag)?.label }}-{{
                projectStatusOptions.find((i) => i.value === item.status)?.label || '已结束'
              }}
            </wd-tag>
          </div>
          <div class="flex items-center gap-2 mb-1">
            <div>小散工程地址: {{ item.address }}</div>
            <wd-icon
              color="#1338D5"
              name="location"
              size="18px"
              @click.stop="toLocation(item)"></wd-icon>
          </div>

          <div class="mb-1">工程开始时间: {{ item.startAt }}</div>
          <div>工程结束时间: {{ item.endAt }}</div>
          <div>最后巡查时间: {{ item.lastPatrolAt }}</div>
          <div class="w-full flex justify-end">
            <wd-button
              class="createBtn"
              :disabled="item.status === 2"
              type="primary"
              size="medium"
              @click.stop="toPatrolRecord(item)">
              发起巡检
            </wd-button>
          </div>
        </div>
      </div>
    </sc-list>
    <sc-button-fixed class="pt-2 box-border z-10">
      <div class="w-full flex items-center justify-center">
        <wd-button custom-class="!rounded-md !w-full" size="large" type="primary" @click="onReport">
          上报未备案工程
        </wd-button>
      </div>
    </sc-button-fixed>

    <sc-region
      v-if="typeof roleLevel === 'number'"
      ref="scRegionRef"
      v-model="search.regions"
      :handle-data="handleData"
      is-hide></sc-region>
    <wd-select-picker
      ref="distancePickerRef"
      v-model="search.distance"
      :z-index="9999"
      label="输入摊位名称"
      :columns="distanceColumns"
      type="radio"
      use-default-slot
      filterable
      @confirm="onConfirmDistance">
      <div></div>
    </wd-select-picker>
    <wd-select-picker
      ref="timePickerRef"
      v-model="search.time"
      :z-index="9999"
      label="输入摊位名称"
      :columns="timeColumns"
      type="radio"
      use-default-slot
      filterable
      @confirm="onConfirmTime">
      <div></div>
    </wd-select-picker>
  </div>
</template>

<style lang="scss" scoped>
:deep(.wd-radio__label) {
  display: flex;
  align-items: center;
}
:deep(.wd-radio) {
  margin: 0 !important;
}
:deep(.createBtn.is-disabled) {
  background: #666 !important;
}

.bg-sky1 {
  // 天空蓝渐变色
  background-image: linear-gradient(120deg, #a1c4fd 0%, #c2e9fb 100%);
}

.bg2 {
  background-image: linear-gradient(to top, #cfd9df 0%, #e2ebf0 100%);
}

.border-b {
  border-bottom: 1rpx solid #e2ebf0;
}
</style>
