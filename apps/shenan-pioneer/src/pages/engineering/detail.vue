<script setup lang="ts">
import {
  ApiGetMonitorFlowList,
  ApiGetProjectCheckLimit,
  ApiGetProjectShow,
  ProjectShowRes,
} from '@/api';
import { ApiUpdateMessageStatus } from '@/api/modules/message';
import { StorageSetMessageRefresh } from '@/storage/message';
import {
  exceptionHandler,
  FormatDateTime,
  IsMiniProgram,
  IsWeixinH5,
  Navigator,
  Toast,
  ToMoney,
  utilsOss,
} from '@/utils';
import { ServiceGisByIds, ServiceImgByIds } from '@rms/service/src';
import { Dictionary } from '@rms/types';
import { ApiQueryHandler } from '@shencom/api';

const props = defineProps<{
  id: string;
  orderId: string;
}>();

const imgs = {
  icon_jt1: `${utilsOss.imgPath}/engineering/icon_jt1.png`,
  icon_jt2: `${utilsOss.imgPath}/engineering/icon_jt2.png`,
};

const dataInfo = ref<ProjectShowRes>();

const isAdmin = ref(false);

function toPhone(phone: string) {
  uni.makePhoneCall({
    phoneNumber: phone,
  });
}

async function toLocation() {
  if (!dataInfo.value?.poiId) return;

  const gisInfo = await ServiceGisByIds(dataInfo.value.poiId);
  const options = {
    longitude: +gisInfo[0].lng,
    latitude: +gisInfo[0].lat,
    name: dataInfo.value.name,
    address: gisInfo[0].addr,
  };

  if (IsWeixinH5 || IsMiniProgram) {
    uni.$wx.openLocation(options);
  } else {
    uni.openLocation(options);
  }
}

const flowInfo = reactive({
  flow1: [] as any[],
  flow2: [] as any[],
  flow3: [] as any[],
  flow4: [] as any[],
});

const currentFlow = ref(0);

function changeFlow(flow: number) {
  if (rowFlow.value < flow) return;

  currentFlow.value = flow;
}

const rowFlow = ref(0);

onMounted(async () => {
  const res = await ApiGetProjectCheckLimit(props.id);
  isAdmin.value = res;
  if (!res) {
    Toast.warning('您未绑定该工程，无权查看工程信息', {
      duration: 3000,
      complete: () => {
        Navigator.push('/pages/index/index');
      },
    });
    return;
  }
  const data = await ApiGetProjectShow(props.id);
  dataInfo.value = data;

  const { content } = await ApiGetMonitorFlowList({
    query: ApiQueryHandler(data.orderId, 'orderId', 'select'),
    size: 1000,
  });

  flowInfo.flow1 = content.filter((i) => [0, 1].includes(i.flow)).sort((a, b) => a.flow - b.flow);
  flowInfo.flow2 = content.filter((i) => [2, 3].includes(i.flow)).sort((a, b) => a.flow - b.flow);
  flowInfo.flow3 = content.filter((i) => [4].includes(i.flow)).sort((a, b) => a.flow - b.flow);
  flowInfo.flow4 = content
    .filter((i) => [5, 6, 7, 9].includes(i.flow))
    .sort((a, b) => a.flow - b.flow);

  const flow2Ids = flowInfo.flow2
    .map((i) => i.pic?.split(','))
    .flat()
    .filter(Boolean);
  const img = await ServiceImgByIds(flow2Ids);
  flowInfo.flow2.forEach((i) => {
    if (i.pic) {
      Object.values(img).forEach((item: any) => {
        if (i.pic.includes(item.id)) {
          i.img = Array.isArray(i.img) ? i.img : [];
          i.img.push(item);
        }
      });
    }
  });

  const flow4Ids = flowInfo.flow4
    .map((i) => i.pic?.split(','))
    .flat()
    .filter(Boolean);
  const img4 = await ServiceImgByIds(flow4Ids);
  flowInfo.flow4.forEach((i) => {
    if (i.pic) {
      Object.values(img4).forEach((item: any) => {
        if (i.pic.includes(item.id)) {
          i.img = Array.isArray(i.img) ? i.img : [];
          i.img.push(item);
        }
      });
    }
  });

  const { flow1, flow2, flow3, flow4 } = flowInfo;
  const arr = [flow1, flow2, flow3, flow4];

  const index = arr.findIndex((i) => {
    return i.some((j) => j.state === 0);
  });

  if (arr[index]?.length === 1) {
    currentFlow.value = index > 0 ? index - 1 : 0;
  } else if (index !== -1) {
    currentFlow.value = index;
  } else {
    currentFlow.value = 3;
  }

  rowFlow.value = currentFlow.value;
});

function isActive(flowList: Dictionary[]) {
  return flowList && flowList.length - (flowList[flowList.length - 1]?.state ? 0 : 1);
}

onLoad((options) => {
  if (options?.msgId) {
    try {
      ApiUpdateMessageStatus({ id: options.msgId, status: 1 });
      StorageSetMessageRefresh();
    } catch (error) {
      exceptionHandler(error);
    }
  }
});
</script>

<template>
  <view v-if="dataInfo" class="p-5">
    <!-- 基本信息      -->
    <div class="rounded-xl shadow-lg">
      <div class="bg-[#1338D5] text-16 font-bold rounded-t-xl text-center py-1 w-full text-white">
        基本信息
      </div>
      <div class="bg-[#F6F8FF] flex flex-col gap-4 py-3 rounded-b-xl">
        <div class="flex items-center text-15 gap-3">
          <div class="font-bold w-[120px] text-right">备案编号</div>
          <div class="text-gray-500 flex-1">{{ dataInfo.projectNumber }}</div>
        </div>
        <div class="flex items-center text-15 gap-3">
          <div class="font-bold w-[120px] text-right">工程名称</div>
          <div class="text-gray-500 flex-1">{{ dataInfo.name }}</div>
        </div>

        <div class="flex items-center text-15 gap-3">
          <div class="font-bold w-[120px] text-right">工程分类</div>
          <div class="text-gray-500 flex-1">{{ dataInfo.pCateName }}</div>
        </div>

        <div class="flex items-center text-15 gap-3">
          <div class="font-bold w-[120px] text-right">工程类别</div>
          <div class="text-gray-500 flex-1">{{ dataInfo.cateName }}</div>
        </div>

        <div class="flex items-center text-15 gap-3">
          <div class="font-bold w-[120px] text-right">工程金额</div>
          <div class="text-gray-500 flex-1">{{ ToMoney(dataInfo.amount) }}元</div>
        </div>

        <div class="flex items-center text-15 gap-3">
          <div class="font-bold w-[120px] text-right">实际施工面积</div>
          <div class="text-gray-500 flex-1">{{ ToMoney(dataInfo.area) }}㎡</div>
        </div>

        <div class="flex items-center text-15 gap-3">
          <div class="font-bold w-[120px] text-right">工程开始时间</div>
          <div class="text-gray-500 flex-1">{{ dataInfo.startAt }}</div>
        </div>

        <div class="flex items-center text-15 gap-3">
          <div class="font-bold w-[120px] text-right">工程结束时间</div>
          <div class="text-gray-500 flex-1">{{ dataInfo.endAt }}</div>
        </div>

        <div class="flex items-center text-15 gap-3">
          <div class="font-bold w-[120px] text-right">所在社区</div>
          <div class="text-gray-500 flex-1">
            {{
              [dataInfo.districtName, dataInfo.streetName, dataInfo.villageName]
                .filter(Boolean)
                .join('')
            }}
          </div>
        </div>

        <div class="flex items-center text-15 gap-3" @click.stop="toLocation">
          <div class="font-bold w-[120px] text-right">详细地址</div>
          <div class="text-gray-500 flex-1">{{ dataInfo.address }}</div>
          <wd-icon color="#1338D5" name="location" size="18px"></wd-icon>
        </div>
      </div>
    </div>

    <div class="rounded-xl shadow-lg">
      <div
        class="bg-[#1338D5] text-16 font-bold rounded-t-xl text-center py-1 w-full text-white mt-5">
        业主信息
      </div>
      <div class="bg-[#F6F8FF] flex flex-col gap-4 py-3 rounded-b-xl">
        <div class="flex items-center text-15 gap-3">
          <div class="font-bold w-[120px] text-right">建设单位</div>
          <div class="text-gray-500 flex-1">{{ dataInfo.constructorName }}</div>
        </div>
        <div class="flex items-center text-15 gap-3">
          <div class="font-bold w-[120px] text-right">负责人姓名</div>
          <div class="text-gray-500 flex-1">{{ dataInfo.constructorCharger }}</div>
        </div>

        <div class="flex items-center text-15 gap-3">
          <div class="font-bold w-[120px] text-right">业主电话</div>
          <div class="text-gray-500 flex-1 flex items-center gap-1">
            <div>{{ dataInfo.ownerMobile }}</div>
            <sc-icon
              name="i-material-symbols:call"
              size="18px"
              class="text-[#1338D5]"
              @click="toPhone(dataInfo.ownerMobile)"></sc-icon>
          </div>
        </div>
      </div>
    </div>
    <div class="rounded-xl shadow-lg">
      <div
        class="bg-[#1338D5] text-16 font-bold rounded-t-xl text-center py-1 w-full text-white mt-5">
        施工方信息
      </div>
      <div class="bg-[#F6F8FF] flex flex-col gap-4 py-3 rounded-b-xl">
        <div class="flex items-center text-15 gap-3">
          <div class="font-bold w-[120px] text-right">施工单位</div>
          <div class="text-gray-500 flex-1">{{ dataInfo.contractorName }}</div>
        </div>
        <div class="flex items-center text-15 gap-3">
          <div class="font-bold w-[120px] text-right">施工负责人</div>
          <div class="text-gray-500 flex-1">{{ dataInfo.contractorCharger }}</div>
        </div>

        <div class="flex items-center text-15 gap-3">
          <div class="font-bold w-[120px] text-right">负责人电话</div>
          <div class="text-gray-500 flex-1 flex items-center gap-1">
            <div>{{ dataInfo.contractorChargerMobile }}</div>
            <sc-icon
              name="i-material-symbols:call"
              size="18px"
              class="text-[#1338D5]"
              @click="toPhone(dataInfo.contractorChargerMobile)"></sc-icon>
          </div>
        </div>
      </div>
    </div>

    <div class="bg-[#F6F8FF] rounded-lg shadow-lg mt-5 p-2">
      <div class="flex">
        <div class="flex items-center br py-1 flex-1 px-1" @click="changeFlow(0)">
          <span>预约安装</span>
          <img
            :src="currentFlow === 0 ? imgs.icon_jt1 : imgs.icon_jt2"
            class="w-3 h-3 ml-1"
            alt="" />
        </div>
        <div class="flex items-center br py-1 flex-1 px-1" @click="changeFlow(1)">
          <span>上门安装</span>
          <img
            :src="currentFlow === 1 ? imgs.icon_jt1 : imgs.icon_jt2"
            class="w-3 h-3 ml-1"
            alt="" />
        </div>
        <div class="flex items-center br py-1 flex-1 px-1" @click="changeFlow(2)">
          <span>接入监管</span>
          <img
            :src="currentFlow === 2 ? imgs.icon_jt1 : imgs.icon_jt2"
            class="w-3 h-3 ml-1"
            alt="" />
        </div>
        <div
          class="flex items-center flex-1 justify-center"
          :class="{ 'text-blue-500': currentFlow === 3 }"
          @click="changeFlow(3)">
          施工完成
        </div>
      </div>
    </div>

    <div v-if="currentFlow === 0" class="rounded-lg shadow-lg mt-5 p-2 bg-[#F6F8FF]">
      <wd-steps :active="isActive(flowInfo.flow1)" vertical>
        <wd-step v-if="flowInfo.flow1[0]">
          <template #title>
            <div class="flex items-center gap-2">
              <div>工程创建</div>
              <div>{{ FormatDateTime(flowInfo.flow1[0].finishTime) }}</div>
            </div>
          </template>
          <template #description>
            <div class="flex flex-col gap-2">
              <div class="text-12 text-gray-500">
                创建人：{{ flowInfo.flow1[0].createdUserName }}
              </div>
            </div>
          </template>
        </wd-step>
        <wd-step v-if="flowInfo.flow1[1]" title="预约安装">
          <template #title>
            <div class="flex items-center gap-2">
              <div>预约安装</div>
              <div v-if="flowInfo.flow1[1].state">
                {{ FormatDateTime(flowInfo.flow1[1].finishTime) }}
              </div>
            </div>
          </template>
          <template #description>
            <div v-if="flowInfo.flow1[1].state" class="flex flex-col gap-2">
              <div class="text-12 text-gray-500">预约人：{{ flowInfo.flow1[1].contactName }}</div>
              <div v-if="flowInfo.flow1[1]?.reservationTime" class="text-12 text-gray-500">
                预约上门时间：{{ FormatDateTime(flowInfo.flow1[1].reservationTime) }}
              </div>
            </div>
          </template>
        </wd-step>
      </wd-steps>
    </div>

    <div v-if="currentFlow === 1" class="rounded-lg shadow-lg mt-5 p-2 bg-[#F6F8FF]">
      <wd-steps :active="isActive(flowInfo.flow2)" vertical>
        <wd-step v-if="flowInfo.flow2[0]" title="现场勘查">
          <template #title>
            <div class="flex items-center gap-2">
              <div>现场勘查</div>
              <div>{{ FormatDateTime(flowInfo.flow2[0].finishTime) }}</div>
            </div>
          </template>
          <template #description>
            <div class="flex flex-col gap-2">
              <div v-if="flowInfo.flow2[0].state" class="text-12 text-gray-500">
                勘查人员：{{ flowInfo.flow2[0].createdUserName }}
              </div>
              <div v-if="flowInfo.flow2[0].state" class="text-12 text-gray-500">
                预约安装时间：{{ FormatDateTime(flowInfo.flow2[0].reservationTime) }}
              </div>
              <div v-if="flowInfo.flow2[0].state" class="flex flex-col gap-2 w-full">
                <div class="text-12 text-gray-500">现场图片：</div>
                <div class="flex gap-2 flex-wrap">
                  <wd-img
                    v-for="item in flowInfo.flow2[0].img"
                    :key="item.id"
                    enable-preview
                    :src="item.remoteUrl"
                    class="w-20 h-20 !rounded-lg overflow-hidden"
                    alt="" />
                </div>
              </div>
              <div v-if="flowInfo.flow2[0].state" class="text-12 text-gray-500">
                评估需安装监控数量：{{ flowInfo.flow2[0].installCnt }}
              </div>
              <div v-if="flowInfo.flow2[0].state" class="text-12 text-gray-500">
                勘查说明：{{ flowInfo.flow2[0].memo }}
              </div>
            </div>
          </template>
        </wd-step>
        <wd-step v-if="flowInfo.flow2[1]" title="上门安装">
          <template #title>
            <div class="flex items-center gap-2">
              <div>上门安装</div>
              <div v-if="flowInfo.flow2[1].state">
                {{ FormatDateTime(flowInfo.flow2[1].finishTime) }}
              </div>
            </div>
          </template>
          <template #description>
            <div class="flex flex-col gap-2">
              <div v-if="flowInfo.flow2[1].state" class="text-12 text-gray-500">
                安装工人：{{ flowInfo.flow2[1].createdUserName }}
              </div>
              <div v-if="flowInfo.flow2[1].state" class="flex flex-col gap-2 w-full">
                <div class="text-12 text-gray-500">安装图片：</div>
                <div class="flex gap-2 flex-wrap">
                  <wd-img
                    v-for="item in flowInfo.flow2[1].img"
                    :key="item.id"
                    enable-preview
                    :src="item.remoteUrl"
                    class="w-20 h-20 !rounded-lg overflow-hidden"
                    alt="" />
                </div>
              </div>
              <div v-if="flowInfo.flow2[1].state" class="text-12 text-gray-500">
                安装监控数量：{{ flowInfo.flow2[1].installCnt }}
              </div>
              <div v-if="flowInfo.flow2[1].state" class="text-12 text-gray-500">
                安装说明：{{ flowInfo.flow2[1].memo }}
              </div>
            </div>
          </template>
        </wd-step>
      </wd-steps>
    </div>

    <div v-if="currentFlow === 2" class="rounded-lg shadow-lg mt-5 p-2 bg-[#F6F8FF]">
      <wd-steps :active="isActive(flowInfo.flow3)" vertical>
        <wd-step v-if="flowInfo.flow3[0]" title="接入监管">
          <template #title>
            <div class="flex items-center gap-2">
              <div>接入监管</div>
              <div v-if="flowInfo.flow3[0].state">
                {{ FormatDateTime(flowInfo.flow3[0].finishTime) }}
              </div>
            </div>
          </template>
          <template #description>
            <div v-if="flowInfo.flow3[0].state" class="flex flex-col gap-2">
              <div class="text-12 text-gray-500 break-all">
                监控设备号：{{ flowInfo.flow3[0].serialNo }}
              </div>
            </div>
          </template>
        </wd-step>
      </wd-steps>
    </div>

    <div v-if="currentFlow === 3" class="rounded-lg shadow-lg mt-5 p-2 bg-[#F6F8FF]">
      <wd-steps :active="isActive(flowInfo.flow4)" vertical>
        <wd-step v-if="flowInfo.flow4[0]" title="施工完成">
          <template #title>
            <div class="flex items-center gap-2">
              <div>施工完成</div>
              <div v-if="flowInfo.flow4[0].state">
                {{ FormatDateTime(flowInfo.flow4[0].finishTime) }}
              </div>
            </div>
          </template>
          <template #description></template>
        </wd-step>
        <wd-step v-if="flowInfo.flow4[1]" title="预约回收">
          <template #title>
            <div class="flex items-center gap-2">
              <div>预约回收</div>
              <div v-if="flowInfo.flow4[1].state">
                {{ FormatDateTime(flowInfo.flow4[1].finishTime) }}
              </div>
            </div>
          </template>
          <template #description>
            <div v-if="flowInfo.flow4[1].state" class="flex flex-col gap-2">
              <div class="text-12 text-gray-500">
                预约人：{{ flowInfo.flow4[1].createdUserName }}
              </div>
              <div class="text-12 text-gray-500">
                预约时间：{{ FormatDateTime(flowInfo.flow4[1].reservationTime) }}
              </div>
            </div>
          </template>
        </wd-step>
        <wd-step v-if="flowInfo.flow4[2]" title="上门回收">
          <template #title>
            <div class="flex items-center gap-2">
              <div>上门回收</div>
              <div v-if="flowInfo.flow4[2].state">
                {{ FormatDateTime(flowInfo.flow4[2].finishTime) }}
              </div>
            </div>
          </template>
          <template #description>
            <div v-if="flowInfo.flow4[2].state" class="flex flex-col gap-2">
              <div class="text-12 text-gray-500">
                回收工人：{{ flowInfo.flow4[2].createdUserName }}
              </div>
              <div class="flex flex-col gap-2 w-full">
                <div class="text-12 text-gray-500">回收图片：</div>
                <div class="flex gap-2 flex-wrap">
                  <wd-img
                    v-for="item in flowInfo.flow4[2].img"
                    :key="item.id"
                    enable-preview
                    :src="item.remoteUrl"
                    class="w-20 h-20 !rounded-lg overflow-hidden"
                    alt="" />
                </div>
              </div>
              <div class="text-12 text-gray-500">
                回收监控数量：{{ flowInfo.flow4[2].recycleCnt }}
              </div>
              <div class="text-12 text-gray-500">回收说明：{{ flowInfo.flow4[2].memo }}</div>
            </div>
          </template>
        </wd-step>
        <wd-step v-if="flowInfo.flow4[3]" title="监管结束">
          <template #title>
            <div class="flex items-center gap-2">
              <div>监管结束</div>
              <div v-if="flowInfo.flow4[3].state">
                {{ FormatDateTime(flowInfo.flow4[3].finishTime) }}
              </div>
            </div>
          </template>
          <template #description></template>
        </wd-step>
      </wd-steps>
    </div>
  </view>
</template>

<style lang="scss" scoped>
.br {
  border-right: 1px solid #e0e0e0;
}

:deep(.wd-step__icon) {
  background: #f6f8ff !important;
}
</style>
