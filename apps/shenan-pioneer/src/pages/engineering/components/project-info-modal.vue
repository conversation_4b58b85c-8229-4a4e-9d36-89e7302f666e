<script setup lang="ts">
import type { ProjectSearchResponse } from '@/api';
import { FormatDate, ToMoney } from '@/utils';

interface Props {
  show: boolean;
  projectInfo: ProjectSearchResponse | null;
  loading?: boolean;
}

interface Emits {
  (e: 'confirm'): void;
  (e: 'close'): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const visible = ref(false);

// 格式化金额
function formatAmount(amount: number) {
  return amount ? `${ToMoney(amount)} 元` : '-';
}

// 格式化面积
function formatArea(area: number) {
  return area ? `${area} 平方米` : '-';
}

// 处理确认绑定
function handleConfirm() {
  emit('confirm');
}

// 处理关闭
function handleClose() {
  emit('close');
}

watch(
  () => props.show,
  (newVal) => {
    visible.value = newVal;
  },
);
</script>

<template>
  <wd-popup
    v-model="visible"
    position="center"
    custom-class="rounded-lg overflow-hidden"
    @close="handleClose">
    <div class="w-[90vw] max-w-[420px] bg-white rounded-lg">
      <!-- 标题 -->
      <div class="px-6 py-4 border-b border-gray-100">
        <h3 class="text-lg font-bold text-gray-800 text-center">工程信息</h3>
        <p class="text-sm text-gray-600 text-center mt-1">请确认以下工程信息</p>
      </div>

      <!-- 工程信息内容 -->
      <div class="px-6 py-4 max-h-[60vh] overflow-y-auto">
        <div class="space-y-4">
          <!-- 基本信息 -->
          <div class="space-y-3">
            <div class="flex justify-between items-start">
              <span class="text-sm text-gray-600 w-20 flex-shrink-0">备案编号:</span>
              <span class="text-sm text-gray-800 flex-1 text-right">
                {{ projectInfo?.projectNumber }}
              </span>
            </div>

            <div class="flex justify-between items-start">
              <span class="text-sm text-gray-600 w-20 flex-shrink-0">工程名称:</span>
              <span class="text-sm text-gray-800 flex-1 text-right">{{ projectInfo?.name }}</span>
            </div>

            <div class="flex justify-between items-start">
              <span class="text-sm text-gray-600 w-20 flex-shrink-0">工程分类:</span>
              <span class="text-sm text-gray-800 flex-1 text-right">
                {{ projectInfo?.pCateName }} - {{ projectInfo?.cateName }}
              </span>
            </div>

            <div class="flex justify-between items-start">
              <span class="text-sm text-gray-600 w-20 flex-shrink-0">工程金额:</span>
              <span class="text-sm text-gray-800 flex-1 text-right">
                {{ formatAmount(projectInfo?.amount || 0) }}
              </span>
            </div>

            <div class="flex justify-between items-start">
              <span class="text-sm text-gray-600 w-20 flex-shrink-0">施工面积:</span>
              <span class="text-sm text-gray-800 flex-1 text-right">
                {{ formatArea(projectInfo?.area || 0) }}
              </span>
            </div>
          </div>

          <!-- 分割线 -->
          <div class="border-t border-gray-100"></div>

          <!-- 时间信息 -->
          <div class="space-y-3">
            <div class="flex justify-between items-start">
              <span class="text-sm text-gray-600 w-20 flex-shrink-0">开始时间:</span>
              <span class="text-sm text-gray-800 flex-1 text-right">
                {{ FormatDate(projectInfo?.startAt) }}
              </span>
            </div>

            <div class="flex justify-between items-start">
              <span class="text-sm text-gray-600 w-20 flex-shrink-0">结束时间:</span>
              <span class="text-sm text-gray-800 flex-1 text-right">
                {{ FormatDate(projectInfo?.endAt) }}
              </span>
            </div>
          </div>

          <!-- 分割线 -->
          <div class="border-t border-gray-100"></div>

          <!-- 地址信息 -->
          <div class="space-y-3">
            <div class="flex justify-between items-start">
              <span class="text-sm text-gray-600 w-20 flex-shrink-0">所在社区:</span>
              <span class="text-sm text-gray-800 flex-1 text-right">
                {{ projectInfo?.streetName }} {{ projectInfo?.villageName }}
              </span>
            </div>

            <div class="flex justify-between items-start">
              <span class="text-sm text-gray-600 w-20 flex-shrink-0">详细地址:</span>
              <span class="text-sm text-gray-800 flex-1 text-right">
                {{ projectInfo?.address }}
              </span>
            </div>
          </div>

          <!-- 分割线 -->
          <div class="border-t border-gray-100"></div>

          <!-- 单位信息 -->
          <div class="space-y-3">
            <div class="flex justify-between items-start">
              <span class="text-sm text-gray-600 w-20 flex-shrink-0">建设单位:</span>
              <span class="text-sm text-gray-800 flex-1 text-right">
                {{ projectInfo?.constructorName }}
              </span>
            </div>

            <div class="flex justify-between items-start">
              <span class="text-sm text-gray-600 w-20 flex-shrink-0">施工单位:</span>
              <span class="text-sm text-gray-800 flex-1 text-right">
                {{ projectInfo?.contractorName }}
              </span>
            </div>
          </div>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="px-6 py-4 border-t border-gray-100">
        <div class="flex gap-3">
          <wd-button type="info" size="medium" class="flex-1" @click="handleClose">取消</wd-button>

          <wd-button
            type="primary"
            size="medium"
            class="flex-1"
            :loading="loading"
            @click="handleConfirm">
            确定绑定
          </wd-button>
        </div>
      </div>
    </div>
  </wd-popup>
</template>

<style lang="scss" scoped>
:deep(.wd-popup__mask) {
  background-color: rgba(0, 0, 0, 0.5);
}

:deep(.wd-popup__container) {
  padding: 0;
}
</style>
