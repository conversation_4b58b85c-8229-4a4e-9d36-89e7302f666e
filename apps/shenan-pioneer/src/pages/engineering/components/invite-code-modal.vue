<script setup lang="ts">
import { ApiGenerateInviteCode, ProjectInviteCodeRes } from '@/api';
import { FormatDate, Loading, Toast, utilsOss } from '@/utils';
// eslint-disable-next-line import/no-relative-packages
import VueQrcode from '../../../components/qrcode-vue3';
import VueCanvasPoster from 'vue3-canvas-poster';

const props = defineProps<{
  show: boolean;
  projectId: string;
}>();

const emit = defineEmits<{
  'update:show': [value: boolean];
}>();

const inviteCodeData = ref<ProjectInviteCodeRes | null>(null);
const loading = ref(false);
const qrcodeValue = ref('');
const posterUrl = ref('');
const painting = ref<any>();
const qrcodeRef = ref();

const imgs = {
  qrcode_bg: `${utilsOss.imgPath}/engineering/img_pop.png`,
  icon: `${utilsOss.imgPath}/engineering/icon_ts.png`,
};

function closeModal() {
  emit('update:show', false);
  inviteCodeData.value = null;
  qrcodeValue.value = '';
  posterUrl.value = '';
  painting.value = null;
}

async function generateInviteCode() {
  if (!props.projectId) {
    Toast.error('工程ID不能为空');
    return;
  }

  loading.value = true;
  Loading.show('生成中...');
  try {
    const data = await ApiGenerateInviteCode({ id: props.projectId });
    inviteCodeData.value = data;
    qrcodeValue.value = `${window.location.origin}/shenan-pioneer/index.html#/pages/engineering/bind?code=${data.inviteCode}`;
  } catch (error) {
    Toast.error('生成邀请码失败，请重试');
  } finally {
    loading.value = false;
    Loading.hide();
  }
}

function handleImageLoaded(img: string) {
  if (!inviteCodeData.value) return;

  uni.getImageInfo({
    src: imgs.qrcode_bg,
    success: (res) => {
      uni.getImageInfo({
        src: img,
        success: (res2) => {
          painting.value = {
            width: `${res.width}px`,
            height: `${res.height}px`,
            background: 'transparent',
            views: [
              {
                type: 'image',
                url: imgs.qrcode_bg,
                css: {
                  borderRadius: '8px',
                  width: `${res.width}px`,
                  height: `${res.height}px`,
                },
              },
              {
                type: 'text',
                text: inviteCodeData.value!.projectName,
                css: {
                  fontSize: '50px',
                  fontWeight: 'bold',
                  textAlign: 'center',
                  color: '#ffffff',
                  width: `${res.width}px`,
                  top: `100px`,
                },
              },
              {
                type: 'image',
                url: img,
                css: {
                  left: `${(res.width - 450) / 2}px`,
                  top: `200px`,
                  width: '450px',
                  height: '450px',
                  borderRadius: '8px',
                  borderWidth: '5px',
                  borderColor: '#ebedff',
                  borderStyle: 'solid',
                },
              },
              {
                type: 'text',
                text: `邀请码: ${inviteCodeData.value!.inviteCode}`,
                css: {
                  fontSize: '38px',
                  fontWeight: 'bold',
                  textAlign: 'center',
                  color: '#666666',
                  width: `${res.width}px`,
                  top: `${res.height - 200}px`,
                },
              },
              {
                type: 'text',
                text: `有效期至: ${FormatDate(inviteCodeData.value!.expireAt)}`,
                css: {
                  fontSize: '28px',
                  fontWeight: 'normal',
                  textAlign: 'center',
                  color: '#999999',
                  width: `${res.width}px`,
                  top: `${res.height - 140}px`,
                },
              },

              {
                type: 'image',
                url: imgs.icon,
                css: {
                  left: `55px`,
                  width: '30px',
                  height: '30px',
                  top: `${res.height - 60}px`,
                },
              },

              {
                type: 'text',
                text: `可扫码绑定该工程，或者填写工程邀请码绑定工程`,
                css: {
                  fontSize: '24px',
                  fontWeight: 'normal',
                  textAlign: 'center',
                  color: '#999999',
                  width: `${res.width}px`,
                  top: `${res.height - 60}px`,
                  left: `15px`,
                },
              },
            ],
          };
        },
      });
    },
  });
}

function onPosterSuccess(url: string) {
  posterUrl.value = url;
}

function onPosterFail(err: any) {
  console.error('生成海报失败:', err);
  Toast.error('生成海报失败');
}

function onCopy() {
  if (!inviteCodeData.value?.inviteCode) {
    Toast.error('邀请码不存在');
    return;
  }

  uni.setClipboardData({
    data: inviteCodeData.value?.inviteCode,
    success: () => {
      Toast.success('复制成功');
    },
    fail: () => {
      Toast.error('复制失败');
    },
  });
}

async function saveToPhone() {
  if (!posterUrl.value) {
    Toast.error('请先生成二维码');
    return;
  }

  try {
    // 保存图片到相册
    await uni.saveImageToPhotosAlbum({
      filePath: posterUrl.value,
    });
    Toast.success('保存成功');
  } catch (error) {
    Toast.show('长按图片保存到手机');
  }
}

const visible = ref(false);

watch(
  () => props.show,
  (newVal) => {
    if (newVal && props.projectId) {
      generateInviteCode();
    }
  },
);

watch(
  () => posterUrl.value,
  (newVal) => {
    visible.value = !!newVal;
  },
);
</script>

<template>
  <template v-if="qrcodeValue">
    <VueQrcode
      ref="qrcodeRef"
      style="opacity: 0"
      type="canvas"
      shape="square"
      :margin="10"
      :value="qrcodeValue"
      :qr-options="{ typeNumber: '0', mode: 'Byte', errorCorrectionLevel: 'Q' }"
      :dots-options="{ type: 'square', color: '#121212' }"
      :background-options="{ round: 0, color: '#ffffff' }"
      :corners-square-options="{ type: 'square', color: '#121212' }"
      :corners-dot-options="{ type: 'square', color: '#121212' }"
      @image-loaded="handleImageLoaded" />

    <VueCanvasPoster
      v-if="painting"
      :poster-url="posterUrl"
      :width-pixels="800"
      :painting="painting"
      style="position: absolute; left: -9999px; top: -9999px; opacity: 0"
      @success="onPosterSuccess"
      @fail="onPosterFail" />
  </template>
  <wd-popup v-model="visible" position="center" custom-class="!bg-transparent" @close="closeModal">
    <div class="w-screen h-full p-6">
      <img v-show="posterUrl" class="w-full" :src="posterUrl" mode="widthFix" />

      <div v-show="posterUrl" class="w-full px-5">
        <wd-button
          size="large"
          icon="download"
          aria-readonly
          custom-class="w-full text-center text-white text-22 mt-5"
          @click="saveToPhone">
          长按图片保存到手机
        </wd-button>
      </div>
      <div v-show="posterUrl" class="w-full px-5">
        <wd-button
          size="large"
          icon="copy"
          type="info"
          aria-readonly
          custom-class="w-full text-center text-white text-22 mt-5"
          @click="onCopy">
          复制邀请码
        </wd-button>
      </div>
    </div>
  </wd-popup>
</template>
