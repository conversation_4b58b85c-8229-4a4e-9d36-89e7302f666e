<script setup lang="ts">
interface Props {
  show: boolean;
}

interface Emits {
  (e: 'confirm'): void;
  (e: 'close'): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const visible = ref(false);

// 处理确认认证
function handleConfirm() {
  emit('confirm');
}

// 处理关闭
function handleClose() {
  emit('close');
}

watch(
  () => props.show,
  (newVal) => {
    visible.value = newVal;
  },
);
</script>

<template>
  <wd-popup
    v-model="visible"
    position="center"
    custom-class="rounded-lg overflow-hidden"
    :close-on-click-modal="false"
    @close="handleClose">
    <div class="w-[90vw] max-w-[380px] bg-white rounded-lg">
      <!-- 标题 -->
      <div class="px-6 py-4 border-b border-gray-100">
        <h3 class="text-lg font-bold text-gray-800 text-center">认证提示</h3>
      </div>

      <!-- 内容 -->
      <div class="px-6 py-6">
        <div class="text-center">
          <!-- 图标 -->
          <div class="mb-4">
            <sc-icon
              name="i-carbon:user-identification"
              size="48"
              class="text-orange-500 mx-auto" />
          </div>

          <!-- 提示文字 -->
          <div class="mb-6">
            <p class="text-gray-800 text-base mb-2">您还未完成身份认证</p>
            <p class="text-gray-600 text-sm">
              为了确保工程绑定的安全性，请先完成身份认证后再进行绑定操作
            </p>
          </div>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="px-6 py-4 border-t border-gray-100">
        <div class="flex gap-3">
          <wd-button type="info" size="medium" class="flex-1" @click="handleClose">
            稍后认证
          </wd-button>

          <wd-button type="primary" size="medium" class="flex-1" @click="handleConfirm">
            我要认证
          </wd-button>
        </div>
      </div>
    </div>
  </wd-popup>
</template>

<style lang="scss" scoped>
:deep(.wd-popup__mask) {
  background-color: rgba(0, 0, 0, 0.5);
}

:deep(.wd-popup__container) {
  padding: 0;
}
</style>
