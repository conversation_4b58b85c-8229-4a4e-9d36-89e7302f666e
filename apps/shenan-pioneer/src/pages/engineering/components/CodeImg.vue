<script setup lang="ts">
import { ApiGetProjectShow } from '@/api';

const props = defineProps<{
  id: string;
}>();

const qrCodeUrl = ref('');
const qrcodeInfo = reactive({
  show: false,
});

function onClose() {
  qrcodeInfo.show = false;
}

async function getQrcodeInfo() {
  const res = await ApiGetProjectShow(props.id);
  qrCodeUrl.value = res.qrCodeUrl;
  qrcodeInfo.show = true;
}
</script>
<template>
  <div class="flex justify-end">
    <div
      class="rounded-sm border border-blue-400 border-solid w-[28px] h-[28px] flex items-center justify-center"
      @click.stop="getQrcodeInfo">
      <sc-icon name="i-carbon:qr-code" size="16" class="text-blue-500 block"></sc-icon>
    </div>
  </div>

  <wd-popup
    v-model="qrcodeInfo.show"
    :z-index="999"
    custom-class="!bg-transparent !overflow-hidden code-popup"
    position="center"
    @click-modal="onClose">
    <div class="w-screen h-full p-6 position-relative rounded-lg">
      <image class="w-full block" mode="widthFix" :src="qrCodeUrl" alt="" />
      <wd-button
        size="large"
        icon="download"
        aria-readonly
        custom-class="w-full text-center text-white text-22 mt-5">
        长按图片下载，可使用微信扫码
      </wd-button>
    </div>
  </wd-popup>
</template>
