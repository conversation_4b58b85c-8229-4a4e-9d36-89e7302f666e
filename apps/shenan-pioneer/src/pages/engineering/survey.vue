<script setup lang="ts">
import {
  ApiGetMonitorFlowList,
  ApiGetMonitorOrderDetail,
  ApiMonitorOrderUpdate,
  MonitorInstallSurveyBody,
} from '@/api';
import { Dayjs, exceptionToast, FormatDate, IsPro, Navigator, Toast } from '@/utils';
import { addWatermarkAfterRead } from '@/pages/recycling/canvas';
import { FormConfig } from '@rms/components/sc-form/sc-form.vue';
import { Dictionary } from '@rms/types';
import { ChooseFile } from '@rms/components/sc-upload/type';
import { StorageSetMonitorFlowRefresh } from '@/storage/monitor';
import { ApiQueryHandler } from '@shencom/api';

const project = ref<Dictionary>({
  title: '',
  address: '',
  flowName: '现场勘查',
  reservationTime: '',
});

const formData = ref<Omit<MonitorInstallSurveyBody, 'pic'> & { pic: Dictionary[] }>({
  id: '',
  state: 1,
  inspectTime: '',
  reservationTime: '',
  memo: '',
  pic: [],
  installCnt: 1,
});

const isSubmit = ref(false);

const scFormRef = ref();

const formConfig = ref<FormConfig['config']>({
  formAttrs: {
    labelWidth: '220rpx',
    fontSize: '15px',
    border: true,
  },
  data: [
    {
      prop: 'pic',
      label: '现场照片',
      tag: {
        tagType: 'upload',
        attr: {
          required: true,
          multiple: true,
          maxCount: 10,
          maxSize: 10 * 1024 * 1024,
          uploadType: IsPro ? 'oss' : 'server',
          sizeType: ['original', 'compressed'],
          autoUpload: false,
          afterRead: async (files: ChooseFile[]) => {
            await addWatermarkAfterRead(files, project.value);
            formData.value.pic.forEach((item) => {
              if (item.tempUrl) return;
              item.tempUrl = item.watermark;
              item.url = item.watermark;
              item.path = item.watermark;
            });
          },
        },
      },
      listeners: {
        oversize: () => {
          Toast.warning('上传文件大小不能超过10M！', {
            duration: 2000,
          });
        },
      },
    },
    {
      prop: 'installCnt',
      label: '预估需安装监控数量',
      tag: {
        tagType: 'component',
        attr: {
          placeholder: '请预估需安装监控数量',
          required: true,
        },
      },
    },
    {
      prop: 'memo',
      label: '勘查说明',
      tag: {
        tagType: 'textarea',
        attr: {
          placeholder: '请输入勘查说明',
          rows: 3,
          maxlength: 300,
          showCount: true,
        },
      },
    },
    {
      prop: 'reservationTime',
      label: '预约安装时间',
      tag: {
        tagType: 'date-picker',
        attr: {
          placeholder: '请选择预约安装时间',
          required: true,
          type: 'datetime',
          'hide-second': true,
        },
      },
    },
  ],
  form: formData,
  rules: [
    {
      name: 'pic',
      rules: [
        {
          required: true,
          errorMessage: '请选择现场照片',
        },
      ],
    },
    {
      name: 'installCnt',
      rules: [
        {
          required: true,
          errorMessage: '请预估需安装监控数量',
        },
      ],
    },
    {
      name: 'reservationTime',
      rules: [
        {
          required: true,
          errorMessage: '请选择预约安装时间',
        },
      ],
    },
  ],
});

onLoad(async (options) => {
  formData.value.id = options?.id || '';

  const data = await ApiGetMonitorOrderDetail(options?.orderId);
  const flowList = await getFlowList(options?.orderId);
  const flow1 = flowList.find((item) => item.flow === 1);
  project.value.title = data?.projectName || '';
  project.value.address = data?.projectAddress || '';
  project.value.reservationTime = FormatDate(flow1?.finishTime, 'YYYY-MM-DD HH:mm') || '';
});

async function onSubmit() {
  if (isSubmit.value) return;

  const flag = await scFormRef.value.validate();
  if (!flag) return;

  isSubmit.value = true;

  try {
    const uploadComponent = scFormRef.value.startRef.pic;
    await uploadComponent.onUpload();
  } catch (error) {
    exceptionToast(error, '图片上传失败');
    isSubmit.value = false;
    return;
  }

  submitForm();
}

async function submitForm() {
  try {
    const formPayload = {
      ...formData.value,
      inspectTime: Dayjs().format('YYYY-MM-DD HH:mm'),
      pic: formData.value.pic.map((item) => item.id).join(','),
    };

    if (!formPayload.pic) {
      isSubmit.value = false;
      return;
    }

    await ApiMonitorOrderUpdate(formPayload);

    // 设置刷新标记，通知相关页面刷新数据
    StorageSetMonitorFlowRefresh();

    Toast.success('提交成功', {
      complete: () => {
        Navigator.back();
      },
    });
  } catch (error) {
    exceptionToast(error, '提交失败');
    isSubmit.value = false;
  }
}

async function getFlowList(id: string) {
  const data = await ApiGetMonitorFlowList({
    query: ApiQueryHandler(id, 'orderId', 'select'),
  });
  return data.content;
}

function onGotoHistory() {
  Navigator.push('/pages/recycling/list', { type: 0, tab: 2 });
}
</script>

<template>
  <div class="w-full min-h-screen p-3 bg-base box-border relative pb-16">
    <div class="mt-3 bg-white px-3 pt-4 rounded-md mb-4">
      <wd-steps :active="1" align-center>
        <wd-step title="安装预约" :description="project.reservationTime"></wd-step>
        <wd-step title="现场勘查" description=" "></wd-step>
        <wd-step title="上门安装" description=" "></wd-step>
      </wd-steps>

      <sc-form ref="scFormRef" :config="formConfig" class="mt-3">
        <template #installCnt>
          <sc-number-box v-model="formData.installCnt" :min="1" class="pr-1" />
        </template>
      </sc-form>
    </div>

    <sc-button-fixed class="pt-5 box-border">
      <div class="w-full flex items-center justify-between">
        <div></div>
        <wd-button custom-class="!mr-6" type="text" icon="list" @click="onGotoHistory">
          进度查询
        </wd-button>
        <wd-button
          :disabled="isSubmit"
          custom-class="!rounded-md !w-1/2"
          size="large"
          type="primary"
          @click="onSubmit">
          提交
        </wd-button>
      </div>
    </sc-button-fixed>
  </div>
</template>
