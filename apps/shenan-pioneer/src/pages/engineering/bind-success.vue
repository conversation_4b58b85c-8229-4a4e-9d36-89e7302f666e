<script setup lang="ts">
import type { ProjectSearchResponse } from '@/api';
import { FormatDate } from '@/utils';

// 从路由参数中获取工程信息
const projectInfo = ref<ProjectSearchResponse | null>(null);

onLoad((options: any) => {
  // 从路由参数中解析工程信息
  if (options.projectInfo) {
    try {
      projectInfo.value = JSON.parse(decodeURIComponent(options.projectInfo));
    } catch (error) {
      console.error('解析工程信息失败:', error);
    }
  }
});

// 返回首页
function handleBackToHome() {
  uni.$wx.miniProgram.redirectTo({
    url: '/pages/index/index',
  });
}

// 查看工程详情
function handleViewProject() {
  if (projectInfo.value) {
    uni.navigateTo({
      url: `/pages/engineering/detail?id=${projectInfo.value.id}`,
    });
  }
}

// 格式化金额
function formatAmount(amount: number) {
  return amount ? `${amount.toLocaleString()} 元` : '-';
}

// 格式化面积
function formatArea(area: number) {
  return area ? `${area} 平方米` : '-';
}
</script>

<template>
  <div class="min-h-screen bg-content">
    <div class="px-4 py-8">
      <!-- 成功状态展示 -->
      <div class="text-center mb-8">
        <!-- 成功图标 -->
        <div class="mb-6">
          <div class="w-20 h-20 mx-auto bg-green-100 rounded-full flex items-center justify-center">
            <sc-icon name="i-carbon:checkmark" size="48" class="text-green-500" />
          </div>
        </div>

        <!-- 成功文字 -->
        <h1 class="text-2xl font-bold text-gray-800 mb-2">工程绑定成功</h1>
        <p class="text-gray-600">恭喜您成功绑定工程，现在可以开始使用相关功能了</p>
      </div>

      <!-- 工程信息卡片 -->
      <div v-if="projectInfo" class="bg-card p-6 mb-6">
        <h3 class="text-lg font-bold text-gray-800 mb-4 flex items-center">
          <sc-icon name="i-carbon:building" size="20" class="mr-2 text-blue-500" />
          工程信息
        </h3>

        <div class="space-y-3">
          <!-- 工程名称 -->
          <div class="flex justify-between items-start">
            <span class="text-sm text-gray-600 w-20 flex-shrink-0">工程名称:</span>
            <span class="text-sm text-gray-800 flex-1 text-right font-medium">
              {{ projectInfo.name }}
            </span>
          </div>

          <!-- 备案编号 -->
          <div class="flex justify-between items-start">
            <span class="text-sm text-gray-600 w-20 flex-shrink-0">备案编号:</span>
            <span class="text-sm text-gray-800 flex-1 text-right">
              {{ projectInfo.projectNumber }}
            </span>
          </div>

          <!-- 工程分类 -->
          <div class="flex justify-between items-start">
            <span class="text-sm text-gray-600 w-20 flex-shrink-0">工程分类:</span>
            <span class="text-sm text-gray-800 flex-1 text-right">
              {{ projectInfo.pCateName }} - {{ projectInfo.cateName }}
            </span>
          </div>

          <!-- 工程金额 -->
          <div class="flex justify-between items-start">
            <span class="text-sm text-gray-600 w-20 flex-shrink-0">工程金额:</span>
            <span class="text-sm text-gray-800 flex-1 text-right">
              {{ formatAmount(projectInfo.amount) }}
            </span>
          </div>

          <!-- 施工面积 -->
          <div class="flex justify-between items-start">
            <span class="text-sm text-gray-600 w-20 flex-shrink-0">施工面积:</span>
            <span class="text-sm text-gray-800 flex-1 text-right">
              {{ formatArea(projectInfo.area) }}
            </span>
          </div>

          <!-- 分割线 -->
          <div class="border-t border-gray-100 my-4"></div>

          <!-- 施工时间 -->
          <div class="flex justify-between items-start">
            <span class="text-sm text-gray-600 w-20 flex-shrink-0">开始时间:</span>
            <span class="text-sm text-gray-800 flex-1 text-right">
              {{ FormatDate(projectInfo.startAt) }}
            </span>
          </div>

          <div class="flex justify-between items-start">
            <span class="text-sm text-gray-600 w-20 flex-shrink-0">结束时间:</span>
            <span class="text-sm text-gray-800 flex-1 text-right">
              {{ FormatDate(projectInfo.endAt) }}
            </span>
          </div>

          <!-- 分割线 -->
          <div class="border-t border-gray-100 my-4"></div>

          <!-- 地址信息 -->
          <div class="flex justify-between items-start">
            <span class="text-sm text-gray-600 w-20 flex-shrink-0">所在社区:</span>
            <span class="text-sm text-gray-800 flex-1 text-right">
              {{ projectInfo.streetName }} {{ projectInfo.villageName }}
            </span>
          </div>

          <div class="flex justify-between items-start">
            <span class="text-sm text-gray-600 w-20 flex-shrink-0">详细地址:</span>
            <span class="text-sm text-gray-800 flex-1 text-right">{{ projectInfo.address }}</span>
          </div>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="space-y-3">
        <!-- 查看工程详情按钮 -->
        <wd-button
          v-if="projectInfo"
          type="primary"
          size="large"
          block
          icon="view"
          @click="handleViewProject">
          查看工程详情
        </wd-button>

        <!-- 返回首页按钮 -->
        <wd-button type="info" size="large" block icon="home" @click="handleBackToHome">
          返回首页
        </wd-button>
      </div>

      <!-- 温馨提示 -->
      <div class="bg-card p-4 mt-6">
        <h3 class="text-sm font-medium text-gray-700 mb-2 flex items-center">
          <sc-icon name="i-carbon:information" size="16" class="mr-1 text-blue-500" />
          温馨提示
        </h3>
        <div class="text-xs text-gray-600 space-y-1">
          <p>• 工程绑定成功后，您可以查看工程详情和相关功能</p>
          <p>• 如需解绑工程，请联系管理员处理</p>
          <p>• 绑定的工程信息会在工程列表中显示</p>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.bg-content {
  background: linear-gradient(180deg, #f8fafc 0%, #f1f5f9 100%);
}

.bg-card {
  @apply bg-white rounded-xl overflow-hidden shadow-md;
}

// 成功图标动画
.w-20.h-20 {
  animation: successPulse 2s ease-in-out infinite;
}

@keyframes successPulse {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}
</style>
