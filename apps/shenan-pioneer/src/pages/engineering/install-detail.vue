<script setup lang="ts">
import {
  ApiGetMonitorOrderDetail,
  ApiGetMonitorFlowList,
  MonitorAppointBody,
  MonitorInstallBody,
  MonitorInstallSurveyBody,
  MonitorOrderFlowListItem,
  MonitorOrderDetailRes,
} from '@/api';
import { Navigator } from '@/utils';
import { onLocation } from '@/utils/location';
import { Dictionary } from '@rms/types';
import { ApiFileShow, ApiQueryHandler } from '@shencom/api';
import { FormatDateTime } from '@shencom/utils';
import { computed, ref } from 'vue';
import { StorageGetMonitorFlowRefresh, StorageRemoveMonitorFlowRefresh } from '@/storage/monitor';
import useRoleStore from '@/hooks/useRole';

const { roleInfo } = useRoleStore();

const params = ref<{ id: string }>({ id: '' });

const detailData = ref<MonitorOrderDetailRes>();
const flowList = ref<MonitorOrderFlowListItem[]>([]);

// 根据状态获取步骤信息
const steps = ref<Dictionary[]>([]);

// 获取当前激活的步骤索引
const activeStep = computed(() => {
  const status = Number(detailData.value?.flow);
  return Math.max(0, status - 1);
});

const flowDetail = ref();

async function getStepDetails() {
  const status = detailData.value?.flow || 1;
  const stepData = [];
  const details = [];

  // 安装预约信息（状态 >= 1 时显示）
  if (status >= 1) {
    const item = flowList.value.find((v) => v.flow === 1 && v.state === 1) as MonitorAppointBody;
    const reservationTime = item?.reservationTime
      ? FormatDateTime(item.reservationTime, 'YYYY-MM-DD HH:mm')
      : ' ';
    stepData.push({
      title: '安装预约',
      description: item?.updatedAt ? FormatDateTime(item?.updatedAt, 'YYYY-MM-DD HH:mm') : ' ',
      active: status >= 1,
      completed: status > 1,
    });
    details.push({
      title: '安装预约',
      content: {
        ...item,
        reservationTime,
      },
    });
  }

  // 现场勘查信息（状态 >= 2 时显示）
  if (status >= 2) {
    const item = flowList.value.find(
      (v) => v.flow === 2 && v.state === 1,
    ) as MonitorInstallSurveyBody;

    const imgs = item?.pic ? (await ApiFileShow({ ids: item?.pic.split(',') })).data || [] : [];
    const inspectTime = item?.inspectTime
      ? FormatDateTime(item.inspectTime, 'YYYY-MM-DD HH:mm')
      : ' ';

    stepData.push({
      title: '现场勘查',
      description: item?.updatedAt ? FormatDateTime(item?.updatedAt, 'YYYY-MM-DD HH:mm') : ' ',
      active: status >= 2,
      completed: status > 2,
    });
    details.push({
      title: '现场勘查',
      content: item && {
        ...item,
        pic: imgs.map((v) => v.remoteUrl) || [],
        inspectTime,
        reservationTime: item?.reservationTime
          ? FormatDateTime(item.reservationTime, 'YYYY-MM-DD HH:mm')
          : ' ',
      },
    });
  }

  // 上门安装信息（状态 >= 3 时显示）
  if (status >= 3) {
    const item = flowList.value.find((v) => v.flow === 3 && v.state === 1) as MonitorInstallBody;

    const imgs = item?.pic ? (await ApiFileShow({ ids: item?.pic.split(',') })).data || [] : [];
    const installTime = item?.installTime
      ? FormatDateTime(item.installTime, 'YYYY-MM-DD HH:mm')
      : ' ';
    stepData.push({
      title: '上门安装',
      description: item?.updatedAt ? FormatDateTime(item?.updatedAt, 'YYYY-MM-DD HH:mm') : ' ',
      active: status >= 3,
      completed: status > 3,
    });
    details.push({
      title: '上门安装',
      content: item && {
        ...item,
        pic: imgs.map((v) => v.remoteUrl) || [],
        installTime,
      },
    });
  }

  steps.value = stepData;
  flowDetail.value = details;
}

async function getDetail() {
  const res = await ApiGetMonitorOrderDetail(params.value.id);
  detailData.value = res;
}

async function getFlowList() {
  const res = await ApiGetMonitorFlowList({
    query: ApiQueryHandler(params.value.id, 'orderId', 'select'),
  });
  flowList.value = res.content || [];
}

function init() {
  Promise.all([getDetail(), getFlowList()]).then(() => {
    getStepDetails();
  });
}

onLoad(async (options) => {
  params.value.id = options?.id || '';
  init();
});

onShow(() => {
  // 检查是否需要刷新数据
  const needRefresh = StorageGetMonitorFlowRefresh();
  if (needRefresh) {
    init();
  }
});

// 图片预览
const previewImage = (images: string[] | undefined, current: string) => {
  if (!images || images.length === 0) return;
  uni.previewImage({
    urls: images,
    current,
  });
};

function onCall(phone?: string) {
  if (!phone) return;
  uni.makePhoneCall({
    phoneNumber: phone,
  });
}

function toSurvey() {
  StorageRemoveMonitorFlowRefresh();

  Navigator.push('/pages/engineering/survey', {
    id: detailData.value?.flowId,
    orderId: detailData.value?.id,
  });
}

function toInstall() {
  const appoint = flowList.value.find((v) => v.flow === 1 && v.state === 1);
  const inspect = flowList.value.find((v) => v.flow === 2 && v.state === 1);

  Navigator.push('/pages/engineering/installation', {
    id: detailData.value?.flowId,
    orderId: detailData.value?.id,
    reservationTime: (appoint as MonitorAppointBody)?.reservationTime,
    inspectTime: (inspect as MonitorInstallSurveyBody)?.inspectTime,
  });
}
</script>

<template>
  <view class="min-h-screen bg-base pb-16">
    <view class="text-gray-700 p-5">
      <view class="flex justify-between items-center">
        <text class="block text-base font-bold">{{ detailData?.projectName || '工程详情' }}</text>
        <view class="bg-gray-200 px-2 rounded-md border border-gray-200">
          <text class="text-xs text-gray-500">{{ detailData?.projectNumber }}</text>
        </view>
      </view>
      <view class="mt-2">
        <text class="text-sm text-gray-500">{{ detailData?.projectAddress }}</text>
        <wd-icon
          v-if="detailData?.projectPoiId"
          name="location"
          size="14"
          class="ml-1"
          @click="onLocation(detailData)" />
      </view>
    </view>

    <view class="bg-white py-5 mx-2.5 rounded-lg shadow-sm">
      <wd-steps :active="activeStep" align-center>
        <wd-step
          v-for="(step, index) in steps"
          :key="index"
          :title="step.title"
          :description="step.description" />
      </wd-steps>
    </view>

    <view class="mt-4 px-2.5">
      <view
        v-for="(detail, index) in flowDetail"
        :key="index"
        class="bg-white rounded-lg mb-4 overflow-hidden shadow-sm">
        <view class="bg-blue-600 text-center py-2 border-b border-gray-100">
          <text class="text-base font-medium text-white">{{ detail.title }}</text>
        </view>

        <view>
          <template v-if="detail.title === '安装预约'">
            <wd-cell-group v-if="detail.content">
              <wd-cell title="预约工程" :value="detailData?.projectName" />
              <wd-cell title="联系人" :value="detail.content.contactName" />
              <wd-cell title="联系方式">
                <view class="flex justify-end items-center">
                  <text>{{ detail.content.contactMobile }}</text>
                  <i
                    class="i-ion-call ml-1 text-blue"
                    @click="onCall(detail.content.contactMobile)"></i>
                </view>
              </wd-cell>
              <wd-cell title="期望上门时间" :value="detail.content.reservationTime" />
            </wd-cell-group>
            <sc-empty v-else class="my-8" text="待预约" />
          </template>

          <template v-if="detail.title === '现场勘查'">
            <wd-cell-group v-if="detail.content">
              <wd-cell title="勘查照片">
                <view class="flex justify-end flex-wrap gap-3">
                  <image
                    v-for="(img, imgIndex) in detail.content.pic"
                    :key="imgIndex"
                    :src="img"
                    mode="aspectFit"
                    class="w-16 h-16 rounded-md border border-solid border-gray-200"
                    @click="previewImage(detail.content.pic, img)" />
                </view>
              </wd-cell>
              <wd-cell title="勘查监控数量" :value="detail.content.installCnt" />
              <wd-cell title="勘查说明" :value="detail.content.memo" />
              <wd-cell title="勘查人员" :value="detail.content.createdUserName" />
              <wd-cell title="联系方式">
                <view class="flex justify-end items-center">
                  <text>{{ detail.content.createdUserPhone }}</text>
                  <i
                    class="i-ion-call ml-1 text-blue"
                    @click="onCall(detail.content.createdUserPhone)"></i>
                </view>
              </wd-cell>
              <wd-cell title="勘查时间" :value="detail.content.inspectTime" />
              <wd-cell title="预约安装时间" :value="detail.content.reservationTime" />
            </wd-cell-group>
            <sc-empty v-else class="my-8" text="待勘查" />
          </template>

          <template v-if="detail.title === '上门安装'">
            <wd-cell-group v-if="detail.content">
              <wd-cell title="安装照片">
                <view class="flex justify-end flex-wrap gap-3">
                  <image
                    v-for="(img, imgIndex) in detail.content.pic"
                    :key="imgIndex"
                    :src="img"
                    mode="aspectFit"
                    class="w-16 h-16 rounded border border-solid border-gray-200"
                    @click="previewImage(detail.content.pic, img)" />
                </view>
              </wd-cell>
              <wd-cell title="安装监控数量" :value="detail.content.installCnt" />
              <wd-cell title="安装说明" :value="detail.content.memo" />
              <wd-cell title="安装人员" :value="detail.content.createdUserName" />
              <wd-cell title="联系方式">
                <view class="flex justify-end items-center">
                  <text>{{ detail.content.createdUserPhone }}</text>
                  <i
                    class="i-ion-call ml-1 text-blue"
                    @click="onCall(detail.content.createdUserPhone)"></i>
                </view>
              </wd-cell>
              <wd-cell title="安装时间" :value="detail.content.installTime" />
            </wd-cell-group>
            <sc-empty v-else class="my-8" text="待安装" />
          </template>
        </view>
      </view>
    </view>

    <sc-button-fixed
      v-if="detailData && [2, 3].includes(detailData.flow) && roleInfo.roleName === '安装人员'"
      class="pt-5 box-border">
      <wd-button
        v-if="detailData.flow === 2"
        block
        custom-class="!bg-gradient-to-br !from-violet-600 !to-blue-600"
        size="large"
        @click="toSurvey">
        去勘查
      </wd-button>
      <wd-button
        v-if="detailData.flow === 3"
        block
        custom-class="!bg-gradient-to-br !from-violet-600 !to-blue-600"
        size="large"
        @click="toInstall">
        去安装
      </wd-button>
    </sc-button-fixed>
  </view>
</template>
