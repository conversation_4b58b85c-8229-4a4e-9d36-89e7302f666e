<script setup lang="ts">
import {
  ApiGetProjectHistoryMemo,
  ApiGetProjectList,
  ApiGetRoleRegion,
  ApiProjectMemoCreate,
  ApiUpdateProjectStatus,
  ProjectInfoRes,
  ProjectMemoRes,
} from '@/api';
import useRoleStore from '@/hooks/useRole';
import { monitorFlagOptions, projectStatusOptions } from '@/state';
import { StorageGetMonitorFlowRefresh, StorageRemoveMonitorFlowRefresh } from '@/storage/monitor';
import {
  Dialog,
  exceptionToast,
  FormatDateTime,
  IsMiniProgram,
  IsWeixinH5,
  Navigator,
  Toast,
  utilsOss,
} from '@/utils';
import { useList } from '@rms/components/sc-list/sc-list';
import { ServiceGisByIds } from '@rms/service/src';
import { watchThrottled } from '@vueuse/core';
import CodeImg from './components/CodeImg.vue';
import InviteCodeModal from './components/invite-code-modal.vue';

// type SearchParams = Parameters<typeof ApiGetProjectList>[0];

const imgs = {
  icon_xc: `${utilsOss.imgPath}/engineering/icon_xc.png`,
  icon_wg: `${utilsOss.imgPath}/engineering/icon_wg.png`,
  icon_zg: `${utilsOss.imgPath}/engineering/icon_zg.png`,
  icon_jk: `${utilsOss.imgPath}/engineering/icon_jk.png`,
  icon_zl: `${utilsOss.imgPath}/engineering/icon_zl.png`,
};

const search = reactive({
  status: 0,
  size: 10,
});

const { list, listProps, getData } = useList<ProjectInfoRes[]>({
  request: ApiGetProjectList,
  params: search,
  immediate: false,
  handle: async (res) => {
    return res;
  },
});

watchThrottled(
  search,
  () => {
    refresh();
  },
  { throttle: 200, immediate: true, deep: true },
);

function toMonitor(item: ProjectInfoRes) {
  Navigator.push('/pages/monitor/index', {
    projectId: item.id,
  });
}

function refresh() {
  getData(true, {
    status: search.status === 0 ? '' : search.status - 1,
  });
}

onMounted(() => {
  refresh();
});

onShow(() => {
  const needRefresh = StorageGetMonitorFlowRefresh();
  if (needRefresh) {
    refresh();
    StorageRemoveMonitorFlowRefresh();
  }
});

const roleRegion = ref();

const isAdmin = computed(() => {
  return ['2', '3', '4', '5', '6'].includes(roleRegion.value?.typeId);
});

const roleStore = useRoleStore();
roleStore.initOrganization(true);

// 业主和施工负责人角色
const isAdmin2 = computed(() => {
  return ['Construction:party', 'Construction:unitleader'].includes(roleStore.roleInfo.roleId);
});

// 安装人员
const isInstall = computed(() => {
  return ['Installation:Manager'].includes(roleStore.roleInfo.roleId);
});

async function getRegionList() {
  roleRegion.value = await ApiGetRoleRegion();
}

onMounted(() => {
  getRegionList();
});

function toDetail(item: ProjectInfoRes) {
  Navigator.push('/pages/engineering/detail', {
    id: item.id,
  });
}

function toInstall(item: ProjectInfoRes) {
  Navigator.push('/pages/engineering/appoint', {
    id: item.flowId,
  });
}

function toRecycle(item: ProjectInfoRes) {
  Navigator.push('/pages/recycling/create', {
    id: item.flowId,
  });
}

async function toLocation(item: ProjectInfoRes) {
  const gisInfo = await ServiceGisByIds(item.poiId);
  const options = {
    longitude: +gisInfo[0].lng,
    latitude: +gisInfo[0].lat,
    name: item.name,
    address: gisInfo[0].addr,
  };

  if (IsWeixinH5 || IsMiniProgram) {
    uni.$wx.openLocation(options);
  } else {
    uni.openLocation(options);
  }
}

async function toFinish(item: ProjectInfoRes) {
  const flag = await Dialog('未到工程结束时间，是否提前施工完成?');
  if (!flag) return;

  try {
    await ApiUpdateProjectStatus({ id: item.id, status: 2 });
    Toast.success('提交成功');
    refresh();
  } catch (error) {
    exceptionToast(error, '');
  }
}

async function toStart(item: ProjectInfoRes) {
  const flag = await Dialog('未到工程开始时间，是否提前开始施工?');
  if (!flag) return;

  try {
    await ApiUpdateProjectStatus({ id: item.id, status: 1 });
    Toast.success('提交成功');
    refresh();
  } catch (error) {
    exceptionToast(error, '');
  }
}

const memoInfo = reactive({
  show: false,
  content: '',
  projectId: '',
  history: [] as ProjectMemoRes[],
});

function openInviteCodeModal(item: ProjectInfoRes) {
  inviteCode.projectId = item.id;
  inviteCode.show = true;
}
const inviteCode = reactive({
  show: false,
  projectId: '',
});
async function getHistoryMemo(item: ProjectInfoRes) {
  const data = await ApiGetProjectHistoryMemo({ projectId: item.id, size: 200 });
  memoInfo.history = data.content;
}

async function addMemo(item: ProjectInfoRes) {
  memoInfo.projectId = item.id;
  memoInfo.content = '';
  memoInfo.history = [];
  await getHistoryMemo(item);
  memoInfo.show = true;
}

let isSubmit = false;

async function onSubmit() {
  if (isSubmit) return;

  if (!memoInfo.content) {
    Toast.error('请输入备注');
    return;
  }

  isSubmit = true;

  try {
    await ApiProjectMemoCreate({ projectId: memoInfo.projectId, content: memoInfo.content });
    Toast.success('提交成功');
    memoInfo.show = false;
    refresh();
  } catch (error) {
    exceptionToast(error, '');
  } finally {
    isSubmit = false;
  }
}

const path = '/pages/patrolRecord/list';
function toPatrol1(item: ProjectInfoRes) {
  Navigator.push(path, {
    projectId: item.id,
  });
}

function toPatrol2(item: ProjectInfoRes) {
  Navigator.push(path, {
    projectId: item.id,
    resultStatusList: '1,2,3',
  });
}

function toPatrol3(item: ProjectInfoRes) {
  Navigator.push(path, {
    projectId: item.id,
    state: 3,
  });
}

function toEvent(item: ProjectInfoRes) {
  Navigator.push('/pages/monitor/warning/list', {
    projectId: item.id,
  });
}

onPullDownRefresh(async () => {
  search.status = 0;

  await refresh();
  uni.stopPullDownRefresh();
});
onReachBottom(() => null);
</script>

<template>
  <div class="">
    <div class="w-full bg-[#F6F8FF] py-3">
      <wd-radio-group
        v-model="search.status"
        class="!bg-[#F6F8FF]"
        custom-class="flex justify-around"
        shape="button">
        <wd-radio :value="0">全部</wd-radio>
        <wd-radio :value="2">施工中</wd-radio>
        <wd-radio :value="1">未开始</wd-radio>
        <wd-radio :value="3">已结束</wd-radio>
      </wd-radio-group>
    </div>

    <sc-list v-bind="listProps" class="!p-0 !h-auto !min-h-[calc(100vh-38px)]">
      <div class="w-full p-3">
        <div
          v-for="item in list"
          :key="item.id"
          class="bg-[#F6F8FF] rounded-xl p-3 mb-3 text-12 relative"
          :class="[
            (item.flow === 6 && !isAdmin) || (item.flow === 1 && !isAdmin) || item.monitorFlag === 1
              ? 'pt-[70px]'
              : 'pt-8',
          ]"
          @click="toDetail(item)">
          <div
            v-if="item.flow === 1 && !isAdmin"
            class="absolute top-0 left-0 px-3 py-2 rounded-t-xl bg-sky1 w-full text-white text-14 font-bold mb-8">
            <span>该工程未接入监管，请先</span>
            <span class="text-red ml-2" @click.stop="toInstall(item)">预约安装>></span>
          </div>

          <div
            v-else-if="item.status === 1 && item.monitorFlag === 1"
            class="absolute top-0 left-0 px-3 py-2 rounded-t-xl bg-sky1 w-full text-white text-14 font-bold mb-8"
            @click.stop="toMonitor(item)">
            <span>该工程已接入监管，我要</span>
            <span class="text-red ml-2">查看监控>></span>
          </div>

          <div
            v-else-if="item.flow === 6 && !isAdmin"
            class="absolute top-0 left-0 px-3 py-2 rounded-t-xl bg-sky1 w-full text-white text-14 font-bold mb-8">
            <span>该工程已结束监管，请先</span>
            <span class="text-red ml-2" @click.stop="toRecycle(item)">预约回收>></span>
          </div>

          <div
            :class="[
              (item.flow === 6 && !isAdmin) ||
              (item.flow === 1 && !isAdmin) ||
              item.monitorFlag === 1
                ? 'top-11  rounded-l-xl'
                : 'top-0 rounded-tr-xl rounded-bl-xl',
            ]"
            class="absolute w-[140px] text-center ellipsis-1 bg2 p-1 text-13 right-0 z-10">
            {{ item.projectNumber || 'sadasd124213123w' }}
          </div>

          <div class="flex items-center gap-2 mb-2">
            <div class="max-w-[200px] text-16 font-bold">{{ item.name }}</div>
            <wd-tag
              :type="projectStatusOptions.find((i) => i.value === item.status)?.type as any"
              custom-class="!text-12">
              {{ monitorFlagOptions.find((i) => i.value === item.monitorFlag)?.label }}-{{
                projectStatusOptions.find((i) => i.value === item.status)?.label || '已结束'
              }}
            </wd-tag>
          </div>
          <div class="flex items-center gap-2 mb-1">
            <div>小散工程地址: {{ item.address }}</div>
            <wd-icon
              color="#1338D5"
              name="location"
              size="18px"
              @click.stop="toLocation(item)"></wd-icon>
          </div>

          <div class="mb-1">工程开始时间: {{ item.startAt }}</div>
          <div>工程结束时间: {{ item.endAt }}</div>

          <!-- 统计 -->
          <!-- gird布局，每行3个 -->
          <div class="grid grid-cols-4 gap-2 py-3">
            <!-- 监控事件 -->
            <div class="flex flex-col items-center gap-2" @click.stop="toEvent(item)">
              <div class="flex items-center gap-1">
                <image :src="imgs.icon_jk" mode="widthFix" class="w-3 h-3"></image>
                <div class="font-bold text-13">监控事件</div>
              </div>
              <div class="text-18 text-[#1338D5] font-bold">
                {{ item.eventNumber }}
              </div>
            </div>
            <!-- 巡查次数 -->
            <div class="flex flex-col items-center gap-2 mb-3" @click.stop="toPatrol1(item)">
              <div class="flex items-center gap-1">
                <image :src="imgs.icon_xc" mode="widthFix" class="w-3 h-3"></image>
                <div class="font-bold text-13">巡查次数</div>
              </div>
              <div class="text-18 text-[#1338D5] font-bold">
                {{ item.inspectRecordNum }}
              </div>
            </div>
            <!-- 违规事项 -->
            <div class="flex flex-col items-center gap-2" @click.stop="toPatrol2(item)">
              <div class="flex items-center gap-1">
                <image :src="imgs.icon_wg" mode="widthFix" class="w-3 h-3"></image>
                <div class="font-bold text-13">违规事项</div>
              </div>
              <div class="text-18 text-[#1338D5] font-bold">
                {{ item.violationRecordNum }}
              </div>
            </div>
            <!-- 已整改 -->
            <div class="flex flex-col items-center gap-2" @click.stop="toPatrol3(item)">
              <div class="flex items-center gap-1">
                <image :src="imgs.icon_zg" mode="widthFix" class="w-3 h-3"></image>
                <div class="font-bold text-13">已整改</div>
              </div>
              <div class="text-18 text-[#1338D5] font-bold">
                {{ item.rectifyRecordNum }}
              </div>
            </div>
            <!-- 已处理 -->
            <!-- <div class="flex flex-col items-center gap-2">
              <div class="flex items-center gap-1">
                <image :src="imgs.icon_zl" mode="widthFix" class="w-3 h-3"></image>
                <div class="font-bold text-13">已处理</div>
              </div>
              <div class="text-18 text-[#1338D5] font-bold">
                {{ item.rectifyEventNumber }}
              </div>
            </div> -->
          </div>
          <div class="flex justify-end mb-2" @click.stop>
            <div
              v-if="isAdmin2"
              class="rounded-sm border border-blue-400 border-solid w-[28px] h-[28px] flex items-center justify-center mr-2"
              @click.stop="openInviteCodeModal(item)">
              <wd-icon name="evaluation" class="text-blue-500 block" size="20px"></wd-icon>
            </div>
            <code-img :id="item.id"></code-img>
          </div>

          <div v-if="item.status === 1 && isAdmin2" class="w-full flex justify-end">
            <wd-button type="primary" size="medium" @click.stop="toFinish(item)">
              施工完成
            </wd-button>
          </div>

          <div v-if="item.status === 0 && isAdmin2" class="w-full flex justify-end">
            <wd-button type="primary" size="medium" @click.stop="toStart(item)">开始施工</wd-button>
          </div>

          <!-- <div v-if="isInstall" class="w-full flex justify-end mt-2">
            <wd-button type="primary" size="medium" @click.stop="addMemo(item)">添加备注</wd-button>
          </div> -->
          <!-- end -->
        </div>
      </div>
    </sc-list>

    <invite-code-modal
      :show="inviteCode.show"
      :project-id="inviteCode.projectId"
      @update:show="inviteCode.show = $event" />

    <wd-popup v-model="memoInfo.show" custom-class="rounded-lg overflow-hidden">
      <div class="w-[90vw] h-[70vh] p-3 flex flex-col justify-between">
        <div class="mb-3">
          <div class="mb-2">添加备注</div>
          <sc-textarea v-model="memoInfo.content" input-border placeholder="请输入备注" />
        </div>
        <div class="mb-2">历史记录</div>
        <div class="flex-1 overflow-y-scroll">
          <div class="text-11 pb-2">
            <div v-for="item in memoInfo.history" :key="item.id" class="flex border-b py-2">
              <div>{{ item.createdUserName }}</div>
              <div class="flex-1 px-2">{{ item.content }}</div>
              <div>{{ FormatDateTime(item.createdAt) }}</div>
            </div>
          </div>
        </div>

        <wd-button type="primary" size="medium" @click.stop="onSubmit">提交</wd-button>
      </div>
    </wd-popup>
  </div>
</template>

<style lang="scss" scoped>
:deep(.wd-radio__label) {
  display: flex;
  align-items: center;
}
:deep(.wd-radio) {
  margin: 0 !important;
}

.bg-sky1 {
  // 天空蓝渐变色
  background-image: linear-gradient(120deg, #a1c4fd 0%, #c2e9fb 100%);
}

.bg2 {
  background-image: linear-gradient(to top, #cfd9df 0%, #e2ebf0 100%);
}

.border-b {
  border-bottom: 1rpx solid #e2ebf0;
}
</style>
