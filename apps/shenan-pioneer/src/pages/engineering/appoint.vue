<script setup lang="ts">
import {
  exceptionHandler,
  exceptionToast,
  Loading,
  Navigator,
  Toast,
  utilsOss,
  ValidatePhone,
} from '@/utils';
import { FormConfig } from '@rms/components/sc-form/sc-form.vue';
import Tab from '../recycling/components/tab.vue';
import { previewFile } from '@/utils/file';
import { ApiQueryHandler } from '@shencom/api';
import {
  ApiGetMonitorOrderList,
  ApiGetProjectList,
  ApiGetProjectShow,
  ApiMonitorOrderUpdate,
  MonitorAppointBody,
  MonitorOrderListRes,
  ProjectInfoRes,
} from '@/api';
import { Dictionary } from '@rms/types';
import { StorageSetMonitorFlowRefresh } from '@/storage/monitor';
import useRoleStore from '@/hooks/useRole';

const { roleInfo } = useRoleStore();

const showTab = ref(false);

const formData = ref<MonitorAppointBody & Dictionary>({
  state: 1,
  id: '',
  service: '',
  contactMobile: '',
  contactName: '',
  reservationTime: '',
});

const projectList = ref<(MonitorOrderListRes | ProjectInfoRes)[]>([]);

watch(
  () => formData.value.id,
  () => {
    getProjectDetail();
  },
);

const isSubmit = ref(false);

const scFormRef = ref();

const formConfig = ref<FormConfig['config']>({
  formAttrs: {
    labelWidth: '220rpx',
    fontSize: '15px',
    border: true,
  },
  data: [
    {
      prop: 'id',
      label: '预约工程',
      tag: {
        tagType: 'picker',
        options: [],
        attr: {
          placeholder: '请选择预约工程',
          required: true,
        },
      },
    },
    {
      prop: 'service',
      label: '选择服务',
      tag: {
        tagType: 'picker',
        options: [{ id: '1', value: ' 专业版 500 元一月' }],
        attr: {
          placeholder: '请选择服务',
          required: true,
        },
      },
    },
    {
      prop: 'contactName',
      label: '联系人',
      tag: {
        tagType: 'text',
        attr: {
          placeholder: '请输入联系人',
          required: true,
        },
      },
    },
    {
      prop: 'contactMobile',
      label: '联系电话',
      tag: {
        tagType: 'text',
        attr: {
          type: 'number',
          placeholder: '请输入联系电话',
          required: true,
        },
      },
    },

    {
      prop: 'reservationTime',
      label: '期望上门时间',
      tag: {
        tagType: 'date-picker',
        attr: {
          placeholder: '请选择期望上门时间',
          required: true,
          type: 'datetime',
          'hide-second': true,
        },
      },
    },
  ],
  form: formData,
  rules: [
    {
      name: 'id',
      rules: [
        {
          required: true,
          errorMessage: '请选择预约工程',
        },
      ],
    },
    {
      name: 'service',
      rules: [
        {
          required: true,
          errorMessage: '请选择服务',
        },
      ],
    },
    {
      name: 'contactName',
      rules: [
        {
          required: true,
          errorMessage: '请输入联系人',
        },
      ],
    },
    {
      name: 'contactMobile',
      rules: [
        {
          required: true,
          errorMessage: '请输入手机号',
        },
        {
          validateFunction: (_, value) => ValidatePhone(value),
          errorMessage: '请输入正确格式的手机号',
        },
      ],
    },
    {
      name: 'reservationTime',
      rules: [
        {
          required: true,
          errorMessage: '请选择期望上门时间',
        },
      ],
    },
  ],
});

onLoad((options) => {
  getProjectOptions(options);
  formData.value.id = options?.id || '';
  showTab.value = !options?.id;
});

async function getProjectOptions(options: Dictionary = {}) {
  try {
    const formItem = formConfig.value.data.find((item) => item.prop === 'id');
    if (!formItem) return;

    const isLimitUser = ['施工单位负责人', '业主/建设方'].includes(roleInfo.roleName);

    formItem.tag.options = isLimitUser
      ? await getLimitUserProjectList(options.id)
      : await getMonitorProjectList(options.id);

    if (options.id) {
      getProjectDetail();
    }
  } catch (error) {
    exceptionHandler(error);
  }
}

async function getMonitorProjectList(projectId: string) {
  const query = projectId
    ? ApiQueryHandler(projectId, 'flowId', 'select')
    : ApiQueryHandler(1, 'flow', 'select');
  const res = await ApiGetMonitorOrderList({ query });

  projectList.value = res.content;

  return res.content.map((item) => ({
    id: item.flowId,
    value: item.projectName,
  }));
}

async function getLimitUserProjectList(projectId: string) {
  const query = projectId ? { flowId: projectId } : { flow: 1 };
  const res = await ApiGetProjectList({ ...query });

  projectList.value = res.content.map((v) => ({
    ...v,
    projectId: v.id,
  }));

  return res.content.map((item) => ({
    id: item.flowId,
    value: item.name,
  }));
}

async function getProjectDetail() {
  const { projectId } =
    (projectList.value.find((item) => item.flowId === formData.value.id) as Dictionary) || {};
  if (!projectId) {
    formData.value.contactName = '';
    formData.value.contactMobile = '';
    return;
  }

  const data = await ApiGetProjectShow(projectId);
  formData.value.contactName = data?.contractorCharger || '';
  formData.value.contactMobile = data?.contractorChargerMobile || '';
}

async function onSubmit() {
  if (isSubmit.value) return;

  const flag = await scFormRef.value.validate();
  if (!flag) return;

  isSubmit.value = true;

  try {
    // 显示加载提示
    Loading.show('提交中');

    await ApiMonitorOrderUpdate(formData.value);

    // 隐藏加载提示
    Loading.hide();

    // 设置刷新标记，通知相关页面刷新数据
    StorageSetMonitorFlowRefresh();

    // 显示成功提示
    Toast.success('预约成功', {
      complete: () => {
        Navigator.replace('/pages/recycling/create-result', {
          time: formData.value.reservationTime,
          type: '安装',
        });
      },
    });
  } catch (error) {
    Loading.hide();
    exceptionToast(error, '提交失败，请重试');
    isSubmit.value = false;
  }
}

function onGotoHistory() {
  Navigator.push('/pages/recycling/list', { type: 0, tab: 0 });
}

function onPreview() {
  previewFile(`${utilsOss.ossPath}/files/小散工程监管解决方案（含报价）.pdf`);
}
</script>

<template>
  <div class="w-full min-h-screen p-3 bg-base box-border relative pb-16">
    <tab v-if="showTab" active="安装服务" />

    <div class="flex justify-end">
      <div class="text-blue underline" @click="onPreview">安装服务介绍</div>
    </div>

    <div class="mt-3 bg-white px-3 pt-4 rounded-md">
      <wd-steps align-center>
        <wd-step title="安装预约"></wd-step>
        <wd-step title="现场勘查"></wd-step>
        <wd-step title="上门安装"></wd-step>
      </wd-steps>

      <sc-form ref="scFormRef" :config="formConfig" class="mt-3"></sc-form>
    </div>

    <sc-button-fixed class="pt-5 box-border">
      <div class="w-full flex items-center justify-between">
        <!-- <wd-button custom-class="!mr-6" type="text" icon="list" @click="onGotoHistory">
          进度查询
        </wd-button> -->
        <wd-button
          :disabled="isSubmit"
          custom-class="!rounded-md !w-full"
          size="large"
          type="primary"
          @click="onSubmit">
          提交
        </wd-button>
      </div>
    </sc-button-fixed>
  </div>
</template>
