<script setup lang="ts">
import {
  ApiGetProjectHistoryMemo,
  ApiGetProjectList,
  ApiGetRoleRegion,
  ApiProjectMemoCreate,
  ApiUpdateProjectStatus,
  ProjectInfoRes,
  ProjectMemoRes,
} from '@/api';
import useRoleStore from '@/hooks/useRole';
import { monitorFlagOptions, projectStatusOptions } from '@/state';
import { StorageGetMonitorFlowRefresh, StorageRemoveMonitorFlowRefresh } from '@/storage/monitor';
import {
  Dialog,
  exceptionToast,
  FormatDateTime,
  IsMiniProgram,
  IsWeixinH5,
  Navigator,
  Toast,
  utilsOss,
} from '@/utils';
import { useList } from '@rms/components/sc-list/sc-list';
import { ServiceGisByIds } from '@rms/service/src';
import { watchThrottled } from '@vueuse/core';

// type SearchParams = Parameters<typeof ApiGetProjectList>[0];
//   预约安装  flowList = [1]
// 预约回收  flowList = [6]
// 现场勘察  flowList = [2]
// 上门安装  flowList = [3]
// 上门回收  flowList = [7]
// 已完成安装  flowList = [4,5,6,7,8,9]
// 已完成回收  flowList = [8,9]
// 过期工程    monitorFlag  = 0 && status = 2
const statusList = ref([
  {
    label: '全部',
    value: 0,
    body: {
      flowList: [],
    },
  },
  {
    label: '预约安装',
    value: 1,
    body: {
      flowList: [1],
    },
  },
  {
    label: '预约回收',
    value: 2,
    body: {
      flowList: [6],
    },
  },
  {
    label: '现场勘查',
    value: 3,
    body: {
      flowList: [2],
    },
  },
  {
    label: '上门安装',
    value: 4,
    body: {
      flowList: [3],
    },
  },
  {
    label: '上门回收',
    value: 5,
    body: {
      flowList: [7],
    },
  },
  {
    label: '已完成安装',
    value: 6,
    body: {
      flowList: [4, 5],
    },
  },
  {
    label: '已完成回收',
    value: 7,
    body: {
      flowList: [8, 9],
      monitorFlag: 2,
    },
  },
  {
    label: '过期工程',
    value: 8,
    body: {
      monitorFlag: 0,
      status: 2,
    },
  },
]);

const imgs = {
  icon_xc: `${utilsOss.imgPath}/engineering/icon_xc.png`,
  icon_wg: `${utilsOss.imgPath}/engineering/icon_wg.png`,
  icon_zg: `${utilsOss.imgPath}/engineering/icon_zg.png`,
  icon_jk: `${utilsOss.imgPath}/engineering/icon_jk.png`,
  icon_zl: `${utilsOss.imgPath}/engineering/icon_zl.png`,
};

const search = reactive({
  status: 0,
  size: 10,
});

const { list, listProps, getData } = useList<ProjectInfoRes[]>({
  request: ApiGetProjectList,
  params: {},
  immediate: false,
});

watchThrottled(
  () => search,
  () => {
    refresh();
  },
  { throttle: 200, immediate: true, deep: true },
);

function toMonitor(item: ProjectInfoRes) {
  Navigator.push('/pages/monitor/index', {
    projectId: item.id,
  });
}

function refresh() {
  const body = statusList.value.find((item) => item.value === search.status)?.body || {};
  getData(true, body);
}

onMounted(() => {
  refresh();
});

onShow(() => {
  const needRefresh = StorageGetMonitorFlowRefresh();
  if (needRefresh) {
    refresh();
    StorageRemoveMonitorFlowRefresh();
  }
});

const roleRegion = ref();

const isAdmin = computed(() => {
  return ['2', '3', '4', '5', '6'].includes(roleRegion.value?.typeId);
});

const roleStore = useRoleStore();

roleStore.initOrganization(true);

// 业主和施工负责人角色
const isAdmin2 = computed(() => {
  return ['Construction:party', 'Construction:unitleader'].includes(roleStore.roleInfo.roleId);
});

// 安装人员
const isInstall = computed(() => {
  return ['Installation:Manager'].includes(roleStore.roleInfo.roleId);
});

async function getRegionList() {
  roleRegion.value = await ApiGetRoleRegion();
}

onMounted(() => {
  getRegionList();
});

function toDetail(item: ProjectInfoRes) {
  Navigator.push('/pages/engineering/detail', {
    id: item.id,
  });
}

function toInstall(item: ProjectInfoRes) {
  Navigator.push('/pages/engineering/appoint', {
    id: item.flowId,
    orderId: item.orderId,
  });
}

function toSurvey(item: ProjectInfoRes) {
  Navigator.push('/pages/engineering/survey', {
    id: item.flowId,
    orderId: item.orderId,
  });
}

function toInstallation(item: ProjectInfoRes) {
  Navigator.push('/pages/engineering/installation', {
    id: item.flowId,
    orderId: item.orderId,
    projectName: item.name,
    projectAddress: item.address,
  });
}

function toRecycleCreate(item: ProjectInfoRes) {
  Navigator.push('/pages/recycling/create', {
    id: item.flowId,
    orderId: item.orderId,
    projectName: item.name,
    projectAddress: item.address,
  });
}

function toRecycle(item: ProjectInfoRes) {
  Navigator.push('/pages/recycling/recycle', {
    id: item.flowId,
    orderId: item.orderId,
    projectName: item.name,
    projectAddress: item.address,
  });
}

async function toLocation(item: ProjectInfoRes) {
  const gisInfo = await ServiceGisByIds(item.poiId);
  const options = {
    longitude: +gisInfo[0].lng,
    latitude: +gisInfo[0].lat,
    name: item.name,
    address: gisInfo[0].addr,
  };

  if (IsWeixinH5 || IsMiniProgram) {
    uni.$wx.openLocation(options);
  } else {
    uni.openLocation(options);
  }
}

async function toFinish(item: ProjectInfoRes) {
  const flag = await Dialog('未到工程结束时间，是否提前施工完成?');
  if (!flag) return;

  try {
    await ApiUpdateProjectStatus({ id: item.id, status: 2 });
    Toast.success('提交成功');
    refresh();
  } catch (error) {
    exceptionToast(error, '');
  }
}

async function toStart(item: ProjectInfoRes) {
  const flag = await Dialog('未到工程开始时间，是否提前开始施工?');
  if (!flag) return;

  try {
    await ApiUpdateProjectStatus({ id: item.id, status: 1 });
    Toast.success('提交成功');
    refresh();
  } catch (error) {
    exceptionToast(error, '');
  }
}

const memoInfo = reactive({
  show: false,
  content: '',
  projectId: '',
  history: [] as ProjectMemoRes[],
});

async function getHistoryMemo(item: ProjectInfoRes) {
  const data = await ApiGetProjectHistoryMemo({ projectId: item.id, size: 200 });
  memoInfo.history = data.content;
}

async function addMemo(item: ProjectInfoRes) {
  memoInfo.projectId = item.id;
  memoInfo.content = '';
  memoInfo.history = [];
  await getHistoryMemo(item);
  memoInfo.show = true;
}

let isSubmit = false;

function isEnd(item: ProjectInfoRes) {
  return item.monitorFlag === 0 && item.status === 2;
}

function isShowTop(item: ProjectInfoRes) {
  if (isEnd(item)) return false;

  return (
    ([1, 2, 3, 6, 7].includes(item.flow) && !isAdmin.value) ||
    (item.status === 1 && item.monitorFlag === 1)
  );
}

// const isShowBtn = computed(() => {
//   return search.status === 0 || search.status === 1 || search.status === 2;
// });

async function onSubmit() {
  if (isSubmit) return;

  if (!memoInfo.content) {
    Toast.error('请输入备注');
    return;
  }

  isSubmit = true;

  try {
    await ApiProjectMemoCreate({ projectId: memoInfo.projectId, content: memoInfo.content });
    Toast.success('提交成功');
    memoInfo.show = false;
    refresh();
  } catch (error) {
    exceptionToast(error, '');
  } finally {
    setTimeout(() => {
      isSubmit = false;
    }, 1000);
  }
}

function getContent(item: ProjectInfoRes) {
  const target = item.memoRespDTOList?.shift();
  return target ? `${target.content}(${FormatDateTime(target.createdAt)})` : '';
}

onPullDownRefresh(async () => {
  search.status = 0;

  await refresh();
  uni.stopPullDownRefresh();
});
onReachBottom(() => null);
</script>

<template>
  <div class="list">
    <div class="w-full bg-[#F6F8FF] py-3">
      <wd-radio-group
        v-model="search.status"
        class="!bg-[#F6F8FF]"
        custom-class="flex justify-around flex-wrap"
        shape="button">
        <wd-radio v-for="item in statusList" :key="item.value" :value="item.value">
          {{ item.label }}
        </wd-radio>
      </wd-radio-group>
    </div>

    <sc-list v-bind="listProps" class="!p-0 !h-auto !min-h-[calc(100vh-38px)]">
      <div class="w-full p-3">
        <div
          v-for="item in list"
          :key="item.id"
          class="bg-[#F6F8FF] rounded-xl p-3 mb-3 text-12 relative"
          :class="[isShowTop(item) ? 'pt-[70px]' : 'pt-8']"
          @click="toDetail(item)">
          <div
            v-if="!isEnd(item) && item.flow === 1 && !isAdmin"
            class="absolute top-0 left-0 px-3 py-2 rounded-t-xl bg-sky1 w-full text-white text-14 font-bold mb-8">
            <span>该工程未接入监管，请先</span>
            <span class="text-red ml-2" @click.stop="toInstall(item)">预约安装>></span>
          </div>

          <div
            v-if="!isEnd(item) && item.flow === 2 && !isAdmin"
            class="absolute top-0 left-0 px-3 py-2 rounded-t-xl bg-sky1 w-full text-white text-14 font-bold mb-8">
            <span>
              期望上门时间：{{ FormatDateTime(item.reservationTime, 'YYYY-MM-DD HH:mm') }}，
            </span>
            <span class="text-red ml-2" @click.stop="toSurvey(item)">现场勘察>></span>
          </div>

          <div
            v-if="!isEnd(item) && item.flow === 3 && !isAdmin"
            class="absolute top-0 left-0 px-3 py-2 rounded-t-xl bg-sky1 w-full text-white text-14 font-bold mb-8">
            <span>
              预约安装时间：{{ FormatDateTime(item.reservationTime, 'YYYY-MM-DD HH:mm') }}，
            </span>
            <span class="text-red ml-2" @click.stop="toInstallation(item)">上门安装>></span>
          </div>

          <div
            v-if="!isEnd(item) && item.flow === 7 && !isAdmin"
            class="absolute top-0 left-0 px-3 py-2 rounded-t-xl bg-sky1 w-full text-white text-14 font-bold mb-8">
            <span>
              预约回收时间：{{ FormatDateTime(item.reservationTime, 'YYYY-MM-DD HH:mm') }}，
            </span>
            <span class="text-red ml-2" @click.stop="toRecycle(item)">上门回收>></span>
          </div>

          <div
            v-else-if="!isEnd(item) && item.status === 1 && item.monitorFlag === 1"
            class="absolute top-0 left-0 px-3 py-2 rounded-t-xl bg-sky1 w-full text-white text-14 font-bold mb-8"
            @click.stop="toMonitor(item)">
            <span>该工程已接入监管，我要</span>
            <span class="text-red ml-2">查看监控>></span>
          </div>

          <div
            v-else-if="!isEnd(item) && item.flow === 6 && !isAdmin"
            class="absolute top-0 left-0 px-3 py-2 rounded-t-xl bg-sky1 w-full text-white text-14 font-bold mb-8">
            <span>该工程已结束监管，请先</span>
            <span class="text-red ml-2" @click.stop="toRecycleCreate(item)">预约回收>></span>
          </div>

          <div
            :class="[
              isShowTop(item) ? 'top-11  rounded-l-xl' : 'top-0 rounded-tr-xl rounded-bl-xl',
            ]"
            class="absolute w-[140px] text-center ellipsis-1 bg2 p-1 text-13 right-0 z-10">
            {{ item.projectNumber || 'sadasd124213123w' }}
          </div>

          <div class="flex items-center gap-2 mb-2">
            <div class="max-w-[200px] text-16 font-bold">{{ item.name }}</div>
            <wd-tag
              :type="projectStatusOptions.find((i) => i.value === item.status)?.type as any"
              custom-class="!text-12">
              {{ monitorFlagOptions.find((i) => i.value === item.monitorFlag)?.label }}-{{
                projectStatusOptions.find((i) => i.value === item.status)?.label || '已结束'
              }}
            </wd-tag>
          </div>
          <div class="flex items-center gap-2 mb-1">
            <div>小散工程地址: {{ item.address }}</div>
            <wd-icon
              color="#1338D5"
              name="location"
              size="18px"
              @click.stop="toLocation(item)"></wd-icon>
          </div>

          <div>备注: {{ getContent(item) }}</div>

          <div v-if="!isEnd(item) && item.status === 1 && isAdmin2" class="w-full flex justify-end">
            <wd-button type="primary" size="medium" @click.stop="toFinish(item)">
              施工完成
            </wd-button>
          </div>

          <div v-if="item.status === 0 && isAdmin2" class="w-full flex justify-end">
            <wd-button type="primary" size="medium" @click.stop="toStart(item)">开始施工</wd-button>
          </div>

          <div v-if="isInstall" class="w-full flex justify-end mt-2">
            <wd-button type="primary" size="medium" @click.stop="addMemo(item)">添加备注</wd-button>
          </div>
          <!-- end -->
        </div>
      </div>
    </sc-list>

    <wd-popup v-model="memoInfo.show" custom-class="rounded-lg overflow-hidden">
      <div class="w-[90vw] h-[70vh] p-3 flex flex-col justify-between">
        <div class="mb-3">
          <div class="mb-2">添加备注</div>
          <sc-textarea v-model="memoInfo.content" input-border placeholder="请输入备注" />
        </div>
        <div class="mb-2">历史记录</div>
        <div class="flex-1 overflow-y-scroll">
          <div class="text-11 pb-2">
            <div v-for="item in memoInfo.history" :key="item.id" class="flex border-b py-2">
              <div>{{ item.createdUserName }}</div>
              <div class="flex-1 px-2">{{ item.content }}</div>
              <div>{{ FormatDateTime(item.createdAt) }}</div>
            </div>
          </div>
        </div>

        <wd-button type="primary" size="medium" @click.stop="onSubmit">提交</wd-button>
      </div>
    </wd-popup>
  </div>
</template>

<style lang="scss" scoped>
:deep(.wd-radio__label) {
  display: flex;
  align-items: center;
}
:deep(.wd-radio) {
  margin: 0 !important;
}

.bg-sky1 {
  // 天空蓝渐变色
  background-image: linear-gradient(120deg, #a1c4fd 0%, #c2e9fb 100%);
}

.bg2 {
  background-image: linear-gradient(to top, #cfd9df 0%, #e2ebf0 100%);
}

.border-b {
  border-bottom: 1rpx solid #e2ebf0;
}

.list {
  :deep(.wd-radio) {
    --wot-radio-button-fs: 11px;
    margin-bottom: 10px !important;
  }
}
</style>
