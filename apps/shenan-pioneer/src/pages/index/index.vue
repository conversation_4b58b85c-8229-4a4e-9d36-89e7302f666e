<script setup lang="ts">
import { exception<PERSON><PERSON><PERSON>, <PERSON><PERSON>, Navigator, UserInfo, utilsOss } from '@/utils';
import { ApiResourceMenuJava } from '@rms/api/src';
import ScCategory from '@/components/sc-category/sc-category.vue';
import ScEngineeringList from './components/sc-engineering-list.vue';
import useRoleStore from '@/hooks/useRole';
import ScMonitorEventList from './components/monitor-event-list.vue';
import { StorageGetMonitorFlowRefresh, StorageRemoveMonitorFlowRefresh } from '@/storage/monitor';

const imgs = {
  img_top: `${utilsOss.imgPath}/home/<USER>
  icon_1: `${utilsOss.imgPath}/home/<USER>
  icon_2: `${utilsOss.imgPath}/home/<USER>
  default_avatar: `${utilsOss.imgPath}/default_avatar.svg`,
  tab1_1: `${utilsOss.imgPath}/tabs/icon_df1_1.png`,
  tab1_2: `${utilsOss.imgPath}/tabs/icon_df1_2.png`,
  tab2_1: `${utilsOss.imgPath}/tabs/icon_df2_1.png`,
  tab2_2: `${utilsOss.imgPath}/tabs/icon_df2_2.png`,
  tab3_1: `${utilsOss.imgPath}/tabs/icon_df3_1.png`,
  tab3_2: `${utilsOss.imgPath}/tabs/icon_df3_2.png`,
  tab4_1: `${utilsOss.imgPath}/tabs/icon_df4_1.png`,
  tab4_2: `${utilsOss.imgPath}/tabs/icon_df4_2.png`,
};

const menuGroups = {
  巡查员: 'shenan-pioneer-xinchayuan',
  市民: 'shenan-pioneer-shimin',
  施工单位负责人: 'shenan-pioneer-shigong',
  菜单2: 'shenan-pioneer-home2',
};

const scMenusRef1 = ref();
const scMenusRef2 = ref();
const scEngineeringListRef = ref();
const scMonitorEventListRef = ref();

const userInfo = ref<SC.User.Info>();

const isLogin = ref(false);

const btnShow = ref(false);

async function getBtnShow() {
  try {
    const { data } = await ApiResourceMenuJava({
      group: ['userinfo-login'],
      size: 1,
    });

    btnShow.value = !!data.length;
  } catch (error) {
    exceptionHandler(error);
  }
}

function toLogin() {
  if (!btnShow.value) return;
  uni.scLogin({
    force: true,
  });
}

function toUser() {
  Navigator.push('/pages/user/index');
}

const isShowMenu1 = computed(() => !!scMenusRef1.value?.menus.length);

async function initPage(refresh = false) {
  getBtnShow();
  await scMenusRef1.value?.init(refresh);
  await scMenusRef2.value?.init(refresh);
  scEngineeringListRef.value?.refreshData();
  scMonitorEventListRef.value?.refreshData();
}

function toMenuPage(e: any) {
  Navigator.toMenuPage(e);
}

const roleStore = useRoleStore();

const roleName = computed(() => roleStore.roleInfo.roleName);

const isAdmin = computed(() => {
  return [
    'Senior:citylevel',
    'Senior:districtlevel',
    'Senior:street',
    'Senior:community',
    'Ordinary:citylevel',
    'Ordinary:districtlevel',
    'Ordinary:street',
    'Ordinary:community',
    'Senior:patrol',
  ].includes(roleStore.roleInfo.roleId);
});

const selectPickerRef = ref();
function openPicker() {
  selectPickerRef.value?.open();
}

async function onConfirmOrganization(e: any) {
  if (e.value === roleStore.roleInfo.organizationId) return;
  Loading.show();
  await roleStore.switchOrganization(e.value);
  await initPage(true);
  Loading.hide();
}

function init() {
  initPage(true);
}

onPullDownRefresh(async () => {
  await initPage(true);
  uni.stopPullDownRefresh();
});

onMounted(() => {
  if (!UserInfo.isLogin()) {
    initPage(true);
  }
});

// 建设方（业主） ；施工负责人
const isShowMonitorEvent = computed(() => {
  return ['Construction:party', 'Construction:unitleader'].includes(roleStore.roleInfo.roleId);
});

onShow(() => {
  // getBtnShow();
  userInfo.value = UserInfo.getUserInfo()!;
  isLogin.value = UserInfo.isLogin();
  console.log(
    '%c [isLogin.value]-109',
    'font-size:13px; background:#336699; color:#fff;',
    isLogin.value,
  );

  // 检查是否需要刷新工程列表数据
  const needRefresh = StorageGetMonitorFlowRefresh();
  if (needRefresh && scEngineeringListRef.value) {
    scEngineeringListRef.value.refreshData();
    StorageRemoveMonitorFlowRefresh();
  }
});
</script>

<template>
  <view class="home">
    <image :src="imgs.img_top" mode="widthFix" class="w-full" />
    <view class="w-full bg-white rounded-lg p-3 relative z-10 top-[-20px]">
      <!-- 用户信息 -->
      <div
        v-if="isLogin && userInfo"
        class="w-full h-full bg-[#f6f8ff] rounded-lg flex items-center p-3 relative z-10 gap-3">
        <wd-img
          v-if="userInfo?.avatar"
          :src="userInfo?.avatar"
          :width="40"
          :height="40"
          class="!rounded-full overflow-hidden"
          custom-class="!rounded-full overflow-hidden"
          mode="widthFix">
          <template #error>
            <img
              :src="imgs.default_avatar"
              class="w-[40px] h-[40px] rounded-full"
              mode="widthFix" />
          </template>
        </wd-img>
        <img
          v-else
          :src="imgs.default_avatar"
          class="w-[40px] h-[40px] rounded-full"
          mode="widthFix" />
        <div class="flex-1 flex">
          <div class="flex flex-col text-16 gap-2">
            <div class="flex items-center gap-3">
              <div class="text-16">
                {{ userInfo?.realname || userInfo?.nickname || userInfo?.username || '用户' }}
              </div>
              <div
                class="flex items-center gap-1 rounded py-0.5 px-1"
                :class="[
                  roleName === '市民'
                    ? 'bg-orange-200 text-orange-400'
                    : 'bg-blue-200 text-blue-400',
                ]">
                <image :src="roleName === '市民' ? imgs.icon_2 : imgs.icon_1" class="w-3 h-3" />
                <div class="text-11">{{ roleName }}</div>
              </div>
            </div>

            <div class="text-13 flex items-center gap-2">
              <div>{{ userInfo?.phone }}</div>
              <div
                v-if="roleStore.roleInfo.organizationName"
                class="text-15 font-bold flex items-center gap-1"
                @click="openPicker">
                <span>{{ roleStore.roleInfo.organizationName }}</span>
                <wd-icon name="arrow-down" size="13" />
              </div>
            </div>
          </div>
        </div>
        <!-- <wd-button
          icon="edit-outline"
          type="primary"
          custom-class="editBtn"
          size="small"
          @click="toUser">
          编辑
        </wd-button> -->
      </div>

      <div v-else class="w-full h-full flex items-center p-3 relative z-10 gap-3" @click="toLogin">
        <img :src="imgs.default_avatar" class="w-[40px] h-[40px] rounded-full" mode="widthFix" />
        <div class="flex flex-col text-white text-16">
          <wd-button v-if="btnShow" type="primary" custom-class="editBtn" size="small">
            登录
          </wd-button>
        </div>
      </div>

      <!-- 菜单1 -->
      <view v-show="isShowMenu1" class="py-5">
        <sc-menus
          ref="scMenusRef1"
          icon-class="icon1"
          :group="roleStore.roleInfo.roleId || menuGroups.市民"
          @click="toMenuPage"></sc-menus>
      </view>

      <!-- 巡查员菜单2 -->
      <view v-show="isAdmin" class="">
        <sc-menus
          ref="scMenusRef2"
          class="!my-0 mx-0"
          :card-height="77"
          type="card"
          :row-number="2"
          box-class="gap-3 !pt-3"
          item-class="!px-0"
          :group="menuGroups.菜单2"
          @click="toMenuPage"></sc-menus>
      </view>

      <!-- 资讯 -->
      <view v-if="roleName === '市民' || !isLogin" class="mt-5">
        <sc-category type="shimin"></sc-category>
      </view>

      <!-- 工程列表 -->
      <view v-if="isLogin && roleStore.roleInfo.roleId === 'Installation:Manager'" class="mt-5">
        <sc-engineering-list ref="scEngineeringListRef" />
      </view>

      <view v-if="isLogin && isShowMonitorEvent" class="mt-5">
        <sc-monitor-event-list ref="scMonitorEventListRef" />
      </view>
    </view>
  </view>

  <wd-select-picker
    v-if="roleStore.roleInfo.organizationId"
    ref="selectPickerRef"
    :model-value="roleStore.roleInfo.organizationId"
    label=""
    :z-index="9999999999"
    type="radio"
    value-key="id"
    label-key="name"
    title="请选择切换的组织"
    use-default-slot
    safe-area-inset-bottom
    confirm-button-text="切换组织"
    :columns="roleStore.organizationList"
    @confirm="onConfirmOrganization">
    <div></div>
  </wd-select-picker>

  <Tabbar v-if="isLogin" @init="init" />
</template>

<style lang="scss" scoped>
:deep(.icon1) {
  width: 45px !important;
  height: 45px !important;
}
:deep(.wd-drop-menu__list) {
  background-color: transparent;
}
:deep(.wd-drop-menu__item-title) {
  // opacity: 0;
}
</style>
