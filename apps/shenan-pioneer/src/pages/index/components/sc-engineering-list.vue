<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';
import { exceptionToast, FormatDateTime, IsMiniProgram, IsWeixinH5 } from '@/utils';
import { ApiGetMonitorOrderList, MonitorOrderListQuery, MonitorOrderListRes } from '@/api';
import { ServiceGisByIds } from '@rms/service/src';
import { ApiQueryHandler } from '@shencom/api';
import WdTabs from 'wot-design-uni/components/wd-tabs/wd-tabs.vue';
import { toPageFormat } from '@/utils/navigation';

// 当前选中的标签页索引
const currentTab = ref(0);

// 加载状态
const loading = ref(false);

// 标签页配置
const tabs = computed<
  {
    label: string;
    title: string;
    value: number;
    params: MonitorOrderListQuery;
    list: MonitorOrderListRes[];
  }[]
>(() => [
  {
    label: '待预约',
    title: '待预约',
    value: 0,
    params: { query: ApiQueryHandler('1,6', 'flow', 'select') },
    list: [],
  },
  {
    label: '待勘查',
    title: '待勘查',
    value: 1,
    params: { installType: 1 },
    list: [],
  },
  {
    label: '待安装',
    title: '待安装',
    value: 2,
    params: { installType: 2 },
    list: [],
  },
  {
    label: '待回收',
    title: '待回收',
    value: 3,
    params: { recycleType: 1 },
    list: [],
  },
]);

const tabsRef = ref<InstanceType<typeof WdTabs>>();

watch(currentTab, () => {
  fetchEngineeringData();
});

// 状态配置
const statusConfig = {
  1: {
    text: '待预约',
    icon: 'clock',
    buttonText: '预约安装',
  },
  2: {
    text: '待勘查',
    icon: 'search',
    buttonText: '现场勘查',
  },
  3: {
    text: '待安装',
    icon: 'setting',
    buttonText: '上门安装',
  },
  6: {
    text: '待预约',
    icon: 'clock',
    buttonText: '预约回收',
  },
  7: {
    text: '待回收',
    icon: 'refresh',
    buttonText: '上门回收',
  },
};

function getTips(item: MonitorOrderListRes) {
  if (item.flow === 1) {
    return '该工程未接入监管';
  }
  if (item.flow === 6) {
    return '该工程已结束监管';
  }
  const reservationTime = (item as any).reservationTime;
  const time = (reservationTime && FormatDateTime(reservationTime, 'YYYY-MM-DD HH:mm')) || '';
  if (item.flow === 2) {
    return `期望上门时间 ${time}`;
  }
  if (item.flow === 3) {
    return `预约安装时间 ${time}`;
  }
  return `预约回收时间 ${time}`;
}

// 获取状态样式
const getStatusConfig = (type: number) => {
  return statusConfig[type as keyof typeof statusConfig] || statusConfig[1];
};

// 处理项目点击
const handleItemClick = (item: MonitorOrderListRes) => {
  toPageFormat('/pages/engineering/detail', {
    id: item.projectId,
    orderId: item.id,
  });
};

// 处理定位按钮点击
async function handleLocationClick(item: MonitorOrderListRes) {
  const gisInfo = await ServiceGisByIds(item.projectPoiId);
  if (!gisInfo) {
    uni.showToast({
      title: '暂无位置信息',
      icon: 'none',
    });
    return;
  }

  const options = {
    longitude: +gisInfo[0].lng,
    latitude: +gisInfo[0].lat,
    name: item.projectName,
    address: gisInfo[0].addr,
  };

  if (IsWeixinH5 || IsMiniProgram) {
    uni.$wx.openLocation(options);
  } else {
    uni.openLocation(options);
  }
}

// 模拟数据获取
const fetchEngineeringData = async (isRefresh = false) => {
  const curTab = tabs.value[currentTab.value];
  if (!isRefresh && curTab.list.length > 0) {
    return;
  }

  loading.value = true;
  try {
    const response = await ApiGetMonitorOrderList({
      page: 0,
      size: 3,
      ...curTab.params,
    });
    curTab.list = response.content;
    curTab.title = curTab.list.length > 0 ? `${curTab.label}(${curTab.list.length})` : curTab.label;
    tabsRef.value?.setActive(curTab.value, true, true);
  } catch (error) {
    exceptionToast(error, '获取工程列表失败');
  } finally {
    loading.value = false;
  }
};

function toMore(title: string) {
  if (title.includes('待预约')) {
    toPageFormat('/pages/recycling/list', { type: 0, tab: 0 });
  } else if (title.includes('待勘查')) {
    toPageFormat('/pages/recycling/list', { type: 0, tab: 1 });
  } else if (title.includes('待安装')) {
    toPageFormat('/pages/recycling/list', { type: 0, tab: 2 });
  } else if (title.includes('待回收')) {
    toPageFormat('/pages/recycling/list', { type: 1, tab: 1 });
  }
}

// 处理操作按钮点击
const handleActionClick = (item: MonitorOrderListRes, tab: number) => {
  const urlMap = {
    1: '/pages/engineering/appoint',
    2: '/pages/engineering/survey',
    3: '/pages/engineering/installation',
    6: '/pages/recycling/create',
    7: '/pages/recycling/recycle',
  };
  const url = urlMap[tab as keyof typeof urlMap];
  if (url) {
    toPageFormat(url, { id: item.flowId, orderId: item.id });
  }
};

// 组件挂载时获取数据
onMounted(() => {
  fetchEngineeringData();
});

// 添加刷新方法，清空数据并重新获取
const refreshData = () => {
  // 清空所有标签页的数据
  tabs.value.forEach((tab) => {
    tab.list = [];
    tab.title = tab.label;
  });
  // 重新获取当前标签页数据
  fetchEngineeringData(true);
};

// 暴露刷新方法给父组件
defineExpose({
  refreshData,
});
</script>

<template>
  <view>
    <!-- 标题栏 -->
    <view class="flex items-center justify-between mb-4">
      <view class="flex items-center gap-2">
        <wd-icon name="location" size="18px" color="#3b82f6" />
        <text class="text-lg font-bold text-gray-800">工程进度</text>
      </view>
    </view>

    <!-- WOT 标签页 -->
    <wd-tabs ref="tabsRef" v-model="currentTab" auto-line-width animated>
      <wd-tab v-for="(tab, index) in tabs" :key="index" :title="`${tab.title}`">
        <!-- 加载状态 -->
        <view v-if="loading" class="flex flex-col items-center justify-center py-4">
          <wd-loading type="ring" />
          <text class="text-gray-500 mt-2">加载中...</text>
        </view>

        <!-- 空状态 -->
        <view v-else-if="tab.list.length === 0" class="py-8">
          <sc-empty :text="`暂无${tab.label || ''}工程`" />
        </view>

        <!-- 工程列表 -->
        <view v-else class="mt-2 space-y-4">
          <view
            v-for="item in tab.list"
            :key="item.id"
            class="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden"
            @tap="handleItemClick(item)">
            <!-- 彩色顶部装饰条 -->
            <view
              class="px-3 py-2 bg-gradient-to-r from-blue-500 to-sky-200 text-white flex items-center justify-between">
              <text class="font-medium leading-relaxed">{{ getTips(item) }}</text>

              <wd-button
                type="primary"
                size="small"
                class="rounded-full shadow-sm hover:shadow-md transition-all duration-200 active:scale-95"
                @tap.stop="handleActionClick(item, item.flow)">
                <wd-icon :name="getStatusConfig(item.flow)?.icon" size="14px" class="mr-1" />
                {{ getStatusConfig(item.flow)?.buttonText }}
              </wd-button>
            </view>

            <!-- 卡片头部 -->
            <view class="flex justify-end">
              <view class="bg-gray-50 px-2 py-1 rounded-md border border-gray-200">
                <text class="text-xs text-gray-600 font-mono">{{ item.projectNumber }}</text>
              </view>
            </view>

            <!-- 项目信息 -->
            <view class="px-3 py-2">
              <view class="flex items-center">
                <!-- <wd-icon name="home" size="16px" color="#3b82f6" class="mr-2" /> -->
                <text class="text-sm font-semibold text-gray-800">{{ item.projectName }}</text>
              </view>

              <view class="flex items-start">
                <text class="text-sm text-gray-600 flex-1">{{ item.projectAddress }}</text>
                <wd-button
                  type="text"
                  size="small"
                  custom-class="pl-1 -mt-1"
                  icon="location"
                  @tap.stop="handleLocationClick(item)"></wd-button>
              </view>
            </view>
          </view>

          <view class="mt-2 flex justify-center">
            <wd-button type="text" underline @click="toMore(tab.label)">
              <view class="flex items-center">
                <text>查看更多</text>
                <i class="i-iconoir-fast-arrow-right text-base ml-1"></i>
              </view>
            </wd-button>
          </view>
        </view>
      </wd-tab>
    </wd-tabs>
  </view>
</template>
