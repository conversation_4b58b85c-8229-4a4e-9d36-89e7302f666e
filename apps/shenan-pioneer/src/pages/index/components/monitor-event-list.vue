<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';
import { exceptionToast, FormatDateTime, IsMiniProgram, IsWeixinH5, Navigator } from '@/utils';
import { ApiGetWarningList, ApiGetWarningListRes } from '@/api';
import WdTabs from 'wot-design-uni/components/wd-tabs/wd-tabs.vue';
import Picture from './picture.vue';
import { toPageFormat } from '@/utils/navigation';

// 加载状态
const loading = ref(false);

const list = ref<ApiGetWarningListRes[]>([]);

// 模拟数据获取
const fetchEngineeringData = async (isRefresh = false) => {
  if (!isRefresh && list.value.length > 0) {
    return;
  }

  loading.value = true;
  try {
    const response = await ApiGetWarningList({
      page: 0,
      size: 3,
    });
    list.value = response.content;
  } catch (error) {
    exceptionToast(error, '获取工程列表失败');
  } finally {
    loading.value = false;
  }
};

// 组件挂载时获取数据
onMounted(() => {
  fetchEngineeringData();
});

// 添加刷新方法，清空数据并重新获取
const refreshData = () => {
  // 清空所有标签页的数据
  list.value = [];
  // 重新获取当前标签页数据
  fetchEngineeringData(true);
};

function toDetail(item: ApiGetWarningListRes) {
  toPageFormat('/pages/monitor/warning/detail', {
    id: item.id,
  });
}

function toMore() {
  toPageFormat('/pages/monitor/warning/list', {});
}

// 暴露刷新方法给父组件
defineExpose({
  refreshData,
});
</script>

<template>
  <view class="pt-3">
    <!-- 标题栏 -->
    <view class="flex items-center justify-between mb-4">
      <view class="flex items-center gap-2">
        <wd-icon name="location" size="18px" color="#3b82f6" />
        <text class="text-lg font-bold text-gray-800">实时告警</text>
      </view>
    </view>

    <view v-if="loading" class="flex flex-col items-center justify-center py-4">
      <wd-loading type="ring" />
      <text class="text-gray-500 mt-2">加载中...</text>
    </view>

    <!-- 空状态 -->
    <view v-else-if="list.length === 0" class="py-8">
      <sc-empty text="暂无实时告警" />
    </view>

    <!-- 工程列表 -->
    <view v-else class="mt-2 space-y-4">
      <view
        v-for="item in list"
        :key="item.id"
        class="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden"
        @tap="toDetail(item)">
        <!-- 彩色顶部装饰条 -->
        <view
          class="px-3 py-2 bg-gradient-to-r from-blue-500 to-sky-200 text-white flex justify-between">
          <text class="font-medium leading-relaxed">{{ item.typeName }}</text>

          <wd-button
            type="primary"
            size="small"
            class="rounded-full shadow-sm hover:shadow-md transition-all duration-200 active:scale-95"
            @tap.stop="toDetail(item)">
            <wd-icon name="warning" size="14px" class="mr-1" />
            查看详情
          </wd-button>
        </view>

        <!-- 项目信息 -->
        <view class="px-3 py-2 flex gap-2">
          <div class="w-[80px] h-[80px] pt-1">
            <Picture
              :width="80"
              :height="80"
              :imgs="{ pics: [item.pics], boxs: [item.violationBox] }"></Picture>
          </div>
          <div class="flex-1 flex flex-col gap-1 text-13">
            <div>报警时间：{{ FormatDateTime(item.eventAt) }}</div>
            <div>小散工程名称：{{ item.projectName }}</div>
            <div>小散工程地址：{{ item.projectAddress }}</div>
          </div>
        </view>
      </view>

      <view class="mt-2 flex justify-center">
        <wd-button type="text" underline @tap="toMore">
          <view class="flex items-center">
            <text>查看更多</text>
            <i class="i-iconoir-fast-arrow-right text-base ml-1"></i>
          </view>
        </wd-button>
      </view>
    </view>
  </view>
</template>
