<script setup lang="ts">
import {
  ApiProjectNotFiledList,
  ApiGetRoleRegion,
  ProjectInfoRes,
  ProjectNotFiledListRes,
  ApiProjectNotFiledUpdate,
} from '@/api';
import useRoleStore from '@/hooks/useRole';
import {
  Dayjs,
  Dialog,
  getLocationAdapter,
  IsMiniProgram,
  IsWeixinH5,
  Navigator,
  FormatDate,
  Toast,
  exceptionHandler,
  Loading,
} from '@/utils';
import { useList } from '@rms/components/sc-list/sc-list';
import { ServiceGisByIds, ServiceRegion } from '@rms/service/src';
import { watchThrottled } from '@vueuse/core';
import { useRoleRegion } from '@/hooks/useRoleRegion';
import {
  StorageGetPatrolRecordRefresh,
  StorageRemovePatrolRecordRefresh,
} from '@/storage/patrolRecord';
import { Dictionary } from '@rms/types';

const search = reactive({
  regions: [] as string[],
  isFiled: 0,
  size: 10,
});

const { list, listProps, getData } = useList<ProjectNotFiledListRes[]>({
  request: ApiProjectNotFiledList,
  params: search,
  immediate: false,
  handle: async (res) => {
    return res;
  },
});

async function setRegionName(region: string[]) {
  let res = await ServiceRegion();

  filterData.regionName =
    region
      .filter(Boolean)
      .map((r) => {
        const item = res.find((i) => i.id === r);
        res = item?.children || [];
        return item?.title;
      })
      .pop() || '';
}

watch(
  () => search.regions,
  () => {
    setRegionName(search.regions);
  },
  { deep: true },
);

watchThrottled(
  search,
  () => {
    refresh();
  },
  { throttle: 200, immediate: true, deep: true },
);

function refresh() {
  const [regionPid, regionId, regionCid] = search.regions;

  const params: Dictionary = {
    ...search,
    regionPid,
    regionId,
    regionCid,
  };

  if (typeof params.time === 'number') {
    params.isPatrol = params.time === 0 ? 2 : 1;
    delete params.time;
  }
  delete params.regions;
  getData(true, params);
}

onShow(() => {
  const needRefresh = StorageGetPatrolRecordRefresh();
  if (needRefresh) {
    refresh();
    StorageRemovePatrolRecordRefresh();
  }
});

const roleStore = useRoleStore();
roleStore.initOrganization(true);

onMounted(async () => {
  refresh();
});

async function toLocation(item: ProjectNotFiledListRes) {
  const options = {
    longitude: item.lng,
    latitude: item.lat,
    name: item.name,
    address: item.address,
  };

  if (IsWeixinH5 || IsMiniProgram) {
    uni.$wx.openLocation(options);
  } else {
    uni.openLocation(options);
  }
}

async function toPatrolRecord(item: ProjectNotFiledListRes) {
  const flag = await Dialog('确定已完成备案？', {
    title: '',
    confirmText: '确定',
    cancelText: '取消',
  });
  if (!flag) return;
  Loading.show();
  try {
    await ApiProjectNotFiledUpdate({ id: item.id, isFiled: 1 });
    await refresh();
  } catch (error) {
    exceptionHandler(error);
  } finally {
    Loading.hide();
  }
}

const { roleLevel, handleData } = useRoleRegion();
const scRegionRef = ref();
function onRegionShow() {
  if (typeof roleLevel.value !== 'number') {
    Toast.error('暂无权限');
    return;
  }

  scRegionRef.value.show();
}

const filterData = reactive({
  regionName: '',
  distanceText: '',
  time: '',
});

onPullDownRefresh(async () => {
  search.isFiled = 0;
  search.regions = [];
  await refresh();
  uni.stopPullDownRefresh();
});
onReachBottom(() => null);
</script>

<template>
  <div class="">
    <div class="w-full bg-[#F6F8FF] py-3">
      <div class="flex items-center text-gray-500 text-12 px-2 mb-2">
        <div
          class="rounded-2xl text-center bg-gray-100 h-[30px] ellipsis-1 w-[100px] leading-[30px] px-1"
          @click="onRegionShow">
          <span class="mr-1">{{ filterData.regionName || '所属区域' }}</span>
          <wd-icon name="arrow-down" size="15px"></wd-icon>
        </div>
      </div>
      <wd-radio-group
        v-model="search.isFiled"
        class="!bg-[#F6F8FF]"
        custom-class="flex justify-around"
        shape="button">
        <wd-radio :value="0">未备案</wd-radio>
        <wd-radio :value="1">已完成备案</wd-radio>
      </wd-radio-group>
    </div>

    <sc-list v-bind="listProps" class="!p-0 !h-auto !min-h-[calc(100vh-38px)]">
      <div class="w-full p-3">
        <div
          v-for="item in list"
          :key="item.id"
          class="bg-[#F6F8FF] rounded-xl p-3 mb-3 text-12 relative">
          <div class="flex items-center gap-2 mb-2">
            <div class="max-w-[200px] text-16 font-bold">{{ item.name }}</div>
            <wd-tag :type="item.isFiled === 0 ? 'warning' : 'success'" custom-class="!text-12">
              {{ item.isFiled === 0 ? '未备案' : '已备案' }}
            </wd-tag>
          </div>
          <div class="flex items-center gap-2 mb-1">
            <div>详细地址: {{ item.address }}</div>
            <wd-icon
              color="#1338D5"
              name="location"
              size="18px"
              @click.stop="toLocation(item)"></wd-icon>
          </div>
          <div class="mt-2">所属社区: {{ item.regionsName }}</div>
          <div class="flex mt-2 justify-between">
            <div>业主姓名: {{ item.constructorCharger }}</div>
            <div>业主电话: {{ item.ownerMobile }}</div>
          </div>
          <div class="mt-2">上报时间: {{ FormatDate(item.createdAt) }}</div>

          <div v-if="item.isFiled === 0" class="w-full flex justify-end">
            <wd-button
              class="createBtn"
              type="primary"
              size="medium"
              @click.stop="toPatrolRecord(item)">
              更改状态
            </wd-button>
          </div>
        </div>
      </div>
    </sc-list>

    <sc-region
      v-if="typeof roleLevel === 'number'"
      ref="scRegionRef"
      v-model="search.regions"
      :handle-data="handleData"
      is-hide></sc-region>
  </div>
</template>

<style lang="scss" scoped>
:deep(.wd-radio__label) {
  display: flex;
  align-items: center;
}
:deep(.wd-radio) {
  margin: 0 !important;
}
:deep(.createBtn.is-disabled) {
  background: #666 !important;
}

.bg-sky1 {
  // 天空蓝渐变色
  background-image: linear-gradient(120deg, #a1c4fd 0%, #c2e9fb 100%);
}

.bg2 {
  background-image: linear-gradient(to top, #cfd9df 0%, #e2ebf0 100%);
}

.border-b {
  border-bottom: 1rpx solid #e2ebf0;
}
</style>
