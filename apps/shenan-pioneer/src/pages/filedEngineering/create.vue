<script setup lang="ts">
import { ApiProjectNotFiledCreate, ProjectNotFiledCreateBody } from '@/api';
import { exceptionToast, Navigator, Toast } from '@/utils';
import { FormConfig } from '@rms/components/sc-form/sc-form.vue';
import { geoServiceKey, geoKey } from '@/config/base';
import { ServiceSetGis } from '@rms/service/src';
import { useRoleRegion } from '@/hooks/useRoleRegion';

const { getRegionByLatlng } = useRoleRegion();
const chooseAmapRef = ref();

const formData = ref<ProjectNotFiledCreateBody>({
  name: '',
  address: '',
  lat: 0,
  lng: 0,
  constructorCharger: '',
  ownerMobile: '',
  region: [],
  regionPid: '',
  regionId: '',
  regionCid: '',
});

const isSubmit = ref(false);

const scFormRef = ref();

const formConfig = ref<FormConfig['config']>({
  formAttrs: {
    labelWidth: '300rpx',
    fontSize: '15px',
    labelPosition: 'top',
    border: true,
  },
  data: [
    {
      prop: 'name',
      label: '工程名称',
      tag: {
        tagType: 'text',
        attr: {
          className: 'w-full flex py-2',
          required: true,
          placeholder: '请输入工程名称',
        },
      },
    },

    {
      prop: 'address',
      label: '详细地址',
      tag: {
        tagType: 'select',
        attr: {
          required: true,
          clearable: true,
          className: 'w-full flex py-2',
          iconName: 'i-carbon-location-filled',
          iconClass: 'text-green-500',
          placeholder: '请选择详细地址',
        },
      },
      listeners: {
        click: () => {
          chooseAmapRef.value.open();
        },
      },
    },
    {
      label: '所在社区',
      prop: 'region',
      tag: {
        tagType: 'region',
        attr: {
          required: true,
          placeholder: '请选择所在社区',
        },
      },
    },
    {
      label: '建设单位负责人姓名',
      prop: 'constructorCharger',
      tag: {
        tagType: 'text',
        attr: {
          className: 'w-full flex py-2',
          required: true,
          placeholder: '请输入建设单位负责人姓名',
        },
      },
    },
    {
      label: '业主电话',
      prop: 'ownerMobile',
      tag: {
        tagType: 'text',
        attr: {
          className: 'w-full flex py-2',
          required: true,
          placeholder: '请输入业主电话',
        },
      },
    },
  ],
  form: formData,
  rules: [
    {
      name: 'name',
      rules: [
        {
          required: true,
          errorMessage: '请输入工程名称',
        },
      ],
    },
    {
      name: 'ownerMobile',
      rules: [
        {
          required: true,
          errorMessage: '请输入业主电话',
        },
      ],
    },
    {
      name: 'constructorCharger',
      rules: [
        {
          required: true,
          errorMessage: '请输入建设单位负责人姓名',
        },
      ],
    },
    {
      name: 'address',
      rules: [
        {
          required: true,
          errorMessage: '请输入详细地址',
        },
      ],
    },
    {
      name: 'region',
      rules: [
        {
          required: true,
          errorMessage: '请选择所在社区',
        },
      ],
    },
  ],
});

async function handleChooseAmapSuccess(data: any) {
  console.log('data', data);
  formData.value.address = data.address;
  formData.value.lat = data.latitude;
  formData.value.lng = data.longitude;
  const gidData = await ServiceSetGis(data, data.address);
  console.log('gidData', gidData);
  // formData.value.gisId = gidData.id;
  const regions = await getRegionByLatlng([data.longitude, data.latitude], true);
  console.log('regions', regions);
  formData.value.region = regions;
}

async function onSubmit() {
  if (isSubmit.value) return;

  const flag = await scFormRef.value.validate();
  if (!flag) return;

  submitForm();
}

async function submitForm() {
  try {
    const [regionPid, regionId, regionCid] = formData.value.region;
    const formPayload = {
      ...formData.value,
      regionPid,
      regionId,
      regionCid,
    };

    await ApiProjectNotFiledCreate(formPayload);

    Toast.success('提交成功', {
      complete: () => {
        Navigator.back();
      },
    });
  } catch (error) {
    exceptionToast(error, '提交失败');
    isSubmit.value = false;
  }
}
</script>

<template>
  <div class="w-full min-h-screen bg-base box-border relative pb-16">
    <div class="mt-3 bg-white px-3 rounded-md mb-4">
      <sc-form ref="scFormRef" :config="formConfig" class="mt-3"></sc-form>
    </div>

    <sc-button-fixed class="pt-3 box-border">
      <div class="w-full flex items-center justify-center">
        <wd-button
          :disabled="isSubmit"
          custom-class="!rounded-md w-full"
          size="large"
          type="primary"
          @click="onSubmit">
          提交
        </wd-button>
      </div>
    </sc-button-fixed>

    <sc-choose-amap
      ref="chooseAmapRef"
      :default-search="formData.address"
      :geo-service-key="geoServiceKey!"
      :geo-key="geoKey!"
      @success="handleChooseAmapSuccess" />
  </div>
</template>

<style lang="scss" scoped>
.title-bg-blue {
  background: #057cb4;
}

.form-list-container {
  padding: 16px 0;
}

.popup-content {
  background: white;
  border-radius: 12px 12px 0 0;

  .popup-header {
    border-bottom: 1px solid #e5e7eb;
    padding-bottom: 12px;
  }

  .popup-body {
    padding: 16px 0;
  }
}
</style>
