import { exceptionToast } from '@rms/utils';
import { ArticlesProps } from '@rms/service';
import http from '@/http';
import { url } from '@/api/url';

export async function ServiceArticleDetail(id: string) {
  try {
    const api = `${url}/service-cms/ncms/articles/show`;

    const { data } = await http.post<ArticlesProps>(api, { id });

    return data;
  } catch (error) {
    exceptionToast(error, '获取文章失败');
    return null;
  }
}
