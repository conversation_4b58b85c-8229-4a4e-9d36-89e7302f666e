import http from '@/http';
import { sporadicProject } from '../api';
import { IndexBodyInterface, IndexInterface } from '@rms/types';

export interface MessageListQuery extends IndexBodyInterface {
  queryType?: number;
  createdAt?: string;
}

export interface ResMessageItem {
  content: string;
  createdAt: number;
  eventAt: number;
  id: string;
  projectId: string;
  relateId: string;
  orderId: string;
  status: number;
  type: number;
  updatedAt: number;
  userId: string;
  memo?: string;
}

export async function ApiGetMessageList(body: MessageListQuery) {
  const api = `${sporadicProject}/sporadic/project/notification/index`;
  const { data } = await http.post<IndexInterface<ResMessageItem>>(api, body);
  return data;
}

export async function ApiUpdateMessageStatus(body: { id: string; status: number }) {
  const api = `${sporadicProject}/sporadic/project/notification/read`;
  const { data } = await http.post(api, body);
  return data;
}
