import http from '@/http';
import { sporadicProject } from '../api';
import { Dictionary, IndexBodyInterface, IndexInterface } from '@rms/types';

export interface ProjectMemoRespDTO {
  content: string;
  createdAt: number;
  createdUserName: string;
  id: string;
  projectId: string;
  updatedAt: number;
  userId: string;
}

export interface ProjectInfoRes {
  address: string;
  endAt: string;
  lastPatrolAt: string;
  eventNumber: number;
  id: string;
  inspectRecordNum: number;
  monitorFlag: number;
  flow: number;
  flowId: string;
  name: string;
  poiId: string;
  projectNumber: string;
  rectifyEventNumber: number;
  rectifyRecordNum: number;
  startAt: string;
  violationRecordNum: number;
  status: number;
  orderId: string;
  reservationTime: string;
  content: string;
  memoRespDTOList?: ProjectMemoRespDTO[];
}

export async function ApiGetProjectList(body: IndexBodyInterface & Dictionary) {
  // const api = `${sporadicProject}/sporadic/project/mobile/index`;
  // const { data } = await http.post<IndexInterface<ProjectInfoRes>>(api, body);
  // return data;

  return {
    content: [
      {
        address: '广东省深圳市罗湖区清水河街道银湖山郊野公园',
        endAt: '2025-08-22',
        eventNumber: 0,
        flow: 9,
        flowId: '1726204855796064256',
        id: '1723884024107708417',
        inspectRecordNum: 0,
        installStatus: 3,
        memoRespDTOList: [],
        monitorFlag: 0,
        name: '0816-5',
        orderId: '1723884024174817280',
        poiId: '1723891026110107648',
        projectNumber: '081605',
        rectifyEventNumber: 0,
        rectifyRecordNum: 0,
        recycleStatus: 2,
        sort: 0,
        startAt: '2025-08-19',
        status: 2,
        violationRecordNum: 0,
      },
      {
        address: '广东省深圳市龙岗区南湾街道富璟花园',
        endAt: '2025-08-16',
        eventNumber: 0,
        flow: 9,
        flowId: '1723893050245722112',
        id: '1723884022916526080',
        inspectRecordNum: 0,
        installStatus: 3,
        memoRespDTOList: [],
        monitorFlag: 0,
        name: '0816-4',
        orderId: '1723884023507922944',
        poiId: '1723884022597758976',
        projectNumber: '081604',
        rectifyEventNumber: 0,
        rectifyRecordNum: 0,
        recycleStatus: 2,
        sort: 0,
        startAt: '2025-08-01',
        status: 2,
        violationRecordNum: 0,
      },
      {
        address: '广东省深圳市罗湖区东晓街道清水河一路112号罗湖投资控股大厦',
        endAt: '2025-08-26',
        eventNumber: 0,
        flow: 1,
        flowId: '1723794320988418048',
        id: '1723794320921309184',
        inspectRecordNum: 0,
        memoRespDTOList: [],
        monitorFlag: 0,
        name: '0816-3',
        orderId: '1723794320984223744',
        poiId: '1723794882013089792',
        projectNumber: '081603',
        rectifyEventNumber: 0,
        rectifyRecordNum: 0,
        sort: 0,
        startAt: '2025-08-19',
        status: 1,
        violationRecordNum: 0,
      },
      {
        address: '广东省深圳市罗湖区东晓街道清水河一路112号罗湖投资控股大厦',
        endAt: '2025-08-25',
        eventNumber: 0,
        flow: 9,
        flowId: '1724550737814478848',
        id: '1723794320501878785',
        inspectRecordNum: 0,
        installStatus: 3,
        memoRespDTOList: [],
        monitorFlag: 0,
        name: '0816-2',
        orderId: '1723794320585764864',
        poiId: '1723794320501878784',
        projectNumber: '081602',
        rectifyEventNumber: 0,
        rectifyRecordNum: 0,
        recycleStatus: 2,
        sort: 0,
        startAt: '2025-08-18',
        status: 2,
        violationRecordNum: 0,
      },
      {
        address: '广东省深圳市罗湖区东晓街道清水河一路112号罗湖投资控股大厦',
        endAt: '2025-08-16',
        eventNumber: 0,
        flow: 9,
        flowId: '1723801843222433792',
        id: '1723794317322596352',
        inspectRecordNum: 0,
        installStatus: 3,
        memoRespDTOList: [],
        monitorFlag: 0,
        name: '0816-1',
        orderId: '1723794318513778688',
        poiId: '1723794317179990016',
        projectNumber: '081601',
        rectifyEventNumber: 0,
        rectifyRecordNum: 0,
        recycleStatus: 2,
        sort: 0,
        startAt: '2025-08-01',
        status: 2,
        violationRecordNum: 0,
      },
      {
        address: '广东省深圳市罗湖区东晓街道清水河一路112号罗湖投资控股大厦',
        endAt: '2025-08-25',
        eventNumber: 0,
        flow: 9,
        flowId: '1724551254431096832',
        id: '1722822094439612416',
        inspectRecordNum: 0,
        installStatus: 3,
        memoRespDTOList: [
          {
            content: '好的备注一下',
            createdAt: 1755075643000,
            createdUserName: '吴玲玲',
            id: '1722822686687920128',
            projectId: '1722822094439612416',
            updatedAt: 1755075643000,
            userId: '1713011160887214080',
          },
        ],
        monitorFlag: 0,
        name: '0813-3',
        orderId: '1722822095064563712',
        poiId: '1722822094427029504',
        projectNumber: '081303',
        rectifyEventNumber: 0,
        rectifyRecordNum: 0,
        recycleStatus: 2,
        sort: 0,
        startAt: '2025-08-15',
        status: 2,
        violationRecordNum: 0,
      },
      {
        address: '广东省深圳市罗湖区东晓街道清水河一路112号罗湖投资控股大厦',
        endAt: '2025-08-25',
        eventNumber: 0,
        flow: 9,
        flowId: '1722823624865644544',
        id: '1722822092979994624',
        inspectRecordNum: 0,
        installStatus: 3,
        memoRespDTOList: [],
        monitorFlag: 0,
        name: '0813-2',
        orderId: '1722822093546225664',
        poiId: '1722822092950634496',
        projectNumber: '081302',
        rectifyEventNumber: 0,
        rectifyRecordNum: 0,
        recycleStatus: 2,
        sort: 0,
        startAt: '2025-08-15',
        status: 2,
        violationRecordNum: 0,
      },
      {
        address: '广东省深圳市罗湖区东晓街道清水河一路112号罗湖投资控股大厦',
        endAt: '2025-08-13',
        eventNumber: 0,
        flow: 9,
        flowId: '1722823562315988992',
        id: '1722822089708437504',
        inspectRecordNum: 0,
        installStatus: 3,
        memoRespDTOList: [],
        monitorFlag: 0,
        name: '0813-1',
        orderId: '1722822091600068608',
        poiId: '1722822089544859648',
        projectNumber: '081301',
        rectifyEventNumber: 0,
        rectifyRecordNum: 0,
        recycleStatus: 2,
        sort: 0,
        startAt: '2025-08-01',
        status: 2,
        violationRecordNum: 0,
      },
      {
        address: '广东省深圳市罗湖区东晓街道清水河一路112号罗湖投资控股大厦',
        endAt: '2025-07-30',
        eventNumber: 0,
        flow: 9,
        flowId: '1717869922586120193',
        id: '1717375114995322881',
        inspectRecordNum: 0,
        installStatus: 3,
        memoRespDTOList: [],
        monitorFlag: 0,
        name: '0729-13',
        orderId: '1717375115058237440',
        poiId: '1717375114995322880',
        projectNumber: '072913',
        rectifyEventNumber: 0,
        rectifyRecordNum: 0,
        recycleStatus: 2,
        sort: 0,
        startAt: '2025-07-24',
        status: 2,
        violationRecordNum: 0,
      },
      {
        address: '广东省深圳市罗湖区东晓街道清水河一路112号罗湖投资控股大厦',
        endAt: '2025-07-27',
        eventNumber: 0,
        flow: 9,
        flowId: '1716782758173556736',
        id: '1715974751190245376',
        inspectRecordNum: 0,
        installStatus: 3,
        memoRespDTOList: [],
        monitorFlag: 0,
        name: '0725-10',
        orderId: '1715974751274131456',
        poiId: '1715974751186051072',
        projectNumber: '072510',
        rectifyEventNumber: 0,
        rectifyRecordNum: 0,
        recycleStatus: 2,
        sort: 0,
        startAt: '2025-07-24',
        status: 2,
        violationRecordNum: 0,
      },
    ],
    empty: false,
    first: true,
    last: false,
    number: 0,
    numberOfElements: 10,
    pageable: {
      offset: 0,
      pageNumber: 0,
      pageSize: 10,
      paged: true,
      sort: {
        empty: true,
        sorted: false,
        unsorted: true,
      },
      unpaged: false,
    },
    size: 10,
    sort: {
      empty: true,
      sorted: false,
      unsorted: true,
    },
    totalElements: 99,
    totalPages: 10,
  };
}

export interface ProjectShowRes {
  address: string;
  amount: number;
  area: number;
  cateId: string;
  cateName: string;
  catePid: string;
  constructorName: string;
  constructorCharger: string;
  contractorCharger: string;
  contractorChargerMobile: string;
  contractorId: string;
  contractorName: string;
  createdAt: string;
  districtName: string;
  endAt: string;
  flow: number;
  flowId: string;
  id: string;
  lat: number;
  lng: number;
  monitorFlag: number;
  name: string;
  orderId: string;
  ownerMobile: string;
  pCateName: string;
  poiId: string;
  projectNumber: string;
  qrCodeUrl: string;
  regionCid: string;
  regionId: string;
  regionPid: string;
  startAt: string;
  status: number;
  statusName: string;
  streetName: string;
  updatedAt: string;
  villageName: string;
  cameraDeviceIds: string[];
}

export async function ApiGetProjectShow(id: string) {
  const api = `${sporadicProject}/sporadic/project/show`;
  const { data } = await http.post<ProjectShowRes>(api, { id });
  return data;
}

export async function ApiUpdateProjectStatus(body: { id: string; status: number }) {
  const api = `${sporadicProject}/sporadic/project/update/status`;
  const { data } = await http.post(api, body);
  return data;
}

export interface MonitorOrderListRes {
  createdAt: number;
  createdUser: string;
  flow: number;
  flowId: string;
  id: string;
  installStatus?: number;
  recycleStatus?: number;
  organizationId: string;
  projectAddress: string;
  projectId: string;
  projectName: string;
  projectNumber: string;
  projectPoiId: string;
  regionCid: string;
  regionId: string;
  regionPid: string;
  updatedAt: number;
}

export interface MonitorOrderListQuery extends IndexBodyInterface {
  /** 0-全部，1-未勘查，2-未安装 ，3-已完成 */
  installType?: number;
  /** 0-全部，1-未回收，2-已完成 */
  recycleType?: number;
}

export async function ApiGetMonitorOrderList(body: MonitorOrderListQuery) {
  const api = `${sporadicProject}/monitor/order/index`;
  const { data } = await http.post<IndexInterface<MonitorOrderListRes>>(api, body);
  return data;
}

export interface MonitorOrderDetailRes {
  createdAt: number;
  createdUser: string;
  flow: number;
  flowId: string;
  id: string;
  installStatus: number;
  organizationId: string;
  projectAddress: string;
  projectId: string;
  projectName: string;
  projectNumber: string;
  projectPoiId: string;
  regionCid: string;
  regionId: string;
  regionPid: string;
  reservationTime: number;
  inspectTime: number;
  updatedAt: number;
  cameraDeviceIds: string[];
  cameraSerialNos: string[];
}

export async function ApiGetMonitorOrderDetail(id: string) {
  const api = `${sporadicProject}/monitor/order/show`;
  const { data } = await http.post<MonitorOrderDetailRes>(api, { id });
  return data;
}

export interface MonitorAppointBody extends Dictionary {
  id: string;
  state: number;
  contactMobile: string;
  contactName: string;
  reservationTime: string;
}

export interface MonitorInstallSurveyBody extends Dictionary {
  id: string;
  state: number;
  inspectTime: string;
  reservationTime: string;
  memo: string;
  pic: string;
  installCnt: number;
}

export interface MonitorInstallBody extends Dictionary {
  id: string;
  state: number;
  installTime: string;
  memo: string;
  pic: string;
  installCnt: number;
  cameraDeviceIds: string[];
}

export interface MonitorRecycleBody extends Dictionary {
  id: string;
  state: number;
  recycleTime: string;
  memo: string;
  pic: string;
  recycleCnt: number;
}

export type MonitorOrderUpdateBody =
  | MonitorInstallSurveyBody
  | MonitorInstallBody
  | MonitorRecycleBody
  | MonitorAppointBody
  | Dictionary;

export async function ApiMonitorOrderUpdate(body: MonitorOrderUpdateBody) {
  const api = `${sporadicProject}/monitor/flow/update`;
  const { data } = await http.post(api, body);
  return data;
}

export interface MonitorOrderFlowListQuery extends IndexBodyInterface {
  /** 当前流程, 0-工程创建，1-安装预约，2-现场勘查，3-上门安装，4-接入监管， 5-施工完成，6-回收预约，7-上门回收，8-移除监管设备， 9-监管结束 */
  flow?: number | string;
  /** 当前环节是否已经结束， 0-未结束  1-已结束 */
  state?: number;
}

interface MonitorOrderFlowListCommonItem {
  flow: number;
  id: string;
  orderId: string;
  state: number;
  finishTime: number;
  updatedAt: number;
}

export type MonitorOrderFlowListItem = MonitorOrderFlowListCommonItem & MonitorOrderUpdateBody;

export async function ApiGetMonitorFlowList(body: MonitorOrderFlowListQuery) {
  const api = `${sporadicProject}/monitor/flow/index`;
  const { data } = await http.post<IndexInterface<MonitorOrderFlowListItem>>(api, body);
  return data;
}

export async function ApiMonitorFlowShow(id: string) {
  const api = `${sporadicProject}/monitor/flow/show`;
  const { data } = await http.post<MonitorOrderFlowListItem>(api, { id });
  return data;
}

export interface ProjectMemoRes {
  content: string;
  createdAt: number;
  createdUserName: string;
  id: string;
  projectId: string;
  updatedAt: number;
  userId: string;
}

// /sporadic/project/memo/index
export async function ApiGetProjectHistoryMemo(body: IndexBodyInterface & { projectId: string }) {
  const api = `${sporadicProject}/sporadic/project/memo/index`;
  const { data } = await http.post<IndexInterface<ProjectMemoRes>>(api, body);
  return data;
}

export async function ApiProjectMemoCreate(body: { projectId: string; content: string }) {
  const api = `${sporadicProject}/sporadic/project/memo/create`;
  const { data } = await http.post(api, body);
  return data;
}

export interface CameraDeviceNotRelevanceRes {
  id: string;
  serialNo: string;
}

export async function ApiGetCameraDeviceNotRelevance(body: IndexBodyInterface) {
  const api = `${sporadicProject}/event/camera/point/device/not/relevance/index`;
  const { data } = await http.post<IndexInterface<CameraDeviceNotRelevanceRes>>(api, body);
  return data;
}

export async function ApiGetPatrolProjectList(body: IndexBodyInterface & Dictionary) {
  const api = `${sporadicProject}/sporadic/project/patrol/mobile/index`;
  const { data } = await http.post<IndexInterface<ProjectInfoRes>>(api, body);
  return data;
}

// sporadic/project/check/limit
export async function ApiGetProjectCheckLimit(projectId: string) {
  const api = `${sporadicProject}/sporadic/project/check/limit`;
  const { data } = await http.post<boolean>(api, { projectId });
  return data;
}

// 邀请码生成接口返回类型
export interface ProjectInviteCodeRes {
  expireAt: number;
  expireSeconds: number;
  generatedAt: number;
  inviteCode: string;
  projectId: string;
  projectName: string;
}

// 生成工程邀请码
export async function ApiGenerateInviteCode(body: { id: string }) {
  const api = `${sporadicProject}/sporadic/project/invite-code/generate`;
  const { data } = await http.post<ProjectInviteCodeRes>(api, body);
  return data;
}

// 查找工程信息（通过备案号和邀请码）
export interface ProjectSearchRequest {
  projectNumber: string;
  inviteCode: string;
}

export interface ProjectSearchResponse {
  actualEndAt: string;
  actualStartAt: string;
  address: string;
  amount: number;
  area: number;
  cateId: string;
  cateName: string;
  catePid: string;
  constructorCharger: string;
  constructorName: string;
  contractorCharger: string;
  contractorChargerMobile: string;
  contractorId: string;
  contractorName: string;
  createdAt: string;
  districtName: string;
  endAt: string;
  flow: number;
  flowId: string;
  id: string;
  lat: number;
  lng: number;
  memoRespDTOList: any[];
  monitorFlag: number;
  name: string;
  orderId: string;
  ownerMobile: string;
  pCateName: string;
  poiId: string;
  projectNumber: string;
  qrCodeUrl: string;
  regionCid: string;
  regionId: string;
  regionPid: string;
  startAt: string;
  status: number;
  statusName: string;
  streetName: string;
  updatedAt: string;
  villageName: string;
}

export async function ApiSearchProject(body: ProjectSearchRequest) {
  const api = `${sporadicProject}/sporadic/project/invite-code/project`;
  const { data } = await http.post<ProjectSearchResponse>(api, body);
  return data;
}

// 绑定工程
export interface ProjectBindRequest {
  inviteCode: string;
  projectNumber: string;
}

export async function ApiBindProject(body: ProjectBindRequest) {
  const api = `${sporadicProject}/sporadic/project/invite-code/bind`;
  const { data } = await http.post(api, body);
  return data;
}
