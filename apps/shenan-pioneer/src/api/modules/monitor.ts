// /sporadic-project/event/camera/point/device/index

import { Api<PERSON>uery<PERSON>andler, ApiSortsHandler } from '@shencom/api';
import { deviceXsgc, sporadicProject } from '../api';
import http from '@/http';
import { IndexBodyInterface, IndexInterface } from '@rms/types';

export interface EventCameraPointRes {
  channel: string;
  channelId: string;
  createdAt: number;
  deviceId: string;
  flvAddress: string;
  hdFlvAddress: string;
  id: string;
  isDeleted: number;
  isHealthy: number;
  isLock: number;
  modelNo: string;
  monitorName: string;
  monitorNo: string;
  openLive: number;
  projectId: string;
  realStatus: number;
  scModelNo: string;
  serialNo: string;
  channelNo: string;
  smart: number;
  status: number;
  type: number;
  updatedAt: number;
  voiceBroadcast: number;
  url: string;
}

export interface MonitorInfoRes {
  cateName: string;
  createdAt: string;
  districtName: string;
  eventCameraPointList: EventCameraPointRes[];
  id: string;
  isHealthy: number;
  modelNo: string;
  pCateName: string;
  projectId: string;
  projectName: string;
  regionCid: string;
  regionId: string;
  regionPid: string;
  serialNo: string;
  smart: number;
  streetName: string;
  type: number;
  updatedAt: string;
  villageName: string;
  voiceBroadcast: number;
}

export async function ApiGetMonitorList(id: string) {
  const query = id ? ApiQueryHandler(id, 'projectId', 'select') : null;
  const api = `${sporadicProject}/event/camera/point/device/index`;
  const { data } = await http.post<IndexInterface<MonitorInfoRes>>(api, { query });
  return data;
}

// /event/camera/point/device/mobile/index

export async function ApiGetMonitorListMobile(id: string) {
  const api = `${sporadicProject}/event/camera/point/device/mobile/index`;
  const { data } = await http.post<IndexInterface<MonitorInfoRes>>(api, { projectId: id });
  return data;
}

export async function ApiGetRegionList() {
  const api = `${sporadicProject}/com/region/tree`;
  const { data } = await http.post(api);
  return data;
}

interface CameraLiveRes {
  flvAddress: string;
  needReplace: boolean;
}

export async function ApiGetCameraLive(body: {
  channelNo: string;
  serialNo: string;
  type: number;
}) {
  const api = `${sporadicProject}/event/camera/point/live`;
  const { data } = await http.post<CameraLiveRes>(api, body);
  return data;
}

export interface ApiGetWarningListRes {
  channelCode: string;
  createdAt: number;
  deviceCode: string;
  districtName: string;
  eventAt: number;
  eventNo: string;
  id: string;
  monitorNo: string;
  pics: string;
  projectAddress: string;
  projectCateName: string;
  projectId: string;
  projectName: string;
  regionCid: string;
  regionId: string;
  regionPid: string;
  sceneCode: string;
  streetName: string;
  typeName: string;
  updatedAt: number;
  villageName: string;
  violationBox: string;
  regionName: string;
  sceneCategoryName: string;
  sceneName: string;
}

export async function ApiGetWarningList(body: IndexBodyInterface) {
  const api = `${sporadicProject}/aiot/event/mobile/index`;
  const { data } = await http.post<IndexInterface<ApiGetWarningListRes>>(api, {
    ...body,
  });
  return data;
}

export async function ApiGetWarningInfo(id: string) {
  const api = `${sporadicProject}/aiot/event/show`;
  const { data } = await http.post<ApiGetWarningListRes>(api, { id });
  return data;
}

// /event/camera/point/device/show
export async function ApiGetMonitorInfo(id: string) {
  const api = `${sporadicProject}/event/camera/point/device/show`;
  const { data } = await http.post<MonitorInfoRes>(api, { id });
  return data;
}

// /event/camera/point/history/list

export interface ApiGetMonitorHistoryListRes {
  endTime: string;
  name: string;
  startTime: string;
}

export async function ApiGetMonitorHistoryList(body: any) {
  const api = `${sporadicProject}/event/camera/point/history/list`;
  const { data } = await http.post<ApiGetMonitorHistoryListRes[]>(api, body);
  return data;
}

// /sporadic-project/event/camera/point/history
export async function ApiGetMonitorHistory(body: any) {
  const api = `${sporadicProject}/event/camera/point/history`;
  const { data } = await http.post(api, body);
  return data;
}
