import http from '@/http';
import { sporadicProject } from '../api';
import { IndexBodyInterface, IndexInterface } from '@rms/types';

export interface ProjectNotFiledCreateBody {
  name: string;
  regionPid: string;
  regionId: string;
  regionCid: string;
  address: string;
  lat: number;
  lng: number;
  region: string[];
  constructorCharger: string;
  ownerMobile: string;
}
// 创建未备案工程
// sporadic/project/not/filed/create
export async function ApiProjectNotFiledCreate(body: ProjectNotFiledCreateBody) {
  const api = `${sporadicProject}/sporadic/project/not/filed/create`;
  const { data } = await http.post(api, body);
  return data;
}

interface ProjectNotFiledListBody extends IndexBodyInterface {
  isFiled: number;
  regionPid?: string;
  regionId?: string;
  regionCid?: string;
}

export interface ProjectNotFiledListRes {
  address: string;
  constructorCharger: string;
  createUser: string;
  createdAt: number;
  id: string;
  isFiled: number;
  lat: number;
  lng: number;
  name: string;
  organizationId: string;
  ownerMobile: string;
  regionCid: string;
  regionId: string;
  regionPid: string;
  updatedAt: number;
  regionsName: string;
}
// sporadic/project/not/filed/list
export async function ApiProjectNotFiledList(body: ProjectNotFiledListBody) {
  const api = `${sporadicProject}/sporadic/project/not/filed/index`;
  const { data } = await http.post<IndexInterface<ProjectNotFiledListRes>>(api, body);
  return data;
}

// sporadic/project/not/filed/update
export async function ApiProjectNotFiledUpdate(body: { id: string; isFiled: number }) {
  const api = `${sporadicProject}/sporadic/project/not/filed/update`;
  const { data } = await http.post(api, body);
  return data;
}
