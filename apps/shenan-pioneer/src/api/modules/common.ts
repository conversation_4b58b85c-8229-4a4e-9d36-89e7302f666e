import http from '@/http';
import { sporadicProject, gis, uaa } from '../api';
import { exceptionHandler } from '@/utils';
import { IndexInterface } from '@rms/types';

/** 获取角色权限 */
export async function ApiGetAllPermission() {
  const api = `${sporadicProject}/sys/permission/allPermission`;
  try {
    const { data } = await http.get<string[]>(api, {});

    return data;
  } catch (error) {
    exceptionHandler(error);
    return [];
  }
}

export interface Role {
  roleName: string;
  displayName: string;
}

export async function ApiGetAllRoles() {
  const api = `${sporadicProject}/sys/role/user/organization/all/roles`;
  try {
    const { data } = await http.post<Role[]>(api, {});

    return data;
  } catch (error) {
    exceptionHandler(error);
    return [];
  }
}

export interface Organization {
  id: string;
  name: string;
}

/** 获取组织列表 */
export async function ApiGetOrganizationList() {
  const api = `${sporadicProject}/fn/rmsv3/members/type/relate/show/all/organization`;
  try {
    const { data } = await http.post<Organization[]>(api, {});

    return data;
  } catch (error) {
    exceptionHandler(error);
    return [];
  }
}

/** 获取组织 */
export async function ApiGetOrganization(organizationId?: string) {
  const api = `${sporadicProject}/sys/role/user/organization/switch`;
  try {
    const { data } = await http.post<Organization>(api, { organizationId, platform: 1 });

    return data;
  } catch (error) {
    exceptionHandler(error);
    return undefined;
  }
}

interface RegionIds {
  createdAt: number;
  id: string;
  isDeleted: number;
  regionCid: string;
  regionId: string;
  regionPid: string;
  relateId: string;
  updatedAt: number;
}
interface RoleRegionRes {
  active: number;
  createdAt: number;
  id: string;
  level: number;
  memberId: string;
  regionIds: RegionIds[];
  typeId: string;
  updatedAt: number;
}

// /fn/rmsv3/members/type/relate/region
export async function ApiGetRoleRegion() {
  const api = `${sporadicProject}/fn/rmsv3/members/type/relate/region`;
  try {
    const { data } = await http.post<RoleRegionRes>(api, {});

    return data;
  } catch (error) {
    exceptionHandler(error);
    return {} as RoleRegionRes;
  }
}

export interface RelateMember {
  active: number;
  createdAt: number;
  id: string;
  level: number;
  memberId: string;
  mobile: string;
  realname: string;
  regionNames: string;
  typeId: string;
  updatedAt: number;
  userId: string;
}

interface ApiGetRelateMemberParams {
  typeId: string;
  projectId: string;
}

// members/type/relate/index
export async function ApiGetRelateMember(body: ApiGetRelateMemberParams) {
  const api = `${sporadicProject}/fn/rmsv3/members/type/relate/mobile/index`;
  try {
    const { data } = await http.post<IndexInterface<RelateMember>>(api, { ...body, size: 1000 });

    return data.content;
  } catch (error) {
    exceptionHandler(error);
    return [];
  }
}

interface ApiFindRegionByLnglatRes {
  regionId: string;
  name: string;
}

export async function ApiFindRegionByLnglat(body: { lng: number; lat: number }) {
  const { data } = await http.post<ApiFindRegionByLnglatRes[]>(
    `${gis}/geo/find/region/by/lnglat`,
    body,
  );
  return data;
}

// sys/user/current2
export async function ApiGetCurrentUser() {
  const api = `${uaa}/sys/user/current2`;
  const { data } = await http.get(api);
  return data;
}
