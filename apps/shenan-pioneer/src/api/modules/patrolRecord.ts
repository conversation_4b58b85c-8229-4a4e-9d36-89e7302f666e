import http from '@/http';
import { sporadicProject } from '../api';
import { IndexBodyInterface, IndexInterface } from '@rms/types';
import { ApiQueryHandler } from '@shencom/api';

export interface MessageListQuery extends IndexBodyInterface {
  queryType?: number;
  createdAt?: string;
}

export interface ResPatrolTableItem {
  createdAt: number;
  id: string;
  isDeleted: number;
  memo: string;
  name: string;
  num: number;
  organizationIds: string;
  organizationNames: string;
  status: number;
  updatedAt: number;
}

export interface PatrolRecordFormItem extends ResPatrolItem {
  qualified: number;
  picture: any;
  memo: string;
}

export async function ApiGetPatrolTable() {
  const api = `${sporadicProject}/patrol/table/role/index`;
  const { data } = await http.post<IndexInterface<ResPatrolTableItem>>(api, {
    query: ApiQueryHandler([[1, 'status', 'number']]),
    size: 100000,
  });
  return data.content;
}

export interface ResPatrolInfo {
  content: string;
  createdAt: number;
  id: string;
  isDeleted: number;
  itemId: string;
  score: number;
  tableId: string;
  updatedAt: number;
  name: string;
}

export interface ResPatrolItem {
  createdAt: number;
  description: string;
  id: string;
  isDeleted: number;
  name: string;
  tableId: string;
  updatedAt: number;
  score: number;
  content: string;
  matchedItemAt: number;
}

export async function ApiGetPatrolInfoList(id: string) {
  const api = `${sporadicProject}/patrol/item/info/index`;
  const { data } = await http.post<IndexInterface<ResPatrolInfo>>(api, {
    tableId: id,
    size: 10000,
  });
  return data.content;
}

export async function ApiGetPatrolItemList(id: string) {
  const api = `${sporadicProject}/patrol/item/index`;
  const { data } = await http.post<IndexInterface<ResPatrolItem>>(api, {
    tableId: id,
    size: 10000,
  });
  return data.content;
}

export interface ResultItem {
  id: string;
  qualified: number;
  score: number;
  picture?: string;
  memo?: string;
}

export interface ReqPatrolCreate {
  memberId: string;
  projectId: string;
  results: {
    [key: string]: ResultItem;
  };
  resultStatus: number;
  tableId: string;
}

export async function ApiGetPatrolCreate(body: ReqPatrolCreate) {
  const api = `${sporadicProject}/sporadic/project/patrol/record/create`;
  const { data } = await http.post<IndexInterface<ResPatrolItem>>(api, body);
  return data.content;
}

export interface ReqPatrolListItem {
  createdAt: number;
  defectiveNumber: number;
  districtName: string;
  id: string;
  isRecheck: boolean;
  isRectify: boolean;
  isTimeout: boolean;
  itemNumber: number;
  memberId: string;
  overtime: number;
  patrolUserNames: string;
  projectAddress: string;
  projectId: string;
  projectMonitorFlag: string;
  projectName: string;
  projectNumber: string;
  projectStatus: string;
  projectPoiId: string;
  regionNames: string;
  repairTimes: number;
  resultStatus: number;
  score: number;
  streetName: string;
  tableId: string;
  tableNames: string;
  updatedAt: number;
  villageName: string;
  state: number;
}
export async function ApiGetPatrolList(body: ReqPatrolCreate) {
  const api = `${sporadicProject}/sporadic/project/patrol/record/mobile/index`;
  const { data } = await http.post<IndexInterface<ReqPatrolListItem>>(api, body);
  return data;
}

export async function ApiGetPatrolTabList(body: ReqPatrolCreate) {
  const api = `${sporadicProject}/sporadic/project/patrol/record/mobile/tab/index`;
  const { data } = await http.post<IndexInterface<ReqPatrolListItem>>(api, body);
  return data;
}

export interface patrolItemInfoSnapshotListItem {
  content: string;
  createdAt: number;
  data: {
    createdAt: number;
    id: string;
    itemId: string;
    qualified: number;
    recordId: string;
    recordTableId: string;
    updatedAt: number;
    picture: string;
    rectifiedPicture: string;
    memo: string;
  };
  id: string;
  isDeleted: number;
  itemId: string;
  originInfoId: string;
  score: number;
  tableId: string;
  updatedAt: number;
}

export interface patrolItemSnapshotsItem {
  createdAt: number;
  id: string;
  isDeleted: number;
  name: string;
  originItemId: string;
  patrolItemInfoSnapshotList: patrolItemInfoSnapshotListItem[];
}

export interface PatrolTableSnapshotItem {
  createdAt: number;
  id: string;
  isDeleted: number;
  name: string;
  organizationIds: string;
  originTableId: string;
  patrolItemSnapshots: patrolItemSnapshotsItem[];
}

export interface RecordTableItem {
  createdAt: number;
  id: string;
  tableName: string;
  patrolTableSnapshot: PatrolTableSnapshotItem;
}

export interface DetailData {
  createdAt: number;
  defectiveNumber: number;
  districtName: string;
  id: string;
  itemNumber: number;
  memberId: string;
  patrolUserNames: string;
  projectAddress: string;
  projectId: string;
  projectName: string;
  projectNumber: string;
  recordTables: RecordTableItem[];
  state: number;
  resultStatus: number;
  regionNames: string;
  repairTimes: number;
  score: number;
  streetName: string;
  tableId: string;
  tableNames: string;
  updatedAt: number;
  villageName: string;
  isTimeout: boolean;
  overtime: number;
}
export interface TableItem {
  id: string;
  name: string;
  itemId: string;
  content: string;
  qualified: number;
  qualifiedText: string;
  memo: string;
  colspanNum: number;
  picture: string;
  pictureUrl?: string;
  afterPhoto: string;
  rectifiedPictureUrl?: string;
  score: number;
  rectifiedPicture?: string;
  rectifiedPictureList: any;
}

export interface DataItem {
  name: string;
  tableList: Partial<TableItem>[];
  isExpanded?: boolean;
}

export async function ApiGetPatrolDetail(id: string) {
  const api = `${sporadicProject}/sporadic/project/patrol/record/show`;
  const { data } = await http.post<DetailData>(api, { id });
  return data;
}

export interface ReqPatrolReCheck {
  id: string;
  status: string;
}

export async function ApiGetPatrolReCheck(body: ReqPatrolReCheck) {
  const api = `${sporadicProject}/sporadic/project/patrol/record/recheck`;
  const { data } = await http.post<DetailData>(api, body);
  return data;
}

// project/patrol/record/submit/rectify
export async function ApiSubmitRectificationResult(body: any) {
  const api = `${sporadicProject}/sporadic/project/patrol/record/submit/rectify`;
  const { data } = await http.post<DetailData>(api, body);
  return data;
}

interface ReqReCheckRectify {
  id: string;
  isPassRecheck: number;
  memo?: string;
}
// sporadic/project/patrol/record/recheck/rectify
export async function ApiReCheckRectify(body: ReqReCheckRectify) {
  const api = `${sporadicProject}/sporadic/project/patrol/record/recheck/rectify`;
  const { data } = await http.post<DetailData>(api, body);
  return data;
}

export interface ResPatrolFlow {
  createdAt: number;
  flow: number; // 1发起巡查 2整改待审核 3整改复核通过 4整改复核不通过
  id: string;
  realname: string;
  recordId: string;
  updatedAt: number;
  userId: string;
}

// sporadic/project/patrol/record/flow/show
export async function ApiGetPatrolFlow(id: string) {
  const api = `${sporadicProject}/sporadic/project/patrol/record/flow/index`;
  const { data } = await http.post<IndexInterface<ResPatrolFlow>>(api, { recordId: id, size: 100 });
  return data.content;
}
