import http from '@/http';
import { sporadicProject } from '../api';
import { exceptionHandler, ValidateIdCard, ValidatePhone } from '@/utils';
import type { MemberAuthenticationDTO, EngineeringMembersRespDTO } from '@/@types/authentication';
import { EngineeringRoleEnum } from '@/config/engineering';

/**
 * 人员认证接口
 * POST /engineering/members/authentication
 */
export async function ApiMemberAuthentication(
  data: MemberAuthenticationDTO,
): Promise<EngineeringMembersRespDTO> {
  const api = `${sporadicProject}/engineering/members/authentication`;
  try {
    const { data: response } = await http.post<EngineeringMembersRespDTO>(api, data);
    return response;
  } catch (error) {
    exceptionHandler(error);
    throw error;
  }
}

/**
 * 获取工种类型选项列表
 */
export function getWorkTypeOptions() {
  return [
    { label: '木工', value: '木工' },
    { label: '泥工', value: '泥工' },
    { label: '钢筋工', value: '钢筋工' },
    { label: '混凝土工', value: '混凝土工' },
    { label: '油漆工', value: '油漆工' },
    { label: '玻璃工', value: '玻璃工' },
    { label: '起重工', value: '起重工' },
    { label: '吊车司机和指挥', value: '吊车司机和指挥' },
    { label: '电焊工', value: '电焊工' },
    { label: '机修工', value: '机修工' },
    { label: '维修电工', value: '维修电工' },
    { label: '测量工', value: '测量工' },
    { label: '防水工', value: '防水工' },
    { label: '架子工', value: '架子工' },
    { label: '水工', value: '水工' },
    { label: '电工', value: '电工' },
    { label: '杂工', value: '杂工' },
  ];
}

/**
 * 获取人员类型选项列表
 */
export function getPersonnelTypeOptions() {
  return [
    {
      label: '建设方（业主）',
      value: EngineeringRoleEnum.OWNER_NOT_AUTH,
      description: '工程项目的投资方或建设单位',
    },
    {
      label: '施工负责人',
      value: EngineeringRoleEnum.MANAGER_NOT_AUTH,
      description: '负责施工现场的管理工作',
    },
    {
      label: '施工工人',
      value: EngineeringRoleEnum.WORKER_NOT_AUTH,
      description: '直接参与施工操作的工人',
    },
    {
      label: '施工监理',
      value: EngineeringRoleEnum.SUPERVISOR_NOT_AUTH,
      description: '对施工过程进行监督管理的监理人员',
    },
  ];
}
