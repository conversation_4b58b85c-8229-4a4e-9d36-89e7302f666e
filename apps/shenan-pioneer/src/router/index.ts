import { RouterMethods } from '@rms/router';

/**
 * 首屏加载页面触发
 * @param options onLoad 获取的参数
 * @returns
 */
export const paegsInit: RouterMethods['paegsInit'] = (options) => null;

/**
 * @param url 跳转的 url
 * @param query 路由参数
 * @param route 路由信息
 * @returns {boolean} 是否跳转，`true` 跳转，`false` 不跳转
 */
export const invoke: RouterMethods['invoke'] = (url, query, route) => {
  return true;
};

/**
 * 成功跳转
 * @param url 跳转的 url
 * @returns {any}
 */
export const success: RouterMethods['success'] = (url) => null;

/**
 * 失败跳转
 * @param error 错误信息
 * @returns {any}
 */
export const fail: RouterMethods['fail'] = (error) => null;

export default {
  invoke,
  success,
  fail,
  paegsInit,
};
