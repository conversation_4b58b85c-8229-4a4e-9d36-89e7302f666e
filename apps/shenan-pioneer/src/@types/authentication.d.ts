// 人员认证相关类型定义

// 人员类型枚举
export enum PersonnelType {
  OWNER = 1, // 建设方（业主）
  MANAGER = 2, // 施工单位负责人
  WORKER = 3, // 施工工人
  SUPERVISOR = 4, // 施工监理
}

// 工种类型枚举
export enum WorkType {
  CARPENTER = '木工',
  MASON = '泥工',
  REBAR_WORKER = '钢筋工',
  CONCRETE_WORKER = '混凝土工',
  PAINTER = '油漆工',
  GLAZIER = '玻璃工',
  CRANE_OPERATOR = '起重工',
  CRANE_DRIVER = '吊车司机和指挥',
  WELDER = '电焊工',
  MECHANIC = '机修工',
  ELECTRICIAN = '维修电工',
  SURVEYOR = '测量工',
  WATERPROOFING_WORKER = '防水工',
  SCAFFOLDER = '架子工',
  PLUMBER = '水工',
  ELECTRICIAN_GENERAL = '电工',
  GENERAL_WORKER = '杂工',
}

// 人员认证请求接口
export interface MemberAuthenticationDTO {
  /** 成员姓名 - 必填 */
  realname: string;
  /** 手机号码 - 必填 */
  mobile: string;
  /** 职位类型 - 必填 */
  type: PersonnelType;
  /** 身份证号 - 必填 */
  idCard: string;
  /** 本人照片 - 施工负责人/施工工人/施工监理必填 */
  personalPhoto?: string;
  /** 技术工种名称 - 施工负责人/施工工人选填 */
  workTypeName?: WorkType;
  /** 证书编号 - 施工负责人/施工工人选填 */
  certificateNumber?: string;
  /** 工种证件有效期开始时间 - 施工负责人/施工工人选填 */
  certificateStartDate?: string;
  /** 工种证件有效期结束时间 - 施工负责人/施工工人选填 */
  certificateEndDate?: string;
  /** 证书照片 - 施工负责人/施工工人选填 */
  certificatePic?: string;
  /** 监理员证件编号 - 施工监理必填 */
  supervisorCertificateNumber?: string;
  /** 监理员证件有效期开始时间 - 施工监理必填 */
  supervisorCertificateStartDate?: string;
  /** 监理员证件有效期结束时间 - 施工监理必填 */
  supervisorCertificateEndDate?: string;
  /** 监理员证件照片 - 施工监理必填 */
  supervisorCertificatePic?: string;
  /** 描述 */
  desc?: string;
  /** 项目ID - 可选，用于关联项目 */
  projectId?: string;
  /** 组织ID - 可选 */
  organizationId?: string;
}

// 人员认证表单类型
export interface MemberAuthenticationFormDTO
  extends Omit<
    MemberAuthenticationDTO,
    'personalPhoto' | 'certificatePic' | 'supervisorCertificatePic'
  > {
  personalPhoto?: SC.File.Info[];
  certificatePic?: SC.File.Info[];
  supervisorCertificatePic?: SC.File.Info[];
}

// 人员认证响应接口
export interface EngineeringMembersRespDTO {
  /** ID */
  id: string;
  /** 用户id */
  userId: string;
  /** 成员姓名 */
  realname: string;
  /** 手机号码 */
  mobile: string;
  /** 职位类型 */
  type: PersonnelType;
  /** 身份证号 */
  idCard: string;
  /** 工种名称 */
  workTypeName?: WorkType;
  /** 证书编号 */
  certificateNumber?: string;
  /** 证书有效日期-开始时间 */
  certificateStartDate?: string;
  /** 证书有效日期-结束时间 */
  certificateEndDate?: string;
  /** 证书图片 */
  certificatePic?: string;
  /** 描述 */
  desc?: string;
  /** 有效状态，0-无效，1-有效 */
  status: number;
  /** 创建时间 */
  createdAt: string;
  /** 最后更新时间 */
  updatedAt: string;
}

// 人员类型选项
export interface PersonnelTypeOption {
  label: string;
  value: PersonnelType;
  description?: string;
}
