import { Storage } from '@/utils';

/** 刷新工程列表 */
const MonitorFlowRefresh = 'MonitorFlowRefresh';
export function StorageGetMonitorFlowRefresh() {
  const data = Storage.getState<boolean>(MonitorFlowRefresh);
  return data;
}

export function StorageSetMonitorFlowRefresh() {
  Storage.setState(MonitorFlowRefresh, true);
}

export function StorageRemoveMonitorFlowRefresh() {
  Storage.removeState(MonitorFlowRefresh);
}
