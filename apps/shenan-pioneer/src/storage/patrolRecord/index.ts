import { Storage } from '@/utils';

/** 刷新巡查记录列表 */
const PatrolRecordRefresh = 'PatrolRecordRefresh';
export function StorageGetPatrolRecordRefresh() {
  const data = Storage.getState<boolean>(PatrolRecordRefresh);
  return data;
}

export function StorageSetPatrolRecordRefresh() {
  Storage.setState(PatrolRecordRefresh, true);
}

export function StorageRemovePatrolRecordRefresh() {
  Storage.removeState(PatrolRecordRefresh);
}

interface PatrolRecordMembers {
  memberId: string[];
  memberTexts: string[];
}
/** 获取已选择的巡查人员 */
const PatrolRecordMembers = 'PatrolRecordMembers';
export function StorageGetPatrolRecordMembers() {
  const data = Storage.get<PatrolRecordMembers>(PatrolRecordMembers);
  return data;
}

export function StorageSetPatrolRecordMembers(members: PatrolRecordMembers) {
  Storage.set(PatrolRecordMembers, members);
}

export function StorageRemovePatrolRecordMembers() {
  Storage.remove(PatrolRecordMembers);
}
