<script setup lang="ts">
import { onLaunch, onShow, onHide } from '@dcloudio/uni-app';
import { routeInterceptor } from '@rms/router';
import { IsH5 } from '@rms/utils';
import useRoleStore from '@/hooks/useRole';

const roleStore = useRoleStore();

onLaunch(async (option) => {
  routeInterceptor(option);
});
onShow(async () => {
  if (IsH5) {
    window.sessionStorage.removeItem('organization');
    await roleStore.initOrganization(true);
  } else {
    uni.removeStorageSync('organization');
  }

  console.log('App Show');
});
onHide(() => {
  console.log('App Hide');
});
</script>

<style lang="scss">
@import '@rms/style';
@import './styles/index';
</style>
