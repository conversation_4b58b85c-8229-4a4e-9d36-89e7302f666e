import { Tenant, _GetUrlKey } from '@rms/utils';
import { name, version } from '../../package.json';
import { AppConfig } from '../../app.config';

const geoServiceKey = AppConfig.base.geoServiceKey;
const appid = AppConfig.base.appid;
const geoKey = AppConfig.base.geoKey;
const lngLat = { lng: 113.2333, lat: 23.1666 };
const scid = AppConfig.base.scid;

const { isGuangzhou, isFutian } = Tenant.getTenetInfo(scid);

export { geoServiceKey, appid, geoKey, lngLat, scid, name, version, isGuangzhou, isFutian };

export default {
  geoServiceKey,
  appid,

  geoKey,
  lngLat,
  scid,
  name,
  version,
};
