import { httpInit as HttpInit } from '@rms/api';
import { init as ApiInit } from '@shencom/api';
import { serviceInit as ServiceInit } from '@rms/service';
import { url } from '@/api/url';
import http from '@/http';
import pages from '@/pages.json';
import { init as CdnInit } from '@rms/cdn';
import {
  Navigator,
  Storage,
  UserInfo,
  clearOldStorage,
  updateMiniprogram,
  utilsOss,
} from '@/utils';
import { AppConfig } from '../../app.config';

HttpInit(http, url);

ApiInit(http, url);

ServiceInit({
  appConfig: AppConfig,
  http,
  Navigator,
  Storage,
  utilsOss,
  UserInfo,
  pages,
});

CdnInit();

// #ifdef H5
if (typeof window.global === 'undefined') {
  window.global = window;
}
// #endif

// #ifdef MP-WEIXIN
updateMiniprogram();
clearOldStorage(AppConfig.base.version, Storage);
// #endif
