.bg-content {
  background: linear-gradient(180deg, #f8fafc 0%, #f1f5f9 100%) !important;
}

.bg-card {
  @apply bg-white rounded-xl overflow-hidden shadow-md;
}

.register-form {
  .uni-forms-item__label {
    uni-text:last-child {
      span {
        color: #64748b !important;
      }
    }
  }

  .content-clear-icon {
    font-size: 25px !important;
  }

  .uni-forms-item__content {
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    // overflow: hidden;
    padding: 6px 8px;
  }

  .wd-checkbox-group {
    .is-checked {
      .wd-checkbox__label {
        color: #34d19d !important;
        background-color: #f0fdf4 !important;
        border-color: #34d19d !important;
      }
    }
  }
}

:deep(.wd-button.is-disabled) {
  background: var(--wot-button-success-bg-color, var(--wot-color-success, #34d19d)) !important;
  color: var(--wot-button-success-color, var(--wot-color-white, rgb(255, 255, 255))) !important;
}

.disabledForm {
  .sc-picker-cnt {
    display: none;
  }
}

div {
  box-sizing: border-box;
}

.is-disabled {
  background: #fff !important;
}

.imgInfo {
  width: 142px;
  object-fit: cover;
  border-radius: 5px;
}

.tabs {
  --wot-tabbar-item-title-font-size: 12px;
}