import { commonPlugins } from '../../plugins';
import { version, name } from './package.json';

export default commonPlugins({
  version,
  name,
  config: {
    /** 本地代理 */
    server: {
      proxy: {
        '/sporadic-project': {
          target: 'http://127.0.0.1:12245',
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/sporadic-project/, ''),
        },
        '/java': {
          target: 'https://tst-ai.vansafe.cn',
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/java/, ''),
        },
        '/pro': {
          target: 'https://ai-app.vansafe.cn',
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/pro/, ''),
        },
      },
    },
  },
});
