---
description: This rule governs TypeScript development practices across the entire project. It should be considered whenever: (1) Planning new TypeScript features or components, (2) Modifying existing TypeScript code, (3) Reviewing or fixing TypeScript-related bugs, (4) Making architectural decisions that involve TypeScript, (5) Creating new TypeScript files or refactoring existing ones. The rule ensures consistent type safety, modern TypeScript patterns, and maintainable code structure. It's particularly crucial when dealing with data models, API interfaces, state management, and component architecture. Apply this rule when discussing, planning, or implementing any TypeScript-related changes to maintain code quality and prevent common pitfalls.
globs:
alwaysApply: false
---
# TypeScript Best Practices for Vue 3 + Uni-app Project

## Critical Rules

### 🧩 Component Reuse & Architecture
- **MANDATORY**: Always check `@/components`, `packages/components/`, and `wot-design-uni` for existing components before creating new ones
- Use `@rms/*` workspace packages for shared functionality across the monorepo
- Prefer composition API (`<script setup lang="ts">`) for all new Vue components
- Create reusable composables in `src/hooks/` or `packages/utils/src/` for shared logic
- Follow the existing component naming pattern: `sc-*` for internal components, `wd-*` for wot-design-uni components

### 🎨 Styling Guidelines
- **PRIMARY**: Use Tailwind CSS classes for all styling needs
- **SECONDARY**: Use UnoCSS utilities when Tailwind CSS doesn't provide the needed class
- **LAST RESORT**: Write custom SCSS only when absolutely necessary (complex animations, vendor-specific styles)
- Use `rpx` units for responsive design in uni-app contexts
- Utilize the `addUnit()` and `addStyle()` utilities from `@/components/utils.ts` for dynamic styling

### 📝 Documentation Standards (Balanced Approach)
- **File Headers**: Include JSDoc file description for complex files (>100 lines) or public APIs
- **Interface Documentation**: Always document exported interfaces with `@description` and property descriptions
- **Function Documentation**: Document exported functions with:
  - `@description` - what the function does
  - `@param` - parameter types and descriptions (if not obvious from TypeScript)
  - `@returns` - return type description (if complex)
  - `@example` - usage example for utility functions
- **Inline Comments**: Use for complex business logic, not obvious code
- **Skip Documentation**: Simple getters, setters, obvious one-liners, internal helper functions

### 📦 Import Organization (Alphabetical Groups)
Organize imports in this exact order with alphabetical sorting within each group:

```typescript
// 1. Vue/Uni-app core imports (alphabetical)
import { computed, ref, watch } from 'vue'
import { onLoad, onShow } from '@dcloudio/uni-app'

// 2. Third-party libraries (alphabetical)
import { cloneDeep } from 'lodash-es'
import dayjs from 'dayjs'

// 3. Internal workspace packages @rms/* (alphabetical)
import { ApiGetProjectList } from '@rms/api'
import Register from '@rms/register'
import { UserInfo } from '@rms/types'

// 4. Internal @shencom/* packages (alphabetical)
import { request } from '@shencom/request'
import { utils } from '@shencom/utils'

// 5. Relative imports (alphabetical)
import { useRecyclingData } from '@/hooks/useRecyclingData'
import { ProjectInfoRes } from '@/api/modules/engineering'
import { config } from '@/config'

// 6. Type-only imports (alphabetical)
import type { ComponentProps, Ref } from 'vue'
import type { IndexBodyInterface } from '@rms/types'
```

### 🔧 Code Quality & Encapsulation
- **DRY Principle**: Extract repeated logic into composables or utility functions
- **KISS Principle**: Keep functions simple and focused on single responsibilities
- **SOLID Principles**:
  - Single Responsibility: One function, one purpose
  - Open/Closed: Use interfaces and generics for extensibility
  - Interface Segregation: Create specific interfaces rather than large ones
  - Dependency Injection: Use composables for dependency management
- **YAGNI Principle**: Don't add functionality until it's needed
- **File Size Limit**: Split files >500 lines into logical modules following domain boundaries
- **Function Complexity**: Keep functions under 50 lines; extract complex logic

### 🔒 Type Safety Rules
- Use `strict: true` TypeScript configuration (already configured)
- Prefer `interface` over `type` for object structures
- Use `type` for unions, intersections, and computed types
- Always type API responses and request bodies
- Use generic constraints for reusable utility functions
- Leverage `as const` for immutable objects and arrays
- Use template literal types for string validation

### 🚀 Performance & Best Practices
- Use `defineAsyncComponent` for lazy-loaded components
- Implement proper error boundaries with `onErrorCaptured`
- Use `shallowRef` for large objects that don't need deep reactivity
- Implement proper cleanup in `onUnmounted` hooks
- Use `markRaw` for non-reactive objects
- Prefer `readonly` for props and configuration objects

### 🔄 Git Commit Standards
- Use `git-cz` (commitizen) for all commits
- Follow conventional commit format: `type(scope): description`
- Types: `feat`, `fix`, `docs`, `style`, `refactor`, `test`, `chore`
- Include breaking changes in commit body when applicable
- Keep commit messages under 72 characters for the subject line

## Examples

<example>
  ```typescript
  // ✅ Good: Well-organized imports, proper documentation, type safety
  import { computed, ref } from 'vue'
  import { onLoad } from '@dcloudio/uni-app'

  import { cloneDeep } from 'lodash-es'

  import { ApiGetProjectList } from '@rms/api'
  import Register from '@rms/register'

  import { useRecyclingData } from '@/hooks/useRecyclingData'

  import type { ProjectInfoRes } from '@/api/modules/engineering'

  /**
   * @description Manages project list data with filtering and pagination
   * @param initialFilters - Initial filter state
   * @returns Project list management composable
   */
  export function useProjectList(initialFilters: ProjectFilters) {
    const projects = ref<ProjectInfoRes[]>([])
    const loading = ref(false)

    const filteredProjects = computed(() =>
      projects.value.filter(project =>
        project.status === filters.value.status
      )
    )

    /**
     * @description Fetches project list with applied filters
     * @param refresh - Whether to refresh the entire list
     */
    async function fetchProjects(refresh = false) {
      if (refresh) projects.value = []
      loading.value = true

      try {
        const data = await ApiGetProjectList(filters.value)
        projects.value = refresh ? data.list : [...projects.value, ...data.list]
      } catch (error) {
        console.error('Failed to fetch projects:', error)
      } finally {
        loading.value = false
      }
    }

    return {
      projects: readonly(projects),
      loading: readonly(loading),
      filteredProjects,
      fetchProjects
    }
  }
  ```
</example>

<example type="invalid">
  ```typescript
  // ❌ Bad: Unorganized imports, missing types, no documentation
  import { useRecyclingData } from '@/hooks/useRecyclingData'
  import { ref } from 'vue'
  import { ApiGetProjectList } from '@rms/api'
  import { cloneDeep } from 'lodash-es'
  import Register from '@rms/register'

  export function useProjectList(initialFilters: any) {  // ❌ Using 'any'
    const projects = ref([])  // ❌ No type annotation
    const loading = ref(false)

    // ❌ No error handling, no documentation
    async function fetchProjects() {
      const data = await ApiGetProjectList(initialFilters)
      projects.value = data.list
    }

    return { projects, loading, fetchProjects }  // ❌ Not readonly
  }

  // ❌ Custom CSS instead of Tailwind
  .custom-button {
    background: linear-gradient(45deg, #1e3a8a, #3b82f6);
    padding: 12px 24px;
    border-radius: 8px;
  }
  ```
</example>
