---
description:
globs: *.vue,*.scss
alwaysApply: false
---
# UI Component & Styling Best Practices

## Critical Rules

### 🧩 Component Reuse Strategy
- **FIRST PRIORITY**: Always check `wot-design-uni` component library for existing solutions
- **SECOND PRIORITY**: Use existing `@/components` and `packages/components/sc-*` components
- **THIRD PRIORITY**: Create new reusable components only when necessary
- **MANDATORY**: Review [wot-design-uni documentation](mdc:https:/wot-design-uni.netlify.app) before implementing custom solutions
- Use `wd-` prefix for wot-design-uni components, `sc-` prefix for custom shared components
- Compose complex UI from multiple simple components rather than creating monolithic components

### 🎨 Styling Philosophy
- **PRIMARY**: Use Tailwind CSS utility classes for ALL styling needs
- **SECONDARY**: Use UnoCSS utilities when Tailwind CSS lacks specific classes
- **LAST RESORT**: Write custom CSS/SCSS only for:
  - Complex animations and transitions
  - Vendor-specific styles (-webkit-, -moz-, etc.)
  - Legacy browser compatibility fixes
  - Component-specific styles that can't be achieved with utilities
- **FORBIDDEN**: Writing custom CSS for spacing, colors, typography, layouts that can be achieved with Tailwind

### 📱 Responsive Design Rules
- Use Tailwind's responsive prefixes: `sm:`, `md:`, `lg:`, `xl:`, `2xl:`
- Design mobile-first with progressive enhancement
- Use `rpx` units for uni-app specific responsive needs
- Leverage Tailwind's container classes for consistent layouts
- Test on multiple screen sizes and devices

### 🎯 Component Architecture
- Keep components focused on single responsibilities
- Use composition API (`<script setup lang="ts">`) for all new components
- Implement proper prop validation with TypeScript interfaces
- Use `defineEmits` for component communication
- Expose component methods via `defineExpose` when needed
- Follow the naming convention: `ComponentName.vue` (PascalCase)

### 🔧 Styling Best Practices
- Use Tailwind's design tokens for consistent spacing (`space-*`, `p-*`, `m-*`)
- Leverage Tailwind's color palette instead of custom colors
- Use semantic class names when grouping utilities becomes complex
- Implement dark mode support using Tailwind's `dark:` variants
- Use `@apply` directive sparingly and only for component-specific repeated patterns

### 🚀 Performance Optimization
- Use `v-show` vs `v-if` appropriately for conditional rendering
- Implement lazy loading for heavy components with `defineAsyncComponent`
- Use `v-memo` for expensive list rendering
- Optimize images with proper sizing and formats
- Minimize DOM manipulation in favor of reactive data changes

## Examples

<example>
  ```vue
  <!-- ✅ Good: Using wot-design-uni components with Tailwind utilities -->
  <template>
    <view class="min-h-screen bg-gray-50 px-4 py-6">
      <!-- Use wot-design-uni form components -->
      <wd-form ref="formRef" :model="formData" class="space-y-4">
        <wd-input
          v-model="formData.name"
          label="用户名"
          placeholder="请输入用户名"
          class="w-full"
        />

        <wd-picker
          v-model="formData.city"
          label="城市"
          :columns="cityOptions"
          class="w-full"
        />

        <!-- Use existing sc-* components -->
        <sc-upload
          v-model="formData.avatar"
          :max-count="1"
          class="mb-4"
        />

        <!-- Tailwind utilities for layout and spacing -->
        <view class="flex justify-between items-center mt-6 space-x-4">
          <wd-button
            type="primary"
            size="large"
            @click="handleSubmit"
            class="flex-1"
          >
            提交
          </wd-button>

          <wd-button
            type="default"
            size="large"
            @click="handleReset"
            class="flex-1"
          >
            重置
          </wd-button>
        </view>
      </wd-form>

      <!-- Responsive grid using Tailwind -->
      <view class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mt-8">
        <view
          v-for="item in dataList"
          :key="item.id"
          class="bg-white rounded-lg p-4 shadow-sm hover:shadow-md transition-shadow"
        >
          <text class="text-lg font-semibold text-gray-900">{{ item.title }}</text>
          <text class="text-sm text-gray-600 mt-2">{{ item.description }}</text>
        </view>
      </view>
    </view>
  </template>
  ```
</example>

<example type="invalid">
  ```vue
  <!-- ❌ Bad: Custom components and CSS instead of using existing solutions -->
  <template>
    <view class="container">
      <!-- ❌ Creating custom input instead of using wd-input -->
      <view class="custom-input-wrapper">
        <input
          v-model="formData.name"
          placeholder="请输入用户名"
          class="custom-input"
        />
      </view>

      <!-- ❌ Custom picker instead of wd-picker -->
      <view class="custom-picker" @click="showPicker">
        <text>{{ selectedCity || '请选择城市' }}</text>
        <text class="arrow">></text>
      </view>

      <!-- ❌ Inline styles instead of Tailwind classes -->
      <view style="display: flex; justify-content: space-between; margin-top: 20px;">
        <button
          @click="handleSubmit"
          style="background: #1890ff; color: white; padding: 12px 24px; border-radius: 4px;"
        >
          提交
        </button>
      </view>
    </view>
  </template>

  <style lang="scss" scoped>
  /* ❌ Custom CSS instead of Tailwind utilities */
  .container {
    padding: 20px;
    background-color: #f5f5f5;
    min-height: 100vh;
  }

  .custom-input-wrapper {
    margin-bottom: 16px;
  }

  .custom-input {
    width: 100%;
    padding: 12px;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    font-size: 16px;
  }

  .custom-picker {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px;
    background: white;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    margin-bottom: 16px;
  }

  .arrow {
    color: #999;
    transform: rotate(90deg);
  }
  </style>
  ```
</example>
