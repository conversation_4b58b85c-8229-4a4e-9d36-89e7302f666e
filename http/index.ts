/* eslint-disable @typescript-eslint/no-non-null-assertion */
/* eslint-disable no-console */
import { http as axios } from '@shencom/request/uniapp';
import {
  IsH5,
  Platform,
  exceptionHandler,
  deployBaseUrl,
  IsILongHua,
  IsIYanTian,
  ValidateURL,
  IsDev,
} from '@rms/utils';
import BaseUserInfo from '@rms/userinfo';
import { Dictionary, type AppConfigProps } from '@rms/types';
import Register from '@rms/register';

export const Token = (token: any) => {
  const Authorization = token;
  return Authorization
    ? `${Authorization.includes('bearer ') ? '' : 'bearer '}${Authorization}`
    : null;
};

const platforms: Dictionary = {
  'mp-weixin': true,
  'mp-alipay': 'ali',
  h5: 'h5',
};

async function refreshToken(scid: string) {
  exceptionHandler(`接口401: scid: ${scid}`);

  if (uni?.scLogin) {
    uni.scLogin({
      force: true,
    });
  }
}

function getBaseUrl(scid: string, url?: string) {
  if (!url || ValidateURL(url)) {
    console.log('%c request url: ', 'color:#bc3e3e;font-weight:bold', url);
    return '';
  }
  if (IsILongHua || IsIYanTian(scid)) {
    return '/ljfl';
  }
  if (IsH5 && deployBaseUrl) {
    return `${deployBaseUrl}/sc-api`;
  }
  return '';
}

/** 获取组织 */
export async function ApiGetOrganization(organizationId?: string) {
  const register = new Register();
  const url = IsDev ? 'https://tst-app.shencom.cn' : '';
  const api = `${url}/sporadic-project/sys/role/user/organization/switch`;
  try {
    const { data } = await register.RmsHttp.post<Organization>(api, {
      // 组织id
      organizationId,
      // 1: 移动端 不传/0: 管理端
      platform: 1,
    });

    return data;
  } catch (error) {
    exceptionHandler(error);
    return undefined;
  }
}

interface Organization {
  id: string;
  name: string;
}

function GetOrganizationStorageAdapter(): Organization | null {
  if (IsH5) {
    const organization = window.sessionStorage.getItem('organization');
    return organization ? JSON.parse(organization) : null;
  }
  const register = new Register();
  return register.RmsStorage.get<Organization>('organization');
}

function SetOrganizationStorageAdapter(organization: Organization) {
  if (IsH5) {
    window.sessionStorage.setItem('organization', JSON.stringify(organization));
  } else {
    const register = new Register();
    register.RmsStorage.set('organization', organization);
  }
}
async function getOrganizationId() {
  let organization = GetOrganizationStorageAdapter();
  if (organization) return organization.id || '';

  organization = (await ApiGetOrganization()) as Organization;
  SetOrganizationStorageAdapter(organization || {});

  return organization?.id || '';
}

export function init(scid: string, UserInfo: BaseUserInfo) {
  const miniProgram = platforms[Platform || ''] || Platform;

  axios.defaults.headers.common.scid = scid;
  axios.defaults.headers.common.miniProgram = miniProgram;
  axios.defaults.headers.common.timeout = 20000;

  axios.interceptors.request.use(
    async (res) => {
      // token 处理
      const baseUrl = getBaseUrl(scid, res.url);
      res.url = baseUrl + res.url;

      const { Authorization } = res.headers!;
      if (Authorization === null || JSON.stringify(Authorization) === '{}') {
        delete res.headers?.Authorization;
      } else {
        const token = UserInfo.getToken();
        const _token = Token(Authorization || token);
        if (_token) res.headers!.Authorization = _token;
        else delete res.headers?.Authorization;
      }

      const register = new Register();

      if (
        register.RmsUserInfo.isLogin() &&
        !res.url.includes('sys/role/user/organization/switch')
      ) {
        const organizationId = await getOrganizationId();
        if (organizationId) res.headers.organizationId = organizationId;
      }

      if (register.RmsAppConfig.base.city) {
        const { provId: pid, cityId: cid } = register.RmsAppConfig.base.city || {};
        if (pid) res.headers.provId = pid;
        if (cid) res.headers.cityId = cid;
      }

      if (res.url?.includes('restapi.amap.com/v3')) {
        /* eslint-disable */
        // @ts-ignore
        res.headers = {};
      }

      // 业务处理

      console.log('%c 请求链接', 'font-size:13px; background:#336699; color:#fff;', res.url);
      console.log('%c 请求数据', 'font-size:13px; background:#336699; color:#fff;', res);

      return res;
    },
    (error) => {
      // 业务处理
      return Promise.reject(error);
    },
  );

  axios.interceptors.response.use(
    async (res) => {
      // 业务处理
      console.log('%c 响应链接', 'font-size:13px; background:#00B271; color:#fff;', res.config.url);
      console.log('%c 响应数据', 'font-size:13px; background:#00B271; color:#fff;', res);

      if (res.status !== 200) {
        uni.$emit(`response-${res.status}`);

        // token 过期清除，重新执行登录流程
        if (+res.status === 401) {
          refreshToken(scid);
        }

        return Promise.reject(res);
      }

      if (res.config.url?.includes('restapi.amap.com/v3')) {
        res.config.errcode = '0000';
        res.data.errcode = '0000';
        return res;
      }

      if (res.config.url?.includes('oss-cn-shenzhen.aliyuncs.com')) {
        res.data = '';
        return res;
      }

      return res;
    },
    (error) => {
      if (typeof error === 'object' && error.message === 'canceled') {
        return Promise.reject(error);
      }

      if (typeof error === 'object' && error.response) {
        uni.$emit(`response-${error.response.status}`);

        // token 过期清除，重新执行登录流程
        if (+error.response.status === 401) {
          refreshToken(scid);
        }
      }

      // 业务处理
      return Promise.reject(error);
    },
  );

  return axios;
}
