#!/usr/bin/env node
const chalk = require('chalk');
const { execSync } = require('child_process');

const targetName = process.argv[2];

const excludeBranch = ['develop', 'dev'];

const currentBranch = execSync('git symbolic-ref --short -q HEAD', {
  encoding: 'utf-8',
});

if (excludeBranch.some((v) => v === targetName)) {
  console.log(chalk.red(`检测到非法变基: ${targetName} ⇢ ${currentBranch}`));
  process.exit(1);
}
