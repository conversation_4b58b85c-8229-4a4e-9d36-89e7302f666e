/* eslint-disable import/no-extraneous-dependencies */
import { OSS, isPro, isTst, getCliParam, isUpload, versionExec, _argv } from '@shencom/oss-upload';
import { spawnSync } from 'child_process';
import { sync } from 'glob';
import log from '@shencom/npmlog';
import chalk from 'chalk';

export function upload(name: string, version?: string) {
  const { p, d: deploy, deployOss } = _argv;

  const isH5 = !p || p === 'h5';
  const isMp = p === 'mp-weixin';

  if (deployOss && !isH5) throw new Error('oss 独立部署仅支持 h5 项目');
  // oss 文件夹

  // 本地文件夹
  const dirPath = isH5 ? 'dist/build/h5' : 'public/static';
  const env = isMp
    ? 'cross-env UNI_OUTPUT_DIR=dist'
    : deployOss
    ? `cross-env UNI_DEPLOY_OSS=${deployOss}`
    : '';

  const command = [
    'exec',
    `${env}`,
    'uni',
    'build',
    ...getCliParam().filter((v) => !/deploy/.test(v)),
  ].filter(Boolean);

  log.info('命令 => ', chalk.yellow.bold(command.join(' ')));

  const ossPath = deployOss
    ? `plugins/scloud/${deployOss}/${name}/`
    : deploy
    ? `plugins/scloud/platform/${deploy}/${name}/`
    : `plugins${isPro ? '' : '/test'}/scloud/app/${name}/${isH5 ? '' : 'static'}`;

  async function _upload() {
    if (!isUpload) {
      const { status } = spawnSync('pnpm', command, {
        stdio: 'inherit',
        shell: true,
      });

      if (status !== 0) throw new Error('build failed');
    }

    if (isH5) {
      const _version = sync(`${dirPath}/*/`)
        .map((v) => versionExec(v)?.[1])
        .find(Boolean);

      if (!version) throw new Error('版本号文件不存在');

      spawnSync('cp', [`${dirPath}/index.html`, `${dirPath}/${_version}/index.html`]);
    } else {
      log.success(
        'build',
        chalk.green.bold(
          `✅ 打包${isPro ? '正式服' : isTst ? '测试服' : 'UAT'}成功，是否上传资源图片?`,
        ),
      );
    }

    log.info(
      'info',
      chalk.green.bold(
        `📂 [${isPro ? '正式服' : isTst ? '测试服' : 'UAT'}] OSS文件上传路径: ${ossPath}`,
      ),
    );

    const oss = new OSS({
      ossPath,
      deleteIgnore: isH5 ? [`${dirPath}/static/**`] : [],
    });

    // const h5Config = {
    //   dirPath,
    //   isClearVersion: true,
    //   versionLimit: isPro ? 10 : 1,
    //   version,
    //   diff: false,
    // };

    // const mpConfig = {
    //   dirPath,
    //   diff: false,
    // };

    oss
      .upload({
        dirPath,
        diff: true,
        version,
      })
      .then(() => {
        log.success('success', chalk.green.bold(`✅ 成功`));
      })
      .catch(() => {
        log.error('error', chalk.red.bold(`❎ 失败`));
      });
  }

  _upload();
}
