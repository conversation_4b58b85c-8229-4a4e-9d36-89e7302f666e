const { spawn } = require('child_process');
const path = require('path');
const minimist = require('minimist');
const { exit } = require('process');

const _argv = minimist(process.argv);

const modes = ['serve', 'tst', 'build'];

/** 构建模式 */
const mode = _argv._[2];

/** 模块名 */
const modeName = _argv._[3];

const command = ['-F', modeName, mode];

spawn('pnpm', command, {
  stdio: 'inherit',
  cwd: process.cwd(),
});
