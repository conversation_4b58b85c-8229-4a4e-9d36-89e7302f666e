import { CdnConfigProps } from '@rms/types';

export const defaultCndConfig: CdnConfigProps = {
  wxjssdk: {
    load: true,
    cdn: 'https://res.wx.qq.com/open/js/jweixin-1.6.0.js',
  },
  alijssdk: {
    load: true,
    cdn: 'https://gw.alipayobjects.com/as/g/h5-lib/alipayjsapi/3.1.1/alipayjsapi.inc.min.js',
  },
  aliwebviewjssdk: {
    load: false,
    cdn: 'https://appx/web-view.min.js',
  },
  amap: {
    load: false,
    cdn: 'https://webapi.amap.com/maps?v=1.4.15&key=5a440faf77903a10b09684db6fc5f859',
  },
  vConsole: {
    load: true,
    cdn: 'https://scplugins.oss-cn-shenzhen.aliyuncs.com/plugins/cdn/vConsole/3.3.4/vconsole.min.js',
    tst: true,
    dev: false,
    pro: false,
  },
  isz: {
    load: false,
    cdn: 'https://isz-open.sz.gov.cn/lib/jpasc-0.4.0.js',
  },
  echarts: {
    load: false,
    cdn: 'https://scplugins.oss-cn-shenzhen.aliyuncs.com/plugins/cdn/echarts/4.2.0-rc.2/echarts.min.js',
  },
};
