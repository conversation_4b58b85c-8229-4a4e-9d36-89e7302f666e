{"compilerOptions": {"target": "esnext", "useDefineForClassFields": true, "module": "esnext", "moduleResolution": "node", "strict": true, "jsx": "preserve", "sourceMap": true, "resolveJsonModule": true, "esModuleInterop": true, "lib": ["esnext", "dom"], "types": ["@dcloudio/types", "node", "@rms/types/base", "@rms/components/global.d.ts", "wot-design-uni/global.d.ts"], "baseUrl": "."}, "include": ["*.ts", "./**/*.ts", "*.d.ts", "./**/*.d.ts"]}