import { presetIcons, defineConfig } from 'unocss';
import componentsSafeList from '@rms/components/safelist';
import { resolve } from 'path';
import { existsSync } from 'fs';
import { pathToFileURL } from 'url';

const isMp = process.env.UNI_PLATFORM === 'mp-weixin';

/** 获取子模块 safelist */
async function getTargetSafeList() {
  try {
    const targetSafeListPath = resolve(process.cwd(), 'safelist.ts');

    if (!existsSync(targetSafeListPath)) return [];

    const configPath = pathToFileURL(resolve(targetSafeListPath)).toString();
    const res = (await import(configPath)).default;
    return res;
  } catch (error) {
    console.log(error);
    return [];
  }
}

async function config() {
  const targetSafeList = await getTargetSafeList();

  const getSafelist = () => {
    return [...componentsSafeList, ...targetSafeList];
  };

  return defineConfig({
    safelist: isMp ? getSafelist() : [],
    content: {
      pipeline: {
        exclude: [
          /node_modules\/wot-design-uni($|\/)/,
          /\.(css|postcss|sass|scss|less|stylus|styl)($|\?)/,
        ],
      },
    },
    presets: [
      presetIcons({
        extraProperties: {
          display: 'inline-block',
          'vertical-align': 'middle',
          color: 'none',
        },
        warn: false,
        scale: 1.2,
      }),
    ],
  });
}

export default config();
