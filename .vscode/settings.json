{"editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit"}, "editor.defaultFormatter": "esbenp.prettier-vscode", "editor.quickSuggestions": {"strings": true}, "files.eol": "\n", "files.associations": {"pages.json": "jsonc", "manifest.json": "jsonc"}, "eslint.alwaysShowStatus": true, "eslint.lintTask.enable": true, "eslint.validate": ["typescript", "typescriptreact", "javascript", "javascriptreact", "vue"], "eslint.options": {"extensions": [".js", ".jsx", ".ts", ".tsx", ".vue"]}, "javascript.format.enable": false, "typescript.format.enable": false, "typescript.validate.enable": true, "typescript.tsdk": "node_modules/typescript/lib", "[vue]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[jsonc]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[dotenv]": {"editor.defaultFormatter": null}, "scss.lint.unknownAtRules": "ignore", "[properties]": {"editor.defaultFormatter": null}, "[wxml]": {"editor.defaultFormatter": "qiu8310.minapp-vscode"}, "[javascript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[markdown]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "git.detectSubmodulesLimit": 50, "[typescript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}}