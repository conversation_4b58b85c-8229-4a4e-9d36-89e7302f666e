{"name": "recycle-uni", "version": "0.0.2", "scripts": {"serve": "node scripts/serve.js serve", "test": "node scripts/serve.js tst", "build": "node scripts/serve.js build", "sub": "pnpm sc submodule", "oss": "pnpm sc oss config", "prepare": "husky install", "format": "prettier -w './src/**/*.{vue,ts,tsx}'", "lint": "eslint ./src --ext .vue,.js,.ts,.jsx,.tsx --fix", "commit": "git-cz", "release": "standard-version", "preinstall": "node scripts/preinstall.js"}, "lint-staged": {"*.{vue,ts,tsx}": ["prettier -w", "eslint --fix"]}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "engines": {"node": ">=16.14.0", "pnpm": ">7.0.0"}, "dependencies": {"@dcloudio/uni-app": "3.0.0-4020420240722002", "@dcloudio/uni-app-plus": "3.0.0-4020420240722002", "@dcloudio/uni-components": "3.0.0-4020420240722002", "@dcloudio/uni-h5": "3.0.0-4020420240722002", "@dcloudio/uni-mp-alipay": "3.0.0-4020420240722002", "@dcloudio/uni-mp-baidu": "3.0.0-4020420240722002", "@dcloudio/uni-mp-kuaishou": "3.0.0-4020420240722002", "@dcloudio/uni-mp-lark": "3.0.0-4020420240722002", "@dcloudio/uni-mp-qq": "3.0.0-4020420240722002", "@dcloudio/uni-mp-toutiao": "3.0.0-4020420240722002", "@dcloudio/uni-mp-weixin": "3.0.0-4020420240722002", "@dcloudio/uni-quickapp-webview": "3.0.0-4020420240722002", "@dcloudio/uni-ui": "^1.4.22", "@rms/api": "workspace:*", "@rms/cdn": "workspace:*", "@rms/components": "workspace:*", "@rms/isz": "workspace:*", "@rms/login": "workspace:*", "@rms/register": "workspace:*", "@rms/router": "workspace:*", "@rms/service": "workspace:*", "@rms/style": "workspace:*", "@rms/userinfo": "workspace:*", "@rms/utils": "workspace:*", "@shencom/api": "1.12.0", "@shencom/plugins": "^1.8.3", "@shencom/request": "1.5.1", "@shencom/utils": "^1.13.3", "@vue/shared": "3.4.21", "clipboard": "^2.0.11", "crypto-js": "^4.2.0", "jsencrypt": "^3.3.2", "lodash-es": "^4.17.21", "pinia": "2.0.36", "vue": "3.4.21", "weapp-tailwindcss-webpack-plugin": "1.3.4", "wot-design-uni": "^1.3.10"}, "devDependencies": {"@commitlint/cli": "^16.2.3", "@commitlint/config-conventional": "^16.2.1", "@dcloudio/types": "3.4.11", "@dcloudio/uni-automator": "3.0.0-4020420240722002", "@dcloudio/uni-cli-shared": "3.0.0-4020420240722002", "@dcloudio/vite-plugin-uni": "3.0.0-4020420240722002", "@iconify/json": "^2.1.28", "@rms/types": "workspace:*", "@shencom/cli": "^1.4.0", "@shencom/oss-upload": "^2.6.10", "@types/amap-js-api": "^1.4.10", "@types/amap-js-api-geocoder": "^1.4.1", "@types/amap-js-api-geolocation": "^1.4.1", "@types/amap-js-api-place-search": "^1.4.1", "@types/crypto-js": "^4.2.1", "@types/lodash-es": "^4.17.6", "@types/node": "^17.0.23", "@types/rollup-plugin-visualizer": "^4.2.1", "@typescript-eslint/eslint-plugin": "^5.18.0", "@typescript-eslint/parser": "^5.18.0", "@uni-helper/vite-plugin-uni-components": "^0.0.8", "@vitejs/plugin-legacy": "^3.0.1", "autoprefixer": "^10.4.4", "commitizen": "^4.2.4", "cz-conventional-changelog": "^3.3.0", "eslint": "^8.13.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-import": "^2.26.0", "eslint-plugin-prettier": "^4.0.0", "eslint-plugin-vue": "^8.6.0", "husky": "^7.0.4", "lint-staged": "^12.3.7", "postcss-pxtorem": "^6.0.0", "postcss-rem-to-responsive-pixel": "^5.1.1", "prettier": "^2.6.2", "rollup-plugin-visualizer": "^5.6.0", "sass": "^1.50.0", "standard-version": "^9.3.2", "tailwindcss": "^3.4.10", "tsx": "^4.6.1", "typescript": "5.3.2", "unocss": "0.58.9", "unplugin-auto-import": "^0.17.5", "vite": "5.2.8", "vite-plugin-restart": "^0.4.1"}}