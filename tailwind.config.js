const colors = require('tailwindcss/colors');
const plugin = require('tailwindcss/plugin');

const icons = [...Array(90)]
  .map((v, i) => 10 + i)
  .reduce((pre, cur) => {
    pre[`.icon-${cur}`] = {
      width: `${cur * 2}rpx`,
      height: `${cur * 2}rpx`,
    };
    return pre;
  }, {});

const deprecatedColors = ['lightBlue', 'warmGray', 'trueGray', 'coolGray', 'blueGray'];
const colorsDefault = Object.keys(colors).reduce((pre, cur) => {
  if (deprecatedColors.includes(cur)) return pre;
  if (!colors[cur][500] || pre[cur]) return pre;
  pre[cur] = { DEFAULT: colors[cur][500] };
  return pre;
}, {});

const ellipsis = [...Array(10)]
  .map((v, i) => 1 + i)
  .reduce((pre, cur) => {
    pre[`.ellipsis-${cur}`] = {
      overflow: 'hidden',
      'text-overflow': 'ellipsis',
      'word-break': 'break-all',
      display: '-webkit-box',
      '-webkit-line-clamp': `${cur}`,
      '-webkit-box-orient': 'vertical',
    };
    return pre;
  }, {});

const fontSize = [...Array(50)]
  .map((v, i) => 10 + i)
  .reduce((pre, cur) => {
    pre[cur] = cur * 2 + 'rpx';
    return pre;
  }, {});

/** @type {import('@types/tailwindcss/tailwind-config').TailwindConfig} */
module.exports = {
  content: [
    './index.html',
    './src/**/*.{vue,js,ts,jsx,tsx}',
    '../../packages/**/*.{vue,js,ts,jsx,tsx}',
  ],
  // separator: '__',

  theme: {
    extend: {
      fontSize,
      colors: {
        ...colorsDefault,
        regular: colors.gray[800],
        secondary: colors.gray[500],
        placeholder: colors.gray[400],
      },
    },
  },

  plugins: [
    plugin(({ addUtilities }) => {
      addUtilities(icons);
    }),
    plugin(({ addUtilities }) => {
      addUtilities(ellipsis);
    }),
    plugin(({ addUtilities }) => {
      addUtilities({
        '.bg-base': {
          'background-color': colors.gray[100],
        },
      });
    }),
  ],
  corePlugins: {
    // 兼容小程序，将带有 * 选择器的插件禁用
    preflight: false,
    // space: false,
    // divideColor: false,
    // divideOpacity: false,
    // divideStyle: false,
    // divideWidth: false,
  },
};
