import { parse } from '@vue/compiler-sfc';
import { type Plugin } from 'vite';
import { readFileSync } from 'fs';
import { resolve } from 'path';
import { MainConfig, SubConfig, Route } from '@rms/router';

// 读取对应子模块的 pages.json
function getPages() {
  const content = readFileSync(resolve(process.cwd(), 'src/pages.json'), 'utf-8');
  const jsonWithoutComments = content.replace(/\/\/.*|\/\*[\s\S]*?\*\//g, '');

  return JSON.parse(jsonWithoutComments);
}

export function getMainPages() {
  const pages = getPages();
  return (pages.pages as MainConfig[]).map((v) => ({
    path: `/${v.path}`,
    title: v?.style?.navigationBarTitleText || '-',
    meta: v.meta || {},
    name: v.name,
  }));
}

export function getSubPackages() {
  // const pages = import(`${resolve(process.cwd(), 'src/pages.json')}`).
  const pages = getPages();

  return (pages.subPackages as SubConfig[]).flatMap((v) =>
    v.pages.map((e) => ({
      path: `/${v.root}/${e.path}`,
      title: e?.style?.navigationBarTitleText || '',
      meta: e.meta || {},
      name: e.name,
    })),
  );
}

/** 合并路由对象 */
export function initRoutes() {
  const mainPages = getMainPages();
  const subPackages = getSubPackages();
  const routes: Route[] = mainPages.concat(subPackages);

  return routes;
}

function isPageRoute(id: string) {
  const routes = initRoutes();
  return routes.some((r) => id.endsWith(`${r.path}.vue`));
}

/** 获取 onShow, getCurrentInstance 导入情况 */
function getUniappAndVueImportApi(code: string) {
  const regexUniapp = /import\s*{\s*([^}]+)\s*}\s*from\s*'@dcloudio\/uni-app'/;
  const regexVue = /import\s*{\s*([^}]+)\s*}\s*from\s*'vue'/;
  const matchUniapp = code.match(regexUniapp);
  const matchVue = code.match(regexVue);

  let res: string[] = [];

  if (matchUniapp && matchUniapp[1]) {
    const exportedContent = matchUniapp[1].split(',').map((item) => item.trim());
    res = [...res, ...exportedContent];
  }

  if (matchVue && matchVue[1]) {
    const exportedContent = matchVue[1].split(',').map((item) => item.trim());
    res = [...res, ...exportedContent];
  }

  return res;
}

/** 补充 onShow, getCurrentInstance 导入 */
function getNeedImportApi(code?: string) {
  if (!code) return '';

  const needAai = ['getCurrentInstance', 'onShow'];
  const apis = getUniappAndVueImportApi(code);

  if (needAai.every((api) => !apis.includes(api))) {
    return `
      import { useLogin } from '@rms/router';
      import { onShow } from '@dcloudio/uni-app';
      import { getCurrentInstance } from 'vue';
  `;
  }

  if (needAai.every((api) => apis.includes(api))) return '';

  if (apis.includes('onShow'))
    return `
  import { useLogin } from '@rms/router';
  import { getCurrentInstance } from 'vue';
  `;

  return `
  import { useLogin } from '@rms/router';
  import { onShow } from '@dcloudio/uni-app';
`;
}

function replace(ast: ReturnType<typeof parse>) {
  const template = ast.descriptor.template;
  const scriptSetup = ast.descriptor.scriptSetup;
  const script = ast.descriptor.script;
  const styles = ast.descriptor.styles;

  /* eslint-disable */
  const str = 'uni.$emit(`__login_begin__${uid}`)';

  const extCode = `onShow(() => {
    const route = getCurrentInstance()?.proxy?.route || getCurrentInstance()?.__route__;
    const uid = getCurrentInstance()?.uid;
    const instance = getCurrentInstance();
    if(!route || !uid) return;

    uni.scLogin = (options) => {
      instance.data.__login__ = true;

      const { remove } = useLogin(uid);

      remove({
        url: options?.url || route,
        force: options?.force,
        refresh: options?.refresh,
      });
      ${str}
    };

    uni.scLoginRefresh = (options = {}) => {
      uni.scLogin({ ...options, force: true, refresh: true })
    }
  })`;

  const extScript = script
    ? `
    <script ${scriptSetup?.lang ? `lang=${scriptSetup.lang}` : ''}>
      ${script.content || ''}
    </script>
  `
    : '';

  const styleCode = styles
    .map(
      (s) =>
        `<style ${s.scoped ? 'scoped' : ''} ${s.lang ? `lang=${s.lang}` : ''}>
  ${s.content}
</style>`,
    )
    .join('\n');

  return `<script setup ${scriptSetup?.lang ? `lang=${scriptSetup.lang}` : ''}>
  ${getNeedImportApi(scriptSetup?.content)}
  ${scriptSetup?.content}
  ${extCode}
</script>
${extScript}
<template>
  <template v-if="__init__">
    <view v-show="__init__ && !__login__">
    ${template?.content}
    </view>
    <sc-btn-back></sc-btn-back>
  </template>
  <GlobalLogin v-if="!__init__ || __login__"></GlobalLogin>
</template>
${styleCode}`;
}

export function sfc(): Plugin {
  return {
    name: 'vite-sfc',
    transform: (code, id) => {
      if (isPageRoute(id)) {
        // 解析模板
        const ast = parse(code);

        // 找到 script 标签
        const scriptNode = ast.descriptor.scriptSetup || ast.descriptor.script;

        // const template = ast.descriptor.template;
        // const scriptSetup = ast.descriptor.scriptSetup;
        //   const script = ast.descriptor.script;

        if (scriptNode) {
          // 在 script 标签末尾添加一段额外的代码
          const resCode = replace(ast);

          // 转换为代码字符串并返回
          return {
            code: resCode,
            map: null,
          };
        }
      }

      return {
        code,
        map: null,
      };
    },
  };
}
