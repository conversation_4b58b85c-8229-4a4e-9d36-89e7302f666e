/* eslint-disable import/no-extraneous-dependencies */
import {
  ViteWeappTailwindcssPlugin,
  postcssWeappTailwindcssRename,
} from 'weapp-tailwindcss-webpack-plugin';
import autoprefixer from 'autoprefixer';
import tailwindcss from 'tailwindcss';
import remToRpx from 'postcss-rem-to-responsive-pixel';
import pxtorem from 'postcss-pxtorem';

const isH5 = process.env.UNI_PLATFORM === 'h5';

const postcssPlugins: any[] = [
  autoprefixer(),
  tailwindcss(),
  ...(isH5
    ? [pxtorem({ rootValue: 16, propList: ['*'], exclude: /uni-datetime-picker/i })]
    : [postcssWeappTailwindcssRename()]),
  remToRpx({ rootValue: 32, propList: ['*'], transformUnit: 'rpx' }),
].filter(Boolean);

export function postcssConfig() {
  return postcssPlugins;
}

export function vwt() {
  return ViteWeappTailwindcssPlugin();
}
