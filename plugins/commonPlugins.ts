/* eslint-disable import/no-extraneous-dependencies */
import { defineConfig, UserConfigExport } from 'vite';
import { resolve } from 'path';
import uni from '@dcloudio/vite-plugin-uni';
import { _argv } from '@shencom/oss-upload';
import ScOssBase from '@shencom/utils-oss';
import { merge } from 'lodash';
import {
  projectConfig,
  postcssConfig,
  vwt,
  unocssConfig,
  visualizerConfig,
  legacyCofig,
  sfc,
  AutoImportComponents,
} from '.';
import AutoImport from 'unplugin-auto-import/vite';
import chalk from 'chalk';
import log from '@shencom/npmlog';
import ViteRestart from 'vite-plugin-restart';

const isH5 = process.env.UNI_PLATFORM === 'h5';
const isMp = process.env.UNI_PLATFORM === 'mp-weixin';

/** 构建环境 */
const Env = _argv.mode as 'tst' | 'production' | 'uat';
const isPro = Env === 'production';
const isTst = Env === 'tst';
const Mode = _argv._[2] as 'build' | 'serve';
const isBuild = Mode === 'build';
const deploy = isPro && _argv.d;

const uniConfig: Parameters<typeof uni>[0] = isH5
  ? {
      vueOptions: {
        template: {
          compilerOptions: {
            isCustomElement: (tag) => {
              return tag.startsWith('wx-open');
            },
          },
        },
      },
    }
  : {};

interface PluginConfig {
  name: string;
  version: string;
  config?: UserConfigExport;
}

export function commonPlugins(pluginConfig: PluginConfig) {
  const { name, version, config = {} } = pluginConfig;

  const deployOss = isH5 && process.env.UNI_DEPLOY_OSS;

  const oss = new ScOssBase({
    env: isTst ? 'tst' : isPro ? 'pro' : 'uat',
    projectPath: `${deployOss || 'app'}/${name}/`,
  });

  if (deploy && deploy === 'true') {
    log.error('', chalk.red.bold('请设置独立部署的目录，如: -d futian-h5'));
    process.exit(1);
  }

  const baseConfig: UserConfigExport = {
    /** 开发或生产环境的公共基础路径 */
    base: isBuild && isH5 && !deploy ? oss.path : './',
    optimizeDeps: {
      include: ['@shencom/utils'],
    },
    resolve: {
      alias: {
        '@': resolve(__dirname, 'src'),
        '@shencom/api': resolve(process.cwd(), 'node_modules/@shencom/api'),
      },
    },

    define: isH5
      ? {
          'process.env': {
            ...process.env,
            UNI_DEPLOY: deploy,
            UNI_DEPLOY_OSS: deployOss,
          },
        }
      : undefined,

    /** 打包配置 */
    build: {
      sourcemap: false,
      assetsDir: version,
    },

    /** 静态资源服务的文件夹 */
    publicDir: 'public',

    /** 插件 */
    plugins: [
      /** 自动导入组件 */
      AutoImportComponents(),
      /** 添加额外代码 */
      AutoImport({
        include: [/\.[tj]sx?$/, /\.vue$/, /\.vue\?vue/, /\.md$/],
        imports: [
          // 插件预设支持导入的api
          'vue',
          'uni-app',
        ],
        dts: false,
      }),
      sfc(),
      uni(uniConfig),

      ViteRestart({
        // 更改 packages 文件需要 restart 才生效
        restart: ['../../packages/**'],
      }),

      /** 小程序兼容 tailwindcss */
      {
        ...vwt(),
        apply: () => isMp,
      },

      /** ucocss 配置 */
      unocssConfig(),

      /** legacy */
      legacyCofig(),

      /** visualizer */
      {
        ...visualizerConfig(),
        apply: (c, evn) => evn.mode === 'production' && false,
      },

      /** 小程序开发者工具配置处理 */
      projectConfig(),
    ],

    /** css配置 */
    css: {
      postcss: {
        plugins: postcssConfig(),
      },
      preprocessorOptions: {
        scss: {
          // 去除弃用警告
          silenceDeprecations: ['legacy-js-api', 'color-functions'],
        },
      },
    },
  };

  return defineConfig(merge(baseConfig, config));
}
