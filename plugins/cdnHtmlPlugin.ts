import path from 'node:path';
import fs from 'node:fs';
import { type Plugin, loadEnv } from 'vite';
import minimist from 'minimist';
import crypto from 'crypto';

// TODO
const jsTpl = (url: string, integrity: string) =>
  `<script src="${url}" integrity="${integrity}" crossorigin="anonymous"></script>`;
const cssTpl = (url: string) => `<link href="${url}" rel="stylesheet">`;

interface Module {
  name: string;
  version?: string;
  cdn?: string;
  handler?: (cdn: string) => string;
}

interface Options {
  modules: Module[];
  css?: string[];
}

const _argv = minimist(process.argv);

const env = loadEnv(_argv.mode || process.env.NODE_ENV, process.cwd());
const isDev = !!env.VITE_APP_IS_DEV;
const isH5 = process.env.UNI_PLATFORM === 'h5';

function getModuleVersion(name: string) {
  const pwd = process.cwd();
  const pkgFile = path.resolve(pwd, '../..', 'node_modules', name, 'package.json');
  if (fs.existsSync(pkgFile)) {
    const pkgJson = JSON.parse(fs.readFileSync(pkgFile, 'utf8'));

    return pkgJson.version;
  }

  return '';
}

/**
 * 生成 SRI 哈希值，用于校验资源完整性。
 * 文档：https://developer.mozilla.org/zh-CN/docs/Web/Security/Subresource_Integrity
 */
const getSRIHash = (filePath: string) => {
  const ALG = 'sha384'; // 算法
  const content = fs.readFileSync(filePath, 'utf-8');
  const hash = crypto.createHash(ALG);
  hash.update(content);
  const hashString = hash.digest('base64'); // 十六进制哈希值
  return `${ALG}-${hashString}`;
};

function handlerCdnPath(item: Module) {
  const cdn = item.cdn || `https://cdn.jsdelivr.net/npm/${item.name}`;
  const version = item.version || getModuleVersion(item.name);
  return item.handler ? item.handler(`${cdn}@${version}`) : `${cdn}@${version}`;
}

function cdnHTMLConfig(options: Options): Plugin[] {
  const jsCdnList = options.modules.map(handlerCdnPath);
  const jsCode = jsCdnList.map((url) => jsTpl(url, '')).join('\n');

  const cssCode = options.css
    ?.map((item) => item)
    .map(cssTpl)
    .join('\n');

  if (!isH5 || isDev) return [];

  const plugins: Plugin[] = [
    {
      name: 'cdn-html-plugin',
      transformIndexHtml(html) {
        html = jsCode ? html.replace(/<\/title>/i, `</title>\n${jsCode}`) : html;
        html = cssCode ? html.replace(/<title>/i, `${cssCode}\n<title>`) : html;
        console.log('%c [html]-62', 'font-size:13px; background:#336699; color:#fff;', html);

        return html;
      },
    },
  ];

  return plugins;
}

export { cdnHTMLConfig };
