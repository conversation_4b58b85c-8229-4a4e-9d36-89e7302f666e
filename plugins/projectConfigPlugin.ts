/* eslint-disable import/no-extraneous-dependencies */
import { type Plugin, loadEnv } from 'vite';
import { readFileSync, writeFile } from 'fs';
import minimist from 'minimist';

const _argv = minimist(process.argv);

const env = loadEnv(_argv.mode || process.env.NODE_ENV, process.cwd());
const isDev = !!env.VITE_APP_IS_DEV;
const isMp = process.env.UNI_PLATFORM === 'mp-weixin';

export function projectConfig(): Plugin {
  return {
    name: 'vite-plugin-project-config',
    closeBundle() {
      try {
        if (!isMp) return;

        const file = readFileSync('dist/project.config.json', 'utf8');
        const obj = JSON.parse(file);

        if (!obj.packOptions.ignore.length) return;

        if (isDev) {
          obj.packOptions.ignore = [];

          writeFile('dist/project.config.json', JSON.stringify(obj), () => null);
        }

        obj.srcMiniprogramRoot = 'dist/';
        obj.miniprogramRoot = 'dist/';
        writeFile('project.config.json', JSON.stringify(obj), () => null);
      } catch (error) {
        console.log('error>>>', error);
      }
    },
  };
}
