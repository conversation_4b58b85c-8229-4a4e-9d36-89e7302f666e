import Components, { kebabCase } from '@uni-helper/vite-plugin-uni-components';

export function AutoImportComponents() {
  return Components({
    dts: false,
    resolvers: [
      {
        type: 'component',
        resolve: (n: string) => {
          if (n.match(/^Wd[A-Z]/)) {
            const compName = kebabCase(n);
            return {
              name: n,
              from: `wot-design-uni/components/${compName}/${compName}.vue`,
            };
          }

          return undefined;
        },
      },
      {
        type: 'component',
        resolve: (n: string) => {
          if (n.match(/^Sc[A-Z]/)) {
            const compName = kebabCase(n);
            return {
              name: n,
              from: `@rms/components/${compName}/${compName}.vue`,
            };
          }

          return undefined;
        },
      },
      {
        type: 'component',
        resolve: (n: string) => {
          if (n.match(/^Uni[A-Z]/)) {
            const compName = kebabCase(n);
            return {
              name: n,
              from: `@dcloudio/uni-ui/lib/${compName}/${compName}.vue`,
            };
          }

          return undefined;
        },
      },
      {
        type: 'component',
        resolve: (n: string) => {
          if (n === 'GlobalLogin') {
            return {
              name: n,
              from: '@rms/login/index.vue',
            };
          }

          return undefined;
        },
      },
    ],
  });
}
