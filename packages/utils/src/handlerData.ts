/**
 * 处理金额格式
 *
 * @param {String,Number} val 金额
 * @param {Number} decimalLength 小数点位数(默认:2)
 * @example
 * ```js
 * ToMoney(1000) => 1,000.00
 * ToMoney(1000, 2) => 1,000.00
 * To<PERSON>oney(1000, 0) => 1,000
 * ```
 * @returns {string} 返回的是字符串23,245.12保留2位小数
 */
const ToMoney = (val: string | number, len = 2) => {
  if (val === 0 || len < 0) return String(val);

  let number: number;

  if (typeof val === 'number') {
    number = val;
  } else if (typeof val === 'string') {
    number = Number(val);
  } else {
    return val;
  }

  if (!val) return val;
  if (number === 0 || len < 0) return String(number);

  const isNegative = number < 0;

  if (isNegative) number *= -1;

  let money = number.toLocaleString();
  const [int, decimal = ''] = money.split('.');

  if (len <= 0) {
    money = int;
  } else if (len < decimal.length && decimal.length - len > 0) {
    money = `${int}.${decimal.slice(0, len)}`;
  } else if (len > decimal.length) {
    money = `${int}.${decimal.padEnd(len, '0')}`;
  }

  if (isNegative) money = `-${money}`;

  return money;
};

export const toKmUnit = (m: number | string) => {
  const value = Number(m);

  if (Number.isNaN(value)) return '未知';

  if (value >= 1000) return `${ToMoney(value / 1000)}km`;

  return `${ToMoney(value)}m`;
};

/**
 * 头部补零
 *
 * @param {number} num - 需补 0 的数值
 * @param {number} size - 加上 0 位的位数
 * @example
 * ```
 *  StartFillZero(15, 3) ==> '150'
 * ```
 * @returns {string}
 */
const StartFillZero = (num: number, size: number) => num.toString().padStart(size, '0');

/**
 * 尾部补零
 *
 * @param {number} num - 需补 0 的数值
 * @param {number} size - 加上 0 位的位数
 * @example
 * ```
 *  EndFillZero(15, 3) ==> '015'
 * ```
 * @returns {string}
 */
const EndFillZero = (num: number, size: number) => num.toString().padEnd(size, '0');

/**
 * 隐藏手机号中间信息
 *
 * @param {string} val
 * @example
 * ```
 * HidePhoneNumber('13818341008') ==> '138****1008'
 * ```
 * @returns {string}
 */
// const HidePhoneNumber = (val: string) => val.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2');

/**
 * 隐藏身份证中间信息
 *
 * @param {string} val
 * @example
 * ```
 *  HideIdCard('110101199003075736') ==> '1101******5736'
 * ```
 * @returns {string}
 */
// const HideIdCard = (val: string) => val.replace(/^(.{4})(?:\d+)(.{4})$/, '$1******$2');

const GetUrlKey = (name: string, href: string) => {
  const reg = new RegExp(`[?|&]${name}=([^&;]+?)(&|#|;|$)`);
  return (reg && decodeURIComponent((reg.exec(href) || ['', ''])[1].replace(/\+/g, '%20'))) || null;
};

const GetUrlParam = (href: string) => {
  const result: Record<string, string> = {};
  if (!/\?/.test(href)) return result;

  const urlString = href.substring(href.indexOf('?') + 1);
  const urlArray = urlString.split('&');

  for (let i = 0, len = urlArray.length; i < len; i++) {
    const urlItem = urlArray[i];
    const item = urlItem.split('=');
    result[item[0]] = item[1];
  }

  return result;
};

export { ToMoney, StartFillZero, EndFillZero, GetUrlKey, GetUrlParam };

/** 解析路由字段 */
export const formatNewsUrlParams = (newsUrl: string) => {
  const newsUrlArr = newsUrl.split('/');
  let newsUrlArrIndex = -1;
  const reg = /^[0-9]+$/;
  for (let i = 0; i < newsUrlArr.length; i += 1) {
    if (reg.test(newsUrlArr[i])) {
      newsUrlArrIndex = i;
      break;
    }
  }
  if (newsUrlArrIndex === -1) return [];
  return newsUrlArr.slice(newsUrlArrIndex, newsUrlArrIndex + 4).map((item) => item.split('.')[0]);
};

interface ResRegion {
  id: string;
  pId: string;
  title: string;
  children?: ResRegion[];
}

export const handleRegionData = (data: string[], region: ResRegion[]) => {
  const [regionPid, regionId, regionCid] = data;
  const district = region.find((v) => v.id === regionPid);
  if (!regionId || !district) return (district && [district]) || [];

  const street = district.children && district.children.find((v) => v.id === regionId);
  district.children = (street && [street]) || [];
  if (!regionCid || !street) return (district && [district]) || [];

  const village = street.children && street.children.find((v) => v.id === regionCid);
  district.children[0].children = (village && [village]) || [];
  return (district && [district]) || [];
};
