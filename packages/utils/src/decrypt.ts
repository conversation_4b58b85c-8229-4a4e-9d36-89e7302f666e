import JSEncrypt from 'jsencrypt';
import CryptoJs from 'crypto-js';

export const decrypt = (password: string, key?: string) => {
  const _key =
    key ||
    'MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBAKlcGLXuGzttw7LCk+7zREPi/EwkAOduwu+ZPovYoInsq3ONsOTdc4LsrlwY87TzwwFxunxB8h2D5wA4xSJX21GjRaIrYyYIyFshVvD3RsxW2ELyGtlC81bXa6NW8mW+ujnbpIKmDv0SB05zGaI6vKrzEviQLzh/ROb3TcW2usLnAgMBAAECgYBRJ1x/8TUDaTmZczvICLHxhdc39RipzZ2Din86K/fhn5MYsBDP2n37gsaaQyqkzxnmm0StPjxygD41EP8F0eu5jfWCC4svlmWGVif5mZ/cbR/LcVnOO90kq2QoOyK+iPCR14S30sL31pjHs4JpUBOrFuKD6NGfhD8UF2PxVgH3YQJBAOqbYwx0L/YBnso+FqMoyN+9ITnSD+ZI2uTJHbQiksGv9qPr99CW+r7wy0zo1DFfT0DPKHcLNzotVtKyTKP2U7UCQQC4zZfFg6ACfwa4WIlzDLcGk4u/3yRrq57/DS8BDifeKxNRGeFpF4kT7CtV2GZTNlWDQ8HrYU3RDdfkPX4k5xWrAkEAyBzErtHIOyGErWOqcX+PX/D2D1jTgQ0x0SaPNKCs55NNYOMc3Yp61TWVY8PRg0+VzWUmUex6LiCQR2YYo16wZQJAfltEbK5E17AW3jDbOij1c6+qm/RsqJndyrIu81/EbqmxOYCimQKYnWCbeMTPohHCgk/v5YrQdCIs/Uf2WjEOpQJAbDkhHlWyRaDojVeQLsnAmSBkkk2vhaq3JEJmBQBuK/9y+6bj2SyiNcmPeBEKpl0POVd4ML4iw3iFk2lmqUHj7g==';
  try {
    const crypt = new JSEncrypt();
    crypt.setKey(_key);
    const crypted = crypt.decrypt(password);
    return crypted;
  } catch (ex) {
    return false;
  }
};

export function cryptoAes(encryptKey: string) {
  const key = CryptoJs.enc.Utf8.parse(encryptKey);
  return {
    /** 解密 */
    decrypt(word: string) {
      return CryptoJs.AES.decrypt(word, key, { mode: CryptoJs.mode.ECB }).toString(
        CryptoJs.enc.Utf8,
      );
    },
    /** 加密 */
    encrypt(word: string) {
      const wordc = CryptoJs.enc.Utf8.parse(word);
      return CryptoJs.AES.encrypt(wordc, key, { mode: CryptoJs.mode.ECB }).toString();
    },
  };
}
