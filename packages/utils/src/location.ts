import {
  Dialog,
  IsH5,
  IsIShenzhen,
  IsWeixinH5,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  exceptionHand<PERSON>,
} from '@rms/utils';
import { ReturnPromiseType } from '@shencom/typing';
import { ApiAMapGeocodeRegeo } from '@shencom/api';
import Register from '@rms/register';

// #ifdef H5
import { AmpGeocoder } from '@rms/utils';
import IShenzhen from '@rms/isz';
// #endif

interface CustomOptions {
  onPhoneLocationEnabled?: (...args: any) => void;
  onOpenLocationSetting?: (isSuccess: boolean) => void;
  leftConfirm?: boolean;
  dialogOptions?: UniApp.ShowModalOptions & {
    wxCon?: string;
    phoneCon?: string;
    mpCon?: string;
  };
}

type OptionsType = UniApp.GetLocationOptions & CustomOptions;

let _cacheLocation: ReturnPromiseType<typeof getLocationInfo> | null = null;
let currentIsContrary = false;
let timer: NodeJS.Timeout | null = null;

const defaultOptions = {
  onPhoneLocationEnabled: () => null,
  onOpenLocationSetting: () => null,
  dialogOptions: {} as CustomOptions['dialogOptions'],
};

/**
 *
 * @param isContrary 是否逆解析
 * @returns 位置信息，逆解析结果
 */
export function getLocationInfo(
  isContrary = false,
  options: OptionsType = defaultOptions,
): Promise<{
  res: UniApp.GetLocationSuccess;
  data?: ReturnPromiseType<typeof ApiAMapGeocodeRegeo>;
} | null> {
  return new Promise(async (resolve, reject) => {
    Loading.hide();

    if (currentIsContrary !== isContrary) {
      _cacheLocation = null;
    }

    if (timer) {
      clearTimeout(timer);
    }

    timer = setTimeout(() => {
      _cacheLocation = null;
    }, 6000);

    if (_cacheLocation) {
      resolve(_cacheLocation);
      return;
    }

    const {
      onPhoneLocationEnabled = defaultOptions.onPhoneLocationEnabled,
      dialogOptions = defaultOptions.dialogOptions,
      leftConfirm,
    } = options;

    const { locationEnabled, locationAuthorized } = await uni.getSystemInfoSync();

    if (!locationEnabled || !locationAuthorized) {
      if (dialogOptions?.wxCon && dialogOptions?.phoneCon) {
        dialogOptions.content = locationEnabled ? dialogOptions.wxCon : dialogOptions.phoneCon;
      }

      const flag = await Dialog(`您暂未打开"${locationEnabled ? '微信APP' : '手机'}"的定位功能。`, {
        confirmText: '继续',
        cancelText: '取消',
        ...dialogOptions,
      });

      if (leftConfirm) {
        if (flag) {
          onPhoneLocationEnabled();
          resolve(null);
        } else {
          reject(new Error('定位权限失败'));
        }
      }

      if (!leftConfirm) {
        if (flag) {
          reject(new Error('定位权限失败'));
        } else {
          onPhoneLocationEnabled();
          resolve(null);
        }
      }

      return;
    }

    Loading.show('获取最近地址中');

    uni.getLocation({
      isHighAccuracy: isContrary,
      highAccuracyExpireTime: isContrary ? 3000 : 0,
      type: 'gcj02',
      success: async (res) => {
        const { latitude, longitude } = res;

        currentIsContrary = isContrary;
        if (!isContrary) {
          resolve({ res });
          _cacheLocation = { res };
          return;
        }

        const register = new Register();

        if (!register.RmsAppConfig.base.geoServiceKey) {
          throw new Error('请配置高德地图服务key');
        }

        const data = await ApiAMapGeocodeRegeo({
          location: `${longitude},${latitude}`,
          key: register.RmsAppConfig.base.geoServiceKey,
        });

        resolve({
          res,
          data,
        });
        _cacheLocation = { res };
      },

      fail: async (err) => {
        if (err.errno === 0 || err.errMsg.includes('fail auth deny')) {
          if (dialogOptions?.wxCon && dialogOptions?.phoneCon) {
            dialogOptions.content = dialogOptions.mpCon;
          }

          const flag = await Dialog(`您暂未打开"小程序"的定位功能，请设置授权定位权限`, {
            confirmText: '去设置',
            cancelText: '继续',
            ...dialogOptions,
          });

          if (leftConfirm) {
            if (flag) {
              onPhoneLocationEnabled();
              resolve(null);
            } else {
              openLocationSetting(options);
              reject(new Error('定位权限失败'));
            }
          }

          if (!leftConfirm) {
            if (flag) {
              openLocationSetting(options);
              reject(new Error('定位权限失败'));
            } else {
              onPhoneLocationEnabled();
              resolve(null);
            }
          }
        }

        reject(err);
      },
      complete: () => {
        Loading.hide();
      },
      ...options,
    });
  });
}

const openLocationSetting = (options: OptionsType) => {
  const { onOpenLocationSetting = defaultOptions.onOpenLocationSetting } = options;
  const setting = 'scope.userLocation';
  uni.openSetting({
    success(res) {
      console.log('原生设置页回调->', res);
      const { authSetting } = res || {};
      if (authSetting && authSetting[setting]) {
        onOpenLocationSetting(true);
        console.log('已成功开启位置服务->But这里没有返回任何位置信息相关信息');
      }
    },
    fail() {
      onOpenLocationSetting(false);
      Toast.show('获取位置信息失败，按“右上菜单 - 关于\n - 右上菜单 - 设置 - 位置信息”授权');
    },
  });
};

interface LocationAdapterConfig {
  timeout?: number;
  complete?: (res: [number, number]) => void;
}

export function getLocationAdapter(
  params?: LocationAdapterConfig | ((res: [number, number]) => void),
): Promise<[number, number]> {
  // const storageKey = 'reserve_location_arr';

  return new Promise(async (resolve, reject) => {
    const { complete, timeout = 0 } =
      typeof params === 'function'
        ? { complete: params }
        : ((params || {}) as LocationAdapterConfig);

    if (!IsH5) {
      reject('非 H5 环境');
      return;
    }

    if (IsIShenzhen) {
      IShenzhen.getLocation()
        .then((res) => {
          const { lat, lng } = res;
          if (complete) complete([lng, lat]);
          resolve([lng, lat]);
        })
        .catch(reject);
      return;
    }

    let timer: ReturnType<typeof setTimeout>;

    const _GetGeoLocation = async () => {
      try {
        if (timer) clearTimeout(timer);

        const [lng, lat] = await getGeoLocation();
        console.log('%c _GetGeoLocation-245', 'font-size:13px; background:#336699; color:#fff;', [
          lng,
          lat,
        ]);

        if (complete) complete([lng, lat]);
        resolve([lng, lat]);
      } catch (error) {
        reject(error);
        console.log(
          '%c [_GetGeoLocation]-250',
          'font-size:13px; background:#336699; color:#fff;',
          error,
        );
      }
    };

    // 微信定位or高德定位
    if (IsWeixinH5) {
      try {
        uni.$wx?.ready(() => {
          uni.$wx?.getLocation({
            type: 'gcj02',
            success: (res: any) => {
              console.log('%c [res]-260', 'font-size:13px; background:#336699; color:#fff;', res);
              const { latitude, longitude } = res;
              if (complete) complete(res);
              if (timer) clearTimeout(timer);
              resolve([longitude, latitude]);
            },
            fail: async (error) => {
              exceptionHandler(error);
              _GetGeoLocation();
            },
          });

          if (timeout) {
            console.log(
              '%c [timeout]-272',
              'font-size:13px; background:#336699; color:#fff;',
              timeout,
            );
            timer = setTimeout(() => {
              exceptionHandler({ errMsg: '获取位置信息超时' });
              _GetGeoLocation();
            }, timeout);
          }
        });

        uni.$wx?.error((error) => {
          console.log('%c [error]-281', 'font-size:13px; background:#336699; color:#fff;', error);
          exceptionHandler(error);
          _GetGeoLocation();
        });
      } catch (error) {
        console.log('%c [error]-286', 'font-size:13px; background:#336699; color:#fff;', error);
        exceptionHandler(error);
        _GetGeoLocation();
      }

      return;
    }

    // 高德定位
    _GetGeoLocation();
  });
}

async function getGeoLocation() {
  const geocoder = new AmpGeocoder();
  const lngLat = await geocoder.geolocation();
  const lat = lngLat.getLat();
  const lng = lngLat.getLng();

  return [lng, lat];
}
