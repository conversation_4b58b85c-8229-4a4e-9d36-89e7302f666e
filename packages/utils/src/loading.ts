class Loading {
  private loading: boolean;

  private needLoadingRequestCount = 0;

  constructor() {
    this.loading = false;
  }

  /**
   * 显示 loading
   *
   * @param {ShowLoadingOptions} [param={}]
   * @memberof Loading
   */
  public show(param: Partial<UniApp.ShowLoadingOptions> | string = {}) {
    if (this.needLoadingRequestCount > 0) return;

    this.needLoadingRequestCount++;

    const defaultParam = { mask: true, title: '' };

    if (typeof param === 'string') defaultParam.title = param;
    else Object.assign(defaultParam, param);

    this.loading = true;

    uni.showLoading({
      ...defaultParam,
      fail: () => {
        this.loading = false;
      },
    });
  }

  /**
   * 隐藏 loading
   *
   * @memberof Loading
   */
  public hide() {
    if (this.needLoadingRequestCount <= 0 || !this.loading) return;

    this.needLoadingRequestCount--;

    if (this.needLoadingRequestCount === 0) {
      this.loading = false;
      uni.hideLoading();
    }
  }
}

export default new Loading();
