import { AmapInit } from '@rms/cdn';
import { LngLat } from '@rms/types';
import { IsH5 } from './evn';

interface InitMapOptions extends AMap.Map.Options {
  /** 把地图挂载到的 DOM 节点 `默认(#mapContainer)` */
  elm?: string;

  /** 地图加载成功回调 */
  complete?(map: AMap.Map): void;
}

interface Geolocation extends AMap.Geolocation.Options, Record<string, any> {
  onLoad?: (geolocation: AMap.Geolocation) => void;
}

interface GetLocation {
  (address: string | string[], options?: AMap.Geocoder.Options): Promise<AMap.Geocoder.Geocode[]>;
}

/** 初始化地图 */
export async function InitMap(initOptions?: InitMapOptions): Promise<AMap.Map> {
  const defalutOption = {
    resizeEnable: true,
    zoom: 13,
    elm: 'mapContainer',
  };

  try {
    await AmapInit();

    const options = { ...defalutOption, ...initOptions };

    const map = new AMap.Map(options.elm, options);

    return new Promise((resolve, reject) => {
      map.on('complete', () => {
        if (initOptions?.complete) {
          initOptions.complete(map);
        }
        resolve(map);
      });

      map.on('error', (err) => {
        reject(err);
      });
    });
  } catch (error) {
    return Promise.reject(error);
  }
}

/**
 * 高德地图地理编码服务
 */
export class AmpGeocoder {
  public geocoder: AMap.Geocoder | null = null;

  constructor() {
    if (!IsH5) {
      throw new Error('非 H5 环境');
    }
  }

  /** 使用前需要初始化 */
  private init(options?: AMap.Geocoder.Options) {
    return new Promise<AMap.Geocoder>(async (resolve, reject) => {
      if (this.geocoder && !options) return;

      if (!window.AMap) {
        try {
          await AmapInit();
        } catch (error) {
          reject(error);
          return;
        }
      }

      window.AMap.plugin('AMap.Geocoder', () => {
        this.geocoder = new AMap.Geocoder(options);
        resolve(this.geocoder);
      });
    });
  }

  /**
   * 经纬度转地址
   *
   * @param {AMap.LngLat} lnglat
   * @returns {Promise<any>}
   * @memberof GeocoderFun
   */
  public get(lnglat: AMap.LngLat) {
    return new Promise<AMap.Geocoder.ReGeocode>(async (resolve, reject) => {
      try {
        await this.init();

        if (this.geocoder) {
          this.geocoder.getAddress(lnglat, (status, result) => {
            if (status === 'complete' && typeof result !== 'string' && result.info === 'OK') {
              resolve(result.regeocode);
            } else {
              reject(status);
            }
          });
        } else {
          reject('geocoder 加载失败');
        }
      } catch (error) {
        reject(error);
      }
    });
  }

  /**
   * 地址转经纬度
   *
   * @param {string} address
   * @returns {Promise<number[]>}
   * @memberof Geocoder
   */
  public set(address: string): Promise<AMap.LngLat> {
    return new Promise(async (resolve, reject) => {
      try {
        await this.init();
        if (this.geocoder) {
          this.geocoder.getLocation(address, (status, result) => {
            if (status === 'complete' && typeof result !== 'string' && result.info === 'OK') {
              const data: any = result.geocodes[0];
              const lnglat: AMap.LngLat = new AMap.LngLat(
                data.location.getLng(),
                data.location.getLat(),
              );
              resolve(lnglat);
            } else {
              reject(status);
            }
          });
        } else {
          reject('geocoder 加载失败');
        }
      } catch (error) {
        reject(error);
      }
    });
  }

  /** 获取 Marker 对象 */
  marker<T = any>(options: AMap.Marker.Options): Promise<AMap.Marker<T>> {
    return new Promise(async (resolve, reject) => {
      if (!window.AMap) {
        try {
          await AmapInit();
        } catch (error) {
          reject(error);
          return;
        }
      }

      const marker = new AMap.Marker(options);

      resolve(marker);
    });
  }

  /** 解析 AMap.LngLat */
  getLngLat(location: AMap.LngLat): LngLat {
    const lng = location.getLng();
    const lat = location.getLat();
    return { lng, lat };
  }

  /** 浏览器获取当前定位 */
  geolocation = (options: Geolocation = {}): Promise<AMap.LngLat> =>
    new Promise(async (resolve, reject) => {
      try {
        if (!window.AMap) {
          try {
            await AmapInit();
          } catch (error) {
            reject(error);
            return;
          }
        }

        AMap.plugin('AMap.Geolocation', () => {
          const geolocation = new AMap.Geolocation({
            enableHighAccuracy: true, // 是否使用高精度定位，默认:true
            // noIpLocate: 1, // 移动端禁止 ip定位
            // noGeoLocation: 1, //移动端禁止浏览器定位
            zoomToAccuracy: true,
            timeout: 10000, // 超过10秒后停止定位，默认：无穷大
            maximumAge: 0, // 定位结果缓存毫秒，默认：0
            showCircle: false,
            ...options,
          });

          if (options.onLoad) options.onLoad(geolocation);

          geolocation.getCurrentPosition((status, result) => {
            if (status === 'complete') {
              const data = result as AMap.Geolocation.GeolocationResult;
              if ('LOCATE_SUCCESS'.includes(data.info)) {
                resolve(data.position);
                return;
              }
              reject(result);
            } else {
              reject(result);
            }
          });
        });
      } catch (error) {
        reject(error);
      }
    });

  /**
   * 根据地址描述解析经纬度
   *
   * @type {GetLocation}
   */
  getLocation: GetLocation = (address, options) =>
    new Promise(async (resolve, reject) => {
      try {
        await this.init(options);

        if (this.geocoder) {
          this.geocoder.getLocation(address, (status, result) => {
            if (status === 'complete' && typeof result !== 'string') {
              resolve(result.geocodes);
            } else {
              reject(result);
            }
          });
        } else {
          reject('geocoder 加载失败');
        }
      } catch (error) {
        reject(error);
      }
    });

  /**
   * 根据经纬度坐标解析地址信息
   *
   * @param {AMap.LocationValue} location 定位点
   * @param {AMap.Geocoder} options AMap.Geocoder 初始化配置
   * @returns {Promise<AMap.Geocoder.ReGeocode>}
   */
  getAddress = (
    location: AMap.LocationValue,
    options?: AMap.Geocoder.Options,
  ): Promise<AMap.Geocoder.ReGeocode> =>
    new Promise(async (resolve, reject) => {
      try {
        await this.init(options);

        if (this.geocoder) {
          this.geocoder.getAddress(location, (status, result) => {
            if (status === 'complete' && typeof result !== 'string') {
              resolve(result.regeocode);
            } else {
              reject(result);
            }
          });
        } else {
          reject('geocoder 加载失败');
        }
      } catch (error) {
        reject(error);
      }
    });

  /**
   * 其他坐标转高德坐标
   * @param {AMap.LocationValue} gps 经纬度
   * @param {AMap.convertFrom.Type} type 坐标服务商
   */
  convertForm(
    gps: AMap.LocationValue,
    type: AMap.convertFrom.Type = 'gps',
  ): Promise<string | AMap.LngLat[]> {
    return new Promise(async (resolve, reject) => {
      try {
        if (!window.AMap) {
          try {
            await AmapInit();
          } catch (error) {
            reject(error);
            return;
          }
        }

        AMap.convertFrom(gps, type, (status, result) => {
          if (typeof result === 'string') {
            reject(result);
          } else if (result.info === 'ok') {
            resolve(result.locations);
          }
        });
      } catch (error) {
        reject(error);
      }
    });
  }
}
