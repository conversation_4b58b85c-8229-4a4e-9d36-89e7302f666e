import { ScStorageBase } from '@shencom/utils';
import { nextTick } from 'vue';
import { pickBy } from 'lodash-es';
import { debounce } from './helpers';
import { exceptionHandler } from './error';

type StorageOption = ConstructorParameters<typeof ScStorageBase>[0] & {
  // 额外清空空间大小，实际清空大小 >= _purgeLoadMax + data.size 单位为 kb
  _purgeLoadMax: number;
  // 优先清除的数据 prefix + name
  useless: string[];
  // 不会被清除的数据
  ignore: string[];
  // 城市id
  cityId?: string;
  // storage 最大 size
  maxSize: () => number;
  // storage 当前 size
  currentSize: () => number;
};

type IgnoreTypes =
  | '_prefixMap'
  | '_prefixLru'
  | '_prefixState'
  | 'prefixData'
  | 'prefixUser'
  | 'prefixLasting';

export class StorageLRU extends ScStorageBase {
  private _localLru: StorageOption;

  private _prefixLru: string;

  private _cityId?: string;

  private _prefixState: string;

  private _prefixMap: string;

  private _ignore: string[];

  private _useless: string[];

  private _maxSize = 0;

  private _localKeys = new Map<string, string>();

  constructor(local: StorageOption) {
    super(local);
    this._clearAllExpireData();
    this._maxSize = local.maxSize();
    this._localLru = local;
    this._cityId = local.cityId;
    this._prefixLru = `sc_${local.scid}_`;
    this._prefixState = `state_`;
    this._prefixMap = `__prefix_map__`;
    this._ignore = [this._prefixMap, this.prefixUser, '__DC_STAT_UUID'].concat(local.ignore);
    this._useless = local.useless.map((item) => this._prefixLru + item);
  }

  private _prefixKey(key: string) {
    if (key.includes(this.prefixUser)) return key;

    if (this._cityId) {
      return `${this._cityId}_${key}`;
    }

    return key;
  }

  private _parseData(data: string | null, key: string) {
    try {
      const value = data ? JSON.parse(data) : null;
      if (!value) {
        return null;
      }
      if (value.time) {
        const now = new Date().getTime();
        if (Number(value.time) < now) {
          this.remove(key);
          return null;
        }
        return value.value;
      }
      return value.value;
    } catch (error) {
      return null;
    }
  }

  private _checkIgnore(key: string) {
    return this._ignore.some((item) => key.includes(item));
  }

  private _checkState(key: string) {
    return key.includes(this._prefixState);
  }

  // 处理错误，删除 old data
  private _purge(error: any, data: string) {
    const mapIter = this._localKeys.keys();
    const dataSize = data.length / 1024;

    // 优先需要删除的数据
    this._useless.forEach((key) => {
      this.remove(key);
    });

    let index = 99;
    while (this._localLru.currentSize() + this._localLru._purgeLoadMax + dataSize > this._maxSize) {
      // 防止特殊情况死循环
      if (index < 0) {
        exceptionHandler(error);
        return;
      }

      index--;
      const mapKey = mapIter.next().value;

      if (!this._checkIgnore(mapKey)) {
        this.remove(mapKey);
      }
    }
  }

  // 处理 get
  private _handleGet(key: string) {
    const localKey = this._prefixLru + key;

    const data = this._localLru.get(localKey);

    // 如果是忽略的 key 不需要做 lru
    if (!this._checkIgnore(localKey)) {
      this._localKeys.delete(localKey);

      if (data) {
        this._localKeys.set(localKey, localKey);
      }

      this._setMap();
    }

    return data;
  }

  // 更新 map，设置防抖函数，防止短时间多次更新
  private _setMap = debounce(() => {
    nextTick(() => {
      const val = JSON.stringify({ time: null, value: [...this._localKeys.values()] });

      this._localLru.set(this._prefixMap, val);
    });
  }, 500);

  // 获取 map
  private _getMap<T>(): T | null {
    const data = this._localKeys.get(this._prefixMap);

    if (data) {
      return JSON.parse(data);
    }

    return null;
  }

  // 清除过期的 gis 和 image 资源
  private handleImgAndGisInit() {
    const currentTime = Date.now();

    this._localLru.useless.forEach((key) => {
      const data = this.get(key);

      if (data) {
        this.set(
          key,
          pickBy(data, (item) => currentTime < item.time),
        );
      }
    });
  }

  // 清理所有过期的 data
  private _clearAllExpireData() {
    nextTick(() => {
      // 初始化map
      const map = this._getMap<string[]>();

      if (map) {
        this._localKeys = new Map(map.map((item) => [item, item]));
      } else {
        this._localKeys = new Map(this._localLru.keys().map((key) => [key, key]));
      }

      this._localKeys.forEach((key) => {
        if (this._checkIgnore(key)) return;

        // 如果是 state 数据，直接删除
        if (this._checkState(key)) {
          this.remove(key);
          return;
        }

        const data = this._localLru.get(key);
        this._parseData(data, key);
      });

      this.handleImgAndGisInit();

      this._setMap();
    });
  }

  /**
   * 设置 storage
   * @param key
   * @param data
   * @param time 设置过期时间(单位为分钟)，如 10 => 10分钟
   */
  set(key: string, data: any, time = 0): void {
    key = this._prefixKey(key);

    const H = time * 1e3 * 60;
    const now = new Date().getTime() + H;
    const val = JSON.stringify({ time: time ? String(now) : null, value: data });
    const localKey = this._prefixLru + key;

    try {
      if (this._localKeys.has(localKey)) {
        this._localKeys.delete(localKey);
      }

      this._localLru.set(localKey, val);
    } catch (error) {
      // 清理
      this._purge(error, val);
      this.set(key, data, time);
    } finally {
      this._localKeys.set(localKey, localKey);

      this._setMap();
    }
  }

  /**
   * 获取 storage
   * @param key
   * @returns
   */
  get<T = any>(key: string): T | null {
    key = this._prefixKey(key);
    const data = this._handleGet(key);

    return this._parseData(data, key);
  }

  /**
   *  移除 storage
   * @param key
   */
  remove(key: string): void {
    key = this._prefixKey(key);
    const localKey = key.includes(this._prefixLru) ? key : this._prefixLru + key;

    this._localKeys.delete(localKey);
    this._localLru.remove(localKey);
    this._setMap();
  }

  /**
   * 移除所有 storage
   * @param isAll 移除所有缓存是否包括 `lasting_` 后缀的 storage
   * @returns
   */
  clear(isAll = false) {
    if (isAll) {
      this._localLru.clear();
      return;
    }
    const reg = new RegExp(`^${this._prefixLru}${this.prefixLasting}`);
    this._localLru.keys().forEach((key) => {
      if (!reg.test(key)) {
        this.remove(key);
      }
    });
  }

  /**
   * 选择性移除 storage
   * @param ignore 要忽略的key，前缀匹配
   * @returns
   */
  matchClear(ignore: IgnoreTypes[]) {
    const ignoreRes = ignore.map((i) => `^${this._prefixLru}${this[i as IgnoreTypes] || i}`);

    const ignoreReg = ignore.length
      ? `^(${ignoreRes.join('|')}|${this.prefixLasting})`
      : `^(${this._prefixLru}${this.prefixLasting})`;

    const reg = new RegExp(ignoreReg);

    this._localLru.keys().forEach((key) => {
      if (!reg.test(key)) {
        this.remove(key);
      }
    });
  }

  /**
   * 获取 key 前缀为 `user_` 的 storage
   * @param key
   * @returns
   */
  getUser<T = any>(key: string) {
    return this.get<T>(this.prefixUser + key);
  }

  /**
   * 设置 key 前缀为 `user_` 的 storage
   * @param key
   * @param data
   * @param time 设置过期时间(单位为分钟)，如 10 => 10分钟
   */
  setUser(key: string, data: any, time = 0) {
    this.set(this.prefixUser + key, data, time);
  }

  /**
   * 移除 key 前缀为 `user_` 的 storage
   * @param key
   */
  removeUser(key: string) {
    this.remove(this.prefixUser + key);
  }

  /**
   * 获取 key 前缀为 `data_` 的 storage
   * @param key
   * @returns
   */
  getData<T = any>(key: string) {
    return this.get<T>(this.prefixData + key);
  }

  /**
   * 设置 key 前缀为 `data_` 的 storage
   * @param key
   * @param data
   * @param time 设置过期时间(单位为分钟)，如 10 => 10分钟
   */
  setData(key: string, data: any, time = 0) {
    this.set(this.prefixData + key, data, time);
  }

  /**
   * 移除 key 前缀为 `data_` 的 storage
   * @param key
   */
  removeData(key: string) {
    this.remove(this.prefixData + key);
  }

  /**
   * 获取 key 前缀为 `lasting_` 的 storage
   * @param key
   * @returns
   */
  getLasting<T = any>(key: string) {
    return this.get<T>(this.prefixLasting + key);
  }

  /**
   * 设置 key 前缀为 `lasting_` 的 storage
   * @param key
   * @param data
   */
  setLasting(key: string, data: any) {
    this.set(this.prefixLasting + key, data);
  }

  /**
   * 移除 key 前缀为 `lasting_` 的 storage
   * @param key
   */
  removeLasting(key: string) {
    this.remove(this.prefixLasting + key);
  }

  /**
   * 获取 key 前缀为 `state_` 的 storage
   * state数据用于通信，只存在一次，且初始化会被清空
   * @param key
   * @returns
   */
  getState<T = any>(key: string) {
    const _data = this.get<T>(this._prefixState + key);

    if (_data) {
      nextTick(() => {
        this.removeState(key);
      });
    }

    return _data;
  }

  /**
   * 设置 key 前缀为 `state_` 的 storage
   * @param key
   * @param data
   */
  setState(key: string, data?: any) {
    this.set(this._prefixState + key, data || true);
  }

  /**
   * 移除 key 前缀为 `state_` 的 storage
   * @param key
   */
  removeState(key: string) {
    this.remove(this._prefixState + key);
  }
}

// const Storage = new StorageLRU({
//   scid,
//   get: uni.getStorageSync,
//   set: uni.setStorageSync,
//   remove: uni.removeStorageSync,
//   clear: uni.clearStorageSync,
//   keys: () => uni.getStorageInfoSync().keys,
//   maxSize: () => uni.getStorageInfoSync().limitSize,
//   currentSize: () => uni.getStorageInfoSync().currentSize,
//   _purgeLoadMax: 1000,
//   useless: ['lasting_cache_gis', 'lasting_cache_image'],
//   ignore: ['QMAPI_'],
// });

export const SessionStorage: Omit<StorageOption, 'scid' | '_purgeLoadMax' | 'useless' | 'ignore'> =
  {
    // #ifdef H5
    get: window.sessionStorage.getItem.bind(window.sessionStorage),
    set: window.sessionStorage.setItem.bind(window.sessionStorage),
    remove: window.sessionStorage.removeItem.bind(window.sessionStorage),
    clear: window.sessionStorage.clear.bind(window.sessionStorage),
    keys: () => Object.keys(window.sessionStorage),
    maxSize: () => 5 * 1024,
    currentSize: () => {
      const byte = Object.entries(window.sessionStorage).reduce((size, [key, value]) => {
        size += key.length * 2;
        size += value.length * 2;
        return size;
      }, 0);
      return byte / 1024;
    },
    // #endif
  };

export const UniStorage: Omit<StorageOption, 'scid' | '_purgeLoadMax' | 'useless' | 'ignore'> = {
  get: uni.getStorageSync,
  set: uni.setStorageSync,
  remove: uni.removeStorageSync,
  clear: uni.clearStorageSync,
  keys: () => uni.getStorageInfoSync().keys,
  maxSize: () => uni.getStorageInfoSync().limitSize,
  currentSize: () => uni.getStorageInfoSync().currentSize,
};
