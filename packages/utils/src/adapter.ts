import { IsH5, <PERSON><PERSON><PERSON><PERSON>, <PERSON>W<PERSON>xinH5 } from '@rms/utils';
import { _GetUrlKey } from './helpers';

type ScanCodeOptions = (
  | Omit<UniApp.ScanCodeOptions, 'success'>
  | Omit<wx.scanQRCodeOptions, 'success'>
) & {
  scanType: wx.scanQRCodeOptions['scanType'];
  success: UniApp.ScanCodeOptions['success'];
};

export function AdapterScanCode(option: ScanCodeOptions) {
  if (IsH5 && IsWeixinH5) {
    const options = option as wx.scanQRCodeOptions;
    wx.scanQRCode({
      ...options,
      success(res: any) {
        res.result = res.resultStr;
        if (option.success) option.success(res);
      },
    });
  } else {
    uni.scanCode(option as UniApp.ScanCodeOptions);
  }
}

export function AdapterLogin(): Promise<UniApp.LoginRes | false> {
  return new Promise((resolve, reject) => {
    if (IsH5) {
      resolve(false);

      const sccode = _GetUrlKey('sccode');
      const curPage = getCurrentPages().find((page) => page.route?.includes('pages/login/index'));

      if (curPage || sccode) return;

      // uni.navigateTo({
      //   url: '/',
      // });
      return;
    }

    if (IsWeixin) {
      uni.login({
        success: resolve,
        fail: reject,
      });
    }
  });
}

export function AdapterCheckSession(): Promise<boolean> {
  return new Promise((resolve) => {
    // 只做了微信的兼容
    uni.checkSession({
      success: (res) => {
        if (res.errMsg.includes('ok')) resolve(true);
        else resolve(false);
      },
      fail: () => resolve(false),
    });
  });
}
