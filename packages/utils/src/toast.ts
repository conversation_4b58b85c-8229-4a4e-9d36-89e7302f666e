type ToastOption = UniApp.ShowToastOptions;

class Toast {
  private static toast(title: string, opt: ToastOption = {}) {
    const duration = opt.duration || 1500;
    const isMpWx = process.env.UNI_PLATFORM === 'mp-weixin';

    if (isMpWx && title.length > 7) {
      opt.icon = 'none';
      delete opt.image;
    }

    uni.showToast({
      ...opt,
      title,
      duration,
      success() {
        if (opt.success) setTimeout(opt.success, duration - 1000);
      },
      complete() {
        if (opt.complete) setTimeout(opt.complete, duration - 1000);
      },
      fail() {
        if (opt.fail) setTimeout(opt.fail, duration - 1000);
      },
    });
  }

  static show(title: string) {
    this.toast(title, {
      icon: 'none',
    });
  }

  static warning(title: string, option: ToastOption = {}) {
    this.toast(title, { ...option, image: `/static/images/warning.png` });
  }

  static success(title: string, option: ToastOption = {}) {
    this.toast(title, { ...option, image: `/static/images/success.png` });
  }

  static error(title: string, option: ToastOption = {}) {
    this.toast(title, { ...option, image: `/static/images/error.png` });
  }
}

export default Toast;
