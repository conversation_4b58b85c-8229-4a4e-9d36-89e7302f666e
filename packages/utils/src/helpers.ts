import Register from '@rms/register';
import { IsH5 } from './evn';
import { ApiGetScCode } from '@shencom/api';
import { exceptionHandler, exceptionToast } from './error';
import { cryptoAes } from './decrypt';

type Dictionary<T = any> = Record<string, T>;

/** 对象转数组
 *
 * @template T
 * @param {Dictionary<T>} obj
 * @returns {T[]}
 */
export const ObjectToArray = <T>(obj: Dictionary<T>): T[] => Object.keys(obj).map((k) => obj[k]);

/**
 * 解析带参二维码路由参数
 * @export
 * @param {(Record<string, string | undefined>)} options onLoad 获取的参数
 * @return {{t: string; c: string; ts: string}} t => scid、c => code、ts => timestamp
 */
export function parseUrlParams(options: Record<string, string | undefined>) {
  console.log('_ParseUrlParams query :>> ', options);

  if (!options || !options.q) return null;

  const config = decodeURIComponent(options.q);

  const [, search] = config.split('?');
  if (!search) return {};

  const params: Record<string, string> = {};

  search.split('&').forEach((value) => {
    const [key, val] = value.split('=');
    params[key] = val;
  });

  console.log('_ParseUrlParams params :>> ', params);

  return params;
}

/**
 * 对象转 url 参数
 *
 * @export
 * @param {(Record<string, string | number>)} obj
 * @return {string}
 */
export function objectToUrlParams(obj: Record<string, string | number | undefined>) {
  const valueMap: Record<string, any> = {
    true: true,
    false: false,
  };
  return Object.entries(obj)
    .map(([key, value]) =>
      typeof value === 'undefined' ? null : `${key}=${valueMap[value] || value}`,
    )
    .filter(Boolean)
    .join('&');
}

/**
 *
 * @param fn 执行函数
 * @param delay 延迟时间
 * @returns
 */
export const throttle = <R, A extends any[]>(
  fn: (...args: A) => R,
  delay = 50,
): [(...args: A) => R | undefined, () => void] => {
  let wait = false;
  let timeout: NodeJS.Timeout | null;
  let cancelled = false;

  return [
    (...args: A) => {
      if (cancelled) return undefined;
      if (wait) return undefined;

      const val = fn(...args);

      wait = true;

      timeout = setTimeout(() => {
        wait = false;
      }, delay);

      return val;
    },
    () => {
      cancelled = true;
      if (timeout) {
        clearTimeout(timeout);
      }
    },
  ];
};

export type Options<Result> = {
  isImmediate?: boolean;
  maxWait?: number;
  callback?: (data: Result) => void;
};

export interface DebouncedFunction<Args extends any[], F extends (...args: Args) => any> {
  (this: ThisParameterType<F>, ...args: Args & Parameters<F>): Promise<ReturnType<F>>;
  cancel: (reason?: any) => void;
}

interface DebouncedPromise<FunctionReturn> {
  resolve: (result: FunctionReturn) => void;
  reject: (reason?: any) => void;
}

/**
 * 防抖函数
 * @param func 执行函数
 * @param delay 延迟时间 ms
 * @param options { isImmediate: 是否立即执行, maxWait: 最大等待时间, callback: 回调 }
 */
export function debounce<Args extends any[], F extends (...args: Args) => any>(
  func: F,
  delay = 50,
  options: Options<ReturnType<F>> = {},
): DebouncedFunction<Args, F> {
  let timeoutId: ReturnType<typeof setTimeout> | undefined;
  const isImmediate = options.isImmediate ?? false;
  const callback = options.callback ?? false;
  const maxWait = options.maxWait;
  let lastInvokeTime = Date.now();

  let promises: DebouncedPromise<ReturnType<F>>[] = [];

  function nextInvokeTimeout() {
    if (maxWait !== undefined) {
      const timeSinceLastInvocation = Date.now() - lastInvokeTime;

      if (timeSinceLastInvocation + delay >= maxWait) {
        return maxWait - timeSinceLastInvocation;
      }
    }

    return delay;
  }

  const debouncedFunction = function (this: ThisParameterType<F>, ...args: Parameters<F>) {
    const context = this;
    return new Promise<ReturnType<F>>((resolve, reject) => {
      const invokeFunction = function () {
        timeoutId = undefined;
        lastInvokeTime = Date.now();
        if (!isImmediate) {
          const result = func.apply(context, args);
          if (callback) {
            callback(result);
          }

          promises.forEach(({ resolve: res }) => res(result));
          promises = [];
        }
      };

      const shouldCallNow = isImmediate && timeoutId === undefined;

      if (timeoutId !== undefined) {
        clearTimeout(timeoutId);
      }

      timeoutId = setTimeout(invokeFunction, nextInvokeTimeout());

      if (shouldCallNow) {
        const result = func.apply(context, args);
        if (callback) {
          callback(result);
        }
        resolve(result);
        return;
      }
      promises.push({ resolve, reject });
    });
  };

  debouncedFunction.cancel = function (reason?: any) {
    if (timeoutId !== undefined) {
      clearTimeout(timeoutId);
    }
    promises.forEach(({ reject }) => reject(reason));
    promises = [];
  };

  return debouncedFunction;
}

/**
 *
 * @param value string | number
 * @param options array
 * @returns string
 */
export const getOptionLabel = (value: string | number, options: any[]) => {
  const selectItem = options.find((item) => String(item.value) === String(value));
  return (selectItem && selectItem.text) || '';
};

/**
 * 获取路由参数
 */
export const _GetUrlKey = (name: string) => {
  if (!IsH5) return null;

  const reg = new RegExp(`[?|&]${name}=([^&;]+?)(&|#|;|$)`);
  const { href } = window.location;
  return (reg && decodeURIComponent((reg.exec(href) || ['', ''])[1].replace(/\+/g, '%20'))) || null;
};

/**
 * 解析i福田路由加密参数
 */
export const _GetIftParams = (key: string, field?: string) => {
  const casToken = _GetUrlKey('casToken');
  if (!casToken) return field ? undefined : {};
  try {
    const params = JSON.parse(cryptoAes(key).decrypt(casToken)) || {};
    return field ? params[field] : params;
  } catch (error) {
    exceptionHandler(error);
    return field ? undefined : {};
  }
};

/**
 * h5跳转若需要权限，路由拼接sccode
 * @param {string} url 目标页面路径
 * @return {string} 拼接sccode路由
 */
export async function getAuthLink(url: string, is_share = false) {
  try {
    const register = new Register();
    const { code } = (await ApiGetScCode()).data;
    // 截取 #/ 及后面内容
    const routeReg = /#\/(.*)/;
    const route = url.match(routeReg);
    let query = (route && route[0]) || '';

    const uid = register.RmsUserInfo.getUid();

    // 获取拼接分隔符
    const hasQuery = query.match(/(?:\?)(.*)/);
    const separator = hasQuery ? (hasQuery[1] ? '&' : '') : '?';
    // 拼接授权字段
    query = `${query}${separator}sccode=${code}&uid=${uid}${is_share ? '&is_share=1' : ''}`;

    const { provId, cityId } = register.RmsAppConfig.base.city || {};
    if (provId) {
      query += `&provId=${provId}&cityId=${cityId}`;
    }

    return url.replace(routeReg, query);
  } catch (error) {
    exceptionToast(error, '');
  }
}

/**
 * 计算两点直接距离
 * @param lat1
 * @param lng1
 * @param lat2
 * @param lng2
 * @returns
 */
export function getFlatternDistance(
  lat1: number | string,
  lng1: number | string,
  lat2: number | string,
  lng2: number | string,
) {
  lat1 = Number(lat1);
  lng1 = Number(lng1);
  lat2 = Number(lat2);
  lng2 = Number(lng2);

  const EARTH_RADIUS = 6378137.0; //单位M
  const PI = Math.PI;

  const getRad = (d: number) => (d * PI) / 180.0;

  let f = getRad((lat1 + lat2) / 2);
  let g = getRad((lat1 - lat2) / 2);
  let l = getRad((lng1 - lng2) / 2);

  let sg = Math.sin(g);
  let sl = Math.sin(l);
  let sf = Math.sin(f);

  let s, c, w, r, d, h1, h2;
  let a = EARTH_RADIUS;
  let fl = 1 / 298.257;

  sg = sg * sg;
  sl = sl * sl;
  sf = sf * sf;

  s = sg * (1 - sl) + (1 - sf) * sl;
  c = (1 - sg) * (1 - sl) + sf * sl;

  w = Math.atan(Math.sqrt(s / c));
  r = Math.sqrt(s * c) / w;
  d = 2 * w * a;
  h1 = (3 * r - 1) / 2 / c;
  h2 = (3 * r + 1) / 2 / s;

  return d * (1 + fl * (h1 * sf * (1 - sg) - h2 * (1 - sf) * sg));
}

export class Calc {
  /**
   * 加法运算
   * @param {number} num1
   * @param {number} num2
   * @returns {*}
   */
  add(num1: number, num2: number): number {
    num1 = Number(num1);
    num2 = Number(num2);
    let dec1: number, dec2: number, times: number;
    try {
      dec1 = this.countDecimals(num1) + 1;
    } catch (e) {
      dec1 = 0;
    }
    try {
      dec2 = this.countDecimals(num2) + 1;
    } catch (e) {
      dec2 = 0;
    }
    times = Math.pow(10, Math.max(dec1, dec2));
    const result = (this.mul(num1, times) + this.mul(num2, times)) / times;
    return this.getCorrectResult('add', num1, num2, result);
  }

  /**
   * 减法运算
   * @param {number} num1
   * @param {number} num2
   * @returns {number}
   */
  sub(num1: number, num2: number): number {
    num1 = Number(num1);
    num2 = Number(num2);
    let dec1: number, dec2: number, times: number;
    try {
      dec1 = this.countDecimals(num1) + 1;
    } catch (e) {
      dec1 = 0;
    }
    try {
      dec2 = this.countDecimals(num2) + 1;
    } catch (e) {
      dec2 = 0;
    }
    times = Math.pow(10, Math.max(dec1, dec2));
    const result = Number((this.mul(num1, times) - this.mul(num2, times)) / times);
    return this.getCorrectResult('sub', num1, num2, result);
  }

  /**
   * 除法运算
   * @param {number} num1
   * @param {number} num2
   * @returns {number}
   */
  div(num1: number, num2: number): number {
    num1 = Number(num1);
    num2 = Number(num2);
    let t1 = 0,
      t2 = 0,
      dec1: number,
      dec2: number;
    try {
      t1 = this.countDecimals(num1);
    } catch (e) {}
    try {
      t2 = this.countDecimals(num2);
    } catch (e) {}
    dec1 = this.convertToInt(num1);
    dec2 = this.convertToInt(num2);
    const result = this.mul(dec1 / dec2, Math.pow(10, t2 - t1));
    return this.getCorrectResult('div', num1, num2, result);
  }
  /**
   * 乘法运算
   * @param {number} num1
   * @param {number} num2
   * @returns {number}
   */
  mul(num1: number, num2: number): number {
    num1 = Number(num1);
    num2 = Number(num2);
    let times = 0,
      s1 = num1.toString(),
      s2 = num2.toString();
    try {
      times += this.countDecimals(s1);
    } catch (e) {}
    try {
      times += this.countDecimals(s2);
    } catch (e) {}
    const result = (this.convertToInt(s1) * this.convertToInt(s2)) / Math.pow(10, times);
    return this.getCorrectResult('mul', num1, num2, result);
  }

  /**
   * 计算小数位的长度
   * @param {*} num
   * @returns {number}
   */
  private countDecimals(num: any): number {
    let len = 0;
    try {
      num = Number(num);
      let str = num.toString().toUpperCase();
      if (str.split('E').length === 2) {
        // 科学记数法
        let isDecimal = false;
        if (str.split('.').length === 2) {
          str = str.split('.')[1];
          if (parseInt(str.split('E')[0]) !== 0) {
            isDecimal = true;
          }
        }
        let x = str.split('E');
        if (isDecimal) {
          len = x[0].length;
        }
        len -= parseInt(x[1]);
      } else if (str.split('.').length === 2) {
        // 十进制
        if (parseInt(str.split('.')[1]) !== 0) {
          len = str.split('.')[1].length;
        }
      }
    } catch (e) {
      throw e;
    } finally {
      if (isNaN(len) || len < 0) {
        len = 0;
      }
      return len;
    }
  }

  /**
   * 将小数转成整数
   * @param {*} num
   * @returns {*}
   */
  private convertToInt(num: any): number {
    num = Number(num);
    let newNum = num;
    let times = this.countDecimals(num);
    let temp_num = num.toString().toUpperCase();
    if (temp_num.split('E').length === 2) {
      newNum = Math.round(num * Math.pow(10, times));
    } else {
      newNum = Number(temp_num.replace('.', ''));
    }
    return newNum;
  }

  /**
   * 确认我们的计算结果无误，以防万一
   * @param {string} type
   * @param {number} num1
   * @param {number} num2
   * @param {number} result
   * @returns {number}
   */
  private getCorrectResult(
    type: 'add' | 'sub' | 'div' | 'mul',
    num1: number,
    num2: number,
    result: number,
  ): number {
    let temp_result = 0;
    switch (type) {
      case 'add':
        temp_result = num1 + num2;
        break;
      case 'sub':
        temp_result = num1 - num2;
        break;
      case 'div':
        temp_result = num1 / num2;
        break;
      case 'mul':
        temp_result = num1 * num2;
        break;
    }
    if (Math.abs(result - temp_result) > 1) {
      return temp_result;
    }
    return result;
  }
}
