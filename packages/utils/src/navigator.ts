import { ValidateURL } from '@shencom/utils';
import {
  IsDev,
  IsH5,
  Toast,
  getAuthLink,
  objectToUrlParams,
  deployBaseUrl,
  deployOss,
} from '@rms/utils';
import { Menu } from '@rms/types';
import Register from '@rms/register';

type UniNavigatorOptions =
  | Omit<UniApp.NavigateToOptions, 'url'>
  | Omit<UniApp.RedirectToOptions, 'url'>
  | Omit<UniApp.SwitchTabOptions, 'url'>;

type NavigateType = typeof uni.navigateTo | typeof uni.redirectTo;

/**
 * 获取完整的带参路由
 *
 * @param {string} url 目标页面路径
 * @param {object} [query={}] 路由参数
 * @return {string} 带参路由
 */
function getUrl(url: string, query = {}) {
  const queryString = objectToUrlParams(query);
  return url + (queryString ? `?${queryString}` : '');
}

/**
 * 获取页面跳转方法
 *
 * @param {string} url 目标页面路径
 * @param {NavigateType} method 当前期望跳转方式
 * @return {NavigateType|'switchTab'} uni 相应跳转方法
 */
function getMethod(url: string, method: NavigateType) {
  const register = new Register();

  return register.RmsPages.tabBar?.list.some(({ pagePath }: any) => url.includes(pagePath))
    ? uni.switchTab
    : method;
}

/** 路由跳转 */
export class Navigator {
  /**a
   * 跳转新页面，若新页面是 tab 页则自动使用 switchTab 跳转
   *
   * @export
   * @param {string} url 跳转路由
   * @param {object} [query={}] 路由携带参数
   * @param {UniNavigatorOptions} [options={}] uni 路由跳转的其他参数
   */
  static push(url: string, query = {}, options: UniNavigatorOptions = {}) {
    const link = getUrl(url, query);
    const method: typeof uni.navigateTo = getMethod(url, uni.navigateTo);

    method({ url: link, ...options });
  }

  /**
   * 替换当前页面，若新页面是 tab 页则自动使用 switchTab 跳转
   *
   * @export
   * @param {string} url 跳转路由
   * @param {object} [query={}] 路由携带参数
   * @param {UniNavigatorOptions} [options={}] uni 路由跳转的其他参数
   */
  static replace(url: string, query = {}, options: UniNavigatorOptions = {}) {
    const method: typeof uni.redirectTo = getMethod(url, uni.redirectTo);

    method({ url: getUrl(url, query), ...options });
  }

  /**
   * 返回上级页面
   *
   * @export
   * @param {number} [delta=1] 返回层级数
   * @param {Omit<UniApp.NavigateBackOptions, 'delta'>} [options={}] uni 路由跳转的其他参数
   */
  static async back(delta = 1, options: Omit<UniApp.NavigateBackOptions, 'delta'> = {}) {
    const register = new Register();
    const curPages = getCurrentPages();
    if (curPages.length > delta) {
      await uni.navigateBack({
        delta,
        fail: () => {
          Navigator.replace('/' + register.RmsPages.pages[0].path);
        },
        ...options,
      });
    } else {
      if (IsH5 && window.history.length > 1) {
        window.history.go(-1);
      } else {
        Navigator.replace('/' + register.RmsPages.pages[0].path);
      }
    }
  }

  /**
   * 小程序 & webview 菜单跳转通用方法
   *
   * @export
   * @param {(Menu & { title?: string })} item 菜单配置
   */
  static async toMenuPage(
    item: Partial<Menu> & { title?: string } & Pick<Menu, 'type'>,
    isReplace?: boolean,
  ) {
    const { type, permit, is_share, isShare = 0, title } = item;
    let link = item.link || '';

    if (type === 'url') {
      if (ValidateURL(link)) {
        if (IsH5) {
          if (deployOss) {
            const linkHost = new URL(link).host;
            link = link.replace(linkHost, linkHost + deployBaseUrl);
          }
          if (link.includes(window.location.pathname) && !IsDev) {
            link = link.split('#').pop() || '';
            Navigator[isReplace ? 'replace' : 'push'](link);
            return;
          }

          let toLink = deployOss ? link : item.link;
          if (!!permit && toLink) {
            toLink = await getAuthLink(toLink);
          }
          if (isReplace && toLink) {
            window.location.replace(toLink);
          } else {
            window.location.href = toLink || window.location.href;
          }
        } else {
          const query: Record<string, any> = {
            url: encodeURIComponent(link),
            is_share: is_share || isShare,
            title,
          };

          if (!!permit) {
            query.isToken = true;
          }
          Navigator[isReplace ? 'replace' : 'push']('/pages/webview/index', query);
        }
      } else {
        throw new Error(`webview 链接错误: ${link}`);
      }
      return;
    }

    if (link) {
      Navigator[isReplace ? 'replace' : 'push'](link);
    } else {
      Toast.warning('暂未开放，敬请期待');
    }
  }
}
