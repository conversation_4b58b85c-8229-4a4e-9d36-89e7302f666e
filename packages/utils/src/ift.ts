import { _GetIftParams, cryptoAes } from '@rms/utils';

const decryptKey = 'F28477A1DBBA81E1';

export const IFutianConfig = {
  scid: _GetIftParams(decryptKey, 'scid'),
  key: decryptKey,
};

export const IFutianMessage = () => {
  // 监控事件-详情
  const config = {
    title: '监控事件-详情',
    memo: '请尽快处理~',
    path: `https://ftljfl-app.szft.gov.cn/recycle-monitor-event/index.html#/?scid=sc910cfa34b525d93a&path=${encodeURIComponent(
      '/pages/index/detail?id=1540145869505974272',
    )}`,
    // path: `https://tst-app.shencom.cn/recycle-monitor-event/index.html#/?scid=sc910cfa34b525d93a&path=${encodeURIComponent(
    //   '/pages/index/detail?id=1527842215375179776',
    // )}`,
  };

  // 蒲公英-种子计划注册讲师
  // const config = {
  //   title: '种子计划',
  //   memo: '加入讲师~',
  //   path: `https://ftljfl-app.szft.gov.cn/recycle-dandelion/index.html#/?scid=sc910cfa34b525d93a&path=${encodeURIComponent(
  //     '/pages/seeded-plan/create/index?resourceId=1380284566042128384',
  //   )}`,
  // };

  const mpPath = 'subs/website/pages/outside/outside?id=1177256106373046272&shareUrl=';

  const json = {
    // phoneNumbers: ['15818344108', '13691728371'],
    phoneNumbers: ['15818344108'],
    contentName: config.title,
    noticesRemark: config.memo,
    pagePath: `${mpPath}${encodeURIComponent(config.path)}`,
  };
  const value = cryptoAes('r9dplb23mzdl4ivb').encrypt(JSON.stringify(json));
  console.log('i福田服务通知加密参数: ', value, json);
};
