import { Dictionary } from '@rms/types';
import { Ref } from 'vue';

export function changeFormConfigIsHide(
  config: Ref<UniApp.UI.FormConfig>,
  prop: string,
  value: boolean,
) {
  const data = config.value.data.find((item) => item.prop === prop);

  if (data) {
    data.isHide = value;
  }
}

export const findFormItem = (formConfig: Ref<UniApp.UI.FormConfig>, prop: string) => {
  const index = formConfig.value.data.findIndex((item) => item.prop === prop);
  if (index !== -1) {
    return formConfig.value.data[index];
  }
  return null;
};

export const findEnumer = (
  status: Dictionary[],
  type?: number | string,
  prop = 'value',
  key = 'id',
): string => {
  const optionItem = status.find((item) => item[key] === type);
  if (optionItem) {
    return optionItem[prop] as string;
  }
  return '';
};

/**
 * 获取到配置的某一项
 *
 * @param data UniApp.UI.DataType[]
 * @param prop string
 * @returns UniApp.UI.DataType
 */
export const _GetConfigItemData = (
  data: UniApp.UI.FormConfig['data'],
  prop: string,
): UniApp.UI.FormConfig['data'][number] | null => {
  const item = data.find((k) => prop === k.prop);
  if (item) return item;

  return null;
};
