import { Tenant } from '@shencom/utils';

// 本地开发环境
export const IsDev = import.meta.env.MODE === 'development';

// 正式环境
export const IsPro = !!import.meta.env.VITE_APP_IS_PRO;

// 测试环境
export const IsTst = !IsDev && !IsPro;

export const Platform = process.env.UNI_PLATFORM;

// h5
export const IsH5 = Platform === 'h5';

// 支付宝小程序
export const IsAlipay = Platform === 'mp-alipay';

// 微信小程序
export const IsWeixin = Platform === 'mp-weixin';

// app
export const IsAppPlus = Platform === 'app-plus';

// 支付宝 h5
export const IsAlipayH5 = IsH5 && navigator.userAgent.toLowerCase().includes('alipayclient');

// 微信 h5
export const IsWeixinH5 = IsH5 && navigator.userAgent.toLowerCase().includes('micromessenger');

// 微信小程序 webview 环境
export const IsMiniProgram =
  IsH5 &&
  ((window as any).__wxjs_environment === 'miniprogram' ||
    navigator.userAgent.includes('miniProgram'));

// 微信开发者工具
export const IsWechatDevTools =
  IsH5 && navigator.userAgent.toLowerCase().includes('wechatdevtools');

// 支付宝小程序 webview 环境
export const IsAlipayMiniProgram =
  IsH5 &&
  navigator.userAgent.toLowerCase().includes('miniprogram') &&
  navigator.userAgent.toLowerCase().includes('alipayclient');

// 判断是否为i深圳 APP 内嵌 h5
export const IsIShenzhen = IsH5 && navigator.userAgent.includes('SZSMT');

// 判断是否为i福田小程序内嵌 h5
export const IsIFutian = (scid: string) =>
  IsH5 &&
  (window.location.hash.includes('casToken=') ||
    (IsMiniProgram && Tenant.getTenetInfo(scid).isFutian));

// 是否独立部署
export const IsDeploy = IsH5 && process.env.UNI_DEPLOY;

// 独立部署 oss 配置
export const deployOss = IsH5 && process.env.UNI_DEPLOY_OSS;

// 判断是否为i龙华小程序内嵌 h5
export const IsILongHua =
  IsH5 &&
  IsMiniProgram &&
  window.location.href.includes('usertoken') &&
  window.location.href.includes('loginType');

// 判断是否为i盐田小程序内嵌 h5
export const IsIYanTian = (scid: string) => {
  return (
    IsH5 &&
    IsMiniProgram &&
    window.location.href.includes('usercode') &&
    ['sc66a4a6e1bdb584ce'].includes(scid)
  );
};

// 判断是否为i南山内嵌 h5
export const IsINanShan = IsH5 && IsMiniProgram && window.location.href.includes('sys=nanshan');

// 判断是否APP内嵌宝安h5
export const IsAppBaoAn = IsH5 && window.location.href.includes('sys=bkbaoan');

// 获取ios系统版本号
export const getIOSVersion = () => {
  var userAgent = navigator.userAgent;
  var regex = /OS (\d+)_(\d+)_?(\d+)?/;
  var matches = regex.exec(userAgent);
  if (matches && matches.length >= 2) {
    var version = matches[1] + '.' + matches[2];
    return version;
  }
  return '';
};
