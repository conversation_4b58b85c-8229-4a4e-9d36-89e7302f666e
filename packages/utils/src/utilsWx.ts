import { Adapter<PERSON>canC<PERSON>, Dialog, Toast } from '@rms/utils';
import { ReturnPromiseType } from '@shencom/typing';
import { exceptionHandler } from './error';
import loading from './loading';

export function previewMedia(
  url: string | UniApp.MediaSource[],
  type: 'image' | 'video' = 'image',
  options: Partial<UniApp.PreviewMediaOption> = {},
): Promise<UniApp.GeneralCallbackResult> {
  return new Promise((resolve, reject) => {
    const sources: UniApp.MediaSource[] =
      typeof url === 'string'
        ? [
            {
              url,
              type: type || 'image',
            },
          ]
        : url;

    const option: UniApp.PreviewMediaOption = {
      sources,
      success: resolve,
      fail: reject,
    };

    uni.previewMedia({
      ...option,
      ...options,
    });
  });
}
/**
 * 选择地址
 */
export function chooseLocation(
  option?: Partial<UniApp.ChooseLocationOptions>,
): Promise<UniApp.ChooseLocationSuccess> {
  return new Promise((resolve, reject) => {
    uni.chooseLocation({
      success: resolve,
      fail: reject,
      ...option,
    });
  });
}

interface CustomOptions {
  onPhoneLocationEnabled?: (...args: any) => void;
  onOpenLocationSetting?: (isSuccess: boolean) => void;
}

/**
 * @return {{ barHeight: number, menuButtonInfo:  WechatMiniprogram.Rect }}
 * @return barHeight: 导航栏高度
 * @return menuButtonInfo: 获取菜单按钮（右上角胶囊按钮）的布局位置信息
 */
export const getBarHeight = (): Promise<{
  barHeight: number;
  paddingTop: number;
  menuButtonInfo: UniApp.GetMenuButtonBoundingClientRectRes;
  systemInfo: UniApp.GetSystemInfoResult;
}> =>
  new Promise(async (resolve, reject) => {
    // #ifdef MP-WEIXIN
    try {
      // 获取系统信息
      const systemInfo = await uni.getSystemInfoSync();
      // 胶囊按钮位置信息
      const menuButtonInfo = await uni.getMenuButtonBoundingClientRect();
      const barHeight =
        ((menuButtonInfo.top - (systemInfo.statusBarHeight || 0)) * 2 +
          menuButtonInfo.height +
          (systemInfo.statusBarHeight || 0)) /
        2;

      const paddingTop = menuButtonInfo.top + barHeight;

      resolve({
        barHeight,
        paddingTop,
        menuButtonInfo,
        systemInfo,
      });
    } catch (error) {
      reject(error);
    }
    // #endif
    // #ifndef MP-WEIXIN
    reject();
    // #endif
  });

/** 版本号比较 */
export function compareVersion(v1: string, v2: string) {
  const _v1 = v1.split('.');
  const _v2 = v2.split('.');
  const len = Math.max(_v1.length, _v2.length);

  while (_v1.length < len) {
    _v1.push('0');
  }
  while (_v2.length < len) {
    _v2.push('0');
  }

  for (let i = 0; i < len; i++) {
    const num1 = Number(_v1[i]);
    const num2 = Number(_v2[i]);

    if (num1 > num2) {
      return 1;
    }
    if (num1 < num2) {
      return -1;
    }
  }

  return 0;
}

/** 预览PDF、Word和Excel文件 */
export function openDocument(url: string | undefined) {
  if (!url) {
    Toast.warning('文件不存在', {
      duration: 2000,
    });
    return;
  }
  loading.show('加载中...');
  uni.downloadFile({
    url,
    success: (res) => {
      loading.hide();
      const filePath = res.tempFilePath;
      const ext = url.split('.').pop();
      loading.show('正在打开');
      uni.openDocument({
        filePath,
        fileType: ext,
        complete: () => {
          loading.hide();
        },
      });
    },
    fail: (err) => {
      loading.hide();
      exceptionHandler(err);
    },
  });
}

export function previewImg(imgs: SC.File.Info[] | string[] = [], current = 0) {
  const urls =
    typeof imgs[0] === 'string'
      ? (imgs as string[])
      : (imgs as SC.File.Info[]).map((img) => img.remoteUrl);

  uni.previewImage({
    urls,
    current,
  });
}

interface WxLocationInfo {
  latitude: number;
  longitude: number;
}
interface WxLocationConfig extends wx.getLocationOptions {
  $wx?: typeof wx;
}

/** 微信h5 jssdk 获取定位方法 */
export function getWxLocation(config: WxLocationConfig): Promise<WxLocationInfo> {
  const _wx = config.$wx;

  delete config.$wx;

  return new Promise((resolve, reject) => {
    _wx?.getLocation({
      success: (res) => {
        resolve(res as WxLocationInfo);
      },
      fail: reject,
      ...config,
    });
  });
}

/**
 * 预览或下载文件
 * 兼容苹果手机预览文件损坏问题
 * @param url 文件url
 */
export function reviewOrCopyUrl(url: string) {
  const reviewExt = ['pdf', 'docx', 'xlsx', 'pptx', 'ppt'];
  const ext = url.split('.').pop();
  if (ext && reviewExt.includes(ext)) {
    openDocument(url);
  } else {
    uni.setClipboardData({
      data: url,
      success: () => {
        Toast.success('复制成功，请粘贴到浏览器进行查看或下载');
      },
      fail: () => {
        Toast.error('复制失败');
      },
    });
  }
}

export function scanCode(): Promise<UniApp.ScanCodeSuccessRes> {
  return new Promise((resolve, reject) => {
    AdapterScanCode({
      needResult: 1,
      scanType: ['qrCode'],
      success: (res) => {
        resolve(res);
      },
      fail(err: any) {
        reject(err);
      },
    });
  });
}

interface ResGetPrivacySetting {
  /** 是否需要用户授权隐私协议 */
  needAuthorization: boolean;
  /** 小程序用户隐私协议名称 */
  privacyContractName: string;
}

export function getPrivacySetting(): Promise<ResGetPrivacySetting> {
  return new Promise((resolve, reject) => {
    uni.getPrivacySetting({
      success: resolve,
      fail: reject,
    });
  });
}
