import { Sentry } from '@shencom/plugins';
import { FormatDateTime, IsDev, IsPro } from '@rms/utils';
import { createSSRApp } from 'vue';
import Register from '@rms/register';

interface Options {
  /** vue实例 */
  Vue: ReturnType<typeof createSSRApp>;
  /** 项目版本号 */
  version: string;
  /** 项目名 */
  projectName: string;
  /** 租户scid */
  scid: string;
  /** 用于连接到 Sentry 并识别项目的 Dsn。如果省略，则 SDK不会向Sentry发送任何数据。 */
  dsn: string;
}
/** 初始化 */
function SentryInstall(option: Options) {
  const { Vue, version, projectName, scid, dsn } = option;

  const release = version + (IsPro ? '' : '.tst');
  const isDev = IsDev;
  const isPro = IsPro;

  if (!isPro) return;

  Sentry.install({
    Vue,
    dsn,
    scid,
    isDev,
    isPro,
    release,
    beforeBreadcrumb(breadcrumb, hint) {
      return breadcrumb.level === 'log' ? null : breadcrumb;
    },
    beforeSend(event) {
      // 上报拦截，根据需要自定义上报数据
      const base = {
        scid,
        projectName,
        date: FormatDateTime(),
        environment: IsPro ? '正式' : '测试',
        origin: window.location.origin,
        hash: window.location.hash,
        pathname: window.location.pathname,
        search: window.location.search,
      };

      // 上报 tag.name 可以通过当前用户名来查找全部出现异常的情况
      const register = new Register();
      const user = register.RmsUserInfo.getUserInfo();

      if (!event.user) event.user = {};
      Object.assign(event.user, user);

      if (!event.extra) event.extra = {};
      Object.assign(event.extra, base);

      return event;
    },
  });

  // 拦截路由跳转
  const fail = (args: any) => {
    Sentry.captureMessage('路由跳转错误', {
      tags: {
        name: '路由跳转错误',
      },
      extra: {
        info: args,
        error: args.errMsg,
        url: args.url,
      },
    });
  };

  uni.addInterceptor('redirectTo', { fail });

  uni.addInterceptor('navigateTo', { fail });
}

export { SentryInstall };
