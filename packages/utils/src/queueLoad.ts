import { exceptionHandler } from './error';

interface LoadConfig {
  // script标签id
  id: string;
  // 加载链接
  src: string;

  onload?: (script?: HTMLScriptElement) => void;
}

const queue: Record<string, { success: Function[]; fail: Function[] }> = {};

/**
 * 队列加载脚本，只加载一次
 * @param config
 * @returns
 */
export function queueLoad(config: LoadConfig) {
  return new Promise<HTMLScriptElement>((resolve, reject) => {
    const { id, src, onload } = config;
    const scriptDom = document.querySelector(`#${id}`);

    if (scriptDom) {
      if (scriptDom.getAttribute('loading')) {
        queue[id].success.push(resolve);
        queue[id].fail.push(reject);
      } else {
        resolve(scriptDom as HTMLScriptElement);
      }
      return;
    }

    if (!Array.isArray(queue[id]?.success)) {
      queue[id] = {
        success: [],
        fail: [],
      };
    }

    queue[id].success.push(resolve);
    queue[id].fail.push(reject);

    const script = document.createElement('script');
    script.type = 'text/javascript';
    script.async = true;
    script.src = src;
    script.id = id;
    script.setAttribute('loading', 'true');

    script.onload = () => {
      script.removeAttribute('loading');
      if (onload) {
        onload(script);
      }

      while (queue[id].success.length) {
        const fn = queue[id].success.shift();
        if (typeof fn === 'function') fn(script);
      }

      queue[id].fail = [];
    };
    script.onerror = (error) => {
      document.body.removeChild(script);

      while (queue[id].fail.length) {
        const fn = queue[id].fail.shift();
        if (typeof fn === 'function') fn(error);
      }

      queue[id].success = [];
    };

    document.body.appendChild(script);
  });
}

/**
 * 管道方法，多次异步方法调用只执行一次
 * @param targetFunction
 * @param shouldCacheResult
 * @returns
 */
export function pipeline<T>(targetFunction: () => Promise<T>, shouldCacheResult: boolean = false) {
  let promiseResult: T, startedPromise: Promise<T> | undefined;

  return (): Promise<T> => {
    if (shouldCacheResult && promiseResult) {
      return Promise.resolve(promiseResult);
    }

    if (!startedPromise) {
      startedPromise = new Promise((resolve, reject) => {
        targetFunction()
          .then((result) => {
            shouldCacheResult && result && (promiseResult = result);
            resolve(result);
          })
          .catch((error) => reject(error))
          .finally(() => {
            startedPromise = undefined;
          });
      });
    }

    return startedPromise;
  };
}
