import { Is<PERSON>eixin } from './evn';
import Toast from './toast';

/**
 * 获取图片信息，网络图片需先配置download域名才能生效
 *
 * @param {string} url 图片链接（网络链接 / 临时链接）
 * @returns {object} 图片宽高等
 */
export const getImageInfo = (url: string) =>
  // eslint-disable-next-line @typescript-eslint/ban-ts-comment
  // @ts-ignore
  new Promise<UniApp.GetImageInfoSuccessData>((resolve, reject) => {
    uni.getImageInfo({
      src: url,
      success: (res) => {
        if (!url.includes('http')) {
          res.path = process.env.NODE_ENV === 'production' ? res.path : `/${res.path}`;
        }
        resolve(res);
      },
      fail: (err) => {
        reject(err);
        Toast.error('网络错误请重试');
      },
    });
  });

/**
 * 根据屏幕像素比获取缩放比例
 * @returns {number}
 */
const createRpx2px = (times = 1) => {
  if (!IsWeixin) return () => times;
  const { windowWidth } = uni.getSystemInfoSync();
  return (px: number) => (windowWidth / 750) * (times * px);
};

/**
 * 根据2倍屏幕像素比获取尺寸大小
 * @returns {number}
 */
export const rpx2px = createRpx2px(2);

/**
 * canvas 导出图片
 *
 * @param {string} canvasId
 * @returns {string} 临时图片链接
 */
export const canvasToTempFilePath = (canvasId: string) =>
  new Promise<string>((resolve, reject) => {
    uni.canvasToTempFilePath({
      canvasId,
      success: (res) => {
        resolve(res.tempFilePath);
      },
      fail: reject,
    });
  });

/**
 * base64转为二进制流
 */
const toBlob = (baseStr: string, type: string) => {
  const text = window.atob(baseStr.split(',')[1]);
  const buffer = new ArrayBuffer(text.length);
  const ubuffer = new Uint8Array(buffer);

  for (let i = 0; i < text.length; i++) {
    ubuffer[i] = text.charCodeAt(i);
  }

  // eslint-disable-next-line @typescript-eslint/ban-ts-comment
  // @ts-ignore
  const Builder = window.WebKitBlobBuilder || window.MozBlobBuilder;
  let blob;
  if (Builder) {
    const builder = new Builder();
    builder.append(buffer);
    blob = builder.getBlob(type);
  } else {
    blob = new window.Blob([buffer], {
      type,
    });
  }
  return blob;
};

/**
 * 图片压缩
 */
export const compressPic = (file: File & { path: string }) => {
  if (!file) {
    Toast.error('图片不存在');
  }
  const canvas = document.createElement('canvas');
  const ctx = canvas.getContext('2d');

  const tCanvas = document.createElement('canvas');
  const tCtx = tCanvas.getContext('2d');

  return new Promise<Blob>((resolve) => {
    const initSize = file.size;
    const image = new Image();
    image.src = file.path;
    image.onload = () => {
      if (!ctx || !tCtx) return;
      let width = image.width;
      let height = image.height;

      let ratio = (width * height) / 4000000;
      if (ratio > 1) {
        width /= ratio;
        height /= ratio;
      } else {
        ratio = 1;
      }

      canvas.width = width;
      canvas.height = height;

      // 铺底色
      ctx.fillStyle = '#fff';
      ctx.fillRect(0, 0, canvas.width, canvas.height);

      let count = (width * height) / 1000000;
      if (count > 1) {
        count = Math.ceil(count);
        const nw = Math.floor(width / count);
        const nh = Math.floor(height / count);
        tCanvas.width = nw;
        tCanvas.height = nh;
        tCtx.drawImage(image, 0, 0, nw * ratio, nh * ratio, 0, 0, nw, nh);
        ctx.drawImage(tCanvas, 0, 0);
        tCtx.drawImage(image, nw * ratio, 0, nw * ratio, nh * ratio, 0, 0, nw, nh);
        ctx.drawImage(tCanvas, nw, 0);
        for (let i = 0; i < count; i++) {
          for (let j = 0; j < count; j++) {
            tCtx.drawImage(
              image,
              i * nw * ratio,
              j * nh * ratio,
              nw * ratio,
              nh * ratio,
              0,
              0,
              nw,
              nh,
            );

            ctx.drawImage(tCanvas, i * nw, j * nh);
          }
        }
      } else {
        ctx.drawImage(image, 0, 0, width, height);
      }

      // 进行压缩
      const vData = canvas.toDataURL('image/jpeg', 0.7);
      console.log(`压缩前: ${(initSize / 1024 / 1024).toFixed(1)}Mb`);
      console.log(`压缩前: ${(vData.length / 1024).toFixed(1)}Kb`);
      tCanvas.width = 0;
      tCanvas.height = 0;
      canvas.width = 0;
      canvas.height = 0;
      resolve(toBlob(vData, file.type));
    };
  });
};
