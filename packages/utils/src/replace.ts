import { IsH5 } from './evn';

function replaceWxml(original: string, keepEOL = false) {
  const res = original
    .replace(/\[/g, '_l_') // [
    .replace(/\]/g, '_r_') // ]
    .replace(/\(/g, '_p_') // (
    .replace(/\)/g, '_q_') // )
    .replace(/#/g, '_h_') // hex
    .replace(/!/g, '_i_') // css !important
    .replace(/\//g, '-div-') // /
    .replace(/\./g, '-dot-') // .
    // :
    .replace(/:/g, '_c_')
    // https://github.com/sonofmagic/weapp-tailwindcss-webpack-plugin/issues/8
    .replace(/%/g, '_pct_');

  if (IsH5) {
    return original;
  }

  if (keepEOL) {
    return res;
  }

  return (
    res
      // 去除无用换行符和空格
      .replace(/[\r\n]+/g, '')
  );
}

// .sm\\:text-3xl
// css 中，要多加一个 '\' 来转义
function cssSelectorReplacer(selector: string) {
  return (
    selector
      .replace(/\\\[/g, '_l_') // \[
      .replace(/\\\]/g, '_r_') // \]
      .replace(/\\\(/g, '_p_') // \(
      .replace(/\\\)/g, '_q_') // \)
      .replace(/\\#/g, '_h_') // \# : hex
      .replace(/\\!/g, '_i_') // \! : !important
      .replace(/\\\//g, '-div-') // \/ : w-1/2 -> width:50%
      .replace(/\\\./g, '-dot-') // \. : w-1.5
      // \\:
      .replace(/\\:/g, '_c_') // colon for screen
      // https://github.com/sonofmagic/weapp-tailwindcss-webpack-plugin/issues/8
      .replace(/\\%/g, '_pct_')
  );
}

export { cssSelectorReplacer as replaceCss, replaceWxml as replaceJs };
