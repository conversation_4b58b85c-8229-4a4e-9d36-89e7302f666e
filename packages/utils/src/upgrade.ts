import { StorageLRU } from './storage';
import { IsDev } from './evn';

/**
 * 检查小程序最新版本，并强制更新到最新版本
 *
 * @param {(...args: any[]) => any} callback 执行更新后的回调函数
 */
export function updateMiniprogram(callback?: (...args: any[]) => any) {
  const updateManager = uni.getUpdateManager && uni.getUpdateManager();
  if (!updateManager) return;

  updateManager.onCheckForUpdate((res) => {
    console.log('小程序是否可更新---', res.hasUpdate);
    if (res.hasUpdate) {
      uni.showLoading({ title: '新版本更新中...' });
    }
  });

  updateManager.onUpdateReady(() => {
    console.log('更新包下载成功---');
    updateManager.applyUpdate();
    uni.hideLoading();
    if (callback) callback();
  });

  updateManager.onUpdateFailed((error) => {
    console.log('小程序更新失败---', error);
    uni.showToast({ title: '更新失败，请手动删除小程序进行更新', icon: 'none' });
    if (callback) callback();
  });
}

/**
 * 清除旧版本的缓存数据，包括旧环境（正式服/测试服切换）、旧小程序版本
 */
export function clearOldStorage(version: string, storage: StorageLRU) {
  const env = storage.get('lasting_env_dev');
  const app_v = storage.get('lasting_version');

  if (IsDev !== env) {
    storage.matchClear(['prefixUser']);
    storage.set('lasting_env_dev', IsDev);
    storage.set('lasting_version', version);
  } else if (version !== app_v) {
    storage.clear();
    storage.set('lasting_version', version);
  }
}
