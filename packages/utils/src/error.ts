import Toast from './toast';
// #ifdef H5
import { Sentry } from '@shencom/plugins';
// #endif

const responseError = (error: any) => {
  console.log('请求错误--- :', error, '\n', { ...error });

  if (!error || !error.statusCode) return;

  const { config, request, response, message } = error;

  const method = (config.method && config.method.toLocaleUpperCase()) || '';

  const { url = '' } = config;
  const { status } = request;
  const data = error?.data || response?.data;
  const body = config.params || JSON.parse(config.data || '{}');

  // #ifdef H5
  Sentry.setRequest({
    url,
    data,
    body,
    status,
    method,
    message,
    header: config.headers || {},
  });
  // #endif
};

const tryError = (error: any) => {
  console.log('脚本错误--- :', error, { ...error });

  // #ifdef H5
  Sentry.captureException(error);
  // #endif
};

/** 全局错误处理
 *
 * @param {(Error | HttpError)} error 错误信息
 * @param {boolean} [isHttp=false] 在请求拦截中传 `true` 为了防止请求错误重复上报
 * @return {*}
 */
export const exceptionHandler = (error: any, isHttp = false) => {
  console.log('error :>> ', error);

  // 如果是初始化中取消的请求不处理
  if (!error || (!uni.initRouter && error.name === 'CanceledError')) return;

  if (error.statusCode) {
    if (isHttp) responseError(error);
  } else {
    tryError(error);
  }
};

export const exceptionToast = (error: any, msg: string) => {
  if (!error || (!uni.initRouter && error.name === 'CanceledError')) return;

  exceptionHandler(error);

  if (error) {
    const errmsg = error.data?.errmsg || error.data?.message || error.message || error.errMsg;
    msg += ` ${errmsg || error.statusCode || ''}`;
  }

  Toast.error(msg);
  return msg;
};

export const returnErrmsg = (error: any) => {
  let msg = '';
  if (error) {
    msg = error.data?.errmsg || error.data?.message || error.message || '';
  }
  return msg;
};
