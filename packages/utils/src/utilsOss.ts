import { ScOssBase } from '@shencom/utils';
import { IsDeploy, IsDev, IsH5, IsPro, deployOss } from './evn';
import Register from '@rms/register';
export class _utilsOss extends ScOssBase {
  constructor(name: string) {
    const devAssetsPath = IsH5 ? (IsDev ? '/public' : '') : '';
    super({
      env: IsDev ? 'dev' : IsPro ? 'pro' : 'tst',
      projectPath: `${deployOss || 'app'}/${name}`,
      devAssetsPath,
    });
  }

  /** 本地或 oss 项目图片路径 */
  get imgPath() {
    return `${IsDeploy ? '.' : this.path}/static/images`;
  }

  /** oss 图片路径 */
  get ossImgPath() {
    return `${IsDeploy ? '.' : this.path}/static/images`;
  }

  /** oss 资源根路径 */
  get ossPath() {
    return `${IsDeploy ? '.' : this.path}/static`;
  }

  /** 本地项目图片路径 */
  get localityPath() {
    return `/static/images`;
  }

  get logo() {
    const register = new Register();
    return `${this.imgPath}/${register.RmsAppConfig.base.logo || 'logo.png'}`;
  }

  /** 图片百分比缩放，默认: `50%` */
  zoomPercent(percent = 50) {
    return IsDeploy ? '' : super.zoomPercent(percent);
  }

  /** 自定义图片缩放
   * - [参考链接](https://help.aliyun.com/document_detail/44688.html)
   */
  zoomCustom(param: { w?: string; h?: string; m?: string } = {}) {
    return IsDeploy ? '' : super.zoomCustom(param);
  }
}
