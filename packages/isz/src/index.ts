import Register from '@rms/register';
import { queueLoad } from '@rms/utils/src';
import { exception<PERSON><PERSON><PERSON>, Toast, Dialog } from '@rms/utils';
import {
  ApiGetIShenzhenInitCode,
  ApiIShenzhenGetUserInfo,
  ApiIShenzhenLogin,
  ApiIShenzhenLoginByRequestCode,
} from './api';
import { Dictionary, IShenzhenAppConfig, LngLat } from '@rms/types';
import { ISZInit } from '@rms/cdn';

const getConfig = () => {
  const register = new Register();
  const { isz = {} } = register.RmsAppConfig;
  return isz;
};

class IShenzhen {
  // i深圳 sdk 是否配置成功
  private static isInit = false;

  private static appConfig: IShenzhenAppConfig = {
    debug: false,
    appId: '',
    // 需要调用i深圳的JS接口列表
    nativeApis: [
      'phoneCall',
      'notification',
      'sms',
      'contact',
      'gps',
      'navigation',
      'qrCode',
      'openLocation',
      'reportRecord',
      'chooseImage',
      'verify',
      'face',
      'isLogin',
      'login',
      'getCityIndividualToken',
    ],
  };

  static setConfig(appConfig: Partial<IShenzhenAppConfig>) {
    IShenzhen.appConfig = { ...IShenzhen.appConfig, ...appConfig };
  }

  static getIsInit() {
    return IShenzhen.isInit;
  }

  /** 加载 i深圳 sdk */

  static async load() {
    try {
      await ISZInit();
    } catch (error) {
      await Dialog(`加载 isz sdk 失败: ${error}`, { showCancel: false });
      throw error;
    }
  }

  /**
   * 获取InitCode
   *
   * @returns {(Promise<string | null>)}
   * @memberof IShenzhen
   */
  static async getInitCode(): Promise<string | null> {
    try {
      const data = await ApiGetIShenzhenInitCode();
      return data;
    } catch (error) {
      const flag = await Dialog('获取initCode失败，是否再次尝试?');
      if (flag) {
        const data = await IShenzhen.getInitCode();
        return data;
      }
      return null;
    }
  }

  /**
   * 根据initCode注入权限验证配置
   *
   * @
   * @param {string} initCode
   * @returns {Promise<boolean>}
   * @memberof IShenzhen
   */
  static async initAppConfig(initCode: string): Promise<boolean> {
    if (!IShenzhen.appConfig.appId) {
      await Dialog('接入i深圳的应用 appId 未定义');
      throw new Error('接入i深圳的应用 appId 未定义');
    }
    return new Promise((resolve) => {
      sc.config({
        debug: IShenzhen.appConfig.debug,
        appId: IShenzhen.appConfig.appId,
        initCode,
        nativeApis: IShenzhen.appConfig.nativeApis,
      });
      sc.ready(() => {
        IShenzhen.isInit = true;
        resolve(true);
      });
      sc.error(async (errMsg) => {
        resolve(false);
        await Dialog('i深圳 sdk config 失败' + errMsg, { showCancel: false });
        sc.close();
      });
    });
  }

  /**
   * 判断APP上用户是否登录
   *
   * @
   * @returns {Promise<boolean>}
   * @memberof IShenzhen
   */
  static appIsLogin(): Promise<boolean> {
    return new Promise((resolve) => {
      sc.isLogin(({ data }) => {
        resolve(data.status);
      });
    });
  }

  /**
   * 调用APP用户登录api
   *
   * @
   * @returns {Promise<boolean>}
   * @memberof IShenzhen
   */
  static async appLogin(): Promise<boolean> {
    const isLogin = await IShenzhen.appIsLogin();
    if (isLogin) return true;

    return new Promise((resolve) => {
      sc.login((res) => {
        resolve(res.code === 0);
      });
    });
  }

  /**
   * 获取requestCode
   * @returns {Promise<string | null>}
   */
  static async getRequestCode(): Promise<string | null> {
    return new Promise((resolve) => {
      sc.userAuth({ appId: IShenzhen.appConfig.appId }, async (res: Dictionary) => {
        if (res.code === 0) {
          const { requestCode } = res.data;
          console.log('requestCode: ', requestCode);
          resolve(requestCode);
          return;
        }

        const flag = await Dialog('允许授权才可使用哦~');
        if (flag) {
          const data = await IShenzhen.getRequestCode();
          resolve(data);
        } else {
          await Dialog('用户未允许授权: ' + res.code, { showCancel: false });
          sc.close();
          resolve(null);
        }
      });
    });
  }

  /**
   * 校验 requestCode 是否登录成功
   *
   * @static
   * @returns {Promise<CodeCheckResult | null>}
   * @memberof IShenzhen
   */
  static async checkRequestCode() {
    try {
      // 获取requestCode
      const requestCode = await IShenzhen.getRequestCode();
      if (!requestCode) return null;

      // 根据APP获取的requestCode在后台登录,并判断是否登录成功
      const data = await IShenzhen.loginByRequestCode(requestCode);
      const register = new Register();
      register.RmsStorage.set('iszRequestParam', data, data && data.expiresIn);
      return data;
    } catch (error) {
      console.log('error: ', error);
    }
    return null;
  }

  /**
   * 根据APP获取的requestCode在后台登录
   *
   * @static
   * @param {string} requestCode
   * @returns {Promise<CodeCheckResult | null>}
   * @memberof IShenzhen
   */
  static async loginByRequestCode(requestCode: string) {
    try {
      const data = await ApiIShenzhenLoginByRequestCode(requestCode);
      console.log('%c loginByRequestCode: ', 'color:#40b883;font-weight:bold', data);
      if (!data) {
        sc.close();
        return null;
      }
      return {
        userAccessToken: data.userAccessToken,
        verifyResult: data.verifyResult,
        expiresIn: data.expiresIn,
      };
    } catch (error) {
      try {
        const flag = await Dialog('后台登录失败,是否再次尝试?');
        if (flag) await IShenzhen.loginByRequestCode(requestCode);
      } catch (err) {
        sc.close();
        return null;
      }
    }
    return null;
  }

  /**
   * 用 i深圳 用户信息注册登录深分类
   *
   * @
   * @param {RequestParams} params
   * @returns {Promise<UserRoot | null>}
   * @memberof IShenzhen
   */
  static async loginShencom(requestCode: string) {
    try {
      const appConfig = getConfig();
      if (typeof appConfig.login === 'function') {
        const data = await appConfig.login(requestCode);
        return data;
      }

      // 获取 userAccessToken 和 校验结果 verifyResult
      const userAccessToken = await IShenzhen.checkRequestCode();
      console.log('userAccessToken: ', userAccessToken);
      if (userAccessToken) {
        // const iszUser = await ApiIShenzhenGetUserInfo(userAccessToken);
        // 用i深圳用户信息登录深分类
        const data = await ApiIShenzhenLogin(userAccessToken);
        return data;
      }
    } catch (error) {
      await Dialog('用户登录失败', { showCancel: false });
      sc.close();
      console.error('login shencom error', error);
    }
    return null;
  }

  /**
   * 用户授权登录深分类
   *
   * @
   * @param {UserRoot} data 登录后返回的系统用户信息
   * @returns {Promise<UserRoot | null>}
   * @memberof IShenzhen
   */
  static async login() {
    // 判断APP上用户是否登录
    const isLogin = await IShenzhen.appLogin();
    if (!isLogin) {
      exceptionHandler(Error('登录失败'));
      await Dialog('app登录失败', { showCancel: false });
      sc.close();
      return false;
    }

    try {
      const requestCode = await IShenzhen.getRequestCode();
      if (!requestCode) return null;

      // 用i深圳用户信息登录百分百
      const data = await IShenzhen.loginShencom(requestCode);
      if (data) {
        const register = new Register();
        register.RmsUserInfo.setResponseSysUserInfo(data);
      }
      return !!data;
    } catch (error) {
      console.error('login error: ', error);
      return false;
    }
  }

  /**
   * 初始化APP配置
   * @returns {Promise<boolean>}
   */
  static async init() {
    if (IShenzhen.getIsInit()) return true;
    try {
      await IShenzhen.load();

      const appConfig = getConfig();
      if (!appConfig.config) return false;

      IShenzhen.setConfig(appConfig.config);

      if (typeof appConfig.init === 'function') {
        const flag = await appConfig.init();
        return flag;
      }
      // 获取InitCode
      const initCode = await IShenzhen.getInitCode();
      if (initCode) {
        // 根据initCode注入权限验证配置
        IShenzhen.isInit = await IShenzhen.initAppConfig(initCode);
        return IShenzhen.isInit;
      }
    } catch (error) {
      console.error('isz init error: ', error);
    }
    return false;
  }

  /**
   * 打开地图坐标
   *
   * @param {Location} location
   * @memberof IShenzhen
   */
  static openLocation(location: sc.LocationOptions) {
    IShenzhen.init().then((isInit) => {
      if (isInit) sc.openLocation(location);
    });
  }

  /**
   * 获取当前定位
   *
   * @param {sc.GPSType} [type=1]
   * @returns {Promise<LngLat>}
   * @memberof IShenzhen
   */
  static getLocation(type: sc.GPSType = 1): Promise<LngLat> {
    return new Promise((resolve, reject) => {
      IShenzhen.init()
        .then((isInit) => {
          if (!isInit) {
            reject(new Error('i深圳 sdk 初始化失败'));
            return;
          }
          sc.gps({ type }, (res) => {
            if (res.code === 0) {
              resolve({ lng: res.data.longitude!, lat: res.data.latitude! });
            } else {
              reject(res);
            }
          });
        })
        .catch(reject);
    });
  }
  /**
   * 选择图片
   *
   * @param {sc.ChooseImageParams} params
   * @returns {Promise<object>}
   * @memberof IShenzhen
   */
  static chooseImage(
    params: sc.ChooseImageParams & {
      success?: (res: UniNamespace.ChooseImageSuccessCallbackResult) => void;
      fail?: (reason?: any) => void;
    },
  ): Promise<any> {
    const {
      count = 1,
      sourceType = ['album', 'camera'],
      sizeType = ['original', 'compressed'],
      success,
      fail,
    } = params;
    return new Promise((resolve, reject) => {
      IShenzhen.init()
        .then((isInit) => {
          if (!isInit) {
            reject(new Error('i深圳 sdk 初始化失败'));
            return;
          }
          sc.chooseImage({ count, sourceType, sizeType }, (res) => {
            if (res.code === 0) {
              if (success) success(res.data);
              resolve(res.data);
            } else {
              if (fail) fail(res);
              reject(res);
            }
          });
        })
        .catch(reject);
    });
  }
}
export default IShenzhen;
