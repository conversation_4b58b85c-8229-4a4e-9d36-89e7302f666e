import Register from '@rms/register';

export interface CodeCheckResult {
  userAccessToken: string;
  verifyResult: boolean;
  expiresIn: number;
}

interface IShenzhenUserInfo {
  loginName: string;
  mobileNo: string;
  openId: string;
  realNameStatus: number;
  sex: string;
  unionId: string;
}

/** 获取 i深圳 jssdk 初始化的 code */
export const ApiGetIShenzhenInitCode = async () => {
  const register = new Register();
  const { data } = await register.RmsHttp.get<{ initCode: string }>(
    '/service-uaa/sys/user/isz/initcode',
  );
  return data.initCode;
};

/**
 * 根据APP获取的requestCode在后台登录
 *
 * @static
 * @param {string} requestCode
 * @returns {Promise<CodeCheckResult | null>}
 * @memberof IShenzhen
 */
export const ApiIShenzhenLoginByRequestCode = async (requestCode: string) => {
  const formData = new FormData();
  formData.append('requestCode', requestCode);
  formData.append('iSlegalPerson', '0');

  const register = new Register();
  const { data } = await register.RmsHttp.post(
    '/service-uaa/sys/user/isz/requestcode/check',
    formData,
  );
  return data;
};

/**
 * 获取请求参数
 *
 * @static
 * @param {CodeCheckResult} [data] requestCode 校验结果
 * @returns {FormData} {userAccessToken:string; iSlegalPerson:'0'}
 * @memberof IShenzhen
 */
const getRequestParams = (data?: CodeCheckResult) => {
  const register = new Register();

  const params = data || register.RmsStorage.get<CodeCheckResult>('iszRequestParam');
  console.log('params: ', params);

  const { userAccessToken = '', verifyResult = false } = params || {};
  if (verifyResult) {
    const formData: any = new FormData();
    formData.append('userAccessToken', userAccessToken);
    formData.append('iSlegalPerson', '0');
    console.log('userAccessToken: ', userAccessToken);
    return formData;
  }
  return null;
};

/**
 * 获取i深圳用户信息
 *
 * @static
 * @param {CodeCheckResult} data
 * @returns {(Promise<iShenzhenUserInfo | null>)}
 * @memberof IShenzhen
 */
export const ApiIShenzhenGetUserInfo = async (
  data: CodeCheckResult,
): Promise<IShenzhenUserInfo | null> => {
  const formData = getRequestParams(data);
  if (formData) {
    try {
      const register = new Register();
      const { data: userInfo } = await register.RmsHttp.post<IShenzhenUserInfo>(
        '/service-uaa/sys/user/isz/userinfo',
        formData,
      );
      return userInfo;
    } catch (error) {
      console.error(`获取 i 深圳用户信息失败: ${error}`);
    }
  }
  return null;
};

/** 使用i深圳用户信息登录 */
export const ApiIShenzhenLogin = async (params: CodeCheckResult) => {
  const formData = getRequestParams(params);
  if (!formData) return null;

  try {
    const register = new Register();
    const { data } = await register.RmsHttp.post<{ oAuth2AccessToken: SC.User.RootInfo }>(
      '/service-uaa/sys/user/isz/login',
      formData,
    );
    console.log('%c loginShencom: ', 'color:#40b883;font-weight:bold', data);
    return data && data.oAuth2AccessToken;
  } catch (error) {
    console.error('loginShencom error', error);
  }
  return null;
};
