// @ts-nocheck
import Register from '@rms/register';
import { useLogin } from '@rms/router';
import { MainConfig, SubConfig, Route } from '@rms/router';
import { getCurrentInstance } from 'vue';

function getPages() {
  const register = new Register();
  return register.RmsPages;
}

export function getMainPages() {
  const pages = getPages();

  return (pages.pages as MainConfig[]).map((v) => ({
    path: `/${v.path}`,
    title: v?.style?.navigationBarTitleText || '-',
    meta: v.meta || {},
    name: v.name,
  }));
}

export function getSubPackages() {
  const pages = getPages();

  return (pages.subPackages as SubConfig[]).flatMap((v) =>
    v.pages.map((e) => ({
      path: `/${v.root}/${e.path}`,
      title: e?.style?.navigationBarTitleText || '',
      meta: e.meta || {},
      name: e.name,
    })),
  );
}

/** 合并路由对象 */
export function initRoutes() {
  const mainPages = getMainPages();
  const subPackages = getSubPackages();
  const routes: Route[] = mainPages.concat(subPackages);

  return routes;
}

function isPageRoute(id: string) {
  const routes = initRoutes();

  return routes.some((r) => r.path.includes(id));
}

export default {
  data() {
    return {
      __init__: false,
      __login__: false,
    };
  },
  created() {
    this.__init__ = false;
    const route = getCurrentInstance()?.proxy?.route || getCurrentInstance()?.__route__;
    const uid = getCurrentInstance()?.uid;
    this.__key__ = `${uid}`;
    if (!route || !isPageRoute(route)) return;
    const { remove } = useLogin(uid, true);
    uni.$on(`__login_success__${uid}`, (isInit = true) => {
      this.__init__ = isInit;
      this.__login__ = !isInit;
    });
    remove({
      url: route,
    });
  },
};
