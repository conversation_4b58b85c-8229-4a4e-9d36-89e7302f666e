import Register from '@rms/register';
import { ErrorLoginType, H5LoginType, MiniLoginType } from './mode';
import { ApiGetScCode } from '@shencom/api';
import { ServiceLogin } from '@rms/service';
import { exceptionHandler } from '@rms/utils';

export async function refreshLogin(type: 'h5' | 'mini') {
  try {
    const { refresh } = uni.initLoginOptions || {};

    if (!refresh) return ErrorLoginType.nextLogin;

    const register = new Register();

    if (!register.RmsUserInfo.isLogin()) return ErrorLoginType.exitLogin;

    const { data } = await ApiGetScCode();
    const sccode = data.code;

    if (!sccode) return ErrorLoginType.exitLogin;

    await ServiceLogin.scCode(sccode);

    if (!register.RmsUserInfo.isLogin()) return ErrorLoginType.exitLogin;

    const loginType = type === 'h5' ? H5LoginType.RefreshLogin : MiniLoginType.RefreshLogin;

    return loginType;
  } catch (error) {
    exceptionHandler(error, true);
    return ErrorLoginType.exitLogin;
  }
}
