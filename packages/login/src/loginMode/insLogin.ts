import Register from '@rms/register';
import { exceptionToast, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ev, IsINan<PERSON>han } from '@rms/utils';
import { ErrorLoginType, H5LoginType } from './mode';

export async function iNanShanLogin() {
  if (!IsINanShan) return ErrorLoginType.nextLogin;

  const businessToken = GetUrlKey('businessToken', window.location.href) || '';

  if (!businessToken) return H5LoginType.errorLogin;

  try {
    const data = await iNanShanAuth(businessToken);
    const register = new Register();
    await register.RmsUserInfo.setRootInfo(data);

    return register.RmsUserInfo.isLogin() ? H5LoginType.insLogin : H5LoginType.errorLogin;
  } catch (error) {
    exceptionToast(error, 'i南山登录失败');
    return H5LoginType.errorLogin;
  }
}

async function iNanShanAuth(businessToken: string) {
  const register = new Register();

  const api = `${IsDev ? 'https://tst-app.shencom.cn' : ''}/service-uaa/ns/sys/users/auth`;
  const { data } = await register.RmsHttp.post<SC.User.RootInfo>(api, {
    casToken: businessToken,
  });

  return data;
}
