import { ServiceLogin } from '@rms/service';
import { Dictionary } from '@rms/types';
import { Toast } from '@rms/utils';
import { ErrorLoginType, H5LoginType } from './mode';

export async function h5OAuthLogin() {
  // 获取页面路由参数
  const currentPage: Dictionary = getCurrentPages()[getCurrentPages().length - 1];
  const { options } = currentPage.$page;

  console.log('自动登录 options :>> ', options);

  const { uid, appid, content, nonce, signature, timestamp } = options;

  if (![appid, content, nonce, signature, timestamp].every(Boolean)) {
    return ErrorLoginType.nextLogin;
  }

  console.log('自动登录 uid :>> ', uid);

  try {
    await ServiceLogin.OAuthLogin({ appid, content, nonce, signature, timestamp });
    return H5LoginType.OAuthLogin;
  } catch (error) {
    Toast.error('登录失败');
    setTimeout(() => {
      uni.$wx.miniProgram.navigateBack();
    }, 1000);
    return ErrorLoginType.exitLogin;
  }
}
