import Register from '@rms/register';
import { ServiceLogin } from '@rms/service';
import { exceptionHandler } from '@rms/utils';
import { ErrorLoginType, MiniLoginType } from './mode';

export async function miniAutoLogin() {
  try {
    const flag = await ServiceLogin.autoLogin();
    const register = new Register();

    return flag && register.RmsUserInfo.isLogin()
      ? MiniLoginType.autoLogin
      : ErrorLoginType.nextLogin;
  } catch (error) {
    exceptionHandler(error);
    return ErrorLoginType.nextLogin;
  }
}
