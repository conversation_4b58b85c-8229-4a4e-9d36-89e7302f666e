import Register from '@rms/register';
import { ServiceLogin } from '@rms/service';
import { exceptionHandler } from '@rms/utils';
import { ErrorLoginType, H5LoginType, MiniLoginType } from './mode';

export async function refreshToken(type: 'h5' | 'mini') {
  const { force } = uni.initLoginOptions || {};
  if (force) return ErrorLoginType.nextLogin;

  try {
    const register = new Register();

    if (!register.RmsUserInfo.getRefreshToken()) return ErrorLoginType.nextLogin;

    await ServiceLogin.refreshToken();

    const loginType = type === 'h5' ? H5LoginType.refreshToken : MiniLoginType.refreshToken;

    return register.RmsUserInfo.isLogin() ? loginType : ErrorLoginType.nextLogin;
  } catch (error) {
    exceptionHandler(error);
    return ErrorLoginType.nextLogin;
  }
}
