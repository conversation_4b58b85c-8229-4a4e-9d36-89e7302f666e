import Register from '@rms/register';
import { ServiceLogin } from '@rms/service';
import { exceptionHandler, IsDev } from '@rms/utils';
import { ErrorLoginType, H5LoginType } from './mode';

export function canSilentLogin() {
  const register = new Register();
  const { h5 } = register.RmsAppConfig.login;

  return !IsDev && h5?.silentLogin;
}

async function silentLogin() {
  try {
    await ServiceLogin.wxLogin();
    const register = new Register();

    if (register.RmsUserInfo.isLogin()) return H5LoginType.silentLogin;

    const phone = register.RmsUserInfo.getPhone();
    const uid = register.RmsUserInfo.getUid();

    if (!(phone && uid)) return H5LoginType.silentBind;

    return ErrorLoginType.silentError;
  } catch (error) {
    exceptionHandler(error);
    return ErrorLoginType.silentError;
  }
}

/**
 * 微信h5 静默授权
 */
export async function h5WxSilentLogin() {
  try {
    if (!canSilentLogin()) return ErrorLoginType.nextLogin;

    const { code = uni.ext.code } = ServiceLogin.getUrlParams();

    if (code) {
      return await silentLogin();
    }

    await ServiceLogin.getWxLoginCode();

    return ErrorLoginType.keepLoading;
  } catch (error) {
    exceptionHandler(error);
    return ErrorLoginType.exitLogin;
  }
}
