import Register from '@rms/register';
import { exceptionToast, <PERSON><PERSON><PERSON><PERSON>ey, IsDev, IsAppBaoAn } from '@rms/utils';
import { ErrorLoginType, H5LoginType } from './mode';

export async function appBaoanLogin() {
  if (!IsAppBaoAn) return ErrorLoginType.nextLogin;

  const code = GetUrlKey('code', window.location.href) || '';

  if (!code) return H5LoginType.errorLogin;

  try {
    const data = await appBaoanAuth(code);
    const register = new Register();
    await register.RmsUserInfo.setRootInfo(data);

    return register.RmsUserInfo.isLogin() ? H5LoginType.appBaoAnLogin : H5LoginType.errorLogin;
  } catch (error) {
    exceptionToast(error, '宝安专区登录失败');
    return H5LoginType.errorLogin;
  }
}

async function appBaoanAuth(code: string) {
  const register = new Register();

  const api = `${IsDev ? 'https://tst-app.shencom.cn' : ''}/service-uaa/bk/auth`;
  const { data } = await register.RmsHttp.post(api, {
    code,
  });

  return data.additionalInformation as SC.User.RootInfo;
}
