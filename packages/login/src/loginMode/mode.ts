export enum MiniLoginType {
  refreshToken = 1,
  autoLogin,
  miniLogin,
  accountLogin,
  phoneLogin,
  errorLogin,
  privacy,
  /** 刷新登录信息 */
  RefreshLogin,

  /** H5LoginType的初始化，请放到最后 */
  last,
}

export enum H5LoginType {
  sccodeLogin = MiniLoginType.last,
  refreshToken,
  accountLogin,
  phoneLogin,
  silentLogin,
  silentBind,
  errorLogin,
  /** i深圳 */
  iszLogin,
  /** i福田 */
  iftLogin,
  /** i龙华 */
  ilhLogin,
  /** i盐田 */
  iytLogin,
  /** i南山 */
  insLogin,
  /** app宝安 */
  appBaoAnLogin,
  /** 跨租户 */
  OAuthLogin,
  /** 刷新登录信息 */
  RefreshLogin,

  /** ErrorLoginType的初始化，请放到最后 */
  last,
}

export enum ErrorLoginType {
  // 下一步
  nextLogin = H5LoginType.last,
  // 退出登录页
  exitLogin,
  // 需要微信环境
  needWXEnv,
  // h5微信静默授权失败
  silentError,
  // 保持加载
  keepLoading,
  // 隐私授权错误
  privacyError,
  // 重新加载页面
  reloadPage,
}

export type LoginStatusType = ErrorLoginType | H5LoginType | MiniLoginType;
