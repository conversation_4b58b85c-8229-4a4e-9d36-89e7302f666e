import Register from '@rms/register';
import { exceptionHand<PERSON>, getPrivacySetting } from '@rms/utils';
import { ErrorLoginType, LoginStatusType, MiniLoginType } from './mode';

interface ResMiniPrivacy {
  type: LoginStatusType;
  config?: {
    name: string;
    title: string;
    content: string;
  };
}

export async function miniPrivacy(): Promise<ResMiniPrivacy> {
  try {
    const register = new Register();
    const { privacy } = register.RmsAppConfig;
    if (!privacy) {
      return { type: ErrorLoginType.nextLogin };
    }

    if (!uni.canIUse('getPrivacySetting')) return { type: ErrorLoginType.nextLogin };

    const { needAuthorization, privacyContractName } = await getPrivacySetting();

    return needAuthorization
      ? {
          type: MiniLoginType.privacy,
          config: {
            name: privacyContractName,
            ...privacy,
          },
        }
      : { type: ErrorLoginType.nextLogin };
  } catch (error) {
    exceptionHand<PERSON>(error);
    return { type: ErrorLoginType.privacyError };
  }
}
