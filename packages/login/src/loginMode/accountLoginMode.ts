import Register from '@rms/register';
import { ErrorLoginType, H5LoginType, MiniLoginType } from './mode';

export function accountAndPasswordLogin(type: 'h5' | 'mini') {
  const register = new Register();
  const { mini, h5 } = register.RmsAppConfig.login;
  const isH5 = type === 'h5';
  const config = isH5 ? h5 : mini;

  if (!config?.accountLoginType) return ErrorLoginType.nextLogin;

  const isAccount = config.accountLoginType === 'account';
  const loginType = isH5 ? H5LoginType : MiniLoginType;

  return isAccount ? loginType.accountLogin : loginType.phoneLogin;
}
