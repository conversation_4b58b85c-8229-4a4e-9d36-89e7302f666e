import Register from '@rms/register';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, IsILongHua, exception<PERSON><PERSON><PERSON> } from '@rms/utils';
import { ErrorLoginType, H5LoginType } from './mode';

interface ILuoHuaLoginData {
  oAuth2AccessToken: OAuth2AccessToken;
  phoneNumber: string;
  openid: string;
  tokenid: string;
  creditableLevelOfAccount: string;
  extData: any;
}

interface OAuth2AccessToken {
  additionalInformation: any;
  scope: string[];
  refreshToken: RefreshToken;
  tokenType: string;
  expired: boolean;
  expiration: number;
  expiresIn: string;
  value: string;
}

interface RefreshToken {
  value: string;
  expiration: number;
}

interface _ {}

function getUserToken() {
  const usertoken = GetUrlParam(window.location.href).usertoken || '';

  return usertoken;
}

function getLoginType() {
  const loginType = GetUrlParam(window.location.href).loginType || '';

  return loginType;
}

export async function iLongHuaLogin() {
  try {
    if (!IsILongHua) return ErrorLoginType.nextLogin;

    const usertoken = getUserToken();
    const loginType = getLoginType();

    if (!usertoken || !loginType) return H5LoginType.errorLogin;

    const data = await iLongHuaAuth(usertoken, loginType);

    const register = new Register();
    const rootInfo: SC.User.RootInfo = {
      ...data.oAuth2AccessToken,
    };

    if (data) {
      await register.RmsUserInfo.setRootInfo(rootInfo);

      return register.RmsUserInfo.isLogin() ? H5LoginType.ilhLogin : H5LoginType.errorLogin;
    }

    return H5LoginType.errorLogin;
  } catch (error) {
    exceptionHandler(error);
    return H5LoginType.errorLogin;
  }
}

async function iLongHuaAuth(usertoken: string, loginType: string) {
  const register = new Register();

  const api = `${IsDev ? '/java' : ''}/ljfl/service-uaa/sys/user/lsh/login`;
  const { data } = await register.RmsHttp.get<ILuoHuaLoginData>(api, {
    usertoken,
    loginType,
    jkid: 'MHLOGIN_OUTER',
    loginly: loginType,
    requestly: 'ljfl',
  });

  return data;
}
