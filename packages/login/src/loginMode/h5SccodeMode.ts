import Register from '@rms/register';
import { ServiceLogin } from '@rms/service';
import { Dictionary } from '@rms/types';
import { exceptionHandler, Toast } from '@rms/utils';
import { ErrorLoginType, H5LoginType } from './mode';

async function sccodeLogin(options: any) {
  try {
    const { sccode } = options;
    await ServiceLogin.scCode(sccode);

    const register = new Register();

    return register.RmsUserInfo.isLogin();
  } catch (error) {
    exceptionHandler(error);
    return false;
  }
}

export async function h5SccodeLogin() {
  const register = new Register();
  const { h5 } = register.RmsAppConfig.login;

  if (!h5?.sccode) return ErrorLoginType.nextLogin;

  // 获取页面路由参数
  const currentPage: Dictionary = getCurrentPages()[getCurrentPages().length - 1];
  const { options } = currentPage.$page;

  if (!options.sccode) return ErrorLoginType.nextLogin;

  const isLoginSuccess = await sccodeLogin(options);

  if (isLoginSuccess) return H5LoginType.sccodeLogin;

  Toast.error('授权失败，请重新进入');
  setTimeout(() => {
    uni.$wx.miniProgram.navigateBack();
  }, 1200);

  return ErrorLoginType.exitLogin;
}
