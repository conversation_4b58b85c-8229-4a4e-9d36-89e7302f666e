import IShenzhen from '@rms/isz';
import { ErrorLoginType, H5LoginType } from './mode';
import { IsIShenzhen } from '@rms/utils';

export async function iszLogin() {
  if (!IsIShenzhen) return ErrorLoginType.nextLogin;

  try {
    if ((await IShenzhen.init()) && (await IShenzhen.login())) {
      return H5LoginType.iszLogin;
    }
  } catch (error) {
    console.error('isz login error: ', error);
  }
  return ErrorLoginType.keepLoading;
}
