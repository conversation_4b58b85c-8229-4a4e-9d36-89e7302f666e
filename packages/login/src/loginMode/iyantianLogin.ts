import Register from '@rms/register';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, exception<PERSON><PERSON><PERSON> } from '@rms/utils';
import { ErrorLoginType, H5LoginType } from './mode';

function getUserCode() {
  const usertoken = GetUrlParam(window.location.href).usercode || '';

  return usertoken;
}

export async function iYanTianLogin() {
  try {
    const register = new Register();

    if (!IsIYanTian(register.RmsAppConfig.base.scid)) return ErrorLoginType.nextLogin;

    const usercode = getUserCode();

    if (!usercode) return H5LoginType.errorLogin;

    const data = await iYanTianAuth(usercode);

    if (data) {
      await register.RmsUserInfo.setRootInfo(data);

      return register.RmsUserInfo.isLogin() ? H5LoginType.iytLogin : H5LoginType.errorLogin;
    }

    return H5LoginType.errorLogin;
  } catch (error) {
    exception<PERSON><PERSON><PERSON>(error);
    return H5LoginType.errorLogin;
  }
}

async function iYanTianAuth(usercode: string) {
  const register = new Register();
  const api = `${
    IsDev ? 'https://tst-app.shencom.cn' : ''
  }/service-uaa/api/yantian/auth?usercode=${usercode}&appid=1&openid=2`;
  const { data } = await register.RmsHttp.get(api);

  return data.additionalInformation as SC.User.RootInfo;
}
