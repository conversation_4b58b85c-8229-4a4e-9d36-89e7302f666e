import Register from '@rms/register';
import {
  crypto<PERSON>es,
  Dialog,
  exceptionHandler,
  GetUrlParam,
  IsDev,
  IsIFutian,
  IFutianConfig,
  IsWechatDevTools,
} from '@rms/utils';
import type { ResAdditionalUserInfo } from '@shencom/api/src/core/uaa';
import { ErrorLoginType, H5LoginType } from './mode';

/** 使用i福田用户信息登录 */
const ApiIFutianLogin = async (casToken: string) => {
  try {
    const register = new Register();
    const api = `${IsDev ? '/java' : ''}/service-uaa/ift/auth`;
    const { data } = await register.RmsHttp.post<ResAdditionalUserInfo>(api, {
      casToken,
    });
    return data && data.additionalInformation;
  } catch (error) {
    exceptionHandler(error);
    throw error;
  }
};

export async function iftLogin() {
  const register = new Register();
  const { ift = {}, base } = register.RmsAppConfig;
  if (!IsIFutian(base.scid)) return ErrorLoginType.nextLogin;

  try {
    const accessKey = ift?.key || IFutianConfig.key;
    if (!accessKey) {
      throw new Error(`未配置i福田解密key: app.config.ift.key`);
    }

    const casToken = GetUrlParam(window.location.href).casToken || '';
    if (!casToken) return IsWechatDevTools ? ErrorLoginType.nextLogin : ErrorLoginType.keepLoading;

    const token = decodeURIComponent(casToken);
    const decryptData = JSON.parse(cryptoAes(accessKey).decrypt(token));

    if (uni.ext) {
      uni.ext.decryptData = decryptData;
    }
    if (!decryptData) throw new Error('i福田登录信息解密失败');

    // 保存扫码进入参数
    if (decryptData.scan) {
      window.sessionStorage.setItem('__ScScan__', '1');
    }

    /** 设置重定向路由 */
    if (decryptData.path) {
      register.RmsAppConfig.ift = {
        ...ift,
        path: decodeURIComponent(decryptData.path),
      };
    }

    const data = await ApiIFutianLogin(token);

    if (data) {
      register.RmsUserInfo.setResponseSysUserInfo({
        ...data,
        additionalInformation: {
          ...data.additionalInformation,
        },
      });
      return H5LoginType.iftLogin;
    }

    return ErrorLoginType.keepLoading;
  } catch (error) {
    console.error('ift login error: ', error);

    Dialog('登录失败，请稍后重试', {
      confirmText: '返回',
      showCancel: false,
    }).finally(() => {
      uni.$wx.miniProgram.navigateBack();
    });
  }
  return ErrorLoginType.keepLoading;
}
