<script setup lang="ts">
import Register from '@rms/register';
import { replaceJs } from '@rms/utils';
import { ref, onBeforeMount } from 'vue';

const error = ref('');

onBeforeMount(() => {
  const register = new Register();
  error.value = register.RmsUtilsOss.imgPath + '/empty/401.svg';
});
</script>

<template>
  <view class="w-full h-full flex justify-center items-center">
    <sc-empty
      :icon-class="replaceJs('!w-64')"
      :text-class="replaceJs('text-black')"
      :img-url="error"
      text="当前环境无登录方式，请检查配置"></sc-empty>
  </view>
</template>

<style lang="scss" scoped></style>
