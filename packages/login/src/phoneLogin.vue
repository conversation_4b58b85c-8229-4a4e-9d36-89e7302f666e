<script lang="ts" setup>
import { ref, nextTick, unref, onMounted } from 'vue';
import { onLoad } from '@dcloudio/uni-app';
import { Toast, exceptionToast, Loading, IsWeixin } from '@rms/utils';
import Register from '@rms/register';
import { ServiceLogin } from '@rms/service';
import { BaseInfo } from '@rms/types';

interface Emits {
  (e: 'miniLogin'): void;
  (e: 'success'): void;
}

const emits = defineEmits<Emits>();

// const props = defineProps<IProps>();

const form = ref({ phone: '', password: '' });

const isSubmit = ref(false);

const logo = ref('');

const currentUrl = ref('');

const onLoginEnter = () => {
  if (isSubmit.value) return;

  submit();
};

function toMiniLogin() {
  emits('miniLogin');
}

const submit = async () => {
  isSubmit.value = true;

  const body = unref(form);

  Loading.show('登录中...');

  try {
    await ServiceLogin.phoneAndPassword(body.phone, body.password);
    Toast.success('登录成功', {
      complete() {
        emits('success');
      },
    });
  } catch (error) {
    exceptionToast(error, '');
  } finally {
    isSubmit.value = false;
  }
};

const miniConfig = ref<BaseInfo['mini'] | null>(null);

onMounted(() => {
  const register = new Register();
  miniConfig.value = register.RmsAppConfig.login.mini || null;
  logo.value = register.RmsUtilsOss.logo;
  form.value.phone = register.RmsUserInfo.getPhone() || '';
});
</script>
<template>
  <view class="sc-account-login w-full p-4 bg-gray-50 text-14 flex flex-col items-center">
    <view class="pt-5 px-5 flex flex-col items-center">
      <!-- <image
        v-if="logo"
        :src="logo"
        alt=""
        class="w-20 h-20 rounded-lg drop-shadow"
        mode="widthFix" /> -->
      <view class="text-center mt-[10rpx] py-5 pb-[3vh]">
        您暂未登录，为了更好的提供服务，请登录
      </view>
      <!-- <view class="w-full mb-5">
        <sc-divider width-class="w-full" is-half></sc-divider>
      </view> -->
    </view>
    <!-- form -->
    <view class="w-full flex flex-col items-center bg-white rounded-lg p-4 drop-shadow">
      <form @submit="onLoginEnter" class="w-full">
        <view class="text-15 py-2">手机号</view>
        <view class="items-center py-2 h-16">
          <uni-easyinput
            v-model="form.phone"
            trim
            prefixIcon="person-filled"
            :input-border="false"
            class="w-full"
            :class="{ hasValue: form.phone }"
            :maxlength="11"
            type="number"
            placeholder="请输入手机号" />
        </view>
        <view class="text-15 py-2">密码</view>
        <view class="items-center py-2 h-16">
          <uni-easyinput
            v-model="form.password"
            :class="{ hasValue: form.password }"
            trim
            :input-border="false"
            prefixIcon="locked-filled"
            class="w-full"
            type="password"
            placeholder="请输入密码" />
        </view>
        <view class="my-3">
          <sc-button
            :loading="isSubmit"
            :disabled="isSubmit"
            class-name="!h-30rpx !overflow-hidden !rounded-3xl !mx-auto !my-3 font-bold drop-shadow"
            type="green"
            form-type="submit">
            登录
          </sc-button>
        </view>
      </form>
    </view>

    <view class="w-full mt-3" v-if="IsWeixin && miniConfig?.miniLogin">
      <sc-divider
        text-class="px-2 text-placeholder"
        class-name="py-5"
        is-half
        text="其他登录方式"></sc-divider>

      <view class="flex justify-center">
        <view @click="toMiniLogin" class="flex">
          <sc-icon
            name="i-material-symbols-light-open-in-phone"
            size="36"
            class-name="text-green-500"></sc-icon>
        </view>
        <!-- <view></view> -->
      </view>
    </view>
  </view>
</template>
<style lang="scss" scoped>
.login {
  &-img {
    width: 30vw;
    height: 30vw;
    border-radius: 50%;
  }
}
</style>
