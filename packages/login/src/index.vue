<script setup lang="ts">
import accountLogin from './accountLogin.vue';
import PhoneLogin from './phoneLogin.vue';
import MiniLogin from './miniLogin.vue';
import ErrorLogin from './error.vue';
import { onMounted, getCurrentInstance, ref } from 'vue';
import Register from '@rms/register';
import { IsH5, IsWeixin, getBarHeight, Dialog, Navigator } from '@rms/utils';
import { checkAuth, isTarBarPage, useLogin } from '@rms/router';
import ZeroLoading from './components/zero-loading/zero-loading.vue';
import { checkCurrentPageCustom } from './utils';
import { refreshToken } from './loginMode/refreshTokenMode';
import { ErrorLoginType, H5LoginType, LoginStatusType, MiniLoginType } from './loginMode/mode';
import { miniAutoLogin } from './loginMode/miniAutoLoginMode';
import { miniWxLogin } from './loginMode/miniWxLoginMode';
import { accountAndPasswordLogin } from './loginMode/accountLoginMode';
import { miniPrivacy } from './loginMode/miniPrivacyMode';
import { ReturnPromiseType } from '@rms/types/utils';

import BindLogin from './bindLogin.vue';
import { h5SccodeLogin } from './loginMode/h5SccodeMode';
import { canSilentLogin, h5WxSilentLogin } from './loginMode/h5SilentLogin';
import { h5OAuthLogin } from './loginMode/h5OAuthLogin';
import { iszLogin } from './loginMode/iszLoginMode';
import { iftLogin } from './loginMode/iftLogin';
import { refreshLogin } from './loginMode/refreshLogin';
import { iLongHuaLogin } from './loginMode/ilhLogin';
import { iYanTianLogin } from './loginMode/iyantianLogin';
import { iNanShanLogin } from './loginMode/insLogin';
import { appBaoanLogin } from './loginMode/baoanLogin';
import { ServiceLogin } from '@rms/service';

const show = ref(true);

const privacyConfig = ref<ReturnPromiseType<typeof miniPrivacy>['config']>();

/** 小程序切换登陆模式 */
function onChangeLogin(type: 'mini' | 'account') {
  if (type === 'mini') {
    const register = new Register();
    const { mini } = register.RmsAppConfig.login;

    current.value =
      mini?.accountLoginType === 'account' ? MiniLoginType.accountLogin : MiniLoginType.phoneLogin;
  } else {
    current.value = MiniLoginType.miniLogin;
  }
}

const uid = getCurrentInstance()?.parent?.uid;

async function onCancel() {
  const curRoute = getRoute();

  const isSkip = checkAuth(curRoute);

  if (isSkip) {
    onLoginExit();
    return;
  }

  Dialog('您暂未登录，为了更好的提供服务，请点击授权登录', {
    confirmText: '我知道了',
    showCancel: false,
  });
}

const { callback } = useLogin(uid!);

function openPopup() {
  const curRoute = getRoute();
  show.value = false;

  refMap.value[`loginPopupRef${curRoute}`].open();
}

function onLoginSuccess() {
  // Toast.success('登录成功');
  show.value = false;
  current.value = null;

  const curRoute = getRoute();

  if (isTarBarPage(curRoute)) {
    uni.showTabBar();
  }
  refMap.value[`loginPopupRef${curRoute}`].close();
  callback();
}

function onLoginExit() {
  show.value = false;
  current.value = null;

  const curRoute = getRoute();

  if (isTarBarPage(curRoute)) {
    uni.showTabBar();
  }

  refMap.value[`loginPopupRef${curRoute}`].close();
  callback();
}

function handleLoginType(type: LoginStatusType) {
  switch (type) {
    // 登录不成功，下一步。
    case ErrorLoginType.nextLogin:
      return false;

    // 退出登录流程
    case ErrorLoginType.exitLogin:
      onLoginExit();
      return true;

    case MiniLoginType.errorLogin:
    case H5LoginType.errorLogin:
      current.value = type;
      openPopup();
      return true;

    // 退出登录流程
    case ErrorLoginType.keepLoading:
      return true;

    // 登录成功，结束。
    case MiniLoginType.refreshToken:
    case MiniLoginType.autoLogin:
    case MiniLoginType.RefreshLogin:
    case H5LoginType.refreshToken:
    case H5LoginType.sccodeLogin:
    case H5LoginType.silentLogin:
    case H5LoginType.iszLogin:
    case H5LoginType.OAuthLogin:
    case H5LoginType.RefreshLogin:
    case H5LoginType.ilhLogin:
    case H5LoginType.iytLogin:
    case H5LoginType.insLogin:
    case H5LoginType.appBaoAnLogin:
    case ErrorLoginType.reloadPage:
      onLoginSuccess();
      return true;

    // i福田登录成功
    case H5LoginType.iftLogin:
      const register = new Register();
      const { ift } = register.RmsAppConfig;
      if (ift?.path) {
        setTimeout(() => {
          register.RmsNavigator.replace(ift.path!);
        }, 100);
      } else {
        onLoginSuccess();
      }
      return true;

    // 需要展示登录组件
    case MiniLoginType.miniLogin:
    case MiniLoginType.accountLogin:
    case MiniLoginType.phoneLogin:
    case H5LoginType.accountLogin:
    case H5LoginType.phoneLogin:
    case H5LoginType.silentBind:
    case ErrorLoginType.silentError:
    case MiniLoginType.privacy:
      current.value = type;
      openPopup();
      return true;
  }
}

function mustNextLogin() {
  if (!uni.initLoginOptions) return ErrorLoginType.exitLogin;

  const { url = '', force } = uni.initLoginOptions;

  if (force) return ErrorLoginType.nextLogin;

  if (canSilentLogin() && ServiceLogin.getUrlParams().code) return ErrorLoginType.nextLogin;

  const isSkip = checkAuth(url);

  if (isSkip) return ErrorLoginType.exitLogin;

  return ErrorLoginType.nextLogin;
}

async function miniPreLogin() {
  // 隐私授权
  const { type: miniPrivacyType, config } = await miniPrivacy();
  privacyConfig.value = config;
  const complete0 = handleLoginType(miniPrivacyType);
  if (complete0) return;

  const refreshLoginType = await refreshLogin('mini');
  const complete6 = handleLoginType(refreshLoginType);
  if (complete6) return;

  // 第一步：刷新授权信息
  const refreshTokenType = await refreshToken('mini');
  const complete1 = handleLoginType(refreshTokenType);
  if (complete1) return;

  // 第二步：小程序自动登录
  const miniAutoLoginType = await miniAutoLogin();
  const complete2 = handleLoginType(miniAutoLoginType);
  if (complete2) return;

  // 第三步：判断是否配置 meat: { skpi: true }, 如果配置了且不是 force，直接结束登录流程。
  const mustNextLoginType = mustNextLogin();
  const complete3 = handleLoginType(mustNextLoginType);
  if (complete3) return;

  // 第四步：小程序一键登录，以下是打开组件，而非登录完成
  const miniWxLoginType = miniWxLogin();
  const complete4 = handleLoginType(miniWxLoginType);
  if (complete4) return;

  // 第五步：账号密码登录
  const accountAndPasswordLoginType = accountAndPasswordLogin('mini');
  const complete5 = handleLoginType(accountAndPasswordLoginType);
  if (complete5) return;

  handleLoginType(MiniLoginType.errorLogin);
}

async function h5PreLogin() {
  // app宝安授权
  const appBaoanLoginType = await appBaoanLogin();
  if (handleLoginType(appBaoanLoginType)) return;

  // i南山授权
  const iNanShanLoginType = await iNanShanLogin();
  if (handleLoginType(iNanShanLoginType)) return;

  // i盐田授权
  const iYanTianLoginType = await iYanTianLogin();
  if (handleLoginType(iYanTianLoginType)) return;

  // i龙华授权
  const iLongHuaLoginType = await iLongHuaLogin();
  if (handleLoginType(iLongHuaLoginType)) return;

  // i深圳授权（每次访问重新授权）
  const iszLoginType = await iszLogin();
  if (handleLoginType(iszLoginType)) return;

  // i福田授权（每次访问重新授权）
  const iftLoginType = await iftLogin();
  if (handleLoginType(iftLoginType)) return;

  // 跨租户授权（每次访问重新授权）
  const h5OAuthLoginType = await h5OAuthLogin();
  if (handleLoginType(h5OAuthLoginType)) return;

  // 刷新用户信息
  const refreshLoginType = await refreshLogin('h5');
  const complete6 = handleLoginType(refreshLoginType);
  if (complete6) return;

  // 第一步：刷新授权信息
  const refreshTokenType = await refreshToken('h5');
  const complete1 = handleLoginType(refreshTokenType);
  if (complete1) return;

  // 第二步：小程序 webview 授权
  const h5SccodeLoginType = await h5SccodeLogin();
  const complete3 = handleLoginType(h5SccodeLoginType);
  if (complete3) return;

  // 第四步：判断是否配置 meat: { skpi: true }, 如果配置了且不是 force，直接结束登录流程。
  const mustNextLoginType = mustNextLogin();
  const complete4 = handleLoginType(mustNextLoginType);
  if (complete4) return;

  // 第三步：微信 h5 静默授权
  const h5WxSilentLoginType = await h5WxSilentLogin();
  const complete2 = handleLoginType(h5WxSilentLoginType);
  if (complete2) return;

  // 第五步：账号密码登录
  const accountAndPasswordLoginType = accountAndPasswordLogin('h5');
  const complete5 = handleLoginType(accountAndPasswordLoginType);
  if (complete5) return;

  handleLoginType(H5LoginType.errorLogin);
}

const current = ref<LoginStatusType | null>(null);

function handleLoginEmit() {
  const uid = getCurrentInstance()?.root?.uid;

  uni.$on(`__login_begin__${uid}`, () => {
    if (show.value || current.value) return;
    const register = new Register();
    register.RmsUserInfo.removeToken();

    show.value = true;
    init();
  });
}

async function init() {
  if (IsH5) {
    await h5PreLogin();
  } else {
    await miniPreLogin();
  }
}

onMounted(async () => {
  handleCustomPage();
  handleLoginEmit();

  // 第一次不需要登录
  if (!uni.initLoginOptions) {
    show.value = false;
    return;
  }

  if (uid && uni.pageInstance[uid].executionSituation === 'reload') {
    handleLoginType(ErrorLoginType.reloadPage);
    return;
  }

  init();
});

const paddingTop = ref('0px');

const refMap = ref<any>({});

function setRefMap(el: any) {
  if (el) {
    const curRoute = getRoute();

    refMap.value[`loginPopupRef${curRoute}`] = el;
  }
}

function getRoute() {
  const currentPage = getCurrentPages();
  return getCurrentPages()[currentPage.length - 1].route as string;
}

async function handleCustomPage() {
  if (isTarBarPage(getRoute())) {
    uni.hideTabBar();
  }
  const isCustom = checkCurrentPageCustom();
  if (isCustom) {
    const { paddingTop: pt } = await getBarHeight();
    paddingTop.value = `${pt}px`;
  }
}

function onPrivacyFail() {
  uni.exitMiniProgram();
}

function onPrivacySuccess() {
  current.value = null;
  show.value = true;
  init();
}
</script>

<template>
  <uni-popup :ref="(el: any) => setRefMap(el)" v-show="!show">
    <view id="sc-login" class="fixed z-50 top-0 left-0 w-screen h-screen bg-gray-50 miniPaddingTop">
      <template v-if="current">
        <!-- #ifdef MP-WEIXIN -->
        <template v-if="IsWeixin">
          <mini-login
            @account-login="onChangeLogin('mini')"
            @success="onLoginSuccess"
            @cancel="onCancel"
            v-show="current === MiniLoginType.miniLogin"></mini-login>

          <account-login
            v-show="current === MiniLoginType.accountLogin"
            @mini-login="onChangeLogin('account')"
            @success="onLoginSuccess"></account-login>

          <phone-login
            v-if="current === MiniLoginType.phoneLogin"
            @mini-login="onChangeLogin('account')"
            @success="onLoginSuccess"></phone-login>

          <error-login v-show="current === MiniLoginType.errorLogin"></error-login>

          <sc-privacy
            v-bind="privacyConfig"
            v-if="current === MiniLoginType.privacy"
            @fail="onPrivacyFail"
            @success="onPrivacySuccess"></sc-privacy>
        </template>
        <!-- #endif -->

        <!-- #ifdef H5 -->
        <template v-if="IsH5">
          <!-- 手机号密码登录 -->
          <phone-login
            v-if="current === H5LoginType.phoneLogin"
            @success="onLoginSuccess"></phone-login>
          <!-- 绑定手机号 -->
          <bind-login
            v-if="current === H5LoginType.silentBind"
            @success="onLoginSuccess"></bind-login>
          <error-login v-show="current === H5LoginType.errorLogin"></error-login>
        </template>
        <!-- #endif -->
      </template>
    </view>
  </uni-popup>
  <view class="fixed z-50 top-0 left-0 w-screen h-screen bg-gray-50" v-if="show">
    <zero-loading type="pulse"></zero-loading>
  </view>
</template>

<style lang="scss" scoped>
::v-deep .uni-popup__wrapper {
  width: 100vw !important;
  height: 100vh !important;
  border-radius: 0 !important;
}

.miniPaddingTop {
  padding-top: v-bind(paddingTop);
}
</style>
