<script setup lang="ts">
import { ApiSmsUnbound } from '@shencom/api';
import { reactive, ref, onMounted, nextTick } from 'vue';
import { Loading, Toast, ValidatePhone, exceptionToast } from '@rms/utils';
import { ServiceLogin } from '@rms/service';
import Register from '@rms/register';

interface Emits {
  (e: 'success'): void;
}

const emits = defineEmits<Emits>();

/** 成功登录状态 */
const PhoneAndPasswordLoginSuccess = 'PhoneAndPasswordLoginSuccess';
function StorageSetPhoneAndPasswordLoginSuccess() {
  const register = new Register();
  const data = register.RmsStorage.getState<boolean>(PhoneAndPasswordLoginSuccess);
  register.RmsStorage.setState(PhoneAndPasswordLoginSuccess, true);
}

const form = reactive({
  phone: '',
  code: '',
});

const isSubmit = ref(false);

const logo = ref('');

async function onLoginEnter() {
  isSubmit.value = true;
  const register = new Register();
  const UserInfo = register.RmsUserInfo;
  const Storage = register.RmsStorage;

  try {
    Loading.show('提交中');

    const { phone, code } = form;

    if (!phone) throw new Error('请输入手机号');
    if (!ValidatePhone(phone)) throw new Error('手机号格式错误');
    if (!code) throw new Error('请获取验证码');

    await ServiceLogin.ValiadteCodeBindPhone({
      code,
      phone,
      wxToken: UserInfo.getWxToken() || '',
    });
    Toast.success('绑定成功', {
      success() {
        emits('success');
      },
    });
  } catch (error) {
    exceptionToast(error, '');
  } finally {
    isSubmit.value = false;
  }
}

const sendConfig = reactive<any>({
  text: '发送',
  timer: null,
  time: 60,
});

function interval() {
  sendConfig.text = `${sendConfig.time}秒`;
  sendConfig.timer = setInterval(() => {
    sendConfig.time--;
    sendConfig.text = `${sendConfig.time}秒`;

    if (sendConfig.time === 0) {
      clearInterval(sendConfig.timer);
      sendConfig.text = '发送';
      sendConfig.time = 60;
    }
  }, 1000);
}

async function onSendCode() {
  try {
    const { phone } = form;
    if (!phone) throw new Error('请输入手机号');
    if (!ValidatePhone(phone)) throw new Error('手机号格式错误');
    Loading.show('发送中...');
    await ApiSmsUnbound({
      phone: form.phone,
    });
    interval();
    Toast.success('发送成功');
  } catch (error) {
    exceptionToast(error, '');
  }
}

const isMounted = ref(false);

onMounted(() => {
  const register = new Register();
  logo.value = register.RmsUtilsOss.logo;
  isMounted.value = true;
});
</script>

<template>
  <view class="sc-account-login w-full p-4 bg-gray-50 text-14 flex flex-col items-center">
    <view class="pt-5 px-5 flex flex-col items-center">
      <image
        v-if="logo"
        :src="logo"
        alt=""
        class="w-20 h-20 rounded-lg drop-shadow"
        mode="widthFix" />
      <view class="text-center mt-[10rpx] py-5 pb-[3vh]">
        您暂未绑定手机号，为了更好的提供服务，请绑定手机号
      </view>
      <!-- <view class="w-full mb-5">
        <sc-divider width-class="w-full" is-half></sc-divider>
      </view> -->
    </view>
    <!-- form -->
    <view class="w-full flex flex-col items-center bg-white rounded-lg p-4 drop-shadow">
      <form @submit="onLoginEnter" class="w-full">
        <view class="text-15 py-2">手机号</view>
        <view class="items-center py-2 h-16">
          <uni-easyinput
            v-model="form.phone"
            trim
            prefixIcon="person-filled"
            :input-border="false"
            class="w-full"
            :class="{ hasValue: form.phone }"
            :maxlength="11"
            type="number"
            placeholder="请输入手机号" />
        </view>
        <view class="text-15 py-2">验证码</view>
        <view class="items-center py-2 h-16 flex">
          <Teleport to=".uniui-round-verified-user" v-if="isMounted">
            <sc-icon name="i-ic-round-verified-user" size="17" class="text-green-500"></sc-icon>
          </Teleport>
          <uni-easyinput
            v-model="form.code"
            trim
            prefixIcon="round-verified-user"
            :input-border="false"
            class="flex-1 pr-1"
            type="number"
            :maxlength="6"
            placeholder="请输入验证码" />
          <sc-button
            class="w-24 !h-10 !overflow-hidden !rounded-3xl drop-shadow"
            size="small"
            type="green"
            :disabled="sendConfig.text !== '发送'"
            @click="onSendCode">
            {{ sendConfig.text }}
          </sc-button>
        </view>
        <view class="my-3">
          <sc-button
            :loading="isSubmit"
            :disabled="isSubmit"
            class-name="!h-30rpx !overflow-hidden !rounded-3xl !mx-auto !my-3 font-bold drop-shadow"
            type="green"
            form-type="submit">
            登录
          </sc-button>
        </view>
      </form>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.login {
  &-img {
    width: 30vw;
    height: 30vw;
    border-radius: 50%;
  }
}
</style>
