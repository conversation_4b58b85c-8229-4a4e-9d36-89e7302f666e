<script setup lang="ts">
import { onLoad, onShow } from '@dcloudio/uni-app';
import { ref, watch, computed } from 'vue';
import { ServiceLogin } from '@rms/service';
import {
  Toast,
  Dialog,
  AdapterLogin,
  Loading,
  exceptionHandler,
  exceptionToast,
  getBarHeight,
  IsH5,
  _GetUrl<PERSON>ey,
  IsDev,
} from '@rms/utils';
import Register from '@rms/register';
import { ReturnPromiseType } from '@rms/types/utils';

interface Emits {
  (e: 'accountLogin'): void;
  (e: 'success'): void;
  (e: 'cancel'): void;
}

const emits = defineEmits<Emits>();

const register = new Register();

const logo = ref('');
const mpName = ref('');

const showBindPhone = ref(false);
const isInit = ref(false);

type Account = ReturnPromiseType<typeof ServiceLogin.getUserAccounts>;
const accounts = ref<Account>([]);

let isLogingIn = false;

const popup = ref();

const miniConfig = ref<typeof register.RmsAppConfig.login.mini | null>(null);

async function getUserAccounts() {
  if (miniConfig.value?.historyAccounts) {
    accounts.value = await ServiceLogin.getUserAccounts();
  }
}

async function onGetPhoneNumber(e: { detail: UniApp.GetPhoneNumber }) {
  const { iv, encryptedData } = e.detail;

  if (!iv || !encryptedData) {
    onRefuse();
    return;
  }
  let openid = register?.RmsUserInfo.getOpenId();
  try {
    Loading.show('加载中...');

    if (!openid) {
      await ServiceLogin.autoLogin();
      openid = register?.RmsUserInfo.getOpenId();
    }

    if (!openid) {
      new Error('openid 为空');
      return;
    }

    const resLogin = await AdapterLogin();

    if (!resLogin) return;

    const { code } = resLogin;
    const { appid } = register?.RmsAppConfig.base;

    if (!appid) throw new Error('请配置 appid');

    const body = { code, iv, encryptedData, appid, openid };

    await ServiceLogin.wechatMiniBindPhone(body);

    Toast.success('登录成功', {
      complete: () => {
        emits('success');
      },
    });
  } catch (error) {
    exceptionToast(error, '');
  } finally {
    Loading.hide();
  }
}

async function accountLogin(account: any) {
  if (isLogingIn) return;
  isLogingIn = true;
  try {
    Loading.show('登录中...');

    await ServiceLogin.AccountLogin(account.id);

    popup.value?.close();

    Toast.success('登录成功', {
      complete: () => {
        emits('success');
      },
    });

    Loading.hide();
  } catch (error: any) {
    Loading.hide();

    const { errcode, message, errmsg } = error.data || {};

    // 账号登录 code 过期
    if (errcode === '601028') {
      Toast.error('账号信息已过期，请重新尝试');
      register.RmsStorage.removeData('history_accounts');
      await getUserAccounts();
      if (!accounts.value.length) popup.value?.close();
      return;
    }

    Toast.error((error && errcode !== '614004' && errmsg) || '网络异常,请重试');
  } finally {
    setTimeout(() => {
      isLogingIn = false;
    }, 500);
  }
}

async function onRefuse() {
  const flag = await Dialog('为了更好的体验，请绑定手机号，如拒绝将无法登录成功哦！', {
    title: '温馨提示',
    confirmText: '重新授权',
    cancelText: '拒绝',
  });
  if (!flag) onCancel();
}

async function onRefuseAccount() {
  popup.value?.close();
  onRefuse();
}

function onCancel() {
  emits('cancel');
}

function onGoLogin() {
  emits('accountLogin');
}

async function init() {
  // await ServiceLogin.autoLogin();

  miniConfig.value = register.RmsAppConfig.login.mini;

  logo.value = register.RmsUtilsOss.logo;
  mpName.value = register.RmsAppConfig.login.mini?.mpName || '';

  showBindPhone.value = register?.RmsUserInfo.isWxUser() && !!register?.RmsUserInfo.getOpenId();

  await getUserAccounts();

  isInit.value = true;
}

async function refresh() {
  try {
    Loading.show('获取用户信息...');
    await ServiceLogin.autoLogin();
    await init();
  } catch (error) {
    exceptionToast(error, '网络拥堵，请重试');
  } finally {
    Loading.hide();
  }
}

console.log('%c []-182', 'font-size:13px; background:#336699; color:#fff;', 312321);
init();
</script>

<template>
  <view class="sc-auth w-full h-full p-4 bg-gray-50 text-14" @click.stop>
    <view class="pt-5 px-5 flex flex-col items-center">
      <image :src="logo" alt="" class="w-20 h-20 rounded-lg drop-shadow" mode="widthFix" />
      <view class="text-center mt-[10rpx] py-5 pb-[3vh]">
        <view>
          <text v-if="showBindPhone">
            您暂未登录，为了更好的提供服务，请点击授权登录，在稍后的授权提示中点击“允许”按钮
          </text>
          <text v-else>网络拥堵，暂时无法查询您的用户信息，请稍后再试</text>
        </view>

        <view v-if="!showBindPhone">
          <sc-button
            type="green"
            class-name="!overflow-hidden !rounded-3xl !mx-auto !mb-[40rpx] !mt-[100rpx] font-bold"
            @click="refresh">
            重试
          </sc-button>
          <sc-button
            type="default"
            class-name="!overflow-hidden !rounded-3xl !mx-auto !my-[40rpx] font-bold !border-green-500 !text-green-500"
            @click="onCancel">
            <text class="">返回</text>
          </sc-button>
        </view>
      </view>
      <view v-if="showBindPhone" class="w-full">
        <sc-divider width-class="w-full" is-half></sc-divider>
      </view>
    </view>

    <view v-if="showBindPhone" class="pt-[3vh]">
      <view>
        <view class="font-bold">该程序将获取下列授权：</view>
        <view class="text-gray-400 mt-[10rpx]">· 获得您的手机号等信息</view>
      </view>

      <sc-button
        v-if="!isInit || accounts.length"
        :disabled="!isInit"
        :loading="!isInit"
        class-name="!h-30rpx !overflow-hidden !rounded-3xl !mx-auto !my-[40rpx] font-bold drop-shadow"
        type="green"
        @tap="popup?.open">
        一键登录
      </sc-button>
      <sc-button
        v-else
        class-name="!h-30rpx !overflow-hidden !rounded-3xl !mx-auto !my-[40rpx] font-bold"
        open-type="getPhoneNumber"
        type="green"
        @getphonenumber="onGetPhoneNumber">
        一键登录
      </sc-button>

      <sc-button
        class-name="!h-30rpx !overflow-hidden !rounded-3xl !mx-auto !box-border !my-[40rpx] !border-green-500 !text-green-500 font-bold"
        type="default"
        @click="onCancel">
        取消
      </sc-button>
    </view>

    <view class="w-full" v-if="miniConfig?.accountLoginType">
      <sc-divider
        text-class="px-2 text-placeholder"
        class-name="py-5"
        is-half
        text="其他登录方式"></sc-divider>

      <view class="flex justify-center">
        <view @click="onGoLogin" class="flex">
          <sc-icon name="i-ri-account-circle-fill" size="36" class-name="text-blue-200"></sc-icon>
        </view>
        <!-- <view></view> -->
      </view>
    </view>

    <!-- 切换账号弹框 -->
    <uni-popup ref="popup" type="bottom" :is-mask-click="false">
      <view class="p-7 bg-gray-100 rounded-xl">
        <view class="flex items-center">
          <image :src="logo" class="w-5 h-5 rounded-full" mode="aspectFit" />
          <text class="ml-2 font-bold">{{ mpName }}</text>
        </view>

        <view class="my-5 text-16 font-medium">请选择你需要登录的账号</view>

        <view>
          <view
            v-for="item in accounts"
            :key="item.id"
            class="mb-2 bg-white rounded-lg p-5 relative text-center font-normal"
            @tap="accountLogin(item)">
            <text>{{ item.phone }}</text>
            <!-- <text class="sc-authorize_account_delete flex-ac p-5"
                  @tap.stop="removeAccount(item)">
              <van-icon name="clear"
                        class="font-20 text-red" />
            </text> -->
          </view>
          <view class="bg-white rounded-lg p-5 text-center" @tap="onRefuseAccount">暂不登录</view>
        </view>

        <view class="flex py-7">
          <button
            class="border-reset bg-transparent font-normal text-14 text-[#506087]"
            @getphonenumber="onGetPhoneNumber"
            open-type="getPhoneNumber">
            使用其他号码
          </button>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<style lang="scss">
.border-reset::after {
  border: 0;
}
</style>
