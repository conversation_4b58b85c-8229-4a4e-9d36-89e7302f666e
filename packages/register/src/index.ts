import { Http } from '@rms/api/@types';
import type { StorageLRU, _utilsOss, Navigator } from '@rms/utils';
import type BaseUserInfo from '@rms/userinfo';
import { AppConfigProps, BaseInfo } from '@rms/types';
import { RouterMethods } from '@rms/router';
interface RegisterConfig {
  Storage: InstanceType<typeof StorageLRU>;
  UserInfo: InstanceType<typeof BaseUserInfo>;
  Navigator: typeof Navigator;
  utilsOss: InstanceType<typeof _utilsOss>;
  appConfig: AppConfigProps;
  http: Http;
  pages: any;
  router?: RouterMethods;
}

export class Register {
  private Storage: InstanceType<typeof StorageLRU> | null = null;
  private UserInfo: InstanceType<typeof BaseUserInfo> | null = null;
  private Navigator: typeof Navigator | null = null;
  private appConfig: AppConfigProps | null = null;
  private utilsOss: InstanceType<typeof _utilsOss> | null = null;
  private http: Http | null = null;
  private pages: any = null;
  private router: RouterMethods | undefined = undefined;

  constructor(config?: RegisterConfig) {
    if (!uni.registerInstance && config) {
      uni.registerInstance = this;
    }

    if (config) {
      this.router = config.router;
      this.Storage = config.Storage;
      this.UserInfo = config.UserInfo;
      this.Navigator = config.Navigator;
      this.http = config.http;
      this.pages = config.pages;
      this.appConfig = config.appConfig;
      this.utilsOss = config.utilsOss;
      this.router = config.router;
    }

    return uni.registerInstance;
  }

  get RmsStorage() {
    if (!this.Storage) throw new Error('Storage 未初始化');

    return this.Storage;
  }

  get RmsUserInfo() {
    if (!this.UserInfo) throw new Error('UserInfo 未初始化');

    return this.UserInfo;
  }

  get RmsNavigator() {
    if (!this.Navigator) throw new Error('Navigator 未初始化');

    return this.Navigator;
  }

  get RmsAppConfig() {
    if (!this.appConfig) throw new Error('appConfig 未初始化');

    return this.appConfig;
  }

  get RmsUtilsOss() {
    if (!this.utilsOss) throw new Error('UtilsOss 未初始化');

    return this.utilsOss;
  }

  get RmsHttp() {
    if (!this.http) throw new Error('http 未初始化');

    return this.http;
  }

  get RmsPages() {
    if (!this.pages) throw new Error('pages 未初始化');

    return this.pages;
  }

  get RmsRouter() {
    return this.router;
  }
}

export default Register;
