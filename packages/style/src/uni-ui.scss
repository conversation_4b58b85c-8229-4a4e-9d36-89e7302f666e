.uni-easyinput {

  .uni-input-placeholder,
  .uni-easyinput__placeholder-class,
  input {
    @apply font-normal text-placeholder;
    // font-size: 30rpx !important;
    color: #d5d5d5;
  }

  input {
    color: #000;
  }
}

// .uni-easyinput__content-textarea {
//   font-size: 30rpx !important;
// }

// .uni-input-input {
//   @apply text-16;
// }

.uni-group__title {
  padding-left: 30rpx !important;
  background: #f2f3f9 !important;
}

.uni-group__title-text {
  font-size: 32rpx !important;
}

.is-disabled {
  .uni-easyinput__content-input {
    color: #a5a5a5 !important;
  }
}

.uni-error-message {
  display: none;
}

// .uni-forms-item__label {
//   font-size: 32rpx !important;

//   .label-text {
//     @apply text-black;
//     font-size: 32rpx !important;
//   }
// }

.error_input {

  .uni-easyinput__placeholder-class,
  .input-placeholder,
  .view-input {
    color: #ef4444;
  }
}

.uni-popup-dialog {
  .uni-dialog-title-text {
    @apply text-17 text-black;
  }

  .uni-dialog-content-text {
    @apply text-17 text-center;
    color: #858585;
  }

  .uni-dialog-button-text {
    @apply text-16 py-2;
  }
}

.checklist-box_tag {
  .checklist-box {
    border-color: #e6e8ec !important;
    background-color: #fff !important;
  }

  .is-checked {
    border-color: #2979ff !important;
    background-color: #2979ff !important;
  }
}

.uni-searchbar__bgwhite {
  .uni-searchbar__box {
    background: #fff !important;
  }
}

// .is-disabled {
//   background: #fff !important;
// }

.uni-steps__row-line-item {
  .uni-steps__row-circle {
    width: 20rpx;
    height: 20rpx;
  }
}

// 上传图片示例图特殊样式
.exampleUpload {
  .uni-dialog-title {
    display: none !important;
  }

  .uni-dialog-button {
    height: 110rpx !important;
  }

  .uni-popup-dialog {
    width: 90vw !important;
  }

  .uni-dialog-button-text {
    color: #007aff !important;
  }
}

.category {
  ::v-deep .segmented-control__item {
    @apply justify-start;
  }
}

// .uni-data-tree {
//   @apply h-full text-16;
// }
.uni-data-tree-input {
  @apply h-full w-full flex items-center justify-center;
}

.uni-tag {
  @apply box-border inline-block;
}

// sc-tabs
.isFlex {
  .segmented-control {
    width: 100%;
    overflow-x: scroll;
  }

  .segmented-control__item {
    flex: none;

    margin-right: 80rpx;
  }
}

.uni-data-tree-cover {
  z-index: 99999998 !important;
}

.uni-data-tree-dialog {
  z-index: 99999999 !important;
}

.is-required {
  @apply mr-1;
}