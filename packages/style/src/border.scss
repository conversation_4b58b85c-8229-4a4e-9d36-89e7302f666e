// @mixin hairline($color) {
//   @at-root {
//     &::after {
//       position: absolute;
//       box-sizing: border-box;
//       content: ' ';
//       pointer-events: none;

//       top: -50%;
//       right: -50%;
//       bottom: -50%;
//       left: -50%;
//       border: 0 solid $color;

//       transform: scale(0.5);
//     }
//   }
// }

$type: (
  t: top,
  b: bottom,
  l: left,
  r: right,
  x: x,
  y: y,
);

.border {
  // @include hairline($uni-border-color);
  // border-width: 0;

  // position: relative;

  // &:after {
  //   border-width: 1px;
  // }
  border-width: 0.5px;
  @each $key, $val in $type {
    // &-#{$key} {
    //   @include hairline($uni-border-color);
    //   border-width: 0;
    //   position: relative;
    // }

    @if $key == x {
      //   &-#{$key}:after {
      //     border-left-width: 1px;
      //     border-right-width: 1px;
      //   }
      &-#{$key} {
        border-left-width: 0.5px;
        border-right-width: 0.5px;
      }
    } @else if $key == y {
      //   &-#{$key}:after {
      //     border-top-width: 1px;
      //     border-bottom-width: 1px;
      //   }
      &-#{$key} {
        border-top-width: 0.5px;
        border-bottom-width: 0.5px;
      }
    } @else {
      //   &-#{$key}:after {
      //     border-#{$val}-width: 1px;
      //   }
      &-#{$key} {
        border-#{$val}-width: 0.5px;
      }
    }
  }
}
