body,
html,
page {
  --safe-bottom: constant(safe-area-inset-bottom);
  --safe-bottom: env(safe-area-inset-bottom);

  font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Helvetica, Segoe UI, Arial,
    Roboto, 'PingFang SC', 'miui', 'Hiragino Sans GB', 'Microsoft Yahei', sans-serif;

  @apply text-gray-900 font-normal text-14 p-0 m-0;
}

view,
text,
::before {
  box-sizing: border-box;
  border: 0 solid $uni-border-color;
}

view,
text,
::after {
  box-sizing: border-box;
  border: 0 solid $uni-border-color;
}

::-webkit-scrollbar {
  display: none;
}

.amap-logo {
  display: none !important;
  opacity: 0 !important;
}
.amap-copyright {
  display: none !important;
  opacity: 0 !important;
}
