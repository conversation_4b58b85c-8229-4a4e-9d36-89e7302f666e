import { ApiGetWechatJsConfig } from '@rms/api';
import { ServiceLogin } from '@rms/service';
import { exceptionHandler, exceptionToast, IsAlipayH5, IsWeixinH5, queueLoad } from '@rms/utils';
import { CdnConfigProps } from '@rms/types';

function setConfig(config: wx.ConfigOptions) {
  return new Promise((resolve) => {
    jWeixin.config({
      debug: false,
      ...config,
      openTagList: ['wx-open-subscribe', 'wx-open-launch-weapp'],
    });
    jWeixin.ready(() => {
      resolve(true);
    });
    jWeixin.error((err) => {
      console.error('初始化微信配置失败: ', err);
      resolve(false);
    });
  });
}

function load(cdn: string) {
  return queueLoad({
    id: 'wxjssdk',
    src: cdn,
  });
}

function removeUrlParameter(url: string, parameter: string) {
  let urlObject = new URL(url);
  let params = urlObject.searchParams;
  params.delete(parameter);
  return urlObject.toString();
}

function replaceState(state: 'Android' | 'ios') {
  const { origin, pathname, hash } = window.location;
  let url = origin + pathname + hash;
  const { code, scid } = ServiceLogin.getUrlParams();

  uni.ext.code = code;

  if (code) {
    url = removeUrlParameter(url, 'code');
  }

  if (code && state === 'Android' && /(Android)/i.test(navigator.userAgent)) {
    window.history.replaceState({}, '', url);
  }

  if (code && state === 'ios' && !/(Android)/i.test(navigator.userAgent)) {
    window.history.replaceState({}, '', url);
  }
}

export async function _wechatInit(config: CdnConfigProps['wxjssdk']) {
  if (!IsWeixinH5) return Promise.reject('非微信 H5 环境');

  // 初始化直接返回
  if (uni.ext.wechatInit) return true;

  try {
    await load(config.cdn);
  } catch (error) {
    exceptionHandler(error);
    return Promise.reject('加载jssdk失败');
  }

  replaceState('Android');

  uni.$wx = IsAlipayH5 ? ap : jWeixin;

  try {
    const wxConfig = await ApiGetWechatJsConfig();
    await setConfig(wxConfig);

    replaceState('ios');
    uni.ext.wechatInit = true;

    return '微信配置加载成功';
  } catch (error) {
    exceptionHandler(error);
    return Promise.reject('微信 jssdk 加载成功，配置加载失败');
  }
}
