import {
  _Get<PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  IsDev,
  <PERSON><PERSON>5,
  Is<PERSON><PERSON>,
  IsTst,
  queueLoad,
  Toast,
} from '@rms/utils';
import { CdnConfigProps } from '@rms/types';
import Register from '@rms/register';

function load(cdn: string) {
  return queueLoad({
    id: 'vConsole',
    src: cdn,
  });
}

const vConsoleCacheKey = '__vconsole__';

function init(config: CdnConfigProps['vConsole']) {
  const register = new Register();
  const { pro, tst, dev } = config;

  const isOpen = !!_GetUrlKey('vconsole') || !!register.RmsStorage.get(vConsoleCacheKey);

  if (isOpen) {
    new VConsole();
    return;
  }

  let count = 0,
    lastTime = 0;

  const addEventListenerFn = (event: MouseEvent) => {
    if (count === 150) return;

    let currentTime = new Date().getTime();
    //记录两次相连的点击时间间隔，大于1秒，重新记录点击次数
    count = currentTime - lastTime < 500 ? count + 1 : 1;
    lastTime = new Date().getTime();

    if (count === 150) {
      register.RmsStorage.set(vConsoleCacheKey, 3);

      new VConsole();
      document.removeEventListener('click', addEventListenerFn, true);
      Toast.show('已打开vConsole');
      return;
    }

    if (count > 145) {
      Toast.show(`再点击${150 - count}次打开vConsole`);
    }
  };

  document.addEventListener('click', addEventListenerFn, true);

  if (IsPro && !pro) return;

  if (IsDev && !dev) return;

  if (IsTst && !tst) return;

  new VConsole();

  document.removeEventListener('click', addEventListenerFn, true);
}

export async function _vConsoleInit(config: CdnConfigProps['vConsole']) {
  if (!IsH5) return Promise.reject('非 H5 环境');

  // 初始化直接返回
  if (uni.ext.vConsoleInit) return true;

  try {
    await load(config.cdn);
    uni.ext.vConsoleInit = true;

    init(config);

    return 'vConsole 加载成功';
  } catch (error) {
    exceptionHandler(error);
    return Promise.reject('vConsole 加载失败');
  }
}
