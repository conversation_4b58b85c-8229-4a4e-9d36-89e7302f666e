import { exception<PERSON><PERSON><PERSON>, IsIShenzhen, queueLoad } from '@rms/utils';
import { CdnConfigProps } from '@rms/types';

function load(cdn: string) {
  return queueLoad({
    id: 'isz',
    src: cdn,
  });
}

export async function _iszInit(config: CdnConfigProps['wxjssdk']) {
  if (!IsIShenzhen) return Promise.reject('非 isz 环境');

  // 初始化直接返回
  if (uni.ext.iszInit) return true;

  try {
    await load(config.cdn);
    uni.ext.iszInit = true;

    return 'isz jssdk 加载成功';
  } catch (error) {
    exceptionHandler(error);
    return Promise.reject('isz jssdk 加载失败');
  }
}
