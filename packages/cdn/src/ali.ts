import { ApiGetWechatJsConfig } from '@rms/api';
import { ServiceLogin } from '@rms/service';
import { exceptionHandler, exceptionToast, IsAlipayH5, IsWeixinH5, queueLoad } from '@rms/utils';
import { CdnConfigProps } from '@rms/types';

function load(cdn: string) {
  return queueLoad({
    id: 'alijssdk',
    src: cdn,
  });
}

function replaceState() {
  const { origin, pathname, hash } = window.location;
  const { code, scid } = ServiceLogin.getUrlParams();
  let url = origin + pathname + hash;

  uni.ext.code = code;

  if (scid && !url.includes('scid')) {
    url = url.includes('?') ? `${url}&scid=${scid}` : `${url}?scid=${scid}`;
  }

  if (code) {
    window.history.replaceState({}, '', url);
  }
}

export async function _aliInit(config: CdnConfigProps['wxjssdk']) {
  if (!IsAlipayH5) return Promise.reject('非支付宝 H5 环境');

  // 初始化直接返回
  if (uni.ext.aliInit) return true;

  try {
    await load(config.cdn);
    uni.ext.aliInit = true;
    uni.$wx = ap;
    replaceState();

    return '支付宝 jssdk 加载成功';
  } catch (error) {
    exceptionHandler(error);
    return Promise.reject('支付宝 jssdk 加载失败');
  }
}
