import { IsH5, exceptionHandler, queueLoad } from '@rms/utils';
import { CdnConfigProps } from '@rms/types';

function load(cdn: string) {
  return queueLoad({
    id: 'echarts',
    src: cdn,
  });
}

export async function _echartsInit(config: CdnConfigProps['echarts']) {
  if (!IsH5) return Promise.reject('非 h5 环境');

  // 初始化直接返回
  if (uni.ext.echartsInit) return true;

  try {
    await load(config.cdn);
    uni.ext.echartsInit = true;

    return 'echarts.js 加载成功';
  } catch (error) {
    exceptionHandler(error);
    return Promise.reject('echarts.js 加载失败');
  }
}
