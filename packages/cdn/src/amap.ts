import { exceptionHandler, exceptionToast, IsH5, IsWeixinH5, queueLoad } from '@rms/utils';
import { CdnConfigProps } from '@rms/types';
import Register from '@rms/register';

function load(cdn: string) {
  return queueLoad({
    id: 'amap',
    src: cdn,
  });
}

export async function _amapInit(config: CdnConfigProps['amap']) {
  if (!IsH5) return Promise.reject('非 H5 环境');

  // 初始化直接返回
  if (uni.ext.amapInit) return true;

  const register = new Register();

  // jscode 高德web端安全密钥
  window._AMapSecurityConfig = {
    securityJsCode: register.RmsAppConfig.base.securityJsCode || 'd1affcd1747a21b5cad1824ebf5e3fa2',
  };

  // 优先读取项目配置的 geoKey
  const basUrl = `https://webapi.amap.com/maps?v=1.4.15&key=${register.RmsAppConfig.base.geoKey}`;
  const cdn = register.RmsAppConfig.base.geoKey ? basUrl : config.cdn;

  try {
    await load(cdn);

    uni.ext.amapInit = true;
    return '高德地图 jssdk 加载成功';
  } catch (error) {
    exceptionHandler(error);
    return Promise.reject('高德地图 jssdk 加载失败');
  }
}
