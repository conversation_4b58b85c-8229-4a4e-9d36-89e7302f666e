import { Is<PERSON><PERSON>ayH5, <PERSON><PERSON><PERSON>, <PERSON>H5, IsWeixinH5, pipeline } from '@rms/utils';
import { _wechatInit } from './wechat';
import Register from '@rms/register';
import { _aliInit } from './ali';
import { _aliWebViewInit } from './aliwebview';
import { _amapInit } from './amap';
import { _vConsoleInit } from './vConsole';
import { _iszInit } from './isz';
import { _echartsInit } from './echarts';

/** 微信 jssdk 初始化 */
export const WechatInit = pipeline(async () => {
  if (!IsWeixinH5) return Promise.reject('非微信 H5 环境');

  const register = new Register();
  const config = register.RmsAppConfig.cdn;
  if (!config?.wxjssdk) return Promise.reject('未配置微信 jssdk');

  return await _wechatInit(config.wxjssdk);
});

/** 支付宝 jssdk 初始化 */
export const AliInit = pipeline(async () => {
  if (!IsAlipayH5) return Promise.reject('非支付宝 H5 环境');

  const register = new Register();
  const config = register.RmsAppConfig.cdn;
  if (!config?.alijssdk) return Promise.reject('未配置支付宝 jssdk');

  return await _aliInit(config.alijssdk);
});

/** 支付宝 webview jssdk 初始化 */
export const AliWebViewInit = pipeline(async () => {
  if (!IsAlipayH5) return Promise.reject('非支付宝 H5 环境');

  const register = new Register();
  const config = register.RmsAppConfig.cdn;
  if (!config?.aliwebviewjssdk) return Promise.reject('未配置支付宝 webview jssdk');

  return await _aliInit(config.aliwebviewjssdk);
});

/** 高德地图 jssdk 初始化 */
export const AmapInit = pipeline(async () => {
  if (!IsH5) return Promise.reject('非 H5 环境');

  const register = new Register();
  const config = register.RmsAppConfig.cdn;

  return await _amapInit(config.amap);
});

/** vConsole 初始化 */
export const VConsoleInit = pipeline(async () => {
  if (!IsH5) return Promise.reject('非 H5 环境');

  const register = new Register();
  const config = register.RmsAppConfig.cdn;

  return await _vConsoleInit(config.vConsole);
});

/** isz 初始化 */
export const ISZInit = pipeline(async () => {
  if (!IsH5) return Promise.reject('非 H5 环境');

  const register = new Register();
  const config = register.RmsAppConfig.cdn;

  return await _iszInit(config.isz);
});

/** echarts 初始化 */
export const EchartsInit = pipeline(async () => {
  if (!IsH5) return Promise.reject('非 H5 环境');

  const register = new Register();
  const config = register.RmsAppConfig.cdn;

  return await _echartsInit(config.echarts);
});

const CdnConfig = {
  vConsole: VConsoleInit,
  wxjssdk: WechatInit,
  alijssdk: AliInit,
  amap: AmapInit,
  isz: ISZInit,
  echarts: EchartsInit,
  aliwebviewjssdk: AliWebViewInit,
};

function logLoadState(res: any[], key: (keyof typeof CdnConfig)[]) {
  const register = new Register();
  const config = register.RmsAppConfig.cdn;
  const loadRes = res.map((item, index) => {
    return {
      key: key[index],
      cdn: config[key[index]].cdn,
      status: item.status,
      result: item.value || item.reason,
    };
  });
  console.log('%c CdnLoad', 'font-size:13px; background:#336699; color:#fff;', loadRes);
}

export async function init() {
  if (!IsH5) return;

  const register = new Register();
  const config = register.RmsAppConfig.cdn || {};
  const keys = (Object.keys(CdnConfig) as (keyof typeof CdnConfig)[]).filter(
    (key) => !!config?.[key],
  );

  const allLoad = keys.map(async (key) => {
    return config?.[key]?.load ? CdnConfig[key]() : Promise.reject('未开启初始化加载');
  });

  const loadRes = await Promise.allSettled(allLoad);

  logLoadState(loadRes, keys);
}
