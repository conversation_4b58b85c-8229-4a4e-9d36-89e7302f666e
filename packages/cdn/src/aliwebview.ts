import { ApiGetWechatJsConfig } from '@rms/api';
import { ServiceLogin } from '@rms/service';
import { exceptionHandler, exceptionToast, IsAlipayH5, IsWeixinH5, queueLoad } from '@rms/utils';
import { CdnConfigProps } from '@rms/types';

function load(cdn: string) {
  return queueLoad({
    id: 'aliwebviewjssdk',
    src: cdn,
  });
}

export async function _aliWebViewInit(config: CdnConfigProps['wxjssdk']) {
  if (!IsAlipayH5) return Promise.reject('非支付宝 H5 环境');

  // 初始化直接返回
  if (uni.ext.aliWebviewInit) return true;

  try {
    await load(config.cdn);

    uni.ext.aliWebviewInit = true;

    return '支付宝 webview jssdk 加载成功';
  } catch (error) {
    exceptionHandler(error);
    return Promise.reject('支付宝 webview jssdk 加载失败');
  }
}
