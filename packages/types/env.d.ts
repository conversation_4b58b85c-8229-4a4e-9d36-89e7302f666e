/// <reference types="vite/client" />

type PLATFORM = 'h5' | 'app-plus' | 'mp-weixin' | 'mp-alipay';

interface ImportMetaEnv {
  /** 接口 */
  readonly VITE_APP_API: string;
  readonly VITE_APP_SCID: string;
  readonly VITE_APP_IS_PRO: string;
}

interface ImportMeta {
  readonly env: ImportMetaEnv;
}
declare namespace NodeJS {
  interface ProcessEnv {
    readonly NODE_ENV: 'development' | 'production';
    readonly UNI_APP_ID: string;
    readonly VUE_APP_PLATFORM: PLATFORM;
    readonly UNI_PLATFORM: PLATFORM;
  }
}
