declare namespace sc {
  interface callback<P = number, T = object> {
    /** 返回码code: 成功（0），失败（其他） */
    (res: { code: P; message: string; data: T }): void;
  }

  interface InitConfig {
    /** 开启调试模式,调用的所有 api 的返回值会在客户端 alert 出来 */
    debug?: boolean;
    /** 第三方服务在统一 APP 开放平台服务器申请的 appId */
    appId: string;
    /** 获取 initCode 接口返回（需通过业务方后台请求该接口） */
    initCode: string;
    nativeApis?: string[];
  }

  function config(param: InitConfig): void;

  /**
   * 当config通过之后，会调用该方法
   * @param success
   */
  function ready(success: () => void): void;

  /**
   * 当config鉴权失败，会调用该方法
   * @param err
   */
  function error(err: callback): void;

  /**
   * 获取App版本号
   * @returns {string}
   */
  function getAppVersion(): string;

  /**
   * 判断当前客户端版本是否支持指定 JavaScript 接口（单个）
   * @param {{ path: string }} api
   * @param {callback} callback
   */
  function isExistApi(api: { path: string }, callback: callback): void;

  /**
   * 判断当前客户端版本是否支持指定 JavaScript 接口（多个）
   * @param {{ paths: string[] }} api
   * @param {callback} callback
   */
  function isExistApis(api: { paths: string[] }, callback: callback): void;

  /**
   * 判断是否在i深圳APP内
   * @returns {boolean}
   */
  function isSZSMT(): boolean;

  interface LocationOptions {
    longitude: number;
    latitude: number;
    /** 位置名 */
    name?: string;
    /** 详细地址 */
    address?: string;
  }

  /**
   * 打开地图显示坐标位置（高德地图）
   * @param {Location} location
   */
  function openLocation(location: LocationOptions): void;

  interface Location {
    /** 经度 */
    longitude?: number;
    /** 纬度 */
    latitude?: number;
    /** 国家 */
    country?: string;
    /** 省 */
    province?: string;
    /** 市 */
    city?: string;
    /** 区 */
    district?: string;
    /** 详细地址 */
    address?: string;
    /** 城市编码 */
    cityCode?: number;
    /** 区域编码 */
    adcode?: number;
  }

  /** 定位方式（默认为1），0：从缓存获取，1：实时定位获取  */
  type GPSType = 0 | 1;

  /**
   * 获取当前位置GPS
   * @param {{ type: GPSType }} param
   * @param {callback<number, Location>} callback
   */
  function gps(param: { type: GPSType }, callback: callback<number, Location>): void;

  interface ChooseImageParams {
    count: number;
    sourceType: ('album' | 'camera')[];
    sizeType: ('original' | 'compressed')[];
  }
  /**
   * 选择图片
   *
   * @param {(ChooseImageParams)} param
   * @param {callback<number, any>} callback
   */
  function chooseImage(param: ChooseImageParams, callback: callback<number, any>): void;

  /**
   * 用户授权响应码：
   * -1	默认失败
   * -10001	没有初始化 JSSDK
   * -10002	用户点击拒绝授权
   * -10003	用户未登录
   */
  type userAuthCode = 0 | '-1' | '-10001' | '-10002' | '-10003';

  interface userAuthData {
    /** 平台码，每个用户唯一，有则返回，没有为空 */
    openId?: string | null;
    /** 用于用户授权、第三方服务获取用户信息等功能的请求码 */
    requestCode?: string;
    /** requestCode 的时效，默认为 300 秒 */
    expiresIn?: number;
  }

  /**
   * 获取用户授权
   * @param {{ appId?: string }} param
   * @param {callback<userAuthCode, userAuthData>} success
   */
  function userAuth(param: { appId?: string }, success: callback<userAuthCode, userAuthData>): void;

  /**
   * 判断用户是否登录
   * @param {callback<number, { status: boolean }>} callback
   * @returns {boolean} false：未登录 true: 已登录
   */
  function isLogin(callback: callback<number, { status: boolean }>): boolean;

  /**
   * 调起登录页面
   * @param {callback} callback
   */
  function login(callback: callback): void;

  interface VerifyConfig {
    /** 回调文件类型， (0：图片） */
    infoType?: string;
    /**
     * 多个值组合以','分隔，如"1,2,3"。
     * 不传或0 表示全部；1 表示公安人脸；2 表示省统一小程序；
     * 备注：infoType优先级更高，当infoType为0时，只能使用公安人脸。
     */
    authType?: string;
    /** 服务名称 */
    serveName?: string;
    /** 	服务类型（政务服务、公共便民服务、APP基础服务） */
    serveType?: string;
    /** 服务归属单位 */
    serveDept?: string;
  }

  /**
   * 调用实名认证
   * @param {VerifyConfig} param
   * @param {callback<number, { fileBase64String: string }>} cb
   */
  function verify(param: VerifyConfig, cb: callback<number, { fileBase64String: string }>): void;

  /** 调用后，会直接退出当前 WebView */
  function close(): void;
}
