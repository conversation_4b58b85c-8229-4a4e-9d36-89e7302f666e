{"compilerOptions": {"target": "esnext", "useDefineForClassFields": true, "module": "esnext", "moduleResolution": "node", "strict": true, "jsx": "preserve", "sourceMap": true, "resolveJsonModule": true, "esModuleInterop": true, "lib": ["esnext"], "baseUrl": ".", "types": ["@dcloudio/types", "node", "@types/node", "@shencom/typing"]}, "vueCompilerOptions": {"skipTemplateCodegen": true}, "include": ["./*.d.ts"]}