declare namespace SC {
  export interface File {
    count: number;
    createdAt: number;
    /** 上传文件名 */
    fileName: string;
    /** 文件大小 */
    fileSize: number;
    id: string;
    memo: string;
    /** 本地文件名 */
    name: string;
    /** 文件绝对路径 */
    remoteUrl: string;
    /** 是否为签名上传：0否，1是 */
    sign: boolean | 0 | 1;
    /** 是否是重复图片 */
    isSame?: boolean;
    etag?: string;
    /** 签名 */
    signature: string;
    totalCount: number;
    totalPart: number;
  }

  export interface OssSignature {
    /** OSS 的 `AccessKeyId` */
    accessid: string;
    /** 上传所需policy信息 */
    policy: string;
    /** 签名信息 */
    signature: string;
    /** 用户上传文件时指定的前缀 */
    dir: string;
    /** 上传存储空间的访问域名 */
    host: string;
    /** 签名到期时间 */
    expire: string;
  }

  export interface Gis {
    id: string;
    addr: string;
    lat: string;
    lng: string;
  }
}
