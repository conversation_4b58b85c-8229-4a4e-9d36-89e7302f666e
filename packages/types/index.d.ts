interface BannerAndMenu {
  id: string;
  active: number;
  isTab: 1 | 0;
  link?: string;
  icon: string;
  label: string;
  memo: string;
  permit: 1 | 0;
  sort: number;
  type: 'url' | 'event' | 'page' | 'web';
}
export interface Banner extends BannerAndMenu {
  group: string;
  pic: string;
  platform: 'mini';
  remoteUrl: string;
}

export interface Menu extends BannerAndMenu {
  badge?: string;
  group: string;
  remoteUrl?: string;
  class?: string;
  disabled?: boolean;
  is_share?: 0 | 1;
  isShare?: 0 | 1;
}

export type Dictionary<T = any> = Record<string, T>;

/** 分页接口 */
export interface IndexInterface<T = Dictionary> {
  /** 是否第一页 */
  first: boolean;
  /** 是否加载完 */
  last: boolean;
  /** 当前页数 */
  number: number;
  /** 每页个数 */
  size: number;
  /** 总数 */
  totalElements: number;
  /** 列表 */
  content: T[];
}

/** Index接口请求体 */
export interface IndexBodyInterface {
  /** 页数，默认: 0 */
  page?: number;
  /** 个数，默认: 10 */
  size?: number;

  query?: SC.API.IndexQuery;
  sorts?: SC.API.IndexSorts;
}

// ApiSortHandle

/** 地理位置 */
export type LngLat = { lng: number; lat: number };

export interface Image {
  fileName: string;
  fileSize: number;
  id: string;
  name: string;
  remoteUrl: string;
}
/** 表单验证 */
type ExecuteValidator = (
  rule: InternalRuleItem,
  value: any,
  callback: (error?: string[]) => void,
) => void;

interface InternalRuleItem extends Omit<RuleItem, 'validator'> {
  field?: string;
  fullField?: string;
  validator?: RuleItem['validator'] | ExecuteValidator;
}
export interface RuleItem {
  type?: any; // default type is 'string'
  required?: boolean;
  pattern?: RegExp | string;
  min?: number; // Range of type 'string' and 'array'
  max?: number; // Range of type 'string' and 'array'
  len?: number; // Length of type 'string' and 'array'
  enum?: Array<string | number | boolean | null | undefined>; // possible values of type 'enum'
  whitespace?: boolean;
  fields?: Record<string, RuleItem>; // ignore when without required
  defaultField?: RuleItem; // 'object' or 'array' containing validation rules
  message?: string;
  trigger?: ('change' | 'blur')[];
  asyncValidator?: (
    rule: InternalRuleItem,
    value: any,
    callback: (error?: string) => void,
    source: Dictionary,
  ) => void | Promise<void>;
  validator?: (
    rule: InternalRuleItem,
    value: any,
    callback: (error?: string) => void,
    source: Dictionary,
  ) => boolean;
}

export type Rules = Dictionary<RuleItem[]>;

export interface Region {
  district_id: string;
  id: string;
  text: string;
  children: Region[];
}

export interface ICommunity {
  id: string;
  region_pid: string;
  region_id: string;
  region_cid: string;
  district_name: string;
  street_name: string;
  village_name: string;
  name: string;
  address: string;
  poi_id: string;
  pic_url?: any;
}

export type IShenzhenAppConfig = Omit<sc.InitConfig, 'initCode'>;

export interface BaseInfo {
  // scid
  scid: string;
  // 项目名
  name: string;
  // 项目 logo
  logo: string;
  // 版本号
  version: string;
  // 腾讯地图key
  mapKey?: string;
  // 高德地图 key
  geoKey?: string;
  // 高德地图服务 key
  geoServiceKey?: string;
  // appid
  appid?: string;
  // 城市中心区域
  lngLat?: LngLat;
  MP_NAME?: string;
  cdnConfig: CdnConfigProps;
  miniLoginHistoryAccounts?: boolean;
  h5: {
    /** 小程序 sccode 登录 */
    sccode: boolean;
    /** 微信环境-静默授权 */
    silentLogin: boolean;
    /** 账号登录方式: account: 账号密码登录; phone: 手机号密码登录 */
    accountLoginType: 'phone' | 'account' | false;
  };
  mini: {
    /** 一键登录 */
    miniLogin: boolean;
    /** 账号登录方式: account: 账号密码登录; phone: 手机号密码登录 */
    accountLoginType: 'phone' | 'account' | false;
    /** 是否开启历史绑定账号功能 */
    historyAccounts: boolean;
  };
  /** 小程序隐私设置 */
  privacy?: {
    title: string;
    content: string;
  };
  /** i深圳配置 */
  isz?: {
    config?: IShenzhenAppConfig;
    init?: () => Promise<boolean>;
    login?: (requestCode: string) => Promise<SC.User.RootInfo | null>;
  };
  /** i福田配置 */
  ift?: {
    key: string;
    path?: string;
  };
}

interface ConfigType {
  load: boolean;
  cdn: string;
}

interface VConsoleConfigType extends ConfigType {
  dev: boolean;
  tst: boolean;
  pro: boolean;
}

type CdnList = 'wxjssdk' | 'amap' | 'vConsole' | 'alijssdk' | 'isz' | 'echarts' | 'aliwebviewjssdk';

export type CdnConfigProps = Record<CdnList, ConfigType> & { vConsole: VConsoleConfigType };

export interface AppConfigProps {
  base: {
    /** 应用名称 */
    name: string;
    /** 应用版本 */
    version: string;
    /** 租户 */
    scid: string;
    /** 应用logo */
    logo: string;
    /** 小程序 appid */
    appid?: string;
    /** 指定城市 */
    city?: {
      /** 省份 id */
      provId?: string;
      /** 城市 id */
      cityId?: string;
    };
    /** 高德地图key */
    geoKey?: string;
    /** 高德地图安全密钥 */
    securityJsCode?: string;
    /** 高德地图服务key */
    geoServiceKey?: string;
  };
  upload?: {
    type: 'oss' | 'server';
    params?: {
      target: 0 | 1;
      open: 0 | 1;
    };
  };
  login: {
    h5?: {
      /** 是否 sccode 登录 */
      sccode: boolean;
      /** 是否微信静默授权登录 */
      silentLogin: boolean;
      /** 账号登录类型：'phone' | 'account' | false */
      accountLoginType: 'phone' | 'account' | false;
    };
    mini?: {
      /** 是否小程序一键登录 */
      miniLogin: boolean;
      /** 账号登录类型：'phone' | 'account' | false */
      accountLoginType: 'phone' | 'account' | false;
      /** 是否使用历史账号 */
      historyAccounts: boolean;
      /** 小程序名称 */
      mpName?: string;
    };
  };
  cdn: CdnConfigProps;
  privacy?: {
    /** 用户隐私标题 */
    title: string;
    /** 用户隐私内容 */
    content: string;
  };
  /** i深圳配置 */
  isz?: {
    config?: IShenzhenAppConfig;
    init?: () => Promise<boolean>;
    login?: (requestCode: string) => Promise<SC.User.RootInfo | null>;
  };
  /** i福田配置 */
  ift?: {
    key?: string;
    path?: string;
  };
}
