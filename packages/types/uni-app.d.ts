declare namespace UniApp {
  interface GetLocationOptions {
    /** 兼容支付宝参数 https://opendocs.alipay.com/mini/api/mkxuqd */
    // type: 0 | 1 | 2 | 'gcj02';
  }

  type GetPhoneNumber = Pick<UniApp.GetUserInfoRes, 'iv' | 'encryptedData' | 'errMsg'>;

  export interface Event<T = Record<string, any>> {
    detail: T;
    mp: Record<string, any>;
    currentTarget: Record<string, any>;
    target: Record<string, any>;
    timeStamp: number;
    type: string;
  }
}

interface GetPrivacySettingOption {
  /**
   * 接口调用成功的回调函数
   */
  success?: (result: any) => void;
  /**
   * 接口调用失败的回调函数
   */
  fail?: (result: any) => void;
  /**
   * 接口调用结束的回调函数（调用成功、失败都会执行）
   */
  complete?: (result: any) => void;
}

interface LoginOptions {
  /** 登录的目标页面，默认为当前页面，此配置用于路由拦截判断登录 */
  url?: string;
  /** 是否强制登录，忽略 isSkip 路由配置，如菜单点击是需要登录。 */
  force?: boolean;
  refresh?: boolean;
}

interface LoginRefreshOptions extends Omit<LoginOptions, 'refresh'> {}

declare interface Uni {
  /** register 实例 */
  registerInstance: any;
  /** 是否开启静默授权 */
  silentLogin: boolean;
  /** h5 微信 jssdk wx 对象 */
  $wx: typeof wx;
  /** 是否初始化登录 */
  initLoginOptions: LoginOptions | null;
  /** 触发登录流程, url用于判断是否需要授权登录，默认当前页面 */
  scLogin: (options?: LoginOptions) => void;
  /** 使用sccode重新获取最新登录信息 */
  scLoginRefresh: (options?: LoginRefreshOptions) => void;
  /** ServiceLogin */
  ServiceLogin: any;
  /** 获取微信小程序隐私协议情况，临时添加类型 */
  getPrivacySetting: (option?: GetPrivacySettingOption) => void;
  /** 打开微信小程序隐私协议，临时添加类型 */
  openPrivacyContract: (option?: GetPrivacySettingOption) => void;
  /** 页面实例集合 */
  pageInstance: any;
  /** 当前页面实例 */
  cacheCtx: Record<string, any[]>;
  // 额外全局变量
  ext: Record<string, any>;
}
