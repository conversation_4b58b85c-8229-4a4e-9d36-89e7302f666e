interface RuleItemProps {
  /** 是否必填，配置此参数不会显示输入框左边的必填星号，如需要，请配置uni-forms-item组件的的required为true */
  required?: boolean | Ref<boolean>;
  /** 校验失败提示信息语，可添加属性占位符，当前表格内属性都可用作占位符 */
  errorMessage?: string;
  /** 自定义校验规则 */
  validateFunction?: (
    rule: RuleItemProps,
    value: any,
    data: any,
    callback: (msg: string) => void,
  ) => boolean;
  /** 校验数据最大长度 */
  maxLength?: number;
  /** 校验最小值(小于) */
  minimum?: number;
  /** 校验最大值(大于) */
  maximum?: number;
  /* 正则表达式，注意事项见 uni-ui 文档
   * 小程序中，json 中不能使用正则对象，如：/^\S+?@\S+?\.\S+?$/，使用正则对象会被微信序列化，导致正则失效。
   * 所以建议统一使用字符串的方式来使用正则 ，如'^\\S+?@\\S+?\\.\\S+?$' ，需要注意 \ 需要使用 \\ 来转译。
   */
  pattern?: string;
  /** 内置校验规则，如这些规则无法满足需求，可以使用正则匹配或者自定义规则 */
  format?: 'string' | 'number' | 'boolean' | 'array' | 'object' | 'url' | 'email';
  /** 数组至少要有一个元素，且数组内的每一个元素都是唯一的。 */
  range?: any[];
}

interface RulesProps {
  name: string;
  rules: RuleItemProps[];
}

interface DataType {
  /** props */
  prop: any;
  /** label */
  label: any;
  /** 是否隐藏 */
  isHide?: any;
  /** 顺序 */
  order?: number;
  /** item 属性 */
  itemAttrs?: {
    /** label(标签)的宽度，单位px */
    labelWidth?: string | number;
    /** label(标签)的文字的位置 */
    labelPosition?: 'top' | 'left' | Ref<'top' | 'left'>;
    /** itemClass */
    itemClass?: string;
    /** label(标签) 右边显示红色"*"号，样式显示不会对校验规则产生效果 */
    required?: boolean;
    /** label(标签)的文字对齐方式 */
    labelAlign?: 'left' | 'center' | 'right';
  } & Record<string, any>;
  tag: {
    /** 组件所需数据 */
    options?: any;
    /**
     * 组件类型
     * 'components' => 保留 label, 使用插槽替换组件位置，插槽名称对应 prop 字段;
     * 'custom' => 使用插槽替换整 item 下面的内容，插槽名称对应 prop 字段;
     */
    tagType:
      | 'text'
      | 'picker'
      | 'number'
      | 'textarea'
      | 'select'
      | 'component'
      | 'date-picker'
      | 'upload'
      | 'data-checkbox'
      | 'region'
      | 'search-select'
      | 'custom'
      | 'component'
      | 'rate'
      | 'nationwide';
    attr?: {
      /** iconName */
      iconName?: string;
      /** iconClass */
      iconClass?: string;
      /** iconSize */
      iconSize?: string | number;
    } & Record<string, any>;
  };
  listeners?:
    | Record<string, (...args: any) => any>
    | {
        click?: (...args: any) => any;
        change?: (...args: any) => any;
        checkRepect?: (...args: any) => any;
      };
}

interface ScFormType<T = Record<string, any>> {
  form: T;
  /** 校验规则 */
  rules: RulesProps[];
  /** 页面是否 custom，小程序页面为 custom 时设置 */
  isCustom?: boolean;
  /** name, uni-form-item 上面的 name, 默认去 prop */
  name?: string;
  /** 配置项 */
  data: DataType[];
  /** uni-form 的属性 */
  formAttrs?: {
    /** 表单校验时机, blur 仅在 uni-easyinput 中生效 */
    validateTrigger?: 'bind' | 'submit' | 'blur';
    /** label 位置 */
    labelPosition?: 'top' | 'left';
    /** label 宽度，单位 px */
    labelWidth?: string | number;
    /** label 居中方式 */
    labelAlign?: 'left' | 'center' | 'right';
    /** 是否显示分格线 */
    border?: boolean;
    /** 字体大小 */
    fontSize?: string;
    /** label 颜色 */
    labelColor?: string;
    /** input 内容颜色 */
    contentColor?: string;
    /** itemClass */
    itemClass?: string;
  };
}
declare namespace UniApp {
  namespace UI {
    export class Popup {
      open(): void;
      close(): void;
      showPopup: boolean;
    }
    export type FormConfig<T = Record<string, any>> = ScFormType<T>;
  }
}

declare namespace SC {
  export class Popup {
    open(): void;
    close(): void;
    showPopup: boolean;
  }
  export type FormConfig<T = Record<string, any>> = ScFormType<T>;
}
