/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// noinspection JSUnusedGlobalSymbols
// Generated by unplugin-auto-import
export {}
declare global {
  const EffectScope: typeof import('vue')['EffectScope'];
  const computed: typeof import('vue')['computed'];
  const createApp: typeof import('vue')['createApp'];
  const customRef: typeof import('vue')['customRef'];
  const defineAsyncComponent: typeof import('vue')['defineAsyncComponent'];
  const defineComponent: typeof import('vue')['defineComponent'];
  const effectScope: typeof import('vue')['effectScope'];
  const getCurrentInstance: typeof import('vue')['getCurrentInstance'];
  const getCurrentScope: typeof import('vue')['getCurrentScope'];
  const h: typeof import('vue')['h'];
  const inject: typeof import('vue')['inject'];
  const isProxy: typeof import('vue')['isProxy'];
  const isReactive: typeof import('vue')['isReactive'];
  const isReadonly: typeof import('vue')['isReadonly'];
  const isRef: typeof import('vue')['isRef'];
  const markRaw: typeof import('vue')['markRaw'];
  const nextTick: typeof import('vue')['nextTick'];
  const onActivated: typeof import('vue')['onActivated'];
  const onAddToFavorites: typeof import('@dcloudio/uni-app')['onAddToFavorites'];
  const onBackPress: typeof import('@dcloudio/uni-app')['onBackPress'];
  const onBeforeMount: typeof import('vue')['onBeforeMount'];
  const onBeforeUnmount: typeof import('vue')['onBeforeUnmount'];
  const onBeforeUpdate: typeof import('vue')['onBeforeUpdate'];
  const onDeactivated: typeof import('vue')['onDeactivated'];
  const onError: typeof import('@dcloudio/uni-app')['onError'];
  const onErrorCaptured: typeof import('vue')['onErrorCaptured'];
  const onHide: typeof import('@dcloudio/uni-app')['onHide'];
  const onLaunch: typeof import('@dcloudio/uni-app')['onLaunch'];
  const onLoad: typeof import('@dcloudio/uni-app')['onLoad'];
  const onMounted: typeof import('vue')['onMounted'];
  const onNavigationBarButtonTap: typeof import('@dcloudio/uni-app')['onNavigationBarButtonTap'];
  const onNavigationBarSearchInputChanged: typeof import('@dcloudio/uni-app')['onNavigationBarSearchInputChanged'];
  const onNavigationBarSearchInputClicked: typeof import('@dcloudio/uni-app')['onNavigationBarSearchInputClicked'];
  const onNavigationBarSearchInputConfirmed: typeof import('@dcloudio/uni-app')['onNavigationBarSearchInputConfirmed'];
  const onNavigationBarSearchInputFocusChanged: typeof import('@dcloudio/uni-app')['onNavigationBarSearchInputFocusChanged'];
  const onPageNotFound: typeof import('@dcloudio/uni-app')['onPageNotFound'];
  const onPageScroll: typeof import('@dcloudio/uni-app')['onPageScroll'];
  const onPullDownRefresh: typeof import('@dcloudio/uni-app')['onPullDownRefresh'];
  const onReachBottom: typeof import('@dcloudio/uni-app')['onReachBottom'];
  const onReady: typeof import('@dcloudio/uni-app')['onReady'];
  const onRenderTracked: typeof import('vue')['onRenderTracked'];
  const onRenderTriggered: typeof import('vue')['onRenderTriggered'];
  const onResize: typeof import('@dcloudio/uni-app')['onResize'];
  const onScopeDispose: typeof import('vue')['onScopeDispose'];
  const onServerPrefetch: typeof import('vue')['onServerPrefetch'];
  const onShareAppMessage: typeof import('@dcloudio/uni-app')['onShareAppMessage'];
  const onShareTimeline: typeof import('@dcloudio/uni-app')['onShareTimeline'];
  const onShow: typeof import('@dcloudio/uni-app')['onShow'];
  const onTabItemTap: typeof import('@dcloudio/uni-app')['onTabItemTap'];
  const onThemeChange: typeof import('@dcloudio/uni-app')['onThemeChange'];
  const onUnhandledRejection: typeof import('@dcloudio/uni-app')['onUnhandledRejection'];
  const onUnload: typeof import('@dcloudio/uni-app')['onUnload'];
  const onUnmounted: typeof import('vue')['onUnmounted'];
  const onUpdated: typeof import('vue')['onUpdated'];
  const provide: typeof import('vue')['provide'];
  const reactive: typeof import('vue')['reactive'];
  const readonly: typeof import('vue')['readonly'];
  const ref: typeof import('vue')['ref'];
  const resolveComponent: typeof import('vue')['resolveComponent'];
  const shallowReactive: typeof import('vue')['shallowReactive'];
  const shallowReadonly: typeof import('vue')['shallowReadonly'];
  const shallowRef: typeof import('vue')['shallowRef'];
  const toRaw: typeof import('vue')['toRaw'];
  const toRef: typeof import('vue')['toRef'];
  const toRefs: typeof import('vue')['toRefs'];
  const toValue: typeof import('vue')['toValue'];
  const triggerRef: typeof import('vue')['triggerRef'];
  const unref: typeof import('vue')['unref'];
  const useAttrs: typeof import('vue')['useAttrs'];
  const useCssModule: typeof import('vue')['useCssModule'];
  const useCssVars: typeof import('vue')['useCssVars'];
  const useSlots: typeof import('vue')['useSlots'];
  const watch: typeof import('vue')['watch'];
  const watchEffect: typeof import('vue')['watchEffect'];
  const watchPostEffect: typeof import('vue')['watchPostEffect'];
  const watchSyncEffect: typeof import('vue')['watchSyncEffect'];
}
// for type re-export
declare global {
  // @ts-ignore
  export type {
    Component,
    ComponentPublicInstance,
    ComputedRef,
    ExtractDefaultPropTypes,
    ExtractPropTypes,
    ExtractPublicPropTypes,
    InjectionKey,
    PropType,
    Ref,
    VNode,
    WritableComputedRef,
  } from 'vue';
  import('vue');
}
