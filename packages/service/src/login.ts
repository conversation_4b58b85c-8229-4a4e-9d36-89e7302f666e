/* eslint-disable max-classes-per-file */
import { ApiGetWxInfo, ApiGetWxCode } from '@rms/api';
import { ValidatePhone } from '@shencom/utils';
import { exceptionHandler, exceptionToast, AdapterLogin, Loading, Toast, IsH5 } from '@rms/utils';
import Register from '@rms/register';
import { ServiceLogin } from '@rms/service';
import {
  ApiWechatLogout,
  ApiRefreshToken,
  ApiWechatMiniLogin,
  ApiPhoneOrUsernameAndPasswordLogin,
  ApiWechatMiniBindPhone,
  ApiPhoneAndPasswordLogin,
  ApiScCodeLogin,
  ApiGetScCode,
  ApiWechatCodeBindPhone,
  ApiGetUserRecords,
  ApiScOAuthLogin,
} from '@shencom/api';

class AutoLogin {
  protected static loading = false;

  protected static queue: {
    reject: ((reason?: any) => void)[];
    resolve: ((value: unknown) => void)[];
  } = {
    resolve: [],
    reject: [],
  };

  /** 检查token和refreshToken */
  protected static checkToken() {
    const register = new Register();
    const isToken = register.RmsUserInfo.getToken();

    if (isToken) return 'yes';

    const refreshToken = register.RmsUserInfo.getRefreshToken();

    if (refreshToken) return 'refresh';

    return 'no';
  }
}

export class BaseServiceLogin extends AutoLogin {
  private static _error(msg: string) {
    return msg;
  }

  private static async success(data: SC.User.RootInfo, url = '') {
    const register = new Register();
    await register.RmsUserInfo.setRootInfo(data);

    if (url) {
      window.history.replaceState({}, '', url);
    }
  }

  /** 手机号+密码登录 */
  static async phoneAndPassword(phone: string, password: string) {
    if (!(phone && password)) throw new Error('请填写信息');
    if (!ValidatePhone(phone)) throw new Error('手机号格式错误');
    try {
      const { data } = await ApiPhoneAndPasswordLogin({
        phone,
        password,
      });

      if (!data) throw new Error('data 为空');
      BaseServiceLogin.success(data);
      return data;
    } catch (error) {
      exceptionHandler(error);
      throw error;
    }
  }

  static async wechatMiniBindPhone(...args: Parameters<typeof ApiWechatMiniBindPhone>) {
    try {
      const { data } = await ApiWechatMiniBindPhone(...args);

      if (!data) throw new Error('data 为空');

      await BaseServiceLogin.success(data);
    } catch (error) {
      exceptionHandler(error);
      throw error;
    }
  }

  static async ValiadteCodeBindPhone(...args: Parameters<typeof ApiWechatCodeBindPhone>) {
    try {
      const { data } = await ApiWechatCodeBindPhone(...args);

      if (!data) throw new Error('data 为空');

      await BaseServiceLogin.success(data);
    } catch (error) {
      exceptionHandler(error);
      throw error;
    }
  }

  static async userLogin(username: string, password: string) {
    try {
      if (!(username && password)) throw new Error('请填写信息');

      const { data } = await ApiPhoneOrUsernameAndPasswordLogin({
        username,
        password,
      });

      if (!data) throw new Error('data 为空');

      await ServiceLogin.success(data.additionalInformation);
    } catch (error) {
      exceptionHandler(error);
      throw error;
    }
  }

  static async scCode(code: string) {
    const { data } = await ApiScCodeLogin(code);

    if (!data) throw new Error('data 为空');

    BaseServiceLogin.success(data);
    return data;
  }

  static async getScCode() {
    const { data } = await ApiGetScCode();
    return data?.code;
  }

  static async OAuthLogin(...args: Parameters<typeof ApiScOAuthLogin>) {
    const { data } = await ApiScOAuthLogin(...args);

    if (!data) throw new Error('data 为空');

    BaseServiceLogin.success(data);
    return data;
  }

  static async refreshToken() {
    const register = new Register();

    if (!register.RmsUserInfo.isSysUser()) return;
    const token = register.RmsUserInfo.getRefreshToken();
    if (!token) return;

    const { data } = await ApiRefreshToken(token);
    if (data.additionalInformation) BaseServiceLogin.success(data);
  }

  private static async _codeLogin() {
    const resLogin = await AdapterLogin();

    if (!resLogin) return Promise.reject();

    const register = new Register();
    const { appid } = register.RmsAppConfig.base;

    if (!appid) throw new Error('请配置 appid');

    const { data } = await ApiWechatMiniLogin({
      code: resLogin.code,
      appid,
    });

    BaseServiceLogin.success(data);

    return Promise.resolve(data);
  }

  /**
   * 获取路由参数（用于授权跳转时，获取路由上的 code）
   * @returns {object} | `false`
   */
  static getUrlParams() {
    if (!IsH5) return {};

    const { search } = window.location;
    const params: any = {};
    if (search.indexOf('?') !== -1) {
      const paramsArr = search.substring(1).split('&') as string[];
      paramsArr.forEach((value) => {
        const [key, val] = value.split('=');
        params[key] = val;
      });
      return params;
    }
    return {};
  }

  static async wxLogin() {
    if (!IsH5) throw new Error('当前环境不是 h5');

    const { code = uni.ext.code, scid } = this.getUrlParams();

    if (code) {
      const data = await ApiGetWxInfo(code);
      uni.ext.code = '';
      ServiceLogin.success(data);

      return code;
    }
  }

  static async getWxLoginCode() {
    if (!IsH5) throw new Error('当前环境不是 h5');

    const { code = uni.ext.code } = this.getUrlParams();

    if (code) return;

    const isRedirect = await ApiGetWxCode();

    return isRedirect;
  }

  static async autoLogin() {
    return new Promise(async (resolve, reject) => {
      console.log('检查 token :>> ', this.checkToken());
      if (this.checkToken() === 'yes') {
        resolve(true);
        return;
      }

      this.queue.reject.push(reject);
      this.queue.resolve.push(resolve);

      if (this.loading) {
        resolve(true);
        return;
      }

      this.loading = true;
      // Loading.show('自动登录中...');

      try {
        if (this.checkToken() === 'refresh') {
          await this.refreshToken();
        } else if (this.checkToken() === 'no') {
          await this._codeLogin();
        }
        resolve(true);
      } catch (error) {
        exceptionHandler(error);
        resolve(false);
      } finally {
        this.queue.resolve = [];
        this.queue.reject = [];
        this.loading = false;
        // Loading.hide();
      }
    });
  }

  static async logout() {
    try {
      const register = new Register();
      const openid = register.RmsUserInfo.getOpenId() || '';

      if (openid) {
        Loading.show('正在退出登录...');
        await ApiWechatLogout();

        register.RmsStorage.clear();
      } else {
        register.RmsStorage.clear();
      }
      Toast.success('退出登录成功');
    } catch (error) {
      exceptionToast(error, '退出登录失败');
    }
  }

  static async getUserAccounts() {
    const resLogin = await AdapterLogin();

    if (!resLogin) return Promise.reject();

    const register = new Register();
    const { accounts } = register.RmsStorage.getData('history_accounts') || {};
    if (accounts && accounts.length) return accounts;

    const { appid } = register.RmsAppConfig.base;
    if (!appid) throw new Error('请配置 appid');

    try {
      const { data } = await ApiGetUserRecords({
        code: resLogin.code,
        appid,
      });
      register.RmsStorage.setData('history_accounts', { code: resLogin.code, data }, 4.9);
      return data;
    } catch (error) {
      exceptionHandler(error);
      return [];
    }
  }

  static async AccountLogin(recordId: string) {
    if (!recordId) throw new Error('缺少用户登录记录 id');

    const resLogin = await AdapterLogin();
    if (!resLogin) return Promise.reject();

    const register = new Register();

    const { code } = register.RmsStorage.getData('history_accounts') || {};
    if (!code) return Promise.reject({ data: { errcode: '601028' } });

    const { appid } = register.RmsAppConfig.base;

    if (!appid) throw new Error('请配置 appid');

    const { data } = await ApiWechatMiniBindPhone({
      code,
      appid,
      recordId,
    });
    register.RmsStorage.removeData('history_accounts');

    if (!data) throw new Error('data 为空');

    await BaseServiceLogin.success(data);

    return data;
  }
}
