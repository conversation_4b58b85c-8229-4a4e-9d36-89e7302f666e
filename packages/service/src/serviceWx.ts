import { exceptionToast } from '@rms/utils';

interface GetUserProfileRes extends UniApp.GetUserProfileRes {
  userInfo: UniApp.GetUserProfileRes['userInfo'] & {
    city?: string;
    country?: string;
    gender?: number;
    language?: string;
    province?: string;
  };
}

/**
 * 获取用户信息
 * @param custom
 * @returns
 */
export function getUserProfile(
  custom: UniApp.GetUserProfileOptions = {},
): Promise<GetUserProfileRes> {
  return new Promise((resolve, reject) => {
    uni.getUserProfile({
      // 声明获取用户个人信息后的用途，后续会展示在弹窗中，请谨慎填写
      desc: '使用微信头像、昵称完善用户资料',
      success: (res) => {
        resolve({
          ...res,
          userInfo: {
            gender: 0,
            city: '',
            language: '',
            province: '',
            ...res.userInfo,
          },
        });
      },
      fail: (error) => {
        exceptionToast(error, '无法获取微信用户信息');
        reject(error);
      },
      ...custom,
    });
  });
}
