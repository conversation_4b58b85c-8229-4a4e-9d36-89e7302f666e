import Register from '@rms/register';
import { BaseServiceGisByIds, BaseServiceImgByIds, BaseServiceSetGis } from './assets';
import { FunParameters } from '@rms/types/utils';
import { BaseServiceLogin } from './login';
import {
  BaseServiceGetBanner,
  BaseServiceGetCategoryOrArticles,
  BaseServiceGetMenu,
  BaseServiceGetResource,
} from './resources';
import { BaseServiceRegion } from './region';
import { BaseServiceEventTrack } from './tracker';

export * from './serviceWx';
export type { ReqArticlesProps, ReqCategoryProps, ArticlesProps } from './resources';
export { requestArticlesData, requestCategoryData } from './resources';

let register: InstanceType<typeof Register> | null;

function checkParam() {
  if (!register) throw new Error('@rms/service 未初始化');

  return register;
}

export function serviceInit(config: ConstructorParameters<typeof Register>[0]) {
  register = new Register(config);
}

export async function ServiceImgByIds(...args: FunParameters<typeof BaseServiceImgByIds>) {
  const reg = checkParam();
  return BaseServiceImgByIds(reg)(...args);
}

export async function ServiceSetGis(...args: FunParameters<typeof BaseServiceSetGis>) {
  const reg = checkParam();
  return BaseServiceSetGis(reg)(...args);
}

export async function ServiceGisByIds(...args: FunParameters<typeof BaseServiceGisByIds>) {
  const reg = checkParam();
  return BaseServiceGisByIds(reg)(...args);
}

export async function ServiceGetBanner(...args: FunParameters<typeof BaseServiceGetBanner>) {
  const reg = checkParam();
  return BaseServiceGetBanner(reg)(...args);
}

export async function ServiceGetMenu(...args: FunParameters<typeof BaseServiceGetMenu>) {
  const reg = checkParam();
  return BaseServiceGetMenu(reg)(...args);
}

export async function ServiceGetResource(...args: FunParameters<typeof BaseServiceGetResource>) {
  const reg = checkParam();
  return BaseServiceGetResource(reg)(...args);
}

export async function ServiceGetCategoryOrArticles(
  ...args: FunParameters<typeof BaseServiceGetCategoryOrArticles>
) {
  const reg = checkParam();
  return BaseServiceGetCategoryOrArticles(reg)(...args);
}

/**
 *
 * @param isAll  boolean -> 是否每项加上父级项
 * @param level  1 | 2 | 3 | 4 -> 取几层，基于 深圳市-区-街道-社区 对应 1-2-3-4，不受 rootId 的影响
 * @param rootId string -> 查找初始层级
 * @param deep  boolean -> 是否包括自身，配合 rootId 使用
 * @returns
 */
export async function ServiceRegion(...args: FunParameters<typeof BaseServiceRegion>) {
  checkParam();
  return BaseServiceRegion()(...args);
}

export class ServiceLogin extends BaseServiceLogin {}
export class ServiceEventTrack extends BaseServiceEventTrack {}
