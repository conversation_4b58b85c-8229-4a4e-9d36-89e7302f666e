import { ApiGisCreate, ApiGisShow, ApiFileShow } from '@shencom/api';
import { ToArray } from '@rms/utils';
import Register from '@rms/register';

type LocalImgsType = Record<string, SC.File.Info | null>;

let register: InstanceType<typeof Register> | null;

export function BaseServiceImgByIds(reg: InstanceType<typeof Register>) {
  register = reg;

  return async (ids: string[] | string): Promise<LocalImgsType> => {
    if (!ids) return {};

    const resIds = ToArray(ids).join(',').split(',');

    if (!resIds.filter(Boolean).length) return {};

    const _localImgs: LocalImgsType = {};

    const exist = _ImageGetResource(resIds);

    let data: SC.File.Info[] = [];

    const req_ids = resIds.filter((id) => !exist.find((v) => v.id.includes(id)));

    data = exist.map((item) => createImgFile(item));

    if (req_ids.length) {
      const { data: imgData } = await ApiFileShow({ ids: req_ids });
      data = [...data, ...imgData];
      _ImageStorageResource(data);
    }

    resIds.forEach((id) => {
      const target = data.find((img) => img.id === id) || null;

      _localImgs[id] = target;
    });

    return _localImgs;
  };
}

type ResourcesType = 'gis' | 'image';

type ResourcesImgFile = Pick<SC.File.Info, 'id' | 'fileSize' | 'fileName' | 'remoteUrl' | 'name'>;

type ResourcesGisFile = Pick<SC.Gis.Point, 'id' | 'addr' | 'lat' | 'lng'>;

interface ResourcesImgFileData {
  time: number;
  data: ResourcesImgFile;
}

interface ResourcesGisFileData {
  time: number;
  data: ResourcesGisFile;
}

export type StorageResourcesFile = Record<string, ResourcesImgFileData | ResourcesGisFileData>;

/** 获取存储key
 *
 * @param {ResourceType} prefix
 */
const GET_KEY_PREFIX = (prefix: string) => `cache_${prefix}`;

/** 字段过滤
 *
 * @param {object[]} data
 * @param {string[]} fields
 * @returns {object[]}
 */
function _StorageFieldFilter(data: SC.File.Info[], fields: string[]): ResourcesImgFile[];
function _StorageFieldFilter(data: SC.Gis[], fields: string[]): ResourcesGisFile[];
function _StorageFieldFilter(data: any[], fields: string[]) {
  return data.reduce((pre, cur: any) => {
    const obj: Record<string, any> = {};

    Object.keys(cur).forEach((k) => {
      if (fields.includes(k)) obj[k] = cur[k];
    });

    pre.push(obj);
    return pre;
  }, []);
}

/** 存储图片
 *
 * @param {File|File[]} images
 */
export const _ImageStorageResource = (images: SC.File.Info[]) => {
  const storageField = ['id', 'fileSize', 'memo', 'fileName', 'remoteUrl', 'name'];

  const data = _StorageFieldFilter(ToArray(images), storageField);

  _StorageResources('image', data);
};

/** 获取资源
 *
 * @type { GetResources }
 */
function _GetResources(type: 'image', ids: string[]): ResourcesImgFile[];
function _GetResources(type: 'gis', ids: string[]): ResourcesGisFile[];
function _GetResources(type: ResourcesType, ids: string[]) {
  const _ids = ToArray(ids);

  if (!ids.filter(Boolean).length) return [];

  const key = GET_KEY_PREFIX(type);

  const data = register?.RmsStorage.getLasting<StorageResourcesFile>(key);

  if (!data) return [];

  const currentTime = Date.now();
  const result: ResourcesImgFile[] | ResourcesGisFile[] = [];

  Object.keys(data).forEach((k) => {
    if (!_ids.includes(k)) return;
    const item = data[k];
    if (currentTime < item.time) result.push(item.data as any);
  });

  return result;
}

/** 获取本地图片信息
 *
 * @param {Ids} ids
 */
const _ImageGetResource = (ids: string[]) => _GetResources('image', ids);

/** 存储资源
 *
 * @param {ResourceType} type
 * @param {(File|Gis)[]} data
 */
function _StorageResources(type: 'gis', data: ResourcesGisFile[]): void;
function _StorageResources(type: 'image', data: ResourcesImgFile[]): void;
function _StorageResources(type: ResourcesType, data: ResourcesImgFile[] | ResourcesGisFile[]) {
  const key = GET_KEY_PREFIX(type);
  const allData = register?.RmsStorage.getLasting(key) || {};
  const storageTime = Date.now() + 1000 * 60 * 60 * 24 * 7;

  data.forEach((item) => {
    allData[item.id] = { time: storageTime, data: item };
  });

  register?.RmsStorage.setLasting(key, allData);
}

/** 创建一个图片对象 */
function createImgFile(img: ResourcesImgFile): SC.File.Info {
  const id = `${new Date().getTime()}`;
  return {
    createdAt: +id,
    remark: '',
    etag: '',
    sign: 0,
    ...img,
  };
}

const _GisGetResource = (ids: string[]) => _GetResources('gis', ids);

/** 存储gis
 *
 * @param {File|File[]} images
 */
export const _GisStorageResource = (gis: SC.Gis[]) => {
  const storageField = ['id', 'addr', 'lat', 'lng'];

  const data = _StorageFieldFilter(ToArray(gis), storageField);

  _StorageResources('gis', data);
};

export function BaseServiceGisByIds(reg: InstanceType<typeof Register>) {
  register = reg;

  return async (ids: string[] | string) => {
    if (!ids) return [];

    const resIds = ToArray(ids).join(',').split(',');

    if (!resIds.filter(Boolean).length) return [];

    const exist = _GisGetResource(resIds);

    let data: SC.Gis[] = exist;

    const req_ids = resIds.filter((id) => !exist.find((v) => v.id.includes(id)));

    if (req_ids.length) {
      const { data: _data } = await ApiGisShow({
        ids: req_ids,
      });
      data = [...data, ..._data];
      _GisStorageResource(data);
    }

    return data;
  };
}

interface LocationProps {
  longitude: number;
  latitude: number;
  mapType?: 'gaode' | 'baidu' | 'tencent';
}

/** 设置gis信息
 *
 * @param {{longitude:string|number;latitude:string|number}} location
 * @param {string} addr
 * @returns {Promise<{data:Gis}>}
 */
export const BaseServiceSetGis = (reg: InstanceType<typeof Register>) => {
  register = reg;

  return async (location: LocationProps, addr: string) => {
    if (!location && !addr) {
      throw new Error('缺少经纬度和地址，无法创建点位');
    }

    try {
      const { data } = await ApiGisCreate({
        lng: location.longitude,
        lat: location.latitude,
        addr,
        mapType: location.mapType,
      });

      _GisStorageResource([data]);

      return data;
    } catch (error) {
      return Promise.reject(error);
    }
  };
};
