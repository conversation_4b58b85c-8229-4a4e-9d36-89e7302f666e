import { ReturnPromiseType } from '@shencom/typing';
import { ApiRegionGuangzhouList, ApiRegionList, ApiRegionNationwideList } from '@rms/api';
import { Register } from '@rms/register';
import { Tenant } from '@shencom/utils-tenant';
import { Dictionary } from '@rms/types';

type RegionType = ReturnPromiseType<typeof ApiRegionList>['data'];

const REGION_KEY = 'RegionData';
const NEW_REGION_KEY = 'NewRegionData';

function getStorageRegion(isNew = false) {
  if (isNew) return;

  const key = isNew ? NEW_REGION_KEY : REGION_KEY;
  const register = new Register();
  return register.RmsStorage.getData<RegionType>(key);
}

function setStorageRegion(data: RegionType, isNew = false) {
  if (isNew) return;

  const key = isNew ? NEW_REGION_KEY : REGION_KEY;
  const register = new Register();
  register.RmsStorage.setData(key, data);
}

// 处理 level
function handleLevel(data: RegionType, level: number): RegionType {
  if (![1, 2, 3, 4].includes(level)) level = 4;

  if (level === 1) return data.map((v) => ({ ...v, level: 1, children: undefined }));
  if (level === 2)
    return data.map((v) => ({
      ...v,
      level: 1,
      children: v.children?.map((e) => ({ ...e, level: 2, children: undefined })),
    }));
  if (level === 3)
    return data.map((v) => ({
      ...v,
      level: 1,
      children: v.children?.map((e) => ({
        ...e,
        level: 2,
        children: e.children?.map((f) => ({ ...f, level: 3, children: undefined })),
      })),
    }));

  if (level === 4)
    return data.map((v) => ({
      ...v,
      level: 1,
      children: v.children?.map((e) => ({
        ...e,
        level: 2,
        children: e.children?.map((f) => ({
          ...f,
          level: 3,
          children: f.children?.map((g) => ({ ...g, level: 4, children: undefined })),
        })),
      })),
    }));

  return data;
}

// 递归查找 rootId
function findRootReigon(data?: RegionType[number], rootId?: string): RegionType {
  if (!data) return [];

  if (data.id === rootId) return [data];

  let res: RegionType | null = null;

  data.children?.some((item) => {
    const targe = findRootReigon(item, rootId);

    if (targe) {
      res = targe;
    }

    return res?.length;
  });

  return res || [];
}

function handleRootId(data: RegionType, deep?: boolean, rootId?: string): RegionType {
  if (!rootId) return data;

  const res = findRootReigon(data[0], rootId);

  return deep ? res : res[0].children || [];
}

// 每项加上父级项
function handleItemAll(data: RegionType, isAll?: boolean) {
  if (isAll) {
    data.forEach((e) => {
      const all = {
        pId: e.pId,
        id: '-1',
        _id: e.id,
        title: e.title,
        level: e.level || 1,
      };
      if (e?.children?.length) {
        e.children.unshift(all);
        handleItemAll(e.children, isAll);
      }
    });
  }

  return data;
}

async function getRegion(config: Partial<ResServiceRegion>) {
  let cacheData = getStorageRegion(config.isNew);

  if (!cacheData?.length) {
    const register = new Register();
    const { isGuangzhou } = Tenant.getTenetInfo(register.RmsAppConfig.base.scid);

    let fn = isGuangzhou ? ApiRegionGuangzhouList : ApiRegionList;
    let params: Dictionary = isGuangzhou ? { cityTop: true, returnAll: true } : { rootId: 1 };
    let { provId, cityId } = register.RmsAppConfig.base.city || {};

    // 默认使用深圳市
    if (config.isNew) {
      cityId = cityId || '440300000000';
      provId = provId || '440000000000';
    }

    if (config.isNew) {
      params = { rootId: provId, deep: 5 };
      fn = ApiRegionNationwideList;
    }

    let { data } = await fn(params);

    if (config.isNew) {
      params = { rootId: provId, deep: 5 };
      const res = data.find((d) => d.id === cityId);
      if (res) {
        data = [res];
      }
    }

    cacheData = data;
    setStorageRegion(cacheData, config.isNew);
  }

  return cacheData;
}

interface ResServiceRegion {
  // 是否每项加上父级项
  isAll: boolean;
  // 取几层，基于 深圳市-区-街道-社区 对应 1-2-3-4，不受 rootId 的影响
  level: 1 | 2 | 3 | 4;
  // 查找初始层级
  rootId: string;
  // 是否包括自身，配合 rootId 使用
  deep: boolean;
  // 是否未新区域接口
  isNew: boolean;
}

/**
 *
 * @param isAll  boolean -> 是否每项加上父级项
 * @param level  1 | 2 | 3 | 4 -> 取几层，基于 深圳市-区-街道-社区 对应 1-2-3-4，不受 rootId 的影响
 * @param rootId string -> 查找初始层级
 * @param deep  boolean -> 是否包括自身，配合 rootId 使用
 * @returns
 */
function BaseServiceRegion() {
  return async (config?: Partial<ResServiceRegion>) => {
    const register = new Register();
    const { isAll = false, deep } = config || {};
    let { rootId, level = 4 } = config || {};
    let { provId, cityId } = register.RmsAppConfig.base.city || {};

    // 默认使用深圳市
    if (config?.isNew) {
      cityId = cityId || '440300000000';
      provId = provId || '440000000000';
    }

    if (!rootId) {
      const { isGuangzhou } = Tenant.getTenetInfo(register.RmsAppConfig.base.scid);
      rootId = isGuangzhou ? '440100000000' : '2';

      if (config?.isNew) {
        rootId = cityId;
      }
    }

    const regions = await getRegion(config || {});

    let data = regions;

    // 处理层级
    data = handleLevel(regions, level);

    // 处理Root
    data = handleRootId(data, deep, rootId);

    // 处理是否每项加上父级项
    data = handleItemAll(data, isAll);

    return data;
  };
}

export { BaseServiceRegion };
