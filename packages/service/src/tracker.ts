import { exceptionH<PERSON><PERSON> } from '@rms/utils/src';
import { ApiEventTrack } from '@rms/api';
import { ReqTracking } from '@rms/api/src/tracker';

type TrackData = Omit<ReqTracking, 'type'>;

interface Config {
  /** 埋点配置 */
  data?: Partial<TrackData>;
  /** 开启调试 */
  debug?: boolean;
}

/** 事件埋点 */
class EventTracker {
  data: Partial<TrackData> = {};
  config: Omit<Config, 'data'> = {};

  get bizSource() {
    return this.data.bizSource;
  }
  get moduleCode() {
    return this.data.moduleCode;
  }

  constructor(params: Config = {}) {
    const { data, ...config } = params;
    if (data) this.data = data;
    if (config) this.config = config;
  }

  /** 设置埋点数据 */
  init(params: Partial<TrackData>) {
    this.data = { ...this.data, ...params };
  }

  /** 提交埋点数据 */
  async track(data: ReqTracking['type'] | Config['data']) {
    const trackData = typeof data === 'string' ? { type: data } : { ...data };
    const params: Record<string, any> = { ...this.data, ...trackData };

    const checkParam = () => {
      return ['bizSource', 'moduleCode', 'type'].every((key) => {
        const val = params[key];
        if (!val && this.config.debug) {
          throw new Error(`埋点配置: ${key} 未初始化`);
        }
        return !!val;
      });
    };

    if (!checkParam()) return;

    try {
      await ApiEventTrack(params as ReqTracking);
    } catch (error) {
      exceptionHandler(error);
      throw Error('埋点失败');
    }
  }
}

export const BaseServiceEventTrack = EventTracker;
