import {
  ApiResourceMenu,
  ApiResourceBanner,
  ApiResourceFile,
  ApiResourceMenuJava,
  ApiResourceBannerJava,
} from '@rms/api';
import {
  ApiFileShow,
  ApiCMSArticlesIndex,
  ApiQueryHandler,
  ApiSortsHandler,
  ApiCMSCategoryIndex,
} from '@shencom/api';
import { FormatDate, ValidateURL } from '@shencom/utils';
import { exceptionHandler, ArrayToObject, ToArray } from '@rms/utils';
import { Tenant } from '@shencom/utils-tenant';
import { Dictionary, Banner, Menu, IndexInterface, IndexBodyInterface } from '@rms/types';
import Register from '@rms/register';

interface GetBaseDataOptions {
  /** 本地数据 */
  localData: Dictionary<(Banner | Menu)[]>;
  /** 组 */
  groups: string | string[];
  type: string;
  isInit?: boolean;
}

let register: InstanceType<typeof Register> | null;

const menus_key = 'data_menus';

const banners_key = 'data_banners';

const resource_key = 'data_resource';

/** 去重
 * @param {...string[]} arr
 * @returns {string[]}
 */
const removeRepeat = (...arr: string[][]) => Array.from(new Set(arr.flat()));

/** 处理组
 *
 * @param {string[]} list - 本地数据列表
 * @param {GetBaseDataOptions['groups']} groups 组
 * @param {GetBaseDataOptions['isInit']} isInit 是否刷新
 * @returns {[string[],string[]]}
 */
const handlerGroup = (list: string[], groups: string | string[], isInit = false) => {
  const oldGroups = list;

  const newGroups = [];

  groups = ToArray(groups);

  const allGroups = removeRepeat(groups, list);

  if (isInit || !allGroups.length) return [groups, []];

  for (let i = 0; i < allGroups.length; i++) {
    const group = allGroups[i];
    if (list.includes(group)) {
      oldGroups.push(group);
    } else {
      newGroups.push(group);
    }
  }
  return [removeRepeat(newGroups), removeRepeat(oldGroups)];
};

/** 请求组数据
 *
 * @param {string} api
 * @param {string[]} groups
 * @returns
 */
const requestGroupData = async (type: string, groups: string[]) => {
  try {
    const munuFn = ApiResourceMenuJava;
    const bannerFn = ApiResourceBannerJava;

    const fn = type === 'menu' ? munuFn : bannerFn;

    const { data } = await fn({
      group: groups,
      size: 2e4,
    });

    if (!data.length) return [];

    return data;
  } catch (error) {
    exceptionHandler(error);
    return [];
  }
};

type GetBaseDataReturn<T> = Promise<[Dictionary<T[]>?, Dictionary<T[]>?]>;

/** 获取基本数据 */
async function getBaseData<T>(opt: GetBaseDataOptions): GetBaseDataReturn<T>;
async function getBaseData(opt: any): Promise<any> {
  try {
    const { localData, groups, isInit, type } = opt;

    if (!groups) return [];

    const localDataKeys = Object.keys(localData || {}) as string[];

    const [newGroups, oldGroups] = handlerGroup(localDataKeys, groups, isInit);

    const oldData = localDataKeys.reduce<Dictionary<(Banner | Menu)[]>>((pre, cur) => {
      if (oldGroups.includes(cur) && localData[cur]) pre[cur] = localData[cur];
      return pre;
    }, Object.create(null));

    if (!newGroups.length) return [null, oldData];

    const data = (await requestGroupData(type, newGroups)) as Banner[];

    const newData = ArrayToObject(data, 'group');

    return [newData, oldData];
  } catch (error) {
    exceptionHandler(error);
    return [];
  }
}

/** 获取轮播图数据
 *
 * @param {string|string[]} groups
 * @param {boolean} [isInit=false] - 是否刷新
 * @return {Promise<Dictionary<Banner[]>>}
 */
const BaseServiceGetBanner = (reg: InstanceType<typeof Register>) => {
  register = reg;

  return async (groups: string | string[] = [], isInit = false) => {
    try {
      const type = 'banner';

      const localData = register?.RmsStorage.get<Dictionary<Banner[]>>(banners_key) || {};

      const [newData, oldData] = await getBaseData<Banner>({ localData, type, isInit, groups });

      const key = Object.keys(newData || {});
      if (!key.length) {
        const localDataUpdate = register?.RmsStorage.get<Dictionary<Banner[]>>(banners_key) || {};

        const groupsArr = ToArray(groups);

        // 更新最新缓存数据
        groupsArr.forEach((g) => {
          localDataUpdate[g] = oldData ? oldData[g] : [];
        });

        register?.RmsStorage.set(banners_key, { ...localDataUpdate }, 150);
        return oldData || {};
      }

      const bannerList = key.reduce<Banner[]>((per, cur) => {
        if (newData) per.push(...newData[cur]);
        return per;
      }, []);

      const ids = bannerList.map((v) => v.pic).filter(Boolean);

      const imgs = (ids.length && (await ApiFileShow({ ids }))?.data) || [];

      bannerList.forEach((v) => {
        const img = imgs.find((e) => e.id === v.pic);
        if (img) v.remoteUrl = img.remoteUrl;

        if (v.link) v.link = v.link.replace(/\/main$/, '/index');
      });

      const bannerObj = ArrayToObject(bannerList, 'group');
      // 重新获取最新缓存
      const localDataUpdate = register?.RmsStorage.get<Dictionary<Banner[]>>(banners_key) || {};

      // 把现有的本地的数据,和新请求的数据合并,再存起来
      register?.RmsStorage.set(banners_key, { ...localDataUpdate, ...bannerObj }, 150);

      return { ...oldData, ...bannerObj };
    } catch (error) {
      exceptionHandler(error);
      return {};
    }
  };
};

/** 获取菜单数据
 *
 * @param {string|string[]} groups
 * @param {Boolean} [isInit=false] - 是否刷新
 * @return {Promise<Dictionary<Menu[]>>}
 */
const BaseServiceGetMenu = (reg: InstanceType<typeof Register>) => {
  register = reg;
  return async (groups: string | string[] = [], isInit = false) => {
    try {
      const type = 'menu';

      const localData = register?.RmsStorage.get<Dictionary<Menu[]>>(menus_key) || {};

      const [newData, oldData] = await getBaseData<Menu>({ localData, type, isInit, groups });

      const key = Object.keys(newData || {});
      if (!key.length) {
        const localDataUpdate = register?.RmsStorage.get<Dictionary<Menu[]>>(menus_key) || {};

        const groupsArr = ToArray(groups);

        // 更新最新缓存数据
        groupsArr.forEach((g) => {
          localDataUpdate[g] = oldData ? oldData[g] : [];
        });

        register?.RmsStorage.set(menus_key, { ...localDataUpdate }, 150);
        return oldData || {};
      }

      const menusList = key.reduce<Menu[]>((per, cur) => {
        if (newData) per.push(...newData[cur]);
        return per;
      }, []);

      const ids = menusList
        .map((v) => (Number(v.icon) ? v.icon : null))
        .filter(Boolean) as string[];

      const imgs = (ids.length && (await ApiFileShow({ ids }))?.data) || [];
      menusList.forEach((v) => {
        const img = imgs.find((e) => e.id === v.icon);
        if (img) {
          v.remoteUrl = img.remoteUrl;
        } else if (!ValidateURL(v.icon)) {
          v.remoteUrl = `${register?.RmsUtilsOss.ossImgPath}/${v.icon}`;
        } else {
          v.remoteUrl = v.icon;
        }

        if (v.link) v.link = v.link.replace(/\/main$/, '/index');
      });

      const menusObj = ArrayToObject(menusList, 'group');

      // 重新获取最新缓存
      const localDataUpdate = register?.RmsStorage.get<Dictionary<Menu[]>>(menus_key) || {};

      // 把现有的本地的数据,和新请求的数据合并,再存起来
      register?.RmsStorage.set(menus_key, { ...localDataUpdate, ...menusObj }, 150);

      return { ...oldData, ...menusObj };
    } catch (error) {
      exceptionHandler(error);
      return {};
    }
  };
};

interface IResource {
  createdAt: number;
  id: string;
  name: string;
  type: number;
  updatedAt: number;
  url: string;
  menuNames?: string;
}

type StorageIResourceKey = {
  time: number;
  value: IResource['url'];
};

type StorageIResource = Dictionary<StorageIResourceKey>;

const BaseServiceGetResource = (reg: InstanceType<typeof Register>) => {
  register = reg;
  return async (id: string | string[], isInit = false) => {
    const ids = Array.isArray(id) ? id : [id];
    if (!ids.length) return null;

    const local = register?.RmsStorage.get<StorageIResource>(resource_key) || {};

    const currentTime = Date.now();
    const localData: StorageIResource = {};

    // 检测过期数据
    Object.keys(local).forEach((k) => {
      const item = local[k];
      if (currentTime < item.time) localData[k] = item;
    });

    // 过滤本已有的数据, isInit=true 则不过滤
    const req_ids = isInit ? ids : ids.filter((v) => !localData[v]);

    if (req_ids.length) {
      const { data } = await ApiResourceFile({
        query: ApiQueryHandler(req_ids.join(), 'id', 'select'),
      });

      const list = data.content || [];

      // 给每条新数据添加一个过期时间(24小时).
      // 数据只保留 url 字段.
      // 并插入到本地数据中.
      list.forEach((item) => {
        localData[item.id] = { time: Date.now() + 60000 * 60 * 24, value: item.url };
      });

      register?.RmsStorage.set(resource_key, localData);
    }

    // 根据传入的参数返回对应的数据
    const result: Record<string, string> = {};

    ids.forEach((val) => {
      const item = localData[val];
      if (item) result[val] = item.value;
    });

    return result;
  };
};

interface IParams {
  pid: string;
}

/** 获取资讯
 *
 * @param {string} api
 * @param {string[]} groups
 * @returns
 */
export const requestCategoryData = async (
  querys: Parameters<typeof ApiQueryHandler>[0] = [],
  params: Partial<IParams> = {},
) => {
  try {
    const sorts = ApiSortsHandler('sort');
    const query = ApiQueryHandler([[1, 'active'], ...querys.filter(Boolean)]);
    const { data } = await ApiCMSCategoryIndex({
      size: 100,
      sorts,
      query,
      ...params,
    });

    if (!data) return null;

    if (!(data && data.content && data.content.length)) return null;

    // Storage.set(category_key, data.content);

    return data.content;
  } catch (error) {
    exceptionHandler(error);
    return [];
  }
};

export interface ComCounter {
  count: number;
  counter: string;
  id: string;
  refId: string;
  refer: string;
  updatedAt: number;
}

export interface NcmsCategory {
  active: number;
  comCounters: ComCounter[];
  createdAt: number;
  description: string;
  displayName: string;
  icon: string;
  id: string;
  isAudit: number;
  isComment: number;
  isUpvote: number;
  logo: string;
  page: number;
  pid: string;
  pids: string;
  showArticle: boolean;
  size: number;
  sort: number;
  updatedAt: number;
}
export interface NcmsAttachments {
  articleId: string;
  createdAt: number;
  fileId: string;
  fileName: string;
  fileUrl: string;
  id: string;
  status: number;
  type: string;
}

export interface ArticlesProps {
  readonly id: string;
  active: number;
  atype: string;
  author: string;
  categoryId: string;
  categoryIds: string;
  comCounters: ComCounter[];
  count: number;
  counter: string;
  createdAt: number;
  displayName: string;
  isOriginal: number;
  isRecommend: number;
  isShare: number;
  isShowCover: number;
  isTopped: number;
  isTrend: number;
  mine: boolean;
  ncmsArticleSpecials: any[];
  ncmsAttachments: NcmsAttachments[];
  ncmsCategory: NcmsCategory;
  needPage: boolean;
  page: number;
  participate: string;
  participateCount: number;
  picture: string;
  pictureUrl: string;
  shareType: number;
  size: number;
  sort: number;
  title: string;
  type: number;
  userId: string;
  digest: string;
  referUrl: string;
  content: string;
}

export const requestArticlesData = async (
  params: IndexBodyInterface & { categoryId: string } = { categoryId: '' },
) => {
  try {
    const sorts = ApiSortsHandler('sort');

    const { data } = await ApiCMSArticlesIndex({
      sorts,
      query: ApiQueryHandler(1, 'active'),
      size: 5,
      page: 0,
      ...params,
    });

    if (!data) return null;

    if (!(data && data.content && data.content.length)) return null;

    return data;
  } catch (error) {
    exceptionHandler(error);
    return null;
  }
};

export interface ReqCategoryProps {
  readonly id: string;
  active: number;
  children: ReqCategoryProps[];
  comCounters: any[];
  createdAt: number;
  description: string;
  displayName: string;
  icon: string;
  iconList: string[];
  isAudit: number;
  isComment: number;
  isUpvote: number;
  logo: string;
  logoUrl: string;
  page: number;
  pid: string;
  pids: string;
  showArticle: boolean;
  size: number;
  sort: number;
  updatedAt: number;
}

export interface ReqArticlesProps {
  author: string;
  createdAt: string;
  id: string;
  remoteUrl: string;
  title: string;
  digest: string;
  referUrl: string;
}

function BaseServiceGetCategoryOrArticles(reg: InstanceType<typeof Register>) {
  register = reg;

  return async (type: 'articles' | 'category', params?: { id: string } | null) => {
    if (type === 'articles') {
      const data = await requestArticlesData({
        categoryId: params?.id || '',
      });
      const content = data?.content || [];
      const list = content.map((item) => ({
        digest: item.digest || '',
        title: item.title,
        id: item.id,
        author: item.author,
        // @ts-ignore
        referUrl: item.referUrl || '',
        remoteUrl: item.pictureUrl
          ? item.pictureUrl + register?.RmsUtilsOss.zoomCustom()
          : register?.RmsAppConfig.base.logo,
        createdAt: FormatDate(item.createdAt),
      }));

      return list;
    }

    const data = await requestCategoryData();

    return data;
  };
}

export {
  BaseServiceGetBanner,
  BaseServiceGetMenu,
  BaseServiceGetResource,
  BaseServiceGetCategoryOrArticles,
};
