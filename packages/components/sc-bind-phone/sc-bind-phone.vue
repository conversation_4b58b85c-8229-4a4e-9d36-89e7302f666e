<script setup lang="ts">
import { ApiSmsUnbound } from '@shencom/api';
import { reactive, ref } from 'vue';
import { Loading, Toast, ValidatePhone, exceptionToast } from '@rms/utils';
import { ServiceLogin } from '@rms/service';

interface IEmits {
  (e: 'success'): void;
}

const emits = defineEmits<IEmits>();

const bindPopupRef = ref();

function open() {
  bindPopupRef.value?.open();
}

const form = reactive({
  phone: '',
  code: '',
});

const isSubmit = ref(false);

async function onLoginEnter() {
  isSubmit.value = true;
  try {
    Loading.show('提交中');
    const { phone, code } = form;
    await ServiceLogin.ValiadteCodeBindPhone(phone, code);
    Toast.success('绑定成功');
    bindPopupRef.value?.close();
    emits('success');
  } catch (error) {
    exceptionToast(error, '');
  } finally {
    isSubmit.value = false;
  }
}

const sendConfig = reactive<any>({
  text: '发送',
  timer: null,
  time: 60,
});

function interval() {
  sendConfig.text = `${sendConfig.time}秒`;
  sendConfig.timer = setInterval(() => {
    sendConfig.time--;
    sendConfig.text = `${sendConfig.time}秒`;

    if (sendConfig.time === 0) {
      clearInterval(sendConfig.timer);
      sendConfig.text = '发送';
      sendConfig.time = 60;
    }
  }, 1000);
}

async function onSendCode() {
  try {
    const { phone } = form;
    if (!phone) throw new Error('请输入手机号');
    if (!ValidatePhone(phone)) throw new Error('手机号格式错误');
    Loading.show('发送中...');
    await ApiSmsUnbound({
      phone: form.phone,
    });
    interval();
    Toast.success('发送成功');
  } catch (error) {
    exceptionToast(error, '');
  }
}

defineExpose({ open });
</script>

<template>
  <uni-popup ref="bindPopupRef" type="center" :is-mask-click="false">
    <view class="bg-gray-100 rounded overflow-hidden pt-2">
      <view class="w-full text-center text-16 py-3">绑定手机号</view>
      <form @submit="onLoginEnter">
        <view class="w-[80vw] px-2.5 bg-gray-100">
          <view class="w-full flex items-center py-2 border-b">
            <label class="w-16 text-right">手机号：</label>
            <uni-easyinput
              v-model="form.phone"
              focus
              trim
              :input-border="false"
              :maxlength="11"
              class="flex-1"
              type="number"
              placeholder="请输入手机号" />
          </view>
          <view class="w-full flex items-center py-2">
            <label class="w-16 text-right">验证码：</label>
            <uni-easyinput
              v-model="form.code"
              trim
              :input-border="false"
              class="flex-1 pr-1"
              type="number"
              placeholder="请输入验证码" />
            <sc-button
              class="w-12"
              size="small"
              :disabled="sendConfig.text !== '发送'"
              @click="onSendCode">
              {{ sendConfig.text }}
            </sc-button>
          </view>
        </view>
        <view class="mt-10">
          <sc-button :loading="isSubmit" :disabled="isSubmit" type="primary" form-type="submit">
            登录
          </sc-button>
        </view>
      </form>
    </view>
  </uni-popup>
</template>

<style lang="scss" scoped></style>
