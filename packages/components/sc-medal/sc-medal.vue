<script setup lang="ts">
import { ref } from 'vue';
import { onLoad } from '@dcloudio/uni-app';
import { Dictionary, Menu } from '@rms/types';
import Register from '@rms/register';
import { ServiceGetMenu } from '@rms/service';
import { ApiGetMedalLook, ApiGetMedalShow } from '@rms/api';
import { Navigator, exceptionHandler } from '@rms/utils';
import { ResMedalLookData } from '@rms/api/src/medal';
import but_guanbi from './image/but_guanbi.png';
import icon_zan from './image/icon_zan.png';
import img_dicheng from './image/img_dicheng.png';

interface IProps {
  /** 勋章所属应用 */
  appId: string;
}

const props = withDefaults(defineProps<IProps>(), {
  appId: '',
});

const popup = ref<{ open: () => void; close: () => void } | null>(null);

const metal = ref<Dictionary>({});

const userMedal = ref<ResMedalLookData>();

const btnList = ref<Menu[]>();

const open = () => {
  popup.value?.open();
};

const close = () => {
  popup.value?.close();
};

async function init() {
  const register = new Register();
  const flag = register.RmsStorage.getData(`medal_${props.appId}`);
  if (flag) return;

  try {
    const menuType = 'medal_popip_btn';
    const Menu = await ServiceGetMenu(menuType, true);
    btnList.value = Menu[menuType] || [];

    const data = await ApiGetMedalLook({ business: props.appId });
    userMedal.value = data;

    console.log('data && Object.keys(data).length: ', data && Object.keys(data).length);
    if (data && Object.keys(data).length) {
      const medal = await ApiGetMedalShow({ id: data.medalId });
      metal.value = medal;
      open();
    }

    const CACHE_DAYS = 7;
    register.RmsStorage.setData(`medal_${props.appId}`, props.appId, CACHE_DAYS * 24 * 60);
  } catch (error) {
    exceptionHandler(error);
  }
}

function clickBtn(item: Menu) {
  const link = `${item.link}${
    item.link?.includes('pages/index/detail')
      ? `?medalId=${userMedal.value?.medalId}&userId=${userMedal.value?.id}&isFromOther=1`
      : ''
  }`;
  const menu = { ...item, link };
  Navigator.toMenuPage(menu);
}

onLoad(() => {
  init();
});
</script>

<template>
  <uni-popup ref="popup" type="center" mask-background-color="rgba(0,0,0,0.7)">
    <view class="w-100 flex flex-col justify-center items-center">
      <view
        class="w-[530rpx] h-[530rpx] flex flex-col justify-center items-center bg-[length:100%_100%]"
        :style="{ backgroundImage: `url(${metal.popupBgImg || img_dicheng})` }">
        <view class="flex items-center">
          <view class="text-white font-bold text-18 mr-2">恭喜您，获得新勋章</view>
          <image class="icon-14" :src="icon_zan" alt="" />
        </view>
        <image class="w-[220rpx] h-[200rpx] mt-7" mode="aspectFit" :src="metal.iconSvg" alt="" />
        <view class="text-white text-18 mt-7">{{ metal.name }}</view>
        <view class="text-white">{{ metal.issueDate }}</view>
      </view>

      <view class="flex justify-evenly w-screen text-white">
        <view
          v-for="(item, index) in btnList"
          :key="item.id"
          class="w-[260rpx] h-[88rpx] rounded-full flex justify-center items-center"
          :class="index % 2 ? 'bg-cyan-400' : 'border border-solid border-white'"
          @click="clickBtn(item)">
          {{ item.label }}
        </view>
      </view>

      <image class="icon-40 mt-4" :src="but_guanbi" alt="" @click="close" />
    </view>
  </uni-popup>
</template>
