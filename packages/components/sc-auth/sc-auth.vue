<script setup lang="ts">
import { onLoad, onShow } from '@dcloudio/uni-app';
import Register from '@rms/register';

interface IEmits {
  (e: 'successEvery'): void;
  (e: 'successOnce'): void;
}

const emits = defineEmits<IEmits>();

/** 兼容以前的写法 ref.init() */
function init() {
  uni.scLogin({
    force: true,
  });
}

onShow(() => {
  const register = new Register();
  if (register.RmsUserInfo.isLogin()) {
    emits('successEvery');
  }
});

onLoad(() => {
  const register = new Register();
  if (register.RmsUserInfo.isLogin()) {
    emits('successOnce');
  }
});

defineExpose({ init });
</script>

<template>
  <!-- <view ></view> -->
</template>
