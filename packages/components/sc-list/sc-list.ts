import { computed, nextTick, Ref, ref, onBeforeMount } from 'vue';
import { exceptionHandler } from '@rms/utils';
import { Dictionary, IndexInterface } from '@rms/types';

interface IConfig<T = any> {
  /** 列表请求函数，返回值返回 IndexInterface 分页接口。 */
  request: (...args: any) => any;
  /** 请求参数对象 */
  params?: Dictionary;
  /** 处理结果函数，结果需要 return，支持异步 */
  handle?: (content: T) => Promise<T & Dictionary[]> | (T & Dictionary[]);
  /** 同 http custom */
  custom?: any;
  /** 是否返回全部数据(包括分页数据) */
  all?: boolean;
  /** 是否立即执行, 设置为 false 第一次请求需要手动调用 getData */
  immediate?: boolean;
  /** 是否显示 load-more */
  showLoadMore?: boolean;
  /** uni-load-more 组件属性 */
  loadMoreAttr?: Dictionary;
}

interface IError {
  data?: {
    data: string;
    errcode: string;
    errmsg: string;
    a: IndexInterface;
  };
}

/**
 * @param config 配置项
 * @config request 列表请求函数，返回值返回 IndexInterface 分页接口。
 * @config params 请求参数对象
 * @config handle 处理结果函数，结果需要 return，支持异步
 * @config custom 同 http custom
 * @config all 是否返回全部数据(包括分页数据)
 * @config immediate 是否立即执行, 设置为 false 第一次请求需要手动调用 getData
 * @returns 返回值
 * @returns getData 触发请求，支持初始化和传参
 * @returns list 结果数组
 * @returns listProps sc-list 组件需要的 props, '<sc-list v-bind="listProps"></sc-list>'
 * @returns setApiFn 设置列表请求函数
 * @returns setConfig 设置配置项，同 config
 * @returns allData 设置 all 之后能拿到
 */
function useList<T>(config: IConfig<T>) {
  // 初始化 config
  if (!('immediate' in config)) {
    config.immediate = true;
  }

  const isLoading = ref(false);

  const page = ref(0);

  const lasts = ref(false);

  const loadStatus = ref<'noMore' | 'loading' | 'more'>('loading');

  const isRefresh = ref(false);

  const listData = ref<any>([]);

  const allData = ref<IndexInterface<any> | null>(null);

  const currentParams = ref({});

  const emptyText = ref('');

  const isEmpty = computed(() => !listData.value.length && !isLoading.value);

  const ApiFn = ref<Pick<IConfig, 'request'>['request']>(() => null);

  const _config = ref<Omit<IConfig, 'params' | 'request'>>({});

  const contentText = {
    contentdown: '上拉显示更多',
    contentrefresh: ' ',
    contentnomore: '没有更多数据了',
  };

  const listProps = computed(() => ({
    onHandleLower,
    onHandleUpper,
    isRefresh: isRefresh.value,
    isEmpty: isEmpty.value,
    emptyText: emptyText.value,
    loadStatus: loadStatus.value,
    showLoadMore: 'showLoadMore' in config ? config.showLoadMore : true,
    loadMoreAttr: config.loadMoreAttr
      ? { contentText, showIcon: false, ...config.loadMoreAttr }
      : { contentText, showIcon: false },
  }));

  async function emitGetData(isInit = false, params: Dictionary = {}) {
    if (Object.keys(params).length) {
      currentParams.value = params;
    }

    await getData(isInit);
  }

  const onHandleUpper = (active?: boolean) => {
    if (!active) return;
    isRefresh.value = true;
  };

  const onHandleLower = (active?: boolean) => {
    if (!active) return;
    getData();
  };

  const getData = async (isInit = false) => {
    await _getData(currentParams.value, isInit);
  };

  const resetData = () => {
    listData.value = [];
    allData.value = null;
  };

  const _getData = async (params: any, isInit = false) => {
    if (isLoading.value) {
      isRefresh.value = false;
      uni.stopPullDownRefresh();
      return;
    }

    if (isInit) {
      lasts.value = false;
      page.value = 0;
      resetData();
    }

    if (lasts.value) {
      isRefresh.value = false;
      uni.stopPullDownRefresh();
      loadStatus.value = 'noMore';
      return;
    }

    try {
      if (typeof ApiFn.value !== 'function') {
        throw new Error('请检查 request');
      }

      isLoading.value = true;

      loadStatus.value = 'loading';

      const data = await ApiFn.value(
        {
          page: page.value,
          ...params,
        },
        { ...(_config.value?.custom || {}) },
      );

      if (!data) {
        isRefresh.value = false;
        uni.stopPullDownRefresh();
        return;
      }

      const { content, last, number } = data;

      let list = content;

      if (_config.value?.handle) {
        list = await _config.value.handle(list);
      }

      listData.value = [...listData.value, ...list];

      if (_config.value?.all) {
        allData.value = { ...data, content: listData.value };
      }

      lasts.value = last;

      if (!listData.value.length) {
        emptyText.value = '暂无数据';
      }

      loadStatus.value = lasts.value ? 'noMore' : 'more';

      page.value = number + 1;
    } catch (error) {
      if (typeof error === 'object' && error && 'data' in error) {
        emptyText.value = (error as IError).data?.errmsg || '暂无数据';
      }
      exceptionHandler(error);
    } finally {
      isLoading.value = false;
      isRefresh.value = false;
      uni.stopPullDownRefresh();
    }
  };

  function setApiFn(fn: typeof ApiFn.value) {
    ApiFn.value = fn;
  }

  function setConfig(config2: IConfig) {
    _config.value = { ..._config.value, ...config2 };
  }

  const { request, params, ...args } = config;

  _config.value = args;

  ApiFn.value = request;

  currentParams.value = params || {};

  _config.value = config || {};

  if (_config.value.immediate) {
    onBeforeMount(() => {
      getData(true);
    });
  }

  return {
    /** 触发请求，支持初始化和传参 */
    getData: emitGetData,
    /** 结果数组 */
    list: listData as Ref<T>,
    /** sc-list 组件需要的 props, '<sc-list v-bind="listProps"></sc-list>' */
    listProps,
    /** 设置列表请求函数 */
    setApiFn,
    /** 设置配置项，同 config */
    setConfig,
    /** 设置 all 之后能拿到 */
    allData,
    /** 是否加载完毕 */
    lasts,
  };
}

export { useList };
