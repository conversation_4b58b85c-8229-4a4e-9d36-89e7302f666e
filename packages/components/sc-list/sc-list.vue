<script setup lang="ts">
import { onPullDownRefresh, onReachBottom } from '@dcloudio/uni-app';
import { Dictionary } from '@rms/types';
import { replaceJs } from '@rms/utils';

interface IProps {
  /** 是否刷新 */
  isRefresh: boolean;
  /** 是否为空 */
  isEmpty: boolean;
  /** sc-empty 组件的 text 属性 */
  emptyText?: string;
  /** 加载状态 */
  loadStatus: string;
  /** 上拉加载函数 */
  onHandleLower: (active?: boolean) => void;
  /** 下拉加载函数 */
  onHandleUpper: (active?: boolean) => void;
  /**
   * 是否开启局部刷新，默认全局下拉刷新
   * 全局下拉刷新,需要在 pages.json 里，找到的当前页面的pages节点，并在 style 选项中开启 enablePullDownRefresh
   * 局部刷新则关闭 enablePullDownRefresh
   */
  localRefresh?: boolean;
  /** 定义列表高度 */
  heightClass?: string;
  /** 是否能局部刷新 */
  needRefresh?: boolean;
  /** 唯一标识，多个 list 在同一个页面时需要加不同的id */
  active?: boolean;
  /** classNames */
  classNames?: string;
  /** 是否显示 load-more */
  showLoadMore?: boolean;
  /** uni-load-more 组件属性 */
  loadMoreAttr?: Dictionary;
}

const emits = defineEmits<{
  /** 下拉加载函数 */
  (e: 'onHandleUpperFn'): void;
  /** 上拉加载函数 */
  (e: 'onHandleLowerFn'): void;
}>();

const props = withDefaults(defineProps<IProps>(), {
  localRefresh: false,
  emptyText: '暂无数据',
  heightClass: 'h-screen',
  classNames: '',
  needRefresh: true,
  id: '',
  active: true,
  showLoadMore: true,
});

const onHandleUpperFn = () => {
  console.log('%c []-70', 'font-size:13px; background:#336699; color:#fff;', props.loadMoreAttr);
  if (!props.localRefresh) return;

  emits('onHandleUpperFn');
  props.onHandleUpper(props.active);
};

const onHandleLowerFn = () => {
  if (!props.localRefresh) return;

  emits('onHandleLowerFn');
  props.onHandleLower(props.active);
};

onPullDownRefresh(() => {
  if (props.localRefresh) return;

  emits('onHandleUpperFn');
  props.onHandleUpper(props.active);
});

onReachBottom(() => {
  if (props.localRefresh || !props.active) return;

  emits('onHandleLowerFn');
  props.onHandleLower(props.active);
});
</script>
<!-- #ifdef MP-WEIXIN -->
<script lang="ts">
export default {
  behaviors: ['wx://form-field-button'],
  externalClasses: ['class'],
  options: {
    virtualHost: true,
  },
};
</script>
<!-- #endif -->
<template>
  <scroll-view
    v-if="localRefresh"
    scroll-y
    :refresher-enabled="localRefresh && needRefresh"
    :refresher-triggered="isRefresh"
    class="w-full box-border px-5 class"
    :class="replaceJs(heightClass)"
    @refresherrefresh="onHandleUpperFn"
    @scrolltolower="onHandleLowerFn">
    <slot></slot>

    <view v-if="!isEmpty" class="py-2">
      <uni-load-more v-if="showLoadMore" v-bind="loadMoreAttr" :status="loadStatus"></uni-load-more>
    </view>
    <template v-else>
      <slot name="empty">
        <sc-empty class="sc-empty" :show="isEmpty" :text="emptyText || '暂无数据'"></sc-empty>
      </slot>
    </template>
  </scroll-view>
  <view
    v-else
    class="w-full box-border px-5 class relative"
    :class="[replaceJs(heightClass + ' ' + classNames)]">
    <slot></slot>

    <view v-if="!isEmpty" class="py-2">
      <uni-load-more v-if="showLoadMore" v-bind="loadMoreAttr" :status="loadStatus"></uni-load-more>
    </view>
    <template v-else>
      <slot name="empty">
        <sc-empty class="auto-height" :show="isEmpty" :text="emptyText || '暂无数据'"></sc-empty>
      </slot>
    </template>
  </view>
</template>

<style lang="scss" scoped>
.auto-height {
  position: absolute;
  top: 0;
  width: 100%;
  height: 100%;
  left: 0;
}
</style>
