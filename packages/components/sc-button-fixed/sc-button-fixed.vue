<template>
  <view class="sc-button-fixed class">
    <!-- <view class="sc-button-fixed-blank" /> -->
    <view class="sc-button-fixed-container class">
      <slot></slot>
    </view>
  </view>
</template>
<!-- #ifdef MP-WEIXIN -->
<script lang="ts">
export default {
  externalClasses: ['class'],
  options: { virtualHost: true },
};
</script>
<!-- #endif -->
<style lang="scss" scoped>
.sc-button-fixed {
  @apply fixed bottom-0 w-full left-0;

  &-container {
    @apply px-4 bg-white py-3;
    padding-bottom: calc(var(--safe-bottom, 0px) / 2 + 0.75rem);
  }
  &-blank {
    @apply pt-10;
    padding-bottom: calc(var(--safe-bottom, 0px) / 2 + 2.5rem);
  }
}
</style>
