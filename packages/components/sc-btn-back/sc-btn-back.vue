<script setup lang="ts">
// #ifdef H5
import Register from '@rms/register';
import { IsIFutian, _GetUrlKey } from '@rms/utils';
import { ref } from 'vue';
import { onShow } from '@dcloudio/uni-app';

function onBack() {
  window.history.back();
}

const isShow = ref(false);

const ScanKey = '__ScScan__';

function isMizone() {
  const register = new Register();
  return register.RmsAppConfig.base.scid === 'sc8820513B9B1903E4';
}

onShow(() => {
  // @ts-ignore
  let isScan = _GetUrlKey('scan') === '1' || !!uni?.ext?.decryptData?.scan;

  if (isScan) {
    window.sessionStorage.setItem(ScanKey, '1');
  } else {
    isScan = !!window.sessionStorage.getItem(ScanKey);
  }

  const isFirstPage = window.history.length === 1 || +window.history.state?.position === 0;

  const register = new Register();
  // 目前只有福田租户
  isShow.value =
    isScan && (IsIFutian(register.RmsAppConfig.base.scid) || isMizone()) && !isFirstPage;
});
// #endif
</script>

<template>
  <!-- #ifdef H5 -->
  <view v-show="isShow" class="rounded-full fixed z-[9999] top-5 left-1 btn-bg" @click="onBack">
    <sc-icon name="i-carbon:chevron-left" class="text-gray-600" :size="18"></sc-icon>
  </view>
  <!-- #endif -->
  <!-- #ifndef H5 -->
  <view></view>
  <!-- #endif -->
</template>

<style lang="scss" scoped>
.btn-bg {
  background: rgba($color: #fff, $alpha: 0.5);
}
</style>
