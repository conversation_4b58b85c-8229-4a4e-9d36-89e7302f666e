import Register from '@rms/register';

function empty(value: any) {
  switch (typeof value) {
    case 'undefined':
      return true;
    case 'string':
      if (value.replace(/(^[ \t\n\r]*)|([ \t\n\r]*$)/g, '').length === 0) return true;
      break;
    case 'boolean':
      if (!value) return true;
      break;
    case 'number':
      if (value === 0 || Number.isNaN(value)) return true;
      break;
    case 'object':
      if (value === null || value.length === 0) return true;
      return !Object.keys(value).length;

    default:
      return false;
  }
  return false;
}

/**
 * @description 去除空格
 * @param String str 需要去除空格的字符串
 * @param String pos both(左右)|left|right|all 默认both
 */
function trim(str: string | number, pos = 'both') {
  str = String(str);
  if (pos === 'both') {
    return str.replace(/^\s+|\s+$/g, '');
  }
  if (pos === 'left') {
    return str.replace(/^\s*/, '');
  }
  if (pos === 'right') {
    return str.replace(/(\s*$)/g, '');
  }
  if (pos === 'all') {
    return str.replace(/\s+/g, '');
  }
  return str;
}

export function isDef<T>(val: T): val is NonNullable<T> {
  return val !== undefined && val !== null;
}
export function isNumeric(val: string): boolean {
  return /^\d+(\.\d+)?$/.test(val);
}
export function addUnit(value?: string | number): string | undefined {
  if (!isDef(value)) {
    return undefined;
  }

  value = String(value);
  return isNumeric(value) ? `${+value * 2}rpx` : value;
}

/**
 * @description 样式转换
 * 对象转字符串，或者字符串转对象
 * @param {object | string} customStyle 需要转换的目标
 * @param {String} target 转换的目的，object-转为对象，string-转为字符串
 * @returns {object|string}
 */
export function addStyle(customStyle: any, target = 'object') {
  // 字符串转字符串，对象转对象情形，直接返回
  if (empty(customStyle)) return customStyle;
  if (typeof customStyle === 'object' && target === 'object') return customStyle;
  if (target === 'string' && typeof customStyle === 'string') {
    return customStyle;
  }

  // 字符串转对象
  if (target === 'object') {
    // 去除字符串样式中的两端空格(中间的空格不能去掉，比如padding: 20px 0如果去掉了就错了)，空格是无用的
    customStyle = trim(customStyle);
    // 根据";"将字符串转为数组形式
    const styleArray = customStyle.split(';');
    const style: any = {};
    // 历遍数组，拼接成对象
    for (let i = 0; i < styleArray.length; i++) {
      // 'font-size:20px;color:red;'，如此最后字符串有";"的话，会导致styleArray最后一个元素为空字符串，这里需要过滤
      if (styleArray[i]) {
        const item = styleArray[i].split(':');
        style[trim(item[0])] = trim(item[1]);
      }
    }
    return style;
  }
  // 这里为对象转字符串形式
  let string = '';

  Object.keys(customStyle).forEach((k) => {
    // 驼峰转为中划线的形式，否则css内联样式，无法识别驼峰样式属性名
    const key = (k as string).replace(/([A-Z])/g, '-$1').toLowerCase();
    string += `${key}:${customStyle[k]};`;
  });
  // 去除两端空格
  return trim(string);
}

export function checkCurrentPageCustom() {
  const register = new Register();

  const page = getCurrentPages();
  const isCustomInPage =
    register.RmsPages.pages.find((p) => p.path === page[page.length - 1].route)?.style
      ?.navigationStyle === 'custom';

  const subPage = register.RmsPages.subPackages.find((p) =>
    page[page.length - 1].route?.includes(p.root),
  )?.pages as typeof register.RmsPages.pages;

  if (isCustomInPage) return isCustomInPage;

  const isCustomInSubPage =
    subPage?.find((p) => page[page.length - 1].route?.includes(p.path))?.style?.navigationStyle ===
    'custom';

  return isCustomInSubPage;
}
