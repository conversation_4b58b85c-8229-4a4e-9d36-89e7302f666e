declare module 'vue' {
  export interface GlobalComponents {
    ScAuth: typeof import('./sc-auth/sc-auth.vue')['default'];
    ScAutoRegion: typeof import('./sc-auto-region/sc-auto-region.vue')['default'];
    ScAvatar: typeof import('./sc-avatar/sc-avatar.vue')['default'];
    ScBackgroundImage: typeof import('./sc-background-image/sc-background-image.vue')['default'];
    ScBanners: typeof import('./sc-banners/sc-banners.vue')['default'];
    ScBindPhone: typeof import('./sc-bind-phone/sc-bind-phone.vue')['default'];
    ScButton: typeof import('./sc-button/sc-button.vue')['default'];
    ScButtonFixed: typeof import('./sc-button-fixed/sc-button-fixed.vue')['default'];
    ScButtonSubscribe: typeof import('./sc-button-subscribe/sc-button-subscribe.vue')['default'];
    ScCascader: typeof import('./sc-cascader/sc-cascader.vue')['default'];
    ScCategory: typeof import('./sc-category/sc-category.vue')['default'];
    ScChooseAmap: typeof import('./sc-choose-amap/sc-choose-amap.vue')['default'];
    ScCopyright: typeof import('./sc-copyright/sc-copyright.vue')['default'];
    ScDataCheckbox: typeof import('./sc-data-checkbox/sc-data-checkbox.vue')['default'];
    ScDatePicker: typeof import('./sc-date-picker/sc-date-picker.vue')['default'];
    ScDateSelect: typeof import('./sc-date-select/sc-date-select.vue')['default'];
    ScDatetime: typeof import('./sc-datetime/sc-datetime.vue')['default'];
    ScDivider: typeof import('./sc-divider/sc-divider.vue')['default'];
    ScEmpty: typeof import('./sc-empty/sc-empty.vue')['default'];
    ScFilterTabs: typeof import('./sc-filter-tabs/sc-filter-tabs.vue')['default'];
    ScForm: typeof import('./sc-form/sc-form.vue')['default'];
    ScGroup: typeof import('./sc-group/sc-group.vue')['default'];
    ScIcon: typeof import('./sc-icon/sc-icon.vue')['default'];
    ScImage: typeof import('./sc-image/sc-image.vue')['default'];
    ScInput: typeof import('./sc-input/sc-input.vue')['default'];
    ScIntegralTips: typeof import('./sc-integral-tips/sc-integral-tips.vue')['default'];
    ScList: typeof import('./sc-list/sc-list.vue')['default'];
    ScMenus: typeof import('./sc-menus/sc-menus.vue')['default'];
    ScMedal: typeof import('./sc-medal/sc-medal.vue')['default'];
    ScMessage: typeof import('./sc-message/sc-message.vue')['default'];
    ScNavBar: typeof import('./sc-nav-bar/sc-nav-bar.vue')['default'];
    ScNumberBox: typeof import('./sc-number-box/sc-number-box.vue')['default'];
    ScParse: typeof import('./sc-parse/sc-parse.vue')['default'];
    ScPermission: typeof import('./sc-permission/sc-permission.vue')['default'];
    ScPicker: typeof import('./sc-picker/sc-picker.vue')['default'];
    ScPrivacy: typeof import('./sc-privacy/sc-privacy.vue')['default'];
    ScRate: typeof import('./sc-rate/sc-rate.vue')['default'];
    ScRegion: typeof import('./sc-region/sc-region.vue')['default'];
    ScSearch: typeof import('./sc-search/sc-search.vue')['default'];
    ScSelect: typeof import('./sc-select/sc-select.vue')['default'];
    ScSkeleton: typeof import('./sc-skeleton/sc-skeleton.vue')['default'];
    ScSteps: typeof import('./sc-steps/sc-steps.vue')['default'];
    ScTabs: typeof import('./sc-tabs/sc-tabs.vue')['default'];
    ScTag: typeof import('./sc-tag/sc-tag.vue')['default'];
    ScTextarea: typeof import('./sc-textarea/sc-textarea.vue')['default'];
    ScTooltip: typeof import('./sc-tooltip/sc-tooltip.vue')['default'];
    ScUpload: typeof import('./sc-upload/sc-upload.vue')['default'];
    ScNationwide: typeof import('./sc-nationwide/sc-nationwide.vue')['default'];
  }
}

export {};
