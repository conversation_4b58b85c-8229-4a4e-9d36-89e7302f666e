<script setup lang="ts">
import { ref } from 'vue';

interface IProps {
  /** 内容 */
  content: string;
  /** 是否生效 */
  effect?: boolean;
}

const show = ref(false);

function open() {
  if (show.value || !props.effect) return;
  show.value = true;

  setTimeout(() => {
    close();
  }, 1500);
}

function close() {
  show.value = false;
}

const props = withDefaults(defineProps<IProps>(), {
  effect: true,
});
</script>

<template>
  <view class="sc-tooltip" @click="open">
    <slot></slot>
    <view v-if="show && (content || $slots.content)" class="sc-tooltip-popup">
      <slot name="content">
        {{ content }}
      </slot>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.sc-tooltip {
  position: relative;
}

.sc-tooltip-popup {
  z-index: 1;
  position: absolute;
  left: 0;
  background-color: #333;
  border-radius: 8px;
  color: #fff;
  font-size: 12px;
  text-align: left;
  line-height: 16px;
  padding: 12px;
}

.sc-tooltip__open .sc-tooltip-popup {
  display: block;
}
</style>
