<template>
  <view class="uni-numbox">
    <view
      class="uni-numbox__minus uni-numbox-btns"
      :style="{ background }"
      @click="_calcValue('minus')">
      <text
        class="uni-numbox--text"
        :class="{ 'uni-numbox--disabled': (min && +inputValue <= min) || disabled }"
        :style="{ color }">
        -
      </text>
    </view>
    <input
      v-model="inputValue"
      :disabled="disabled"
      class="uni-numbox__value"
      :type="inputType"
      :style="{ background, color }"
      @focus="_onFocus"
      @blur="_onBlur" />
    <view
      class="uni-numbox__plus uni-numbox-btns"
      :style="{ background }"
      @click="_calcValue('plus')">
      <text
        class="uni-numbox--text"
        :class="{ 'uni-numbox--disabled': (max && +inputValue >= max) || disabled }"
        :style="{ color }">
        +
      </text>
    </view>
  </view>
</template>
<script lang="ts" setup>
/**
 * NumberBox 数字输入框
 * @description 带加减按钮的数字输入框
 * @tutorial https://ext.dcloud.net.cn/plugin?id=31
 * @property {Number} value 输入框当前值
 * @property {Number} min 最小值
 * @property {Number} max 最大值
 * @property {Number} step 每次点击改变的间隔大小
 * @property {String} inputType input 的类型
 * @property {String} background 背景色
 * @property {String} color 字体颜色（前景色）
 * @property {Boolean} disabled = [true|false] 是否为禁用状态
 * @event {Function} change 输入框值改变时触发的事件，参数为输入框当前的 value
 * @event {Function} focus 输入框聚焦时触发的事件，参数为 event 对象
 * @event {Function} blur 输入框失焦时触发的事件，参数为 event 对象
 */

import { nextTick, ref, watch } from 'vue';

interface IProps {
  /** 输入框当前值 */
  value?: number;
  /** 输入框当前值 */
  modelValue?: number;
  /** 最小值 */
  min?: number;
  /** 最大值 */
  max?: number;
  /** 每次点击改变的间隔大小 */
  step?: number;
  /** background */
  background?: string;
  /** color */
  color?: string;
  /** 是否为禁用状态 */
  disabled?: boolean;
  /** input 的类型 */
  inputType?: string;
}

const emits = defineEmits<{
  (e: 'change', val: number): void;
  (e: 'input', val: number): void;
  (e: 'update:modelValue', val: number): void;
  (e: 'blur', event: any): void;
  (e: 'focus', event: any): void;
}>();

const props = withDefaults(defineProps<IProps>(), {
  value: undefined,
  modelValue: undefined,
  min: 0,
  max: 100,
  step: 1,
  background: '#f5f5f5',
  color: '#333',
  disabled: false,
  inputType: 'number',
});

const inputValue = ref<string | number>('');

watch(
  () => props.value,
  (val) => {
    inputValue.value = +val!;
  },
);

watch(
  () => props.modelValue,
  (val) => {
    inputValue.value = +val!;
  },
);

function init() {
  if (props.modelValue === undefined) {
    inputValue.value = '';
    return;
  }

  inputValue.value = Math.max(Math.min(props.modelValue, props.max), props.min);
}

init();

function emitsVal() {
  emits('change', +inputValue.value);
  emits('input', +inputValue.value);
  emits('update:modelValue', +inputValue.value);
}

function _calcValue(type: any) {
  if (props.disabled) {
    return;
  }
  const scale = _getDecimalScale();
  let value = +inputValue.value * scale;
  const step = props.step * scale;
  if (type === 'minus') {
    value -= step;
    if (value < props.min * scale) {
      return;
    }
    if (value > props.max * scale) {
      value = props.max * scale;
    }
  }

  if (type === 'plus') {
    value += step;
    if (value > props.max * scale) {
      return;
    }
    if (value < props.min * scale) {
      value = props.min * scale;
    }
  }

  inputValue.value = +(value / scale).toFixed(String(scale).length - 1);
  emitsVal();
}

function _getDecimalScale() {
  let scale = 1;
  // 浮点型
  // eslint-disable-next-line no-bitwise
  if (~~props.step !== props.step) {
    scale = 10 ** String(props.step).split('.')[1].length;
  }
  return scale;
}

function _onBlur(event: any) {
  emits('blur', event);
  let value = event.detail.value;

  if (Number.isNaN(value)) {
    inputValue.value = props.min;
    emitsVal();
    return;
  }
  if (value.length === 0) {
    inputValue.value = 1;
    nextTick(() => {
      inputValue.value = props.min;
    });
    emitsVal();
    return;
  }
  value = +value;
  if (value > props.max) {
    value = props.max;
  } else if (value < props.min) {
    value = props.min;
  }
  const scale = _getDecimalScale();
  inputValue.value = +value.toFixed(String(scale).length - 1);
  emitsVal();
}

function _onFocus(event: any) {
  emits('focus', event);
}
</script>
<style lang="scss">
$box-height: 26px;
$bg: #f5f5f5;
$br: 2px;
$color: #333;

.uni-numbox {
  /* #ifndef APP-NVUE */
  display: flex;
  /* #endif */
  flex-direction: row;
}

.uni-numbox-btns {
  /* #ifndef APP-NVUE */
  display: flex;
  /* #endif */
  flex-direction: row;
  align-items: center;
  justify-content: center;
  padding: 0 8px;
  background-color: $bg;
  /* #ifdef H5 */
  cursor: pointer;
  /* #endif */
}

.uni-numbox__value {
  margin: 0 2px;
  background-color: $bg;
  width: 40px;
  height: $box-height;
  text-align: center;
  font-size: 14px;
  border-left-width: 0;
  border-right-width: 0;
  color: $color;
}

.uni-numbox__minus {
  border-top-left-radius: $br;
  border-bottom-left-radius: $br;
}

.uni-numbox__plus {
  border-top-right-radius: $br;
  border-bottom-right-radius: $br;
}

.uni-numbox--text {
  // fix nvue
  line-height: 20px;

  font-size: 20px;
  font-weight: 300;
  color: $color;
}

.uni-numbox .uni-numbox--disabled {
  color: #c0c0c0 !important;
  /* #ifdef H5 */
  cursor: not-allowed;
  /* #endif */
}
</style>
