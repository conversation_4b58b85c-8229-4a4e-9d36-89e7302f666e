import type { AxiosRequestConfig } from 'axios';
import {
  ApiFileOssSign,
  ApiFileUpdate,
  ApiFileUpload,
  ApiFileOssUpload,
  ApiFileRepeat,
  ApiFileExist,
} from '@shencom/api';
import { exception<PERSON><PERSON><PERSON>, IsH5, <PERSON><PERSON><PERSON>, Tenant } from '@rms/utils';
import { FileInfo, OssFile, TempFile, UploadOssParam } from './type';
import Register from '@rms/register';

type UploadOss = {
  (
    sign: UploadOssParam['sign'],
    file: UploadOssParam['file'],
    custom?: { onUploadProgress?: AxiosRequestConfig['onUploadProgress'] },
  ): Promise<OssFile>;
};

interface ossData {
  policy: string;
  OSSAccessKeyId: string;
  signature: string;
  key: string;
  success_action_status: number;
  name: string;
}

let index = 0;

const UPLOAD_TIMEOUT = 3 * 1000;

function getUid() {
  return `${Date.now()}${++index}`;
}

const handleFileInfo = (file: TempFile) => {
  if (!file) throw new Error('文件不存在');

  // 获取文件后缀名, h5存在 name, 小程序存在 path
  let name = '';
  if ('name' in file) {
    name = file.name;
  } else {
    name = file.path;
  }

  const suffix = name.split('.').pop();
  const fileName = getUid() + '.' + suffix;
  return { file, fileName };
};

export async function h5UrlToBlob(filePath: string) {
  const res = await fetch(filePath);
  const bold = await res.blob();

  return bold;
}

async function createFormData(data: ossData, filePath: string) {
  const formData = new FormData();

  Object.keys(data).forEach((k) => {
    formData.append(`${k}`, String(data[k as keyof ossData]));
  });

  const bold = await h5UrlToBlob(filePath);

  formData.append('file', bold);

  return formData;
}

export async function ossSign() {
  return (await ApiFileOssSign()).data;
}

// TODO: 待优化微信h5上传
export const uploadOss: UploadOss = async (sign, file, custom = {}) => {
  const uid = (file.uuid && String(file.uuid).slice(-4)) || getUid();
  const item = file?.item?.tempFile || file?.item;
  const obj = item?.name
    ? item
    : {
        name: file.name || file.path,
        path: file.path || file.item.tempFilePath,
        ...file.item,
      };
  const { file: fileItem, fileName } = file.item
    ? handleFileInfo(obj)
    : { file: file.path, fileName: file.name || file.path };

  const mpFile = { path: fileItem };
  let targetName = fileName;
  let boldParams: any;
  if (IsH5 && file.blob) {
    const fileObj = new File([file.blob], file.name, { path: file.path, type: file.blob.type });
    targetName = file.name;
    boldParams = {
      ...sign,
      file: fileObj,
      fileName: file.name,
    };
  }

  const res = ApiFileOssUpload(
    boldParams
      ? boldParams
      : {
          ...sign,
          file: file.item ? fileItem : mpFile,
          fileName: targetName,
        },
    custom,
  ).then((headers: any) => {
    const _etag = headers?.ETag || headers?.Etag || headers?.etag || '';

    const info: OssFile = {
      id: uid,
      fileName: item.file?.name || item.name || targetName,
      name: targetName,
      fileSize: file.size,
      signature: sign.signature,
      remoteUrl: `${sign.host}/${sign.dir}${targetName}`,
      sign: 1,
      createdAt: Date.now(),
      memo: ((Array.isArray(_etag) ? _etag[0] : _etag) || '').replace(/"/g, ''),
    };

    return info;
  });

  return res;
};

export const checkedFileExist = async (etag: string) => {
  const { data } = await ApiFileExist({
    etag,
  });

  return data as FileInfo[];
};

export const updateDB = async (file: OssFile, checkFileExist = false) => {
  try {
    const params: Parameters<typeof ApiFileUpdate>[0] = {
      sign: file.sign,
      name: file.name,
      fileSize: file.fileSize,
      fileName: file.fileName,
      remoteUrl: file.remoteUrl,
      signature: file.signature,
      memo: file.memo,
    };

    let isSame = false;

    if (checkFileExist) {
      const data = await checkedFileExist(file.memo);

      isSame = data.length > 0;
      params.remark = file.memo;
    }

    const { data } = await ApiFileUpdate(params);

    return checkFileExist
      ? {
          ...data,
          isSame,
          etag: file.memo,
        }
      : data;
  } catch (error) {
    exceptionHandler(error);

    throw error;
  }
};

/** 检查签名是否过期 */
export function checkSignature(data: SC.OSS.Sign) {
  const { expire } = data;
  const timeout = +expire * 1000;
  const now = new Date().getTime();

  return !(expire && timeout > now + UPLOAD_TIMEOUT);
}

export interface EtagsProps {
  id: string;
  etag: string;
}

/**
 * 判断是否存在重复文件
 *
 * @param {array} fileList 需检查的文件列表，必须是 upload 组件绑定的 fileList 数据，否则无法自动设置重复标识
 * @param {string} origin 业务标识符，若不传则当前检测租户下所有文件
 * @param {boolean=true} checkLocal 是否检查本次上传的文件中是否重复
 * @return {array} { id: string, etag: string }[] 重复的文件列表
 */
export async function checkRepectFile(
  fileList: FileInfo[],
  origin?: string,
  checkLocal = true,
): Promise<EtagsProps[]> {
  if (!Array.isArray(fileList)) {
    throw new Error('function checkRepect 第一个参数 fileList 必须为 array');
  }

  if (!fileList.length) return [];

  // 获取本次上传重复的 etags
  const temp: Record<string, boolean> = {};
  const tags = new Set<string>();

  fileList.forEach((file) => {
    if (temp[file.memo]) {
      tags.add(file.memo);
    }
    temp[file.memo] = true;
  });

  const format = (file: FileInfo) => ({ id: file.id, etag: file.memo });

  // 设置“重复”标识
  const setRepect = (repeatFiles: EtagsProps[]) => {
    const etags = checkLocal ? tags : new Set<string>();

    fileList.forEach((file) => {
      // oss 重复文件校验
      file.isSame = repeatFiles.some(({ etag }) => file.memo === etag);
      // 本地重复文件校验
      if (!file.isSame && checkLocal && etags.has(file.memo)) {
        file.isSame = true;
        repeatFiles.push(format(file));
      }
    });
  };

  // 校验当前业务是否存在重复文件
  if (origin) {
    const { data } = await ApiFileRepeat({
      origin,
      files: fileList.map(format),
    });
    const repectFiles = data.files || [];

    setRepect(repectFiles);
    return repectFiles;
  }

  // 校验当前租户是否存在重复文件
  return new Promise((resolve) => {
    const etags = [...new Set(fileList.map(({ memo }) => memo))];
    const promises = etags.map((etag) => checkedFileExist(etag));

    Promise.all(promises).then((res) => {
      const repectEtags = res.map((v) => v[0] && v[0].memo).filter(Boolean);
      const repectFiles = fileList
        .map((file) => (repectEtags.includes(file.memo) ? file : null))
        .filter(Boolean) as FileInfo[];
      setRepect(repectFiles.map(format));

      resolve(repectFiles.map(format));
    });
  });
}

export type onUploadProgressType = (
  file?: TempFile,
) => NonNullable<AxiosRequestConfig['onUploadProgress']>;

interface ReqOssUpload {
  sign: SC.OSS.Sign;
  file: TempFile;
  onUploadProgress?: NonNullable<AxiosRequestConfig['onUploadProgress']>;
  isUpdateDB?: boolean;
  checkFileExist?: boolean;
  custom?: AxiosRequestConfig;
}

export const ossUpload = async (options: ReqOssUpload) => {
  const {
    sign,
    file,
    onUploadProgress = () => undefined,
    isUpdateDB = true,
    checkFileExist = false,
    custom,
  } = options;
  try {
    const info = await uploadOss(
      sign,
      {
        uuid: Date.now(),
        name: file.name || '',
        size: file.size || 0,
        path: file.url || file.path,
        item: file.file,
        blob: file.blob,
      },
      {
        onUploadProgress,
        ...custom,
      },
    );

    let fileInfo = info;

    if (!info) {
      throw new Error('上传失败');
    }

    if (isUpdateDB) {
      const data = await updateDB(info, checkFileExist);
      fileInfo = data;
    }

    return fileInfo as OssFile | FileInfo;
  } catch (error) {
    exceptionHandler(error);
    throw error;
  }
};

export const serverUpload = async (fileItem: TempFile, custom?: AxiosRequestConfig) => {
  const suffix = fileItem.name.split('.').pop();
  const fileName = getUid() + '.' + suffix;
  const { file } = handleFileInfo(fileItem.file);

  try {
    const register = new Register();

    let boldParams: any = {};

    if (IsH5 && file.blob) {
      const fileObj = new File([file.blob], fileName, { type: file.blob.type });
      boldParams = {
        file: fileObj,
        fileName,
      };
    }

    // 服务器上传参数
    const formData = {
      isDownload: true,
      target: 1,
      open: 1,
      file,
      fileName,
      ...(register.RmsAppConfig.upload?.params || {}),
      ...boldParams,
    };

    const { data } = await ApiFileUpload(formData, custom as any);
    return { ...data, remoteUrl: data.url, memo: data.etag };
  } catch (error) {
    exceptionHandler(error);
    throw error;
  }
};
