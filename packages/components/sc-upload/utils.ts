import { IsH5, IsIShenzhen, IsWeixinH5, Loading } from '@rms/utils';
import { ChooseFile, TempFile } from './type';
import IShenzhen from '@rms/isz';

export function getFileName(fileSrc: string, blob?: Blob) {
  if (!fileSrc) return '';
  if (typeof fileSrc !== 'string') return '';

  if (blob) {
    fileSrc += '.' + blob.type?.split('/')[1] || 'jpeg';
  }

  const arr = fileSrc.split('.');
  return arr
    .splice(arr.length - 2, 2)
    .join('.')
    .replace(/[/:]/g, '');
}

// export function formatImage(res: UniApp.ChooseImageSuccessCallbackResult) {
//   const tempFiles = !Array.isArray(res.tempFiles) ? [res.tempFiles] : res.tempFiles;

//   return tempFiles.map((item) => {
//     const path = 'path' in item ? item.path : '';
//     const name = 'name' in item ? item.name : getFileName(path);

//     return { name, path, size: item.size, file: item, type: 'image' } as ChooseFile;
//   });
// }

export function createUrlByBlob(blob: Blob) {
  return (window.URL || window.webkitURL).createObjectURL(blob);
}

export function toBlob(data: string, type: string) {
  const str = window.atob(data);
  let n = str.length;
  const array = new Uint8Array(n);
  while (n--) {
    array[n] = str.charCodeAt(n);
  }
  return new Blob([array], { type });
}

export function base64ToBlob(base64Imgs: string[]) {
  const res: Blob[] = [];
  base64Imgs.forEach((item) => {
    if (typeof window === 'object' && 'document' in window) {
      const base64 = item.split(',');
      const matchArr = base64[0].match(/:(.*?);/);
      const type = matchArr ? matchArr[1] : 'image/png';
      const blob = toBlob(base64[1], type);
      res.push(blob);
    }
  });

  return res;
}

function getLocalImgData(file: any) {
  return new Promise((resolve, reject) => {
    uni.$wx.getLocalImgData({
      localId: file,
      success(res: any) {
        const localData = res.localData;

        let imageBase64 = '';
        if (localData.indexOf('data:image') === 0) {
          // 苹果的直接赋值，默认生成'data:image/jpeg;base64,'的头部拼接
          imageBase64 = localData;
        } else {
          // 此处是安卓中的唯一得坑！在拼接前需要对localData进行换行符的全局替换
          // 此时一个正常的base64图片路径就完美生成赋值到img的src中了
          imageBase64 = `data:image/jpeg;base64,${localData.replace(/\n/g, '')}`;
        }

        resolve(imageBase64);
      },
      fail: reject,
    });
  });
}

export async function formatImage(res: UniApp.ChooseImageSuccessCallbackResult & any) {
  let tempFiles: any[] = [];

  /** 判断是否为微信jssdk上传 */
  if (IsH5 && res.localIds) {
    const localImg = res.localIds.map((item: any) => getLocalImgData(item));
    const base64Imgs = await Promise.all(localImg);
    const list = base64ToBlob(base64Imgs).map((item) => ({
      blob: item,
      path: createUrlByBlob(item),
    }));
    tempFiles = list;
  } else if (IsIShenzhen) {
    /** i深圳获取的base64数据格式与微信不一致 */
    const list = res
      .map((item: string) => {
        if (typeof window === 'object' && 'document' in window) {
          const blob = toBlob(item, 'image/png');
          return {
            blob: blob,
            path: createUrlByBlob(blob),
          };
        }
        return null;
      })
      .filter(Boolean);
    tempFiles = list;
  } else {
    tempFiles = !Array.isArray(res.tempFiles) ? [res.tempFiles] : res.tempFiles;
  }

  return tempFiles.map((item) => {
    if (typeof item === 'string') {
      return { name: getFileName(item), path: item, size: 0, type: 'image' } as ChooseFile;
    }

    const path = 'path' in item ? item.path : '';
    const name = 'name' in item ? item.name : getFileName(path, item.blob);

    return {
      name,
      path,
      size: item.size,
      file: item,
      type: 'image',
      blob: item.blob,
    } as ChooseFile;
  });
}

function formatVideo(res: UniApp.ChooseVideoSuccess) {
  const path = res.tempFilePath || '';
  const name = 'name' in res ? res.name : getFileName(path);

  const info = { ...res, name, path, size: res.size, file: res, type: 'video' } as ChooseFile;

  return [info];
}

function formatMedia(res: UniApp.ChooseMediaSuccessCallbackResult) {
  return res.tempFiles.map((item) => {
    const path = res.type === 'video' ? item.thumbTempFilePath : item.tempFilePath;
    const name = getFileName(path);

    return { ...item, name, path, type: 'file', file: item, size: item.size } as ChooseFile;
  });
}

function formatFile(
  res: UniApp.ChooseMessageFileSuccessCallbackResult | UniApp.ChooseFileSuccessCallbackResult,
) {
  const tempFiles = Array.isArray(res.tempFiles) ? res.tempFiles : [res.tempFiles];

  return tempFiles.map((item) => {
    const path = 'path' in item ? item.path : '';
    const name = 'name' in item ? item.name : getFileName(path);
    const type = 'type' in item ? item.type : '';

    return { path, size: item.size, file: item, name, type } as ChooseFile;
  });
}

export function chooseFile(options: Record<string, any>): Promise<ChooseFile[]> {
  const {
    accept,
    multiple,
    capture,
    compressed,
    maxDuration,
    sizeType,
    camera,
    maxCount,
    extension,
  } = options;

  return new Promise((resolve, reject) => {
    let chooseImage = uni.chooseImage;

    const isPcMiniProgram =
      IsH5 &&
      ['MiniProgramEnv/Mac', 'MiniProgramEnv/Windows'].some((ua) => {
        return navigator.userAgent.toLowerCase().includes(ua.toLowerCase());
      });

    if (IsH5 && IsWeixinH5 && !!uni.$wx && !isPcMiniProgram) {
      chooseImage = (res: any) => {
        uni.$wx.ready(() => {
          uni.$wx?.chooseImage(res);
        });
      };
    }

    if (IsIShenzhen) {
      chooseImage = IShenzhen.chooseImage;
    }

    const isIOS = IsH5 && !!navigator.userAgent.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/);

    switch (accept) {
      case 'image':
        chooseImage({
          count: multiple ? (IsH5 && !isIOS && !IsWeixinH5 ? 1 : Math.min(maxCount, 9)) : 1,
          sourceType: capture,
          sizeType,
          success: (res) => {
            Loading.show('上传中...');
            return resolve(formatImage(res));
          },
          fail: reject,
          cancel: reject,
        });
        break;
      // #ifdef MP-WEIXIN
      // 只有微信小程序才支持chooseMedia接口
      case 'media':
        uni.chooseMedia({
          count: multiple ? Math.min(maxCount, 9) : 1,
          sourceType: capture,
          maxDuration,
          sizeType,
          camera,
          success: (res: any) => resolve(formatMedia(res)),
          fail: reject,
        });
        break;
      // #endif
      case 'video':
        uni.chooseVideo({
          sourceType: capture,
          compressed,
          maxDuration,
          extension: extension?.length ? extension : undefined,
          camera,
          success: (res) => resolve(formatVideo(res)),
          fail: reject,
        });
        break;
      // #ifdef MP-WEIXIN || H5
      // 只有微信小程序才支持chooseMessageFile接口
      case 'file':
        // #ifdef MP-WEIXIN
        uni.chooseMessageFile({
          count: multiple ? maxCount : 1,
          type: accept,
          extension: extension?.length ? extension : undefined,
          success: (res) => resolve(formatFile(res)),
          fail: reject,
        });
        // #endif
        // #ifdef H5
        // 需要hx2.9.9以上才支持uni.chooseFile
        uni.chooseFile({
          count: multiple ? maxCount : 1,
          type: accept,
          extension: extension?.length ? extension : undefined,
          success: (res) => resolve(formatFile(res)),
          fail: reject,
        });
        // #endif
        break;
      // #endif
      default:
        // 此为保底选项，在accept不为上面任意一项的时候选取全部文件
        // #ifdef MP-WEIXIN
        uni.chooseMessageFile({
          count: multiple ? maxCount : 1,
          type: 'all',
          success: (res) => resolve(formatFile(res)),
          fail: reject,
        });
        // #endif
        // #ifdef H5
        // 需要hx2.9.9以上才支持uni.chooseFile
        uni.chooseFile({
          count: multiple ? maxCount : 1,
          type: 'all',
          extension,
          success: (res) => resolve(formatFile(res)),
          fail: reject,
        });
      // #endif
    }
  });
}

/** 是否图片格式
 * @param {Object} value
 */
export function isImage(value = '') {
  const newValue = value.split('?')[0];
  const IMAGE_REGEXP = /\.(jpeg|jpg|gif|png|svg|webp|jfif|bmp|dpg)/i;
  return IMAGE_REGEXP.test(newValue);
}

/**
 * 是否视频格式
 * @param {Object} value
 */
export function isVideo(value = '') {
  const VIDEO_REGEXP = /\.(mp4|mpg|mpeg|dat|asf|avi|rm|rmvb|mov|wmv|flv|mkv|m3u8|webm)/i;
  return VIDEO_REGEXP.test(value);
}

/**
 * 是否文档格式格式
 * @param {Object} value
 */
export function isDoc(val = '') {
  const validExtensions = ['.doc', '.xls', '.ppt', '.pdf', '.docx', '.xlsx', '.pptx'];
  const fileExtension = val.toLowerCase().split('.').pop();
  return validExtensions.includes('.' + fileExtension);
}

/**
 * 是否promise对象
 * @param {Object} value
 */
export function isPromise(value: any): value is Promise<any> {
  return Object.prototype.toString.call(value) === '[object Promise]';
}

/** 创建一个图片对象 */
export function createImgFile(url: string): SC.File.Info | null {
  if (!url) return null;

  const id = `${new Date().getTime()}`;
  return {
    count: 1,
    createdAt: +id,
    fileName: id,
    fileSize: 1,
    id,
    memo: id,
    name: id,
    remoteUrl: url,
    sign: 0,
    signature: id,
    totalCount: 1,
    totalPart: 1,
  };
}
