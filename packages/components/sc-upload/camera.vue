<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { Dialog } from '@rms/utils';

interface IEmit {
  (e: 'complete', d: any): void;
  (e: 'close'): void;
}

const emits = defineEmits<IEmit>();
const isPlay = ref(false);
const img = ref('');
const videoRef = ref();
const videoContent = ref<any>();
const fileStream = ref();

// 检验设备
const checkCamera = async () => {
  videoRef.value = document.querySelector('#video')!.getElementsByTagName('video')[0];
  videoContent.value = uni.createVideoContext('video');

  // 先检验当前设备是否有摄像设备
  const navigator = window.navigator.mediaDevices;
  const devices = await navigator.enumerateDevices(); // 如果有视频设备，则获取摄像头

  if (devices) {
    const stream = await navigator.getUserMedia({
      audio: false, // 不需要音频
      video: {
        // facingMode: 'user', //前置摄像头
        facingMode: { exact: 'environment' }, //强制后置摄像头
      },
    });

    if (!videoRef.value) return;
    videoRef.value.srcObject = stream;
    videoRef.value.onloadedmetadata = () => {
      videoContent.value.play();
    };
    videoContent.value.play();
  }
};

function generateScreenshot(video: any) {
  // 创建一个canvas元素
  const canvas = document.createElement('canvas');
  let w = video.videoWidth;
  let h = video.videoHeight;
  if (w > h) {
    w = video.videoHeight;
    h = video.videoWidth;
  }
  canvas.width = w;
  canvas.height = h;

  // 在canvas上绘制视频帧
  canvas.getContext('2d')!.drawImage(video, 0, 0, canvas.width, canvas.height);

  // 将canvas转换为图片地址
  const imageSrc = canvas.toDataURL('image/png');

  // 返回图片地址
  return imageSrc;
}

// base64转文件
function base64ToFile(base64Data: string) {
  // 将base64的数据部分提取出来
  const parts = base64Data.split(';base64,');
  const contentType = parts[0].split(':')[1];
  const raw = window.atob(parts[1]);
  const rawLength = raw.length;
  const uInt8Array = new Uint8Array(rawLength);

  for (let i = 0; i < rawLength; ++i) {
    uInt8Array[i] = raw.charCodeAt(i);
  }

  // 使用Blob和提取出的数据以及文件名创建一个新的File对象
  const blob = new Blob([uInt8Array], { type: contentType });
  const imgSrc = URL.createObjectURL(blob);
  const filename = `${imgSrc.split('/').pop()}.jpg`;
  const file: any = new File([blob], filename, { type: contentType });
  file.fileName = filename;
  return {
    file,
    filename,
    name: filename,
    path: imgSrc,
    size: blob.size,
    progress: 100,
    status: 'success',
    type: 'image',
  };
}

function onPlay() {
  isPlay.value = true;
}

function onClose() {
  emits('close');
}

function onCancel() {
  img.value = '';
}

function onUsePic() {
  fileStream.value = base64ToFile(img.value);
  img.value = '';
  emits('complete', fileStream.value);
}

// 点击拍照
const handleShoot = async () => {
  if (!isPlay.value) {
    const flag = await Dialog('摄像头打开失败,是否重试?');
    if (flag) {
      navigator.mediaDevices.getUserMedia({ video: true }).finally(() => {
        checkCamera();
      });
    } else {
      onClose();
    }
    return;
  }
  const imgUrl = generateScreenshot(videoRef.value);
  img.value = imgUrl;
};

onMounted(() => {
  checkCamera();
});
</script>

<template>
  <view class="w-screen h-screen bg-black fixed z-[99] top-0 left-0 pt-16">
    <video
      id="video"
      class="w-screen h-[75vh]"
      :controls="false"
      :show-center-play-btn="false"
      :enable-progress-gesture="false"
      object-fit="cover"
      preload="auto"
      @play="onPlay" />
    <view class="flex items-center px-3 py-6 bg-black">
      <view class="flex-1 text-white text-left" @click="onClose">取消</view>
      <view class="flex-1 flex items-center justify-center" @click="handleShoot">
        <view class="takePhone"></view>
      </view>
      <view class="flex-1"></view>
    </view>

    <view v-show="img" class="absolute z-[999] top-0 left-0 bg-black w-screen h-screen py-16">
      <img :src="img" class="w-screen h-[75vh] object-cover" />
      <view class="flex items-center px-2 py-6 bg-black">
        <view class="flex-1 text-white text-left" @click="onCancel">重拍</view>
        <view class="flex-1 text-white text-right" @click="onUsePic">使用图片</view>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.takePhone {
  width: 120rpx;
  height: 120rpx;
  background-color: #fff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  &:hover {
    &::before {
      transform: scale(1.1);
    }
  }
  &::before {
    content: '';
    display: block;
    width: 75%;
    height: 75%;
    border: 2px solid #000;
    border-radius: 50%;
    transition: all 0.2s;
  }
}
</style>
