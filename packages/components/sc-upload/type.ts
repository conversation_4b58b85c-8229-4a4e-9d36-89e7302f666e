export type Capture = 'album' | 'camera';

export interface ChooseFile {
  size: number;
  name: string;
  path: string;
  type: 'video' | 'image' | 'file' | 'local';
  file?: any;
  blob?: Blob;
}

export interface TempFile extends ChooseFile {
  url?: string;
  isImage?: boolean;
  isVideo?: boolean;
  status?: 'uploading' | 'failed' | 'success' | 'repeat';
  message?: string;
  deletable?: boolean;
  progress?: number;
  tempUrl?: string;
  file: any;
}

export interface UploadOssParam {
  sign: SC.OssSignature;
  file: {
    /** 真实文件名 */
    name: string;
    /** 文件大小 */
    size: number;
    /** 临时文件路径 */
    path: string;
    /** 随机生成id 默认: 时间戳 */
    uuid?: string | number;
    /** 文件格式 */
    extname?: string;
    item: any;
    blob: Blob;
  };
}

export type OssFile = Omit<SC.File, 'count' | 'open' | 'totalPart' | 'totalCount'>;

export interface FileInfo extends SC.File.Info {
  /** 是否是重复图片 */
  isSame?: boolean;
  /** Etag */
  memo: string;
}
