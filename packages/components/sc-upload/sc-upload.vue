<script lang="ts" setup>
import { computed, isRef, onMounted, ref, watch } from 'vue';
import { isEqual } from 'lodash-es';
import type { AxiosRequestConfig } from 'axios';
import {
  checkRepectFile,
  checkSignature,
  h5UrlToBlob,
  onUploadProgressType,
  ossSign,
  ossUpload,
  serverUpload,
} from './file';
import {
  Loading,
  Toast,
  exceptionHandler,
  Dialog,
  IsH5,
  openDocument,
  IsWeixin,
  IsPro,
  IsDeploy,
  IsIOS,
  getIOSVersion,
} from '@rms/utils';
import { chooseFile, isVideo, isImage, isPromise, isDoc } from './utils';
import { addUnit } from '../utils';
import { Capture, ChooseFile, FileInfo, OssFile, TempFile } from './type';
import Register from '@rms/register';
import Camera from './camera.vue';

export type UploadFile = Partial<FileInfo & TempFile & ChooseFile>;

export type EventParams = { files: ChooseFile[]; index: number; name: string };

export type EventFileParam<T extends UploadFile> = { file: T; index: number; name: string };

export type EventFilesParam<T> = { files: T[]; index: number; name: string };

interface IProps {
  /** modelValue */
  modelValue?: UploadFile[];
  /** 上传图标 */
  uploadIcon?: string;
  /** 上传图标大小 */
  uploadIconSize?: string | number;
  /** 上传图标颜色 */
  uploadIconColor?: string;
  /** 上传区域的提示文字 */
  uploadText?: string;
  /** 预览上传的图片时的裁剪模式，和image组件mode属性一致 */
  imageMode?: string;
  /** 内部预览图片区域和选择图片按钮的区域宽度，单位rpx，不能是百分比，或者auto */
  width?: string | number;
  /** 内部预览图片区域和选择图片按钮的区域高度，单位rpx，不能是百分比，或者auto */
  height?: string | number;
  /** 是否全屏预览 */
  previewFullImage?: boolean;
  /** 接受的文件类型, 可选值为all media image file video */
  accept?: 'all' | 'media' | 'image' | 'file' | 'video' | 'local';
  /** 是否开启图片多选，部分安卓机型不支持，默认为 false */
  multiple?: boolean;
  /** 是否启用(显示/隐藏)组件 */
  disabled?: boolean;
  /** 图片或视频拾取模式，当accept为image类型时设置capture可选额外camera可以直接调起摄像头 */
  capture?: Capture | Capture[];
  /** 当accept为video时生效，是否压缩视频，默认为true */
  compressed?: boolean;
  /** 当accept为video时生效，拍摄视频最长拍摄时间，单位秒 */
  maxDuration?: number;
  /** original 原图，compressed 压缩图，默认二者都有，H5无效 */
  sizeType?: ('original' | 'compressed')[];
  /** 最大选择图片的数量 */
  maxCount?: number | string;
  /** 是否显示删除图片的按钮 */
  deletable?: boolean;
  /** 是否在上传完成后展示预览图 */
  previewImage?: boolean;
  /** 当accept为video时生效，可选值为back或front */
  camera?: 'back' | 'front';
  /** 标识符，可以在回调函数的第二项参数中获取 */
  name?: string;
  /** 读取后的处理函数 */
  afterRead?: (files: ChooseFile[], index: number, name: string) => boolean;
  /** 是否启用(显示/隐藏)组件 */
  useBeforeRead?: boolean;
  /** 读取前的处理函数 */
  beforeRead?: (files: ChooseFile[], index: number, name: string) => boolean;
  /** 选择单个文件的最大大小，单位B(byte)，默认不限制 */
  maxSize?: string | number;
  /** 是否自动上传，默认为 true */
  autoUpload?: boolean;
  /** 是否判断重复照片，默认为 false */
  checkFileExist?: boolean;
  /** 上传方式，默认: oss */
  uploadType?: 'oss' | 'server';
  /** 上传方式为 oss 时，是把文件信息更新到数据库 */
  isUpdateDB?: boolean;
  /** 是否换行 */
  isWrap?: boolean;
  /** 删除提示文案 */
  deleteModelText?: string;
  /** 根据文件拓展名过滤，仅 type==file 时有效。每一项都不能是空字符串。默认不过滤。 */
  extension?: string[];
  /** 自定义上传请求配置 */
  custom?: AxiosRequestConfig;
}

interface IEmits {
  (e: 'select', files: ChooseFile[], name: string): void;
  (e: 'beforeRead', file: EventFilesParam<ChooseFile> & { callback: (ok: boolean) => void }): void;
  (e: 'afterRead', file: EventFilesParam<ChooseFile>): void;
  (e: 'oversize', file: EventFilesParam<ChooseFile>): void;
  (e: 'error', error: any): void;
  (e: 'remove', file: EventFileParam<any>, name: string): void;
  (e: 'success', file: EventFileParam<any>, fileList: UploadFile[]): void;
  (e: 'fail', file: EventFileParam<TempFile>, fileList: UploadFile[]): void;
  (e: 'complete', files: EventFilesParam<any>): void;
  (e: 'load', onUpload: () => void): void;
  (e: 'update:modelValue', fileList: UploadFile[]): void;
  (e: 'change', fileList: UploadFile[]): void;
  (e: 'preview', file: UploadFile): void;
}

// #ifdef APP-NVUE
const successIcon =
  'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACgAAAAoCAYAAACM/rhtAAAAAXNSR0IArs4c6QAAAERlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAA6ABAAMAAAABAAEAAKACAAQAAAABAAAAKKADAAQAAAABAAAAKAAAAAB65masAAACP0lEQVRYCc3YXygsURwH8K/dpcWyG3LF5u/6/+dKVylSypuUl6uUPMifKMWL8oKEB1EUT1KeUPdR3uTNUsSLxb2udG/cbvInNuvf2rVnazZ/ZndmZ87snjM1Z+Z3zpzfp9+Z5mEAhlvjRtZgCKs+gnPAOcAkkMOR4jEHfItjDvgRxxSQD8cM0BuOCaAvXNCBQrigAsXgggYUiwsK0B9cwIH+4gIKlIILGFAqLiBAOTjFgXJxigJp4BQD0sIpAqSJow6kjSNAFTnRaHJwLenD6Mud52VQAcrBfTd2oyq+HtGaGGWAcnAVcXWoM3bCZrdi+ncPfaAcXE5UKVpdW/vitGPqqAtn98d0gXJwX7Qp6MmegUYVhvmTIezdmHlxJCjpHRTCFerLkRRu4k0aqdajN3sWOo0BK//msHa+xDuPC/oNFMKRhTtM4xjIX0SCNpXL4+7VIaHuyiWEp2L7ahWLf8fejfPdqPmC3mJicORZUp1CQzm+GiphvljGk+PBvWRbxii+xVTj5M6CiZ/tsDufvaXyxEUDxeLIyvu3m0iOyEFWVAkydcVYdyFrE9tQk9iMq6f/GNlvwt3LjQfh60LUrw9/cFyyMJUW/XkLSNMV4Mi6C5ML+ui4x5ClAX9sB9w0wV6wglJwJCv5fOxcr6EstgbGiEw4XcfUry4cWrcEUW8n+ARKxXEJHhw2WG43UKSvwI/TSZgvl7kh0b3XLZaLEy0QmMgLZAVH7J+ALOE+AVnDvQOyiPMAWcW5gSzjCPAV+78S5WE0GrQAAAAASUVORK5CYII=';
// #endif

const props = withDefaults(defineProps<IProps>(), {
  autoUpload: true,
  isUpdateDB: true,
  checkFileExist: false,
  multiple: false,
  uploadType: 'oss',
  uploadIcon: 'i-carbon-camera',
  uploadIconSize: 26,
  uploadIconColor: '#D3D4D6',
  uploadText: '',
  imageMode: 'aspectFill',
  width: 80,
  height: 80,
  previewFullImage: true,
  accept: 'image',
  maxCount: 52,
  maxDuration: 60,
  deletable: true,
  previewImage: true,
  compressed: true,
  camera: 'back',
  sizeType: () => ['compressed', 'original'],
  capture: () => ['camera', 'album'],
  afterRead: undefined,
  beforeRead: undefined,
  name: 'file',
  maxSize: Number.MAX_VALUE,
  disabled: false,
  isWrap: true,
  deleteModelText: '',
  modelValue: () => [],
  extension: () => [],
});

const showLocalCamera = ref(false);

const emits = defineEmits<IEmits>();

const lists = ref<TempFile[]>([]);

const tempList = ref<UploadFile[]>([]);

const isInCount = computed(() => tempList.value.length < Number(props.maxCount));

// const fileList = computed<TempFile[]>(() => {
//   return lists.value.concat(tempList.value);
// });

const formatFileList = (vals: UploadFile[]) => {
  // const { maxCount } = props;
  // lists.value = [];
  // tempList.value = [];

  const _lists = vals
    .map((item) => {
      const url = item.remoteUrl || item.url || item.path;

      return {
        ...item, // 如果item.url为本地选择的blob文件的话，无法判断其为video还是image，此处优先通过accept做判断处理
        url: item.url || item.remoteUrl,
        status: item.status || 'success',
        isImage: props.accept === 'image' || isImage(url),
        isVideo: props.accept === 'video' || isVideo(url),
        type: props.accept === 'image' || isImage(url) ? 'image' : isVideo(url) ? 'video' : '',
        deletable:
          typeof item.deletable === 'boolean'
            ? item.deletable
            : isRef(props.deletable)
            ? props.deletable.value
            : props.deletable,
      };
    })
    .filter((item) => item.remoteUrl || item.url || item.path);

  tempList.value = _lists as ChooseFile[];

  // tempList.value = [];

  // isInCount.value = _lists.length < Number(maxCount);
};

// watch(
//   () => props.list,
//   (val) => formatFileList(val),
//   { immediate: true },
// );

// watch(
//   () => fileList.value,
//   (val) => {
//     emits('update:modelValue', val);
//   },
//   { immediate: true, deep: true },
// );

watch(
  () => props.modelValue,
  (val) => {
    if (isEqual(val, tempList.value)) return;

    formatFileList(val);
  },
  { immediate: true, deep: true },
);

const onPreviewImage = (file: UploadFile, isFile?: boolean, index?: number) => {
  console.log('onPreviewImage :>> ', file);

  if (isVideo(file.remoteUrl || file.url || file.path)) {
    previewVideo(file.remoteUrl || file.url || file.path);
    return;
  }

  if (!file.type?.includes('image') || !props.previewFullImage) {
    // 指支持小程序文件预览，h5 自行处理
    if (IsWeixin && isDoc(file.remoteUrl || file.url)) {
      openDocument(file.remoteUrl || file.url);
      return;
    }

    emits('preview', file);
    return;
  }

  const urls = tempList.value
    .filter(
      (v) =>
        props.accept === 'image' ||
        isImage(v.tempUrl) ||
        isImage(v.remoteUrl) ||
        isImage(v.url) ||
        isImage(v.path),
    )
    .map((v) => v.tempUrl || v.remoteUrl || v.url || v.path) as string[];

  uni.previewImage({
    urls,
    current: Number.isNaN(index) ? file.tempUrl || file.remoteUrl : index,
    fail() {
      uni.showToast({ title: '预览图片失败', icon: 'none', duration: 2000 });
    },
  });
};

const onChooseFile = (customCapture: any, customAccept?: typeof props.accept) => {
  if (props.disabled) return;

  // 如果用户传入的是字符串，需要格式化成数组
  const isCustom = ['album', 'camera'].includes(customCapture);
  const isCustomAccept = ['all', 'media', 'image', 'file', 'video', 'local'].includes(
    customAccept || '',
  );

  if (props.accept === 'local' && !isCustomAccept) {
    chooseLocal();
    return;
  }

  if (props.accept === 'media' && !isCustomAccept) {
    chooseMedia();
    return;
  }

  const resCapture = isCustom ? customCapture : props.capture;
  const capture = (Array.isArray(resCapture) ? resCapture : resCapture.split(',')) || [];
  const accept = isCustomAccept ? customAccept : props.accept;

  chooseFile({
    multiple: props.multiple,
    accept,
    capture,
    compressed: props.compressed,
    maxDuration: props.maxDuration,
    sizeType: props.sizeType,
    camera: props.camera,
    extension: props.extension,
    maxCount: Number(props.maxCount) - tempList.value.length,
  })
    .then((res: ChooseFile[]) => {
      emits('select', res, props.name);
      onBeforeRead(res);
    })
    .catch((error) => {
      emits('error', error);
    });
};

const onInitiativeChooseFile = (capture: Capture) => {
  onChooseFile(capture);
};

// 文件读取之前
const onBeforeRead = (files: ChooseFile[]) => {
  console.log('onBeforeRead :>> ', files);

  const { beforeRead, useBeforeRead } = props;
  const { index, name } = getDetail();

  let res: boolean | Promise<any> = true;

  // beforeRead是否为一个方法
  if (typeof beforeRead === 'function') {
    // 如果用户定义了此方法，则去执行此方法，并传入读取的文件回调
    res = beforeRead(files, index, name);
  }

  if (useBeforeRead) {
    res = new Promise((resolve, reject) => {
      emits('beforeRead', {
        files,
        ...getDetail(),
        callback: (ok: boolean) => {
          if (ok) resolve(true);
          else reject();
        },
      });
    });
  }

  if (!res) {
    Loading.hide();
    return;
  }

  if (isPromise(res)) {
    res.then(() => onAfterRead(files));
  } else {
    onAfterRead(files);
  }
};

const onAfterRead = (files: ChooseFile[]) => {
  console.log('onAfterRead :>> ', files);
  const { maxSize, afterRead } = props;
  const { index, name } = getDetail();

  const oversize = files.some((item) => item.size > +maxSize);

  if (oversize) {
    emits('oversize', { files, index, name });
    return;
  }

  if (typeof afterRead === 'function') {
    afterRead(files, index, name);
  }

  emits('afterRead', { files, index, name });

  tempList.value = [...tempList.value, ...files];

  if (props.autoUpload) {
    onUpload(files);
  } else {
    emits('update:modelValue', tempList.value);
    emits('change', tempList.value);
    emits('complete', { files: tempList.value, ...getDetail() });
    Loading.hide();
  }
};

const onDeleteItem = async (i: number) => {
  if (props.deleteModelText) {
    const flag = await Dialog(props.deleteModelText);
    if (!flag) return;
  }

  const { index, name } = getDetail(i);

  const file = tempList.value[i];

  lists.value = lists.value.filter((item) => (item.path || item.url) !== (file.path || file.url));

  tempList.value = tempList.value.filter(
    (item) => (item.path || item.url) !== (file.path || file.url),
  );

  emits('update:modelValue', tempList.value);
  emits('change', tempList.value);
  emits('remove', { index, name, file }, props.name);
};

function getDetail(index?: number) {
  return { name: props.name, index: index ?? tempList.value.length };
}

/** 清除上传错误图片 */
function handleUploadError(tempFiles: TempFile[]) {
  tempFiles.forEach((file) => {
    const targetFileIndex = tempList.value.findIndex((item) => item.name === file.name);
    tempList.value.splice(
      targetFileIndex,
      tempList.value[targetFileIndex].status === 'failed' ? 1 : 0,
    );
  });
}

/** 识别上传错误图片 */
function handleChangeErrorFileStatus(tempFiles: TempFile[]) {
  tempFiles.forEach((item) => {
    item.status = 'failed';
    const targetFileIndex = tempList.value.findIndex((t) => item.name === t.name && !t.id);
    if (targetFileIndex !== -1) {
      tempList.value[targetFileIndex].status = 'failed';
    }
  });
}

const onBeforeOssUpload = async (tempFiles: TempFile[], getTask: onUploadProgressType) => {
  try {
    let sign = await ossSign();

    if (!sign) return [];

    let uploadCount = 0;

    const files = tempFiles.map(async (file) => {
      const index = tempList.value.findIndex((v) => v.path === file.path);
      tempList.value[index].status = 'uploading';
      try {
        // 判断 oss 签名是否过期
        const isExpire = checkSignature(sign);

        if (isExpire) {
          sign = await ossSign();
        }

        const res = await ossUpload({
          sign,
          file,
          isUpdateDB: props.isUpdateDB,
          checkFileExist: props.checkFileExist,
          onUploadProgress: getTask(file),
          custom: props.custom,
        });
        tempList.value[index].status = 'success';
        tempList.value[index].progress = 100;
        uploadCount++;

        Loading.show(`已经上传${uploadCount} / ${tempFiles.length}...`);
        return { ...file, ...res, tempUrl: file.path } as OssFile | TempFile;
      } catch (error) {
        tempList.value[index].status = 'failed';
        return Promise.reject(file);
      }
    });

    return files;
  } catch (error) {
    exceptionHandler(error);
    handleChangeErrorFileStatus(tempFiles);
    throw tempFiles;
  }
};

const onUpload = async (tempFiles: ChooseFile[]) => {
  console.log('%c [tempFiles]-484', 'font-size:13px; background:#336699; color:#fff;', tempFiles);
  if (!tempFiles.length) return;

  Loading.show('上传中...');

  const all: (TempFile | OssFile)[] = [];
  let rejected: TempFile[] = [];

  let settledResult: PromiseSettledResult<TempFile | OssFile>[] = [];

  const getTask: onUploadProgressType = (file) => {
    return (task: any) => {
      const index = tempList.value.findIndex((v) => v.path === file?.path);

      const progress = task.progress
        ? +task.progress
        : +(task.loaded / task.total).toFixed(2) * 100;

      tempList.value[index].progress = +(progress === 100 ? 99 : progress || 0).toFixed(0);
    };
  };

  const register = new Register();
  const type =
    register.RmsAppConfig.upload?.type || (IsPro && IsDeploy) ? 'server' : props.uploadType;

  try {
    const files =
      type === 'oss'
        ? await onBeforeOssUpload(tempFiles as TempFile[], getTask)
        : await handleServerUpload(tempFiles as TempFile[]);
    settledResult = await Promise.allSettled(files);
  } catch (error) {
    if (Array.isArray(error)) {
      rejected = error;
    }
  }

  settledResult.forEach((item, i) => {
    const { name, index } = getDetail(i);
    const _index = index + tempList.value.length - settledResult.length;

    if (item.status === 'fulfilled') {
      all.push(item.value);

      tempList.value[_index] = { ...tempList.value[_index], ...item.value };

      emits('success', { file: item.value, name, index: _index }, tempList.value);
    } else {
      rejected.push(item.reason);
      all.push(item.reason);
      emits('fail', { file: item.reason, name, index: _index }, tempList.value);
    }
  });

  Loading.hide();

  if (!rejected.length && settledResult.length === tempFiles.length) {
    Toast.success('上传成功');
  } else if (settledResult.length && tempFiles.length) {
    const flag = await Dialog('部分上传失败,是否重试?');
    if (flag) {
      onUpload(rejected.filter((item) => item.status && item.status === 'failed'));
    } else {
      handleUploadError(rejected.filter((item) => item.status && item.status === 'failed'));
    }
  } else {
    const flag = await Dialog('上传失败,是否重试?');
    if (flag) {
      onUpload(rejected.filter((item) => item.status && item.status === 'failed'));
    } else {
      handleUploadError(rejected.filter((item) => item.status && item.status === 'failed'));
    }
  }

  emits('update:modelValue', tempList.value);
  emits('change', tempList.value);
  emits('complete', { files: all, ...getDetail() });
};

const handleFormData = async (config: Record<string, any>) => {
  const { body, filename, file } = config;
  const formData = new FormData();

  for (const [k, v] of Object.entries(body)) {
    formData.append(k, `${v}`);
  }

  const blob = await h5UrlToBlob(file.path);
  const files = new File([blob], filename, { type: 'image/jpeg' });
  formData.append('file', files);

  return formData;
};

async function handleServerUpload(tempFiles: TempFile[]) {
  try {
    let uploadCount = 0;

    const files = tempFiles.map(async (fileItem) => {
      if (fileItem.status === 'success') return;

      const index = tempList.value.findIndex((v) => v.path === fileItem.path);
      tempList.value[index].status = 'uploading';
      try {
        const res = await serverUpload(fileItem, props.custom);

        tempList.value[index].status = 'success';
        tempList.value[index].progress = 100;
        uploadCount++;

        Loading.show(`已经上传${uploadCount} / ${tempFiles.length}...`);
        return { ...fileItem, ...res, tempUrl: fileItem.path } as OssFile | TempFile;
      } catch (error) {
        tempList.value[index].status = 'failed';
        return Promise.reject(fileItem);
      }
    });

    return files;
  } catch (error) {
    exceptionHandler(error);
    handleChangeErrorFileStatus(tempFiles);
    throw tempFiles;
  }
}

function _onUpload() {
  return async () => {
    await onUpload(tempList.value as ChooseFile[]);
  };
}

async function checkRepectFiles(origin?: string, checkLocal = true) {
  const repectFiles = await checkRepectFile(tempList.value as FileInfo[], origin, checkLocal);

  tempList.value.forEach((item) => {
    if (item.isSame) {
      item.status = 'repeat';
    }
  });

  return repectFiles;
}

const previewVideoSrc = ref('');

const videoPopupRef = ref<SC.Popup>();

function previewVideo(url = '') {
  previewVideoSrc.value = url;
  videoPopupRef.value?.open();
}

function chooseMedia() {
  uni.showActionSheet({
    itemList: ['相册', '视频'],
    success: (res) => {
      onChooseFile('', res.tapIndex === 1 ? 'video' : 'image');
    },
    fail: (res) => {
      console.log(res.errMsg);
    },
  });
}

function onChooseLocalFile() {
  if (props.capture === 'camera') {
    showLocalCamera.value = true;
  } else if (props.capture === 'album') {
    onChooseFile('album', 'image');
  } else {
    uni.showActionSheet({
      itemList: ['相册', '拍照'],
      success: (res) => {
        if (res.tapIndex === 1) {
          showLocalCamera.value = true;
        } else {
          onChooseFile('album', 'image');
        }
      },
      fail: (res) => {
        console.log(res.errMsg);
      },
    });
  }
}

function chooseLocal() {
  const iosVersion = getIOSVersion();
  if (IsH5 && IsIOS() && iosVersion && iosVersion >= '16') {
    onChooseLocalFile();
  } else {
    onChooseFile(props.capture, 'image');
  }
}

function onComplete(file: any) {
  const res = [file];

  showLocalCamera.value = false;

  emits('select', res, props.name);
  onBeforeRead(res);
}

onMounted(() => {
  emits('load', _onUpload());
});

defineExpose({ onUpload: _onUpload(), checkRepectFiles, onChooseFile, name: props.name });
</script>

<template>
  <view class="sc-upload">
    <view class="sc-upload__wrap" :class="{ 'sc-upload__nowrap': !props.isWrap }">
      <template v-if="props.previewImage">
        <slot
          name="left"
          :height="height"
          :width="width"
          :is-show="!tempList.length"
          :on-choose-file="onInitiativeChooseFile"></slot>
        <slot name="content" :temp-list="tempList">
          <template v-if="['image', 'media', 'video', 'local'].includes(accept)">
            <view v-for="(item, index) in tempList" :key="index" class="sc-upload__wrap__preview">
              <image
                v-if="
                  !item.isVideo &&
                  (item.isImage || (item.type && item.type === 'image') || item.type === 'local')
                "
                :src="item.tempUrl || item.remoteUrl || item.url || item.path"
                :mode="imageMode"
                class="sc-upload__wrap__preview__image"
                :style="{ width: addUnit(width), height: addUnit(height) }"
                @tap="onPreviewImage(item, false, index)" />
              <view v-else-if="item.isVideo || (item.type && item.type === 'video')">
                <video
                  class="sc-upload__wrap__preview__image"
                  :disabled="false"
                  :controls="false"
                  :show-center-play-btn="false"
                  :src="item.tempUrl || item.remoteUrl || item.url || item.path">
                  <cover-view
                    class="sc-upload__wrap__preview__image !absolute"
                    @click.stop="
                      previewVideo(item.tempUrl || item.remoteUrl || item.url || item.path)
                    ">
                    <view class="w-full h-full flex items-center justify-center text-white">
                      <sc-icon name="i-ic-outline-play-circle-filled-white"></sc-icon>
                    </view>
                  </cover-view>
                </video>
              </view>
              <view v-else class="sc-upload__wrap__preview__other">文件</view>

              <view
                v-if="item.status === 'uploading' || item.status === 'failed'"
                class="sc-upload__status">
                <view class="sc-upload__status__icon">
                  <text
                    v-if="item.status === 'failed'"
                    class="i-carbon-close-outline"
                    :style="{ fontSize: '25rpx', color: '#ffffff' }"></text>
                  <text v-else class="sc-upload__status__progress">
                    {{ item.progress }}%
                    <text class="dot" />
                  </text>
                </view>
                <text v-if="item.message" class="sc-upload__status__message">
                  {{ item.message }}
                </text>
              </view>
              <view
                v-if="item.status !== 'uploading' && (deletable || item.deletable) && !disabled"
                class="sc-upload__deletable"
                @tap.stop="onDeleteItem(index)">
                <view class="sc-upload__deletable__icon">
                  <uni-icons type="closeempty" color="#ffffff" size="22"></uni-icons>
                </view>
              </view>

              <view
                v-if="item.status === 'repeat'"
                class="sc-upload__repeat"
                @tap.stop="onDeleteItem(index)">
                重复图片
              </view>

              <view v-if="item.status === 'success' && deletable" class="sc-upload__success">
                <!-- #ifdef APP-NVUE -->
                <image :src="successIcon" class="sc-upload__success__icon"></image>
                <!-- #endif -->
                <!-- #ifndef APP-NVUE -->
                <view class="sc-upload__success__icon">
                  <uni-icons type="checkmarkempty" color="#ffffff" size="22"></uni-icons>
                </view>
                <!-- #endif -->
              </view>
            </view>
          </template>

          <template v-else>
            <slot name="content-file" :temp-list="tempList">
              <view
                class="w-full flex mb-3 items-center"
                v-for="(item, index) of tempList"
                :key="item.id">
                <sc-icon
                  name="i-material-symbols-add-photo-alternate-rounded"
                  v-if="isImage(item.remoteUrl || item.url || item.path)"
                  size="15"
                  className="mr-1 text-gray-500"
                  class="mr-1 text-gray-500"></sc-icon>
                <sc-icon
                  name="i-solar-chat-round-video-bold"
                  v-else-if="isVideo(item.remoteUrl || item.url || item.path)"
                  size="15"
                  className="mr-1 text-gray-500"
                  class="mr-1 text-gray-500"></sc-icon>
                <sc-icon
                  name="i-tabler-file-description"
                  v-else
                  size="15"
                  className="mr-1 text-gray-500"
                  class="mr-1 text-gray-500"></sc-icon>
                <view
                  class="flex-1 ellipsis-1 mr-2 text-blue-500 underline"
                  @click="onPreviewImage(item, true, index)">
                  {{ item.fileName || item.file?.name || item.name }}
                </view>
                <view
                  v-if="item.status !== 'uploading' && (deletable || item.deletable) && !disabled"
                  @click.stop="onDeleteItem(index)"
                  class="pl-2">
                  <sc-icon
                    name="i-ri-close-line"
                    className="text-blue-500"
                    class="text-blue-500"
                    size="18"></sc-icon>
                </view>
              </view>
            </slot>
          </template>
        </slot>
      </template>
      <template v-if="isInCount && !disabled && deletable">
        <slot name="btn-wrapper">
          <view v-if="$slots.btn || $slots.$btn" @tap="onChooseFile">
            <slot name="btn" />
          </view>
          <sc-button
            v-else-if="accept === 'file'"
            @click="onChooseFile"
            class-name="w-24 !h-7 !rounded !px-2 !mb-4"
            class="w-24 !h-7 !rounded !px-2 !mb-4">
            <text class="text-11">上传文件</text>
          </sc-button>

          <view
            v-else
            class="sc-upload__button"
            :hover-class="!disabled ? 'sc-upload__button--hover' : ''"
            hover-stay-time="150"
            :class="[disabled && 'sc-upload__button--disabled']"
            :style="{ width: addUnit(width), height: addUnit(height) }"
            @tap="onChooseFile">
            <text
              :class="[uploadIcon]"
              :style="{ color: uploadIconColor, fontSize: addUnit(uploadIconSize) }"></text>
            <text v-if="uploadText" class="sc-upload__button__text">{{ uploadText }}</text>
          </view>
        </slot>
      </template>
    </view>
  </view>

  <uni-popup ref="videoPopupRef">
    <view v-if="previewVideoSrc != ''" class="preview-full">
      <video :autoplay="true" :src="previewVideoSrc"></video>
    </view>
  </uni-popup>

  <Camera v-if="showLocalCamera" @complete="onComplete" @close="showLocalCamera = false" />
</template>
<style lang="scss" scoped>
@mixin flex($direction: row) {
  /* #ifndef APP-NVUE */
  display: flex;
  /* #endif */
  flex-direction: $direction;
}
/* #ifndef APP-NVUE */
// 由于uView是基于nvue环境进行开发的，此环境中普通元素默认为flex-direction: column;
// 所以在非nvue中，需要对元素进行重置为flex-direction: column; 否则可能会表现异常
// view {
//   display: flex;
//   flex-direction: column;
//   flex-shrink: 0;
//   flex-grow: 0;
//   flex-basis: auto;
//   align-items: stretch;
//   align-content: flex-start;
// }
/* #endif */
$sc-success: $uni-color-success !default;
$sc-tips-color: $uni-text-color-grey !default;

$sc-upload-preview-border-radius: 8rpx !default;
$sc-upload-preview-margin: 0 8px 8px 0 !default;
$sc-upload-image-width: 160rpx !default;
$sc-upload-image-height: $sc-upload-image-width;
$sc-upload-other-bgColor: rgb(242, 242, 242) !default;
$sc-upload-other-flex: 1 !default;
$sc-upload-text-font-size: 11px !default;
$sc-upload-text-color: $sc-tips-color !default;
$sc-upload-text-margin-top: 2px !default;
$sc-upload-deletable-right: 0 !default;
$sc-upload-deletable-top: 0 !default;
$sc-upload-deletable-bgColor: rgb(55, 55, 55) !default;
$sc-upload-deletable-height: 47rpx !default;
$sc-upload-deletable-width: $sc-upload-deletable-height;
$sc-upload-deletable-boder-bottom-left-radius: 100px !default;
$sc-upload-deletable-zIndex: 3 !default;
$sc-upload-success-bottom: 0 !default;
$sc-upload-success-right: 0 !default;
$sc-upload-success-border-style: solid !default;
$sc-upload-success-border-top-color: transparent !default;
$sc-upload-success-border-left-color: transparent !default;
$sc-upload-success-border-bottom-color: $sc-success !default;
$sc-upload-success-border-right-color: $sc-upload-success-border-bottom-color;
$sc-upload-success-border-width: 25rpx !default;
$sc-upload-icon-top: -2rpx !default;
$sc-upload-icon-right: -1rpx !default;
$sc-upload-icon-h5-top: 0px !default;
$sc-upload-icon-h5-right: 0 !default;
$sc-upload-icon-width: 16px !default;
$sc-upload-icon-height: $sc-upload-icon-width;
$sc-upload-success-icon-bottom: -32rpx !default;
$sc-upload-success-icon-right: -30rpx !default;
$sc-upload-status-right: 0 !default;
$sc-upload-status-left: 0 !default;
$sc-upload-status-bottom: 0 !default;
$sc-upload-status-top: 0 !default;
$sc-upload-status-bgColor: rgba(0, 0, 0, 0.5) !default;
$sc-upload-status-icon-Zindex: 1 !default;
$sc-upload-message-font-size: 12px !default;
$sc-upload-message-color: #ffffff !default;
$sc-upload-message-margin-top: 5px !default;
$sc-upload-button-width: 80px !default;
$sc-upload-button-height: $sc-upload-button-width;
$sc-upload-button-bgColor: rgb(244, 245, 247) !default;
$sc-upload-button-border-radius: 2px !default;
$sc-upload-botton-margin: 0 8px 0px 0 !default;
$sc-upload-text-font-size: 11px !default;
$sc-upload-text-color: $sc-tips-color !default;
$sc-upload-text-margin-top: 2px !default;
$sc-upload-hover-bgColor: rgb(230, 231, 233) !default;
$sc-upload-disabled-opacity: 0.5 !default;

.sc-upload {
  @include flex;
  flex: 1;

  .preview-full {
    position: fixed;
    top: 0;
    left: 0;
    bottom: 0;
    width: 100%;
    height: 100%;
    z-index: 1002;
  }

  .preview-full video {
    width: 100%;
    height: 100%;
    z-index: 1002;
  }

  &__repeat {
    position: absolute;
    bottom: 0;
    background: #f5222d;
    border-top-left-radius: 8rpx;
    border-top-right-radius: 8rpx;
    width: 100%;
    text-align: center;
    color: #ffffff;
  }

  &__wrap {
    @include flex;
    flex-wrap: wrap;
    flex: 1;
    &::-webkit-scrollbar {
      display: none;
    }

    &__preview {
      border-radius: $sc-upload-preview-border-radius;
      margin: $sc-upload-preview-margin;
      position: relative;
      overflow: hidden;
      @include flex;

      &__image {
        width: $sc-upload-image-width;
        height: $sc-upload-image-height;
      }

      &__other {
        width: $sc-upload-image-width;
        height: $sc-upload-image-height;
        background-color: $sc-upload-other-bgColor;
        flex: $sc-upload-other-flex;
        @include flex(column);
        justify-content: center;
        align-items: center;

        &__text {
          font-size: $sc-upload-text-font-size;
          color: $sc-upload-text-color;
          margin-top: $sc-upload-text-margin-top;
        }
      }
    }
  }

  &__nowrap {
    @apply w-full whitespace-nowrap flex-nowrap overflow-y-hidden box-border overflow-x-scroll;
  }

  &__deletable {
    position: absolute;
    top: $sc-upload-deletable-top;
    right: $sc-upload-deletable-right;
    background-color: $sc-upload-deletable-bgColor;
    height: $sc-upload-deletable-height;
    width: $sc-upload-deletable-width;
    @include flex;
    border-bottom-left-radius: $sc-upload-deletable-boder-bottom-left-radius;
    align-items: center;
    justify-content: center;
    z-index: $sc-upload-deletable-zIndex;

    &__icon {
      position: absolute;
      transform: scale(0.7);
      top: $sc-upload-icon-top;
      right: $sc-upload-icon-right;
      /* #ifdef H5 */
      top: $sc-upload-icon-h5-top;
      right: $sc-upload-icon-h5-right;
      /* #endif */
    }
  }

  &__success {
    z-index: 1;
    position: absolute;
    bottom: $sc-upload-success-bottom;
    right: $sc-upload-success-right;
    @include flex;
    // 由于weex(nvue)为阿里巴巴的KPI(部门业绩考核)的laji产物，不支持css绘制三角形
    // 所以在nvue下使用图片，非nvue下使用css实现
    /* #ifndef APP-NVUE */
    border-style: $sc-upload-success-border-style;
    border-top-color: $sc-upload-success-border-top-color;
    border-left-color: $sc-upload-success-border-left-color;
    border-bottom-color: $sc-upload-success-border-bottom-color;
    border-right-color: $sc-upload-success-border-right-color;
    border-width: $sc-upload-success-border-width;
    align-items: center;
    justify-content: center;
    /* #endif */

    &__icon {
      /* #ifndef APP-NVUE */
      position: absolute;
      transform: scale(0.7);
      @include flex;
      align-items: center;
      justify-content: center;
      bottom: $sc-upload-success-icon-bottom;
      right: $sc-upload-success-icon-right;
      /* #endif */
      /* #ifdef APP-NVUE */
      width: $sc-upload-icon-width;
      height: $sc-upload-icon-height;
      /* #endif */
    }
  }

  &__status {
    position: absolute;
    top: $sc-upload-status-top;
    bottom: $sc-upload-status-bottom;
    left: $sc-upload-status-left;
    right: $sc-upload-status-right;
    background-color: $sc-upload-status-bgColor;
    @include flex(column);
    align-items: center;
    justify-content: center;

    &__icon {
      position: relative;
      z-index: $sc-upload-status-icon-Zindex;
    }

    &__message {
      font-size: $sc-upload-message-font-size;
      color: $sc-upload-message-color;
      margin-top: $sc-upload-message-margin-top;
    }

    &__progress {
      @include flex(column);
      justify-content: center;
      align-items: center;
      color: $sc-upload-message-color;
      font-size: $sc-upload-message-font-size;
    }
  }

  &__button {
    @include flex(column);
    align-items: center;
    justify-content: center;
    width: $sc-upload-button-width;
    height: $sc-upload-button-height;
    background-color: $sc-upload-button-bgColor;
    border-radius: $sc-upload-button-border-radius;
    margin: $sc-upload-botton-margin;
    /* #ifndef APP-NVUE */
    box-sizing: border-box;
    /* #endif */

    &__text {
      font-size: $sc-upload-text-font-size;
      color: $sc-upload-text-color;
      margin-top: $sc-upload-text-margin-top;
    }

    &--hover {
      background-color: $sc-upload-hover-bgColor;
    }

    &--disabled {
      opacity: $sc-upload-disabled-opacity;
    }
  }
}
</style>
