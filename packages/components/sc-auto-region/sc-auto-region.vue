<!-- eslint-disable no-await-in-loop -->
<script setup lang="ts">
import { onLoad } from '@dcloudio/uni-app';
import { ref, watch } from 'vue';
import { ApiAMapGeocodeRegeo } from '@shencom/api';
import { exceptionHandler } from '../../utils/src';

interface IProps {
  /** v-model 支持回显 */
  modelValue?: string[];
  /** 区域数据 */
  regions: any[];
  /** 经度 */
  lng: number | string;
  /** 纬度 */
  lat: number | string;
  /** 从第几级开始，['广东省', '深圳市', '罗湖区', '东晓街道'] */
  /** 默认为2，区级 */
  level?: number;
  /** 数据集合对应的 id */
  mapId?: string;
  /** 数据集合对应的 label */
  mapLabel?: string;
  /** 数据集合对应的 children */
  mapChildren?: string;
  /** sc-cascader attr */
  attr?: any;
}

interface IEmits {
  (e: 'update:modelValue', regions: string[]): void;
  (e: 'update:modelRegionText', regions: string): void;
  (e: 'nodeclick', val: any): void;
}

const props = withDefaults(defineProps<IProps>(), {
  level: 2,
  mapId: 'id',
  mapLabel: 'title',
  mapChildren: 'children',
  attr: () => ({
    defalutSize: '32rpx',
    title: '区域筛选',
    placeholder: '请选择',
  }),
});
const emits = defineEmits<IEmits>();

const regionRanges = ref<any[]>([]);

const region = ref<string[]>([]);

const regionText = ref('');

const scCascaderRef = ref();

function deepRegion(resArr: string[]) {
  let cur = props.regions;
  let res = '';

  resArr?.forEach((item) => {
    const target = cur?.find((c) => c[props.mapId] === item);

    if (!target) {
      return;
    }
    cur = target.children;
    res += target[props.mapLabel];
  });

  return res;
}

watch(
  () => region.value,
  () => {
    regionText.value = deepRegion(region.value);
    emits('update:modelValue', region.value);
  },
  {
    deep: true,
    immediate: true,
  },
);

watch(
  () => regionText.value,
  (val) => {
    emits('update:modelRegionText', val);
  },
  {
    deep: true,
  },
);

async function onNodeclick(e: any) {
  emits('nodeclick', e);
}

async function getRegionRanges() {
  const { lat, lng } = props;

  const data = await ApiAMapGeocodeRegeo({
    location: `${lng},${lat}`,
  });
  if (+data.status) {
    const { province, city, district, township } = data.regeocode.addressComponent;
    return [province, city, district, township].filter(
      (item) => typeof item === 'string',
    ) as string[];
  } else {
    throw new Error(`高德逆解析失败: ${JSON.stringify({ ...data })}`);
  }
}

// 初始化
async function regionInit() {
  if (props.modelValue?.length) {
    props.modelValue.forEach((item) => {
      setTimeout(() => {
        region.value.push(item);
      });
    });
    return;
  }
  try {
    const { mapLabel, mapId, level } = props;
    const regionArrs = (await getRegionRanges()).slice(level);
    console.log(
      '%c 当前level对应数据',
      'font-size:13px; background:#336699; color:#fff;',
      regionArrs,
    );

    let currentRegions = props.regions || [];

    // 根据逆解析结果匹配对应数据
    regionArrs.forEach((r, index) => {
      const target = currentRegions.find((item) => {
        return item[mapLabel].includes(r);
      });

      if (!target) {
        return;
      }

      console.log(
        `%c 匹配结果${index + 1}`,
        'font-size:13px; background:#336699; color:#fff;',
        target,
      );

      currentRegions = target.children;
      region.value.push(target[mapId]);
    });
  } catch (error) {
    exceptionHandler(error);
  }
}

async function init() {
  regionInit();
}

onLoad(async () => {
  init();
});

defineExpose({ init });
</script>

<template>
  <sc-cascader
    ref="scCascaderRef"
    v-model="region"
    class="w-full h-full"
    :range-value="mapId"
    :range-key="mapLabel"
    :range="regions"
    v-bind="attr"
    @nodeclick="onNodeclick">
    <slot :text="regionText">
      <view class="flex-1">{{ regionText }}</view>
    </slot>
    <template #placeholder>
      <slot name="placeholder">
        <view class="flex items-center">
          <text class="flex-1 text-[#d5d5d5] text-16">请选择所在地区</text>
          <sc-icon name="i-gridicons:location" class="text-green-400 mr-2" size="16"></sc-icon>
        </view>
      </slot>
    </template>
    <slot name="icon">
      <sc-icon name="i-gridicons:location" class="text-green-400 mr-2" size="16"></sc-icon>
    </slot>
  </sc-cascader>
</template>

<style lang="scss"></style>
