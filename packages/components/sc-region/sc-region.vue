<script lang="ts" setup>
import { ReturnPromiseType } from '@shencom/typing';
import { computed, onMounted, Ref, ref, toRaw, watch } from 'vue';
import ScCascader from '@rms/components/sc-cascader/sc-cascader.vue';
import { ServiceRegion } from '@rms/service';

export interface Option extends Record<string, any> {
  text?: string;
  value?: string;
}

export interface RangeItem extends Option {
  children?: Option[];
}

export interface ChangeType {
  value: string[];
  items: RangeItem[];
  text: string[];
}

type ScCascaderProps = InstanceType<typeof ScCascader>;

type ServiceRegionProps = NonNullable<Parameters<typeof ServiceRegion>[0]>;
type ResServiceRegion = ReturnPromiseType<typeof ServiceRegion>;

interface IProps {
  /** 返回数据层级 */
  level?: Ref<number> | number;
  /** ids */
  modelValue?: string[];
  /** placeholder */
  placeholder?: string;
  /** contentClass */
  contentClass?: string;
  /** 是否添加'全部'选项  */
  lastText?: boolean;
  /** lastId */
  lastId?: boolean;
  /** itemClass */
  itemClass?: string;
  /** 默认颜色 */
  defalutColor?: string;
  /** 默认字体大小 */
  defalutSize?: string;
  /** 是否添加父级 */
  isAll?: boolean;
  /** 是否禁用 */
  disabled?: boolean;
  /** iconName */
  iconName?: string;
  /** iconClass */
  iconClass?: string;
  /** iconSizee */
  iconSize?: string | number;
  /** 是否点击关闭 */
  popupclosed?: boolean;
  /** 查找初始层级 */
  rootId?: string;
  /** 是否包括自身，配合 rootId 使用 */
  deep?: boolean;
  /** 是否为使用新区域接口页面 */
  isNew?: boolean;
  /** 处理数据 */
  handleData?: (data: ResServiceRegion) => ResServiceRegion;
  /** 是否隐藏 */
  isHide?: boolean;
}

const emits = defineEmits<{
  (event: 'nodeclick', e?: RangeItem): void;
  (event: 'popupopened'): void;
  (e: 'update:modelValue', val: string[]): void;
  (e: 'change', val?: ChangeType, localArr?: ResServiceRegion): void;
  (e: 'popupclosed', val?: ChangeType, localArr?: ResServiceRegion): void;
}>();

const props = withDefaults(defineProps<IProps>(), {
  modelValue: () => [],
  level: 3,
  placeholder: '区域筛选',
  contentClass: '',
  lastId: false,
  lastText: false,
  itemClass: '',
  defalutColor: '#d5d5d5',
  defalutSize: '30rpx',
  isAll: true,
  disabled: false,
  iconName: 'i-ion-chevron-forward-outline',
  iconClass: 'text-black',
  iconSize: 16,
  rootId: undefined,
  deep: undefined,
  isNew: undefined,
  isHide: false,
});

const scCascaderRef = ref<InstanceType<typeof ScCascader>>();

const region = ref(props.modelValue);

const regionRange = ref<ReturnPromiseType<typeof ServiceRegion>>([]);

const regionText = ref<string>('');

function findRegionText() {
  let _regionRange = regionRange.value;
  let _regionText = '';
  let curLevel = 0;

  region.value.forEach((item) => {
    console.log('%c [item]-108', 'font-size:13px; background:#336699; color:#fff;', item);
    const res = _regionRange.find((r) => r.id === item);
    console.log('%c [res]-109', 'font-size:13px; background:#336699; color:#fff;', res);
    if (res?.level && curLevel >= res.level) return;
    curLevel = res?.level || 0;
    _regionText += res?.title || '';
    _regionRange = res?.children || [];
  });

  regionText.value = _regionText;
}

const level = computed(() => toRaw(props.level) as number);

onMounted(() => {
  getRegion();
});

watch(
  [() => props.modelValue, () => regionRange.value],
  () => {
    const length = props.modelValue.length;

    region.value =
      props.modelValue[length - 1] === '-1'
        ? props.modelValue.slice(0, length - 1)
        : props.modelValue;

    console.log('%c []-136', 'font-size:13px; background:#336699; color:#fff;', region.value);
    findRegionText();

    console.log('%c []-139', 'font-size:13px; background:#336699; color:#fff;', region.value);
  },
  {
    immediate: true,
    deep: true,
  },
);

watch([() => props.level, () => props.rootId, () => props.deep], () => {
  getRegion();
});

async function getRegion() {
  regionRange.value = await ServiceRegion({
    isAll: props.isAll,
    level: (level.value + 1) as ServiceRegionProps['level'],
    rootId: props.rootId,
    deep: props.deep,
    isNew: props.isNew,
  });

  if (props.handleData) {
    regionRange.value = props.handleData(regionRange.value);
  }
}

const onNodeclick = (e: RangeItem) => {
  emits('nodeclick', e);
};

const onPopupopened = () => {
  emits('popupopened');
};

const onPopupclosed = (e: ChangeType) => {
  let lastId = '';

  let _regionRange: typeof regionRange.value | null = regionRange.value;

  const localArr = e.value.map((item) => {
    const _data = _regionRange?.find((r) => r.id === item);
    _regionRange = _data?.children || null;
    return _data;
  }) as ResServiceRegion;

  const level = [...new Set(localArr.map((i) => i?.level))].length;

  lastId = region.value[level - 1];

  if (props.lastId) {
    emits('update:modelValue', [lastId]);
  } else {
    emits(
      'update:modelValue',
      region.value.filter((i) => i !== '-1'),
    );
  }

  regionText.value = e.text.slice(0, level).join('');
  emits('popupclosed', e, localArr);
};

function onRegionChange(e: ChangeType) {
  let lastId = '';

  let _regionRange: typeof regionRange.value | null = regionRange.value;

  const localArr = e.value.map((item) => {
    const _data = _regionRange?.find((r) => r.id === item);
    _regionRange = _data?.children || null;
    return _data;
  }) as ResServiceRegion;

  const level = [...new Set(localArr.map((i) => i?.level))].length;

  lastId = region.value[level - 1];

  if (props.lastId) {
    emits('update:modelValue', [lastId]);
  } else {
    emits(
      'update:modelValue',
      region.value.filter((i) => i !== '-1'),
    );
  }

  regionText.value = e.text.slice(0, level).join('');
  emits('change', e, localArr);
}

function show() {
  scCascaderRef.value?.show();
}

function hide() {
  scCascaderRef.value?.hide();
}

defineExpose({
  show,
  hide,
});
</script>
<!-- #ifdef MP-WEIXIN -->
<script lang="ts">
export default {
  options: { virtualHost: true, styleIsolation: 'shared' },
};
</script>
<!-- #endif -->
<template>
  <ScCascader
    ref="scCascaderRef"
    v-model="region"
    class="w-full h-full"
    title="区域筛选"
    range-value="id"
    range-key="title"
    :is-hide="isHide"
    :last-text="lastText"
    :defalut-color="defalutColor"
    :defalut-size="defalutSize"
    :range="regionRange"
    :content-class="contentClass"
    :item-class="itemClass"
    :disabled="disabled"
    :popupclosed="popupclosed"
    :placeholder="placeholder"
    @popupclosed="onPopupclosed"
    @popupopened="onPopupopened"
    @nodeclick="onNodeclick"
    @change="onRegionChange">
    <slot :text="regionText">
      <view class="flex-1">{{ regionText }}</view>
    </slot>
    <template #placeholder>
      <slot name="placeholder">
        <view class="flex items-center">
          <text class="flex-1 text-[#d5d5d5]">{{ props.placeholder }}</text>
          <view class="px-[20rpx] flex items-center">
            <sc-icon
              v-if="iconName && !disabled"
              :name="iconName"
              :size="iconSize"
              :class-name="iconClass"
              :class="iconClass"></sc-icon>
          </view>
        </view>
      </slot>
    </template>
    <slot name="icon">
      <view class="px-[20rpx] flex items-center">
        <sc-icon
          v-if="iconName && !disabled"
          :name="iconName"
          :size="iconSize"
          :class-name="iconClass"
          :class="iconClass"></sc-icon>
      </view>
    </slot>
  </ScCascader>
</template>
