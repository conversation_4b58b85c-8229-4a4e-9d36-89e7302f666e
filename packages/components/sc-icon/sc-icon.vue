<script setup lang="ts">
import { computed } from 'vue';
import { replaceJs } from '@rms/utils';
import { addUnit } from '../utils';

interface IProps {
  /**
   * icon名称 [icon字体库](https://icones.js.org/)
   * prefix: i-
   * eg: icon名字为 ci:add-plus =>  i-ci-add-plus 或 i-ci:add-plus
   * 如果不生效，尝试更换其他图标
   * 如果在小程序组件里使用不生效(或者其他不生效的情况)，请写在安全域名里，入口在 子模块根目录 => safelist.ts
   */
  name: string;
  /** icon 大小 */
  size?: string | number;
  /** className */
  className?: string;
}

const props = withDefaults(defineProps<IProps>(), {
  size: '24',
  className: '',
});

const styles = computed(() => {
  return { fontSize: addUnit(props.size) };
});

const classs = computed(() => {
  return [props.name, replaceJs(props.className)];
});

const emit = defineEmits(['click']);

function handleItemClick() {
  emit('click');
}
</script>
<!-- #ifdef MP-WEIXIN -->
<script lang="ts">
export default {
  externalClasses: ['class'],
  options: { virtualHost: true },
};
</script>
<!-- #endif -->
<template>
  <text class="sc-icon class" :class="classs" :style="styles" @click="handleItemClick" />
</template>
