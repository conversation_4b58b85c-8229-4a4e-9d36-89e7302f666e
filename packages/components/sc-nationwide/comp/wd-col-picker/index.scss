@import 'wot-design-uni/components/common/abstracts/variable';
@import 'wot-design-uni/components/common/abstracts/mixin';

.wot-theme-dark {
  @include b(col-picker) {

    @include when(border) {
      .wd-col-picker__cell {
        @include halfPixelBorder('top', $-cell-padding, $-dark-border-color);
      }
    }

    @include e(label) {
      color: $-dark-color;
    }

    @include e(cell) {
      background-color: $-dark-background2;
      color: $-dark-color;

      @include when(disabled) {
        .wd-col-picker__value {
          color: $-dark-color3;
        }
      }
    }

    @include e(value) {
      color: $-dark-color;

      @include m(placeholder) {
        color: $-dark-color-gray;
      }
    }

    :deep(.wd-col-picker__arrow) {
      color: $-dark-color;
    }

    @include e(list) {
      color: $-dark-color;
    }

    @include e(selected) {
      color: $-dark-color;
    }
  }
}

@include b(col-picker) {
  @include when(border) {
    .wd-col-picker__cell {
      @include halfPixelBorder('top', $-cell-padding);
    }
  }

  @include e(cell) {
    position: relative;
    display: flex;
    padding: $-cell-wrapper-padding $-cell-padding;
    align-items: flex-start;
    background-color: $-color-white;
    text-decoration: none;
    color: $-cell-title-color;
    font-size: $-cell-title-fs;
    overflow: hidden;
    line-height: $-cell-line-height;
  }

  @include e(cell) {
    @include when(disabled) {
      .wd-col-picker__value {
        color: $-input-disabled-color;
      }
    }

    @include when(align-right) {
      .wd-col-picker__value {
        text-align: right;
      }
    }

    @include when(error) {
      .wd-col-picker__value {
        color: $-input-error-color;
      }

      :deep(.wd-col-picker__arrow) {
        color: $-input-error-color;
      }
    }

    @include when(large) {
      font-size: $-cell-title-fs-large;

      :deep(.wd-col-picker__arrow) {
        font-size: $-cell-icon-size-large;
      }
    }
  }

  @include e(error-message) {
    color: $-form-item-error-message-color;
    font-size: $-form-item-error-message-font-size;
    line-height: $-form-item-error-message-line-height;
    text-align: left;
    vertical-align: middle;
  }

  @include e(label) {
    position: relative;
    width: $-input-cell-label-width;
    margin-right: $-cell-padding;
    color: $-cell-title-color;
    box-sizing: border-box;

    @include when(required) {
      padding-left: 12px;

      &::after {
        position: absolute;
        left: 0;
        top: 2px;
        content: '*';
        font-size: $-cell-required-size;
        line-height: 1.1;
        color: $-cell-required-color;
      }
    }
  }

  @include e(value-wraper) {
    display: flex;
  }

  @include e(value) {
    flex: 1;
    margin-right: 10px;
    color: $-cell-value-color;

    @include when(ellipsis) {
      @include lineEllipsis;
    }

    @include m(placeholder) {
      color: $-input-placeholder-color;
    }
  }

  @include e(body) {
    flex: 1;
  }

  @include edeep(arrow) {
    display: block;
    font-size: $-cell-icon-size;
    color: $-cell-arrow-color;
    line-height: $-cell-line-height;
  }

  @include e(selected) {
    height: $-col-picker-selected-height;
    font-size: $-col-picker-selected-fs;
    color: $-col-picker-selected-color;
    overflow: hidden;
  }

  @include e(selected-container) {
    position: relative;
    display: flex;
    user-select: none;
  }

  @include e(selected-item) {
    flex: 0 0 auto;
    height: $-col-picker-selected-height;
    line-height: $-col-picker-selected-height;
    padding: $-col-picker-selected-padding;

    @include when(selected) {
      font-weight: $-col-picker-selected-fw;
    }
  }

  @include e(selected-line) {
    position: absolute;
    bottom: 5px;
    width: $-col-picker-line-width;
    left: 0;
    height: $-col-picker-line-height;
    background: $-col-picker-line-color;
    z-index: 1;
    border-radius: calc($-col-picker-line-height / 2);
    box-shadow: $-col-picker-line-box-shadow;
  }

  @include e(list-container) {
    position: relative;
  }

  @include e(list) {
    height: $-col-picker-list-height;
    padding-bottom: $-col-picker-list-padding-bottom;
    box-sizing: border-box;
    overflow: auto;
    color: $-col-picker-list-color;
    font-size: $-col-picker-list-fs;
    -webkit-overflow-scrolling: touch;
  }

  @include e(list-item) {
    display: flex;
    padding: $-col-picker-list-item-padding;
    align-items: flex-start;

    @include when(selected) {
      color: $-col-picker-list-color-checked;

      :deep(.wd-col-picker__checked) {
        opacity: 1;
      }
    }

    @include when(disabled) {
      color: $-col-picker-list-color-disabled;
    }
  }

  @include e(list-item-label) {
    line-height: 1.285;
  }

  @include e(list-item-tip) {
    margin-top: 2px;
    font-size: $-col-picker-list-fs-tip;
    color: $-col-picker-list-color-tip;
  }

  @include edeep(checked) {
    display: block;
    margin-left: 4px;
    font-size: $-col-picker-list-checked-icon-size;
    color: $-col-picker-list-color-checked;
    opacity: 0;
  }

  @include e(loading) {
    display: flex;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    align-items: center;
    justify-content: center;
  }
}
