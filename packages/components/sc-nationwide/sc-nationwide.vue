<script setup lang="ts">
import { ApiRegionNationwideList } from '@rms/api';
import type { ResRegionList } from '@rms/api/src/region';
import { WdColPickerProps } from './type';
import { isEqual, uniqBy } from 'lodash-es';
import { ref, onMounted, watch, nextTick } from 'vue';
import WdColPicker from './comp/wd-col-picker/wd-col-picker.vue';
import { ReturnPromiseType } from '@rms/types/utils';

type ApiRegionNationwideListRes = ReturnPromiseType<typeof ApiRegionNationwideList>['data'];

interface IProps {
  /** ids */
  modelValue?: string[];
  /** placeholder */
  placeholder?: string;
  placeholderClass?: string;
  /** 是否禁用 */
  disabled?: boolean;
  /** iconName */
  iconName?: string;
  /** iconClass */
  iconClass?: string;
  /** iconSizee */
  iconSize?: string | number;
  /** 查找初始层级 */
  rootId?: string;
  /** 层级 */
  deep?: number;
  /** WdColPicker组件 props */
  wdColPickerProps?: WdColPickerProps;
  /** 是否添加父级 */
  parent?: boolean[];
  /** 是否自动初始化 */
  autoInit?: boolean;
  /** 选项数据过滤 */
  columnFormat: (data: ApiRegionNationwideListRes, index: number) => ApiRegionNationwideListRes;
}

const emits = defineEmits<{
  (event: 'nodeclick', item: ResRegionList, index: number): void;
  (e: 'update:modelValue', val: string[]): void;
  (e: 'confirm', selectedItems: any[]): void;
  (e: 'close'): void;
}>();

const props = withDefaults(defineProps<IProps>(), {
  modelValue: () => [],
  placeholder: '区域筛选',
  disabled: false,
  iconName: 'i-ion-chevron-forward-outline',
  iconClass: 'text-black',
  iconSize: 16,
  rootId: undefined,
  deep: undefined,
  autoInit: true,
  columnFormat: (data: ApiRegionNationwideListRes, index: number) => data,
});

const value = ref<any[]>([]);

const area = ref<any[]>([]);

const wdColPickerRef = ref<InstanceType<typeof WdColPicker>>();

function columnChange(e: any) {
  const { selectedItem, resolve, finish, index } = e;

  emits('nodeclick', selectedItem, index);

  if (selectedItem.parent) {
    finish();
    return;
  }

  if (index + 1 === props.deep) {
    finish();
    return;
  }

  getReigon(selectedItem.value).then((data) => {
    if (data?.length) {
      if (props.parent?.[index]) {
        resolve(props.columnFormat([{ ...selectedItem, parent: true }, ...data], index + 1));
      } else {
        resolve(props.columnFormat(data, index + 1));
      }
    } else {
      finish();
    }
  });
}

const regionText = ref('');

watch(
  () => props.rootId,
  async () => {
    const data = await getReigon(props.rootId);
    area.value = [];
    area.value.push(props.columnFormat(data, 0));

    if (props.autoInit) {
      init();
    }
  },
  {
    deep: true,
    immediate: true,
  },
);

watch(
  () => props.modelValue,
  (val) => {
    if (isEqual(val, value.value)) return;

    const length = val?.length;
    value.value = val;

    if (length && area.value.length) {
      setTimeout(async () => {
        if (props.modelValue?.length && area.value.length) {
          const fn: (...args: any) => void = (v, i) => {
            return new Promise((resolve) => {
              const colIndex = wdColPickerRef.value?.selectList[i].findIndex(
                (item) => item.id === v,
              );
              wdColPickerRef.value?.chooseItem(i, colIndex, () => {
                resolve(true);
              });
            });
          };

          for (const [i, v] of props.modelValue.entries()) {
            await fn(v, i);
          }
        }
      }, 200);
    }
  },
  {
    immediate: true,
    deep: true,
  },
);

function handleConfirm(e: any) {
  const { selectedItems } = e;
  const res = uniqBy<any>(selectedItems, 'value');
  regionText.value = res.map((i) => i.title).join('');
  const value = res.map((i) => i.id);
  emits('update:modelValue', value);
  emits('confirm', selectedItems);
}

function handleClose() {
  emits('close');
}

async function getReigon(rootId?: string) {
  const { data } = await ApiRegionNationwideList({
    deep: 1,
    rootId,
  });

  return data?.map((r) => ({
    ...r,
    label: r.title,
    value: r.id,
  }));
}

// onMounted(async () => {
//   const data = await getReigon(props.rootId);
//   area.value.push(data);

// });

/** 需要手动回显 */
function init() {
  setTimeout(async () => {
    if (props.modelValue?.length && area.value.length) {
      const fn: (...args: any) => void = (v, i) => {
        return new Promise((resolve) => {
          const colIndex = wdColPickerRef.value?.selectList[i].findIndex((item) => item.id === v);
          wdColPickerRef.value?.chooseItem(i, colIndex, () => {
            resolve(true);
          });
        });
      };

      for (const [i, v] of props.modelValue.entries()) {
        await fn(v, i);
      }
    }
  }, 300);
}

function open() {
  wdColPickerRef.value?.open();
}

function close() {
  wdColPickerRef.value?.close();
}

defineExpose({
  init,
  open,
  close,
});
</script>

<template>
  <wd-col-picker
    ref="wdColPickerRef"
    v-model="value"
    :columns="area"
    :z-index="999999"
    :column-change="columnChange"
    use-default-slot
    :disabled="disabled"
    v-bind="wdColPickerProps"
    @close="handleClose"
    @confirm="handleConfirm">
    <slot :regionText="regionText">
      <view class="h-9 flex items-center w-full">
        <view class="flex-1 pl-[20rpx]">
          <slot :text="regionText" v-if="value.length">
            <view class="flex-1" :class="classNames">{{ regionText }}</view>
          </slot>

          <slot name="placeholder" v-else>
            <view class="flex items-center">
              <text :class="placeholderClass" class="flex-1 text-[#d5d5d5]">
                {{ props.placeholder }}
              </text>
            </view>
          </slot>
        </view>

        <slot name="icon">
          <view class="px-[20rpx] flex items-center" v-if="iconName && !disabled">
            <sc-icon
              :name="iconName"
              :size="iconSize"
              :class-name="iconClass"
              :class="iconClass"></sc-icon>
          </view>
        </slot>
      </view>
    </slot>
  </wd-col-picker>
</template>

<style lang="scss" scoped></style>
