<script setup lang="ts">
import { computed } from 'vue';
import { replaceJs } from '@rms/utils';

interface IFormProps {
  /** 标题 */
  title?: string;
  /** classNames */
  classNames?: string;
  /** titleClass */
  titleClass?: string;
  /** pointClass */
  pointClass?: string;
  /** 是否显示 point */
  point?: boolean;
  /** 是否显示 */
  show?: boolean;
}

const props = withDefaults(defineProps<IFormProps>(), {
  classNames: '',
  title: '',
  titleClass: '',
  pointClass: '',
  point: false,
  show: true,
});

const titleClassName = computed(() => {
  return replaceJs(`${props.titleClass}${props.point ? ' flex items-center' : ''}`);
});

const className = `box-border mb-2 p-3 bg-white ${props.classNames} class`;
</script>
<!-- #ifdef MP-WEIXIN -->
<script lang="ts">
export default {
  externalClasses: ['class'],
  options: { virtualHost: true },
};
</script>
<!-- #endif -->
<template>
  <view v-show="show" :class="className">
    <slot name="title">
      <view v-if="title" class="font-bold pb-3" :class="titleClassName">
        <view
          v-if="point"
          class="w-[16rpx] h-[36rpx] rounded-sm bg-[#3355FE] mr-2"
          :point="point"
          :class="replaceJs(pointClass)"></view>
        {{ title }}
      </view>
    </slot>
    <slot></slot>
  </view>
</template>

<style lang="scss" scoped></style>
