<script setup lang="ts">
import { Dayjs } from '@shencom/utils';
import { ref } from 'vue';

interface IChooseData {
  left?: number;
  right?: number;
  righjtList: typeof dataList.value[0]['children'];
  leftVal: string;
  rightVal: string;
  leftLabel: string;
  rightLabel: string;
}

const emits = defineEmits<{
  (e: 'change', left: string, right: string, date: string): void;
}>();

const selectRef = ref<UniApp.UI.Popup | null>(null);

const dataList = ref(createDate());

const chooseData = ref<IChooseData>({
  left: undefined,
  right: undefined,
  righjtList: [],
  leftVal: '',
  leftLabel: '',
  rightLabel: '',
  rightVal: '',
});

function open() {
  if (!selectRef.value) return;

  selectRef.value.open();
}

function handleChoose(
  type: 'left' | 'right',
  index: number,
  date?: Partial<typeof dataList.value[0]>,
) {
  if (type === 'left') {
    chooseData.value.right = undefined;
    chooseData.value.righjtList = date?.children || [];
    chooseData.value.leftVal = date?.value || '';
    chooseData.value.leftLabel = date?.label || '';
    chooseData.value.rightVal = '';
    chooseData.value.rightLabel = '';
  } else {
    chooseData.value.rightLabel = date?.label || '';
    chooseData.value.rightVal = date?.value || '';

    emits(
      'change',
      chooseData.value.leftVal,
      chooseData.value.rightVal,
      `${chooseData.value.leftLabel} ${chooseData.value.rightLabel}`,
    );

    if (!selectRef.value) return;
    selectRef.value.close();
  }

  chooseData.value[type] = index;
}

function createDate() {
  // 设置近7天的日期
  const dateInfo = [
    {
      label: `12:00-15:00 (下午)`,
      value: `12:00-15:00-1`,
    },
    {
      label: `16:00-18:00 (下午)`,
      value: `16:00-18:00-2`,
    },
  ];

  const nowDay = Dayjs().format('YYYY-MM-DD');
  const nowTime = Dayjs().format('HH:mm');
  return Array(7)
    .fill({})
    .map((_, index) => {
      let children: typeof dateInfo | null = null;
      const day = Dayjs().add(index, 'day').format('YYYY-MM-DD');
      if (day <= nowDay) {
        children = dateInfo.filter((res: any) => {
          const time = res.value.split('-');
          const now = Dayjs(nowDay + nowTime).unix(); // 当前时间时间戳
          const start = Dayjs(nowDay + time[0]).unix(); // 区间开始时间戳
          const end = Dayjs(nowDay + time[1]).unix(); // 区间结束时间戳
          return (now >= start && now <= end) || start >= now;
        });
      } else {
        children = dateInfo;
      }
      return {
        label: day,
        value: day,
        children,
      };
    });
}

defineExpose({ open });
</script>

<template>
  <uni-popup ref="selectRef" class="dateSelectPupop" type="bottom" :safe-area="false">
    <view class="h-[45vh] bg-white">
      <view class="py-3 text-center text-16 border-b border-all">选择预约时段</view>
      <view class="flex border-all pr-5 pl-[82rpx] py-2 h-[70rpx] leading-[40rpx]">
        <view v-show="chooseData.leftVal" class="mr-2">{{ chooseData.leftVal }}</view>
        <view v-show="chooseData.rightVal" class="mr-2">{{ chooseData.rightVal }}</view>
        <view v-show="!chooseData.leftVal || !chooseData.rightVal" class="text-red-400">
          请选择
        </view>
      </view>

      <view class="flex">
        <scroll-view scroll-y class="flex flex-col flex-1 h-[40vh]">
          <view
            v-for="(date, index) of dataList"
            :key="date.value"
            class="py-2 pr-2 mx-2 flex border-all"
            @click="handleChoose('left', index, date)">
            <text class="flex-1 text-center">{{ date.label }}</text>
            <text
              class="i-ci-check-big"
              :class="[chooseData.left === index ? 'text-red' : 'text-white']"></text>
          </view>
        </scroll-view>
        <view class="flex-1 bg-gray-100">
          <view
            v-for="(date, index) of chooseData.righjtList"
            :key="date.value"
            class="py-2 mx-2 flex border-all !border-gray-200"
            @click="handleChoose('right', index, date)">
            <text class="flex-1 text-center">{{ date.label }}</text>
            <text
              class="i-ci-check-big"
              :class="[chooseData.right === index ? 'text-red' : 'text-white']"></text>
          </view>
        </view>
      </view>
      <view></view>
    </view>
  </uni-popup>
</template>

<style lang="scss" scoped>
.dateSelectPupop {
  .border-all {
    @apply border-b border-gray-100 border-solid;
  }
}
</style>
