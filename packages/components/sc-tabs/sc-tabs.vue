<script setup lang="ts">
import { computed } from 'vue';
import { replaceJs, rpx2rem, rpx2rem2 } from '@rms/utils';

interface TabsEventProps {
  currentIndex: number;
}

interface IProps {
  /** 当前步骤 */
  current: number;
  /** 数据 */
  tabs: string[];
  /** 活跃颜色 */
  activeColor?: string;
  /** 样式类型 */
  styleType?: string;
  /** 字体大小 */
  fontSize?: number;
  /** 高度 */
  height?: number;
  /** tabClass */
  tabsClass?: string;
  /** 背景色 */
  background?: string;
  /** 是否 flex: none */
  noFlex?: boolean;
  /** 默认颜色 */
  normalColor?: string;
  /** padding */
  padding?: string;
  /** 活跃文案大小 */
  activeText?: number;
  /** 活跃文案 weight */
  activeWeight?: string;
}

const emits = defineEmits<{
  (event: 'click-item', currentIndex: number): void;
}>();

const props = withDefaults(defineProps<IProps>(), {
  activeColor: '#387ef5',
  styleType: 'text',
  fontSize: 30,
  height: 72,
  tabsClass: '',
  background: '#fff',
  normalColor: '#000',
  padding: '',
  activeText: 30,
  activeWeight: 'noraml',
  noFlex: false,
});

const compFontSize = computed(() => rpx2rem(props.fontSize));
const compHeight = computed(() => rpx2rem(props.height));
const compPadding = computed(() => rpx2rem2(props.padding));
const compActiveText = computed(() => rpx2rem(props.activeText));

const classNames = computed(() => {
  return replaceJs(props.tabsClass) + (props.noFlex ? 'isFlex' : '');
});

function handleClickItem(e: TabsEventProps) {
  const { currentIndex } = e;
  emits('click-item', currentIndex);
}
</script>
<!-- #ifdef MP-WEIXIN -->
<script lang="ts">
export default {
  options: { virtualHost: true, styleIsolation: 'shared' },
};
</script>
<!-- #endif -->

<template>
  <view class="sc-tabs class z-10" :class="classNames">
    <uni-segmented-control
      :current="current"
      :values="tabs"
      :style-type="styleType"
      :active-color="activeColor"
      @click-item="handleClickItem"></uni-segmented-control>
  </view>
</template>

<style lang="scss">
// #ifdef MP-WEIXIN
.sc-tabs {
  // #endif
  // #ifndef MP-WEIXIN
  ::v-deep(.sc-tabs) {
    // #endif
    .segmented-control--text {
      padding: v-bind(compPadding) !important;
      background: v-bind(background);
    }
    .segmented-control {
      height: v-bind(compHeight) !important;
    }

    .segmented-control__text {
      font-size: v-bind(compFontSize);
      border-bottom-color: v-bind(activeColor);
      color: v-bind(normalColor) !important;
      padding: v-bind(compPadding) !important;
    }

    .segmented-control__item--text,
    .control--segmented-control__item--text {
      font-size: v-bind(compActiveText);
      font-weight: v-bind(activeWeight);
      color: v-bind(activeColor) !important;
    }

    .isFlex {
      .segmented-control__item {
        flex: none;
        margin-right: 20rpx;
      }
    }
    // #ifdef MP-WEIXIN
  }
  // #endif
  // #ifndef MP-WEIXIN
}
// #endif
</style>
