<script setup lang="ts">
import { ref, computed, watch, nextTick, onMounted } from 'vue';
import { useList } from '@rms/components/sc-list/sc-list';
import { debounce } from '@rms/utils';
import { ScSearchCofing } from './sc-search';
import { isEqual } from 'lodash-es';

interface IProp {
  /** 配置项 */
  config: ScSearchCofing;
  /** 是否显示搜索框 */
  isShowSearch?: boolean;
  /** 搜索框内容 */
  inputValue?: string;
  handleParams?: (e: any) => any;
}

interface IEmit {
  (e: 'select', d: any): void;
  (e: 'change', flag: boolean): void;
  (e: 'open'): void;
  (e: 'close'): void;
}

const emits = defineEmits<IEmit>();

const props = withDefaults(defineProps<IProp>(), {
  isShowSearch: true,
});

const searchValue = ref('');

const isOpen = ref(false);

const popup = ref<{ open: () => void; close: () => void } | null>(null);

const searchPlaceholder = computed(() => props.config.placeholder || '请输入');

const keyword = computed(() => props.config.keyword || 'keywords');

const params = computed(() => {
  return {
    ...(props.config.params || {}),
    size: 20,
    [keyword.value]: searchValue.value,
  };
});

const { list, listProps, getData } = useList<any>({
  immediate: false,
  ...props.config,
  params,
});

const onSearch = () => {
  let p = params.value;
  if (props.handleParams) {
    p = props.handleParams(params.value);
  }
  getListData(true, p);
};

const debounceOnSearch = debounce(onSearch, 500);

watch(searchValue, () => {
  debounceOnSearch();
});

watch(
  () => props.inputValue,
  (val) => {
    searchValue.value = val || '';
  },
);

watch(
  () => props.config.params,
  () => {
    if (isOpen.value) debounceOnSearch();
  },
  {
    deep: true,
  },
);

const onSelect = (item: any) => {
  const data = props.config.handleChooseResult ? props.config.handleChooseResult(item) : item;

  emits('select', data);

  close();
};

const onChangeDrawer = (e: { show: boolean }) => {
  isOpen.value = e.show;
  emits('change', e.show);

  if (e.show) {
    emits('open');
  } else {
    emits('close');
  }
};

const open = () => {
  searchValue.value = props.inputValue || '';
  popup.value?.open();

  if (!searchValue.value) {
    getData(true, params.value);
  }
};

const close = () => {
  popup.value?.close();
};

async function handleUpper() {
  searchValue.value = '';
  getListData(true, params.value);
}

async function getListData(...args: Parameters<typeof getData>) {
  await nextTick();
  await getData(...args);
}

function getNameOrDesc(item: any, type: 'name' | 'desc') {
  const name = props.config.name || 'name';
  const desc = props.config.desc || 'desc';
  const prop = type === 'desc' ? desc : name;
  return item[prop];
}

onMounted(() => {
  if (!props.config) throw new Error('sc-search 缺失配置项');
});

defineExpose({ open, close, getData: getListData, list, listProps, searchValue });
</script>

<template>
  <view class="sc-search">
    <uni-popup ref="popup" type="right" @change="onChangeDrawer">
      <slot>
        <view class="bg-white w-72 flex flex-col h-screen">
          <slot name="top"></slot>
          <template v-if="isShowSearch">
            <slot name="search-bar">
              <uni-search-bar
                v-model="searchValue"
                type="text"
                confirm-type="search"
                :placeholder="searchPlaceholder"
                @confirm="onSearch" />
            </slot>
          </template>
          <slot name="center"></slot>
          <sc-list
            v-bind="listProps"
            local-refresh
            height-class="wrapper"
            class="wrapper"
            @touchmove.stop
            @on-handle-upper-fn="handleUpper">
            <view v-for="item of list" :key="item.id" @click="onSelect(item)">
              <slot name="item" :data="list" :item="item">
                <view class="py-2 border-b text-left">
                  <text v-if="getNameOrDesc(item, 'name')" class="text-14 w-full inline-block">
                    {{ getNameOrDesc(item, 'name') }}
                  </text>
                  <text v-if="getNameOrDesc(item, 'desc')" class="text-12 text-secondary">
                    {{ getNameOrDesc(item, 'desc') }}
                  </text>
                </view>
              </slot>
            </view>
          </sc-list>
          <slot name="bottom"></slot>
        </view>
      </slot>
    </uni-popup>
  </view>
</template>

<style lang="scss" scoped>
.sc-search {
  --window-top: 0;
}
.wrapper {
  height: calc(100vh - 112rpx);
}
.wrapper {
  padding: 0 20rpx;
}
</style>
