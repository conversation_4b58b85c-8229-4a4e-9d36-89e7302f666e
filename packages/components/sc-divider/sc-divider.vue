<script setup lang="ts">
import { computed } from 'vue';
import { replaceJs } from '@rms/utils';

/**
 * @params { text } 中部文本
 * @params { textClass } 中部文本类名
 * @params { className } 类名(主要用于自定义长度)
 * @params { colorClass } 颜色类名(主要用于自定义线条颜色，eg： !border-placeholder)
 * @params { isHalf } 线条粗细是否减半，默认为ture
 */
interface PropsType {
  /** 中间文案 */
  text?: string;
  /** classname */
  className?: string;
  /** textClass */
  textClass?: string;
  /** colorClass */
  colorClass?: string;
  /** 0.5px */
  isHalf?: boolean;
  /** widthClass */
  widthClass?: string;
}

const halfClass = computed(() => (props.isHalf ? ' scale-y-50' : ''));

const props = withDefaults(defineProps<PropsType>(), {
  text: '',
  textClass: '',
  className: '',
  colorClass: '',
  isHalf: true,
  widthClass: 'w-4/5',
});

const classNames = computed(() => {
  return replaceJs(['flex items-center', props.widthClass, props.className].join(' '));
});
</script>
<!-- #ifdef MP-WEIXIN -->
<script lang="ts">
export default {
  externalClasses: ['class'],
  options: { virtualHost: true },
};
</script>
<!-- #endif -->
<template>
  <view class="w-full flex justify-center items-center box-border class">
    <view :class="classNames">
      <view
        class="flex-1 border-b border-solid border-placeholder"
        :class="props.colorClass + halfClass"></view>
      <view :class="props.textClass">{{ props.text }}</view>
      <view
        class="flex-1 w-4/5 border-b border-solid border-placeholder"
        :class="props.colorClass + halfClass"></view>
    </view>
  </view>
</template>
