<script lang="ts" setup>
import { Unfurl } from '@rms/types/utils';
import { computed, ref, watch, onMounted } from 'vue';
import { Toast, replaceJs } from '@rms/utils';
import { Dictionary } from '@rms/types';

export interface Option extends Record<string, any> {
  text?: string;
  value?: string;
}

export interface RangeItem extends Option {
  children?: Option[];
}

export interface ChangeType {
  value: string[];
  items: RangeItem[];
  text: string[];
}

interface IProps {
  /** modelValue */
  modelValue?: string[];
  /** 数据 */
  range: Unfurl<RangeItem>[];
  /** uni-data-picker -> map => text */
  rangeKey?: string;
  /** uni-data-picker -> map => value */
  rangeValue?: string;
  /** 弹窗标题 */
  title?: string;
  /** 是否禁用 */
  disabled?: boolean;
  /** placeholder */
  placeholder?: string;
  /** 是否添加'全部'选项  */
  lastText?: boolean;
  /** class */
  itemClass?: string;
  /**  placeholder 颜色 */
  defalutColor?: string;
  /**  placeholder 大小 */
  defalutSize?: string;
  /** iconName */
  iconName?: string;
  /** iconClass */
  iconClass?: string;
  /** iconSize */
  iconSize?: string | number;
  /** 是否每个选项都获取值 */
  popupclosed?: boolean;
  /** 是否隐藏 */
  isHide?: boolean;
  /** 预加载数据 */
  preload?: boolean;
  /** 是否在叶子节点关闭弹窗，设置为 true 需要手动掉用 hide() 关闭 */
  isLesfClose?: boolean;
  /** 禁用选择历史 index */
  disabledIndex?: number;
}

interface IEmits {
  (event: 'nodeclick', e: RangeItem): void;
  (event: 'popupclosed', e: ChangeType): void;
  (event: 'popupopened'): void;
  (event: 'change', e: ChangeType): void;
  (event: 'update:modelValue', e: string[]): void;
}

const dataPickerRef = ref<any>();

const props = withDefaults(defineProps<IProps>(), {
  modelValue: () => [],
  rangeKey: 'text',
  rangeValue: 'value',
  title: '请选择',
  range: [] as any,
  placeholder: '请选择',
  disabled: false,
  lastText: false,
  itemClass: '',
  defalutColor: '#d5d5d5',
  defalutSize: '30rpx',
  iconName: 'i-ion-chevron-forward-outline',
  iconClass: 'text-gray-300',
  iconSize: 16,
  isHide: false,
  isLesfClose: false,
  disabledIndex: undefined,
  preload: false,
  popupclosed: false,
});

const defaultValue = ref<string[] | string>(['']);

const emits = defineEmits<IEmits>();

const items = ref<RangeItem[]>([]);

const isShow = ref(true);

watch(
  props,
  () => {
    if (props.range && props.range.length) {
      isShow.value = false;
      items.value = props.range;
      isShow.value = true;
    }
  },
  {
    immediate: true,
    deep: true,
  },
);

// const items = computed(() => {
//   console.log('props.range: ', props.range);
//   return props.range;
// });

const handleData = (data: any[]) => {
  if (data.length === 1) {
    return data;
  }
  if (data.length === 2) {
    return data[1].text === '全部' || props.lastText ? data.slice(0, 1) : data;
  }
  return data[2].text === '全部'
    ? props.lastText
      ? [data[1]]
      : data.slice(0, 2)
    : props.lastText
    ? [data[2]]
    : data;
};

watch(
  () => props.modelValue,
  (val) => {
    if (!val || (Array.isArray(val) && !val.length)) {
      defaultValue.value = [''];

      return;
    }
    defaultValue.value = val;
  },
  {
    immediate: true,
    deep: true,
  },
);

const onNodeclick = (e: RangeItem) => {
  emits('nodeclick', e);
};

const onPopupopened = () => {
  setTimeout(() => {
    const pickRef = dataPickerRef.value.$refs.pickerView;
    if (!pickRef) return;
    pickRef.handleSelect = (index: number) => {
      if (props.disabledIndex && index <= props.disabledIndex - 1) {
        Toast.warning('无法选择');
        return;
      }

      dataPickerRef.value.$refs.pickerView.selectedIndex = index;
    };
  });
  emits('popupopened');
};

const onPopupclosed = (isClose?: number) => {
  if (!props.popupclosed || isClose === 1) return;

  dataPickerRef.value.isOpened = false;
  const dateSelected: any[] = dataPickerRef.value?.selected;
  const selected: any[] = dateSelected.filter((i: any) => i.value);
  const itemValue = selected?.map((i) => i.value || '') || [];
  const itemText = selected?.map((i) => i.text) || [];

  result.value = { value: itemValue, items: selected, text: itemText };

  emits('update:modelValue', itemValue);
  emits('popupclosed', result.value);
};

const classNames = computed(() => {
  return replaceJs(props.itemClass);
});

const result = ref<any>({});

const onChange = (e: UniApp.Event<{ value: Dictionary[] }>) => {
  const { value } = e.detail;
  const itemValue = value.map((i) => i.value);
  const itemText = value.map((i) => i.text);
  result.value = { value: itemValue, items: value, text: itemText };

  emits('update:modelValue', itemValue);
  emits('change', result.value);
};

const show = () => {
  dataPickerRef.value.show();
};

const hide = () => {
  dataPickerRef.value.hide(true);
};

// 选择叶子节点禁止关闭弹窗
const reset = () => {
  dataPickerRef.value.hide = (target = false) => {
    dataPickerRef.value.isOpened = !target;
    dataPickerRef.value.$emit('popupclosed', 1);
  };

  dataPickerRef.value.handleClose = () => {
    dataPickerRef.value.isOpened = false;
    dataPickerRef.value.$emit('popupclosed');
  };

  dataPickerRef.value.onPropsChange = () => {
    dataPickerRef.value._treeData = [];
    dataPickerRef.value.selectedIndex = props.modelValue.length;
    dataPickerRef.value.load();
  };
};

// 动态加载节点
function init() {
  setTimeout(() => {
    if (!dataPickerRef.value || !dataPickerRef.value.$refs.pickerView) return;
    dataPickerRef.value.$refs.pickerView.updateData({
      treeData: dataPickerRef.value._treeData,
      selected: dataPickerRef.value.selected,
      selectedIndex: props.modelValue.length - 1,
    });
  }, 200);

  if (!dataPickerRef.value.$refs.pickerView) return;

  dataPickerRef.value.$refs.pickerView.onPropsChange = () => {
    dataPickerRef.value.$refs.pickerView._treeData = [];
    dataPickerRef.value.$refs.pickerView.selectedIndex = props.modelValue.length - 1;
    dataPickerRef.value.$refs.pickerView.$nextTick(() => {
      dataPickerRef.value.$refs.pickerView.loadData();
    });
  };
}

onMounted(() => {
  if (props.isLesfClose) {
    reset();
  }
});

defineExpose({ show, hide, init });
</script>
<!-- #ifdef MP-WEIXIN -->
<script lang="ts">
export default {
  options: { virtualHost: true, styleIsolation: 'shared' },
};
</script>
<!-- #endif -->
<template>
  <uni-data-picker
    v-if="isShow"
    v-slot="{ data, error }"
    ref="dataPickerRef"
    v-model="defaultValue"
    :class="{ isEmpty: !defaultValue.length, 'w-full h-full': !isHide }"
    :localdata="items"
    :map="{ text: rangeKey, value: rangeValue }"
    :clear-icon="true"
    :readonly="disabled"
    :popup-title="title"
    :disabled="disabled"
    :placeholder="props.placeholder"
    :preload="preload"
    @change="onChange"
    @popupclosed="onPopupclosed"
    @popupopened="onPopupopened"
    @nodeclick="onNodeclick">
    <view v-if="isHide"></view>
    <view v-else class="h-9 flex items-center w-full">
      <view v-if="error" class="error flex-1">
        <text>{{ error }}</text>
      </view>
      <view v-else-if="data.length" class="h-full flex items-center flex-1" :class="classNames">
        <slot>
          <template v-for="(item, index) in handleData(data)" :key="index">
            {{ item.text }}
          </template>
        </slot>
      </view>
      <view v-else class="flex-1">
        <slot name="placeholder">
          <text class="text-14 font-normal text-[#d5d5d5]">{{ placeholder }}</text>
        </slot>
      </view>

      <!-- <slot name="icon">
        <sc-icon
          v-if="iconName"
          :name="iconName"
          :size="iconSize"
          :class-name="iconClass"
          :class="iconClass"></sc-icon>
      </slot> -->
    </view>
  </uni-data-picker>
</template>

<style lang="scss" scoped>
.isEmpty {
  ::v-deep .uni-data-tree-input {
    @apply font-normal text-placeholder;
    font-size: v-bind(defalutSize);
    color: v-bind(defalutColor);
  }
}
</style>
