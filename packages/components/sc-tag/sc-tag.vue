<script setup lang="ts">
interface IProps {
  /** type */
  type?: string;
  /** 颜色 */
  color?: string;
  /** 背景色 */
  background?: string;
  /** 边框颜色 */
  border?: string;
  /** padding */
  padding?: string;
  /** 内容 */
  text: string;
  /** uni-tag inverted */
  inverted?: boolean;
  /** uni-tag circle */
  circle?: boolean;
  /** 是否禁用 */
  disabled?: boolean;
  /**  uni-tag customStyle */
  customStyle?: string;
  /**  size */
  size?: 'normal' | 'small';
  /**  className */
  className?: string;
  /**  fontSize */
  fontSize?: string;
}

withDefaults(defineProps<IProps>(), {
  type: 'primary',
  color: '',
  background: '',
  padding: '',
  border: '',
  text: '',
  inverted: false,
  circle: false,
  disabled: false,
  size: 'normal',
  customStyle: '',
  className: '',
  fontSize: '12px',
});
</script>

<!-- #ifdef MP-WEIXIN -->
<script lang="ts">
export default {
  options: { virtualHost: true, styleIsolation: 'shared' },
};
</script>
<!-- #endif -->

<template>
  <uni-tag
    :class="{
      'sc-tag_color': color,
      'sc-tag_background': background,
      'sc-tag_border': border,
      'sc-tag_padding': padding,
      'sc-tag_fontSize': fontSize,
      [className]: className,
    }"
    :text="text"
    :type="type"
    :custom-style="customStyle"
    :inverted="inverted"
    :circle="circle"
    :disabled="disabled"
    :size="size"></uni-tag>
</template>

<style lang="scss">
.sc-tag {
  &_color {
    ::v-deep .uni-tag {
      color: v-bind(color);
    }
  }

  &_background {
    .uni-tag {
      background: v-bind(background);
    }
  }

  &_border {
    ::v-deep .uni-tag {
      border-color: v-bind(border);
    }
  }

  &_padding {
    ::v-deep .uni-tag {
      padding: v-bind(padding);
    }
  }

  &_fontSize {
    ::v-deep .uni-tag {
      font-size: v-bind(fontSize);
      line-height: v-bind(fontSize);
    }
  }
}
</style>
