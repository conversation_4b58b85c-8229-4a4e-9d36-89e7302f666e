export interface RuleItemProps {
  // 是否必填，配置此参数不会显示输入框左边的必填星号，如需要，请配置uni-forms-item组件的的required为true
  required?: boolean;
  // 校验失败提示信息语，可添加属性占位符，当前表格内属性都可用作占位符
  errorMessage?: string;
  // 自定义校验规则
  validateFunction?: (
    rule: RuleItemProps,
    value: any,
    data: any,
    callback: (msg: string) => void,
  ) => boolean;
  // 校验数据最大长度
  maxLength?: number;
  // 校验最小值(小于)
  minimum?: number;
  // 校验最大值(大于)
  maximum?: number;
  // 正则表达式，注意事项见 uni-ui 文档
  // 小程序中，json 中不能使用正则对象，如：/^\S+?@\S+?\.\S+?$/，使用正则对象会被微信序列化，导致正则失效。
  // 所以建议统一使用字符串的方式来使用正则 ，如'^\\S+?@\\S+?\\.\\S+?$' ，需要注意 \ 需要使用 \\ 来转译。
  pattern?: string;
  // 内置校验规则，如这些规则无法满足需求，可以使用正则匹配或者自定义规则
  format?: 'string' | 'number' | 'boolean' | 'array' | 'object' | 'url' | 'email';
  // 数组至少要有一个元素，且数组内的每一个元素都是唯一的。
  range?: any[];
}

export interface RulesProps {
  name: string;
  rules: RuleItemProps[];
}
