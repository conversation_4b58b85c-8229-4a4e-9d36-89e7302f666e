<script setup lang="ts">
import { computed, onMounted, ref, watch, nextTick } from 'vue';
import { isEqual } from 'lodash-es';
import { onReady } from '@dcloudio/uni-app';
import { ScOssBase, Toast, replaceJs } from '@rms/utils';
// import { ScMessage, ScSearch, ScUpload } from '@rms/types';
import { handleRules } from './utils';
import { Dictionary } from '@rms/types';
// import { EtagsProps } from '../sc-upload/file';

export interface RuleItemProps {
  /** 是否必填，配置此参数不会显示输入框左边的必填星号，如需要，请配置uni-forms-item组件的的required为true */
  required?: boolean;
  /** 校验失败提示信息语，可添加属性占位符，当前表格内属性都可用作占位符 */
  errorMessage?: string;
  /** 自定义校验规则 */
  validateFunction?: (
    rule: RuleItemProps,
    value: any,
    data: any,
    callback: (msg: string) => void,
  ) => boolean;
  /** 校验数据最大长度 */
  maxLength?: number;
  /** 校验最小值(小于) */
  minimum?: number;
  /** 校验最大值(大于) */
  maximum?: number;
  /* 正则表达式，注意事项见 uni-ui 文档
   * 小程序中，json 中不能使用正则对象，如：/^\S+?@\S+?\.\S+?$/，使用正则对象会被微信序列化，导致正则失效。
   * 所以建议统一使用字符串的方式来使用正则 ，如'^\\S+?@\\S+?\\.\\S+?$' ，需要注意 \ 需要使用 \\ 来转译。
   */
  pattern?: string;
  /** 内置校验规则，如这些规则无法满足需求，可以使用正则匹配或者自定义规则 */
  format?: 'string' | 'number' | 'boolean' | 'array' | 'object' | 'url' | 'email';
  /** 数组至少要有一个元素，且数组内的每一个元素都是唯一的。 */
  range?: any[];
}

export interface RulesProps {
  /** name */
  name: string;
  /** 规则 */
  rules: RuleItemProps[];
}

export interface DataType {
  /** props */
  prop: string;
  /** label */
  label: string;
  /** 是否隐藏 */
  isHide?: boolean | Ref<boolean>;
  /** 顺序 */
  order?: number;
  /** item 属性 */
  itemAttrs?: {
    /** label(标签)的宽度，单位px */
    labelWidth?: string | number;
    /** label(标签)的文字的位置 */
    labelPosition?: 'top' | 'left';
    /** itemClass */
    itemClass?: string;
    /** label(标签) 右边显示红色"*"号，样式显示不会对校验规则产生效果 */
    required?: boolean;
    /** label(标签)的文字对齐方式 */
    labelAlign?: 'left' | 'center' | 'right';
  } & Dictionary;
  tag: {
    /** 组件所需数据 */
    options?: any[];
    /**
     * 组件类型
     * 'components' => 保留 label, 使用插槽替换组件位置，插槽名称对应 prop 字段;
     * 'custom' => 使用插槽替换整 item 下面的内容，插槽名称对应 prop 字段;
     */
    tagType:
      | 'text'
      | 'picker'
      | 'number'
      | 'textarea'
      | 'select'
      | 'component'
      | 'date-picker'
      | 'upload'
      | 'data-checkbox'
      | 'region'
      | 'search-select'
      | 'custom'
      | 'component'
      | 'nationwide'
      | 'rate';
    attr?: {
      /** iconName */
      iconName?: string;
      /** iconClass */
      iconClass?: string;
      /** iconSize */
      iconSize?: string | number;
    } & Dictionary;
  };
  listeners?: Record<string, (...args: any) => any> & {
    click?: (...args: any) => void;
    change?: (...args: any) => void;
    checkRepect?: (...args: any) => void;
  };
}

export interface FormConfig<T = AnyObject> {
  config: {
    /** form */
    form: T;
    /** 校验规则 */
    rules: RulesProps[];
    /** 页面是否 custom，小程序页面为 custom 时设置 */
    isCustom?: boolean;
    /** name, uni-form-item 上面的 name, 默认去 prop */
    name?: string;
    /** 配置项 */
    data: DataType[];
    /** uni-form 的属性 */
    formAttrs?: {
      /** 表单校验时机, blur 仅在 uni-easyinput 中生效 */
      validateTrigger?: 'bind' | 'submit' | 'blur';
      /** label 位置 */
      labelPosition?: 'top' | 'left';
      /** label 宽度，单位 px */
      labelWidth?: string | number;
      /** label 居中方式 */
      labelAlign?: 'left' | 'center' | 'right';
      /** 是否显示分格线 */
      border?: boolean;
      /** 字体大小 */
      fontSize?: string;
      /** label 颜色 */
      labelColor?: string;
      /** input 内容颜色 */
      contentColor?: string;
      /** itemClass */
      itemClass?: string;
    };
  };
}

type ScMessage = any;
type ScSearch = any;
type ScUpload = any;

const _HandlerToLowerCase = (val: string): string => val.replace(/([A-Z])/g, '-$1').toLowerCase();

function handleAttrs(attrs: FormConfig['config']['formAttrs']) {
  return Object.entries(attrs || {}).reduce((pre, [key, value]) => {
    const labels = ['labelPosition', 'labelWidth', 'labelAlign'];
    const formatKey = labels.includes(key) ? _HandlerToLowerCase(key) : key;
    pre[formatKey] = value;
    return pre;
  }, Object.create({}));
}

const props = withDefaults(defineProps<FormConfig>(), {});

const formRef = ref<{
  validate: () => void;
  setRules: (rules: any) => void;
  setValue: (key: string, value: any) => void;
} | null>(null);

const scSearchRef = ref<[InstanceType<ScSearch>] | null>(null);

const form = ref<any>({});

const fontSize = computed(() => props.config.formAttrs?.fontSize || '30rpx');
const labelColor = computed(() => props.config.formAttrs?.labelColor || '#000');
const contentColor = computed(() => props.config.formAttrs?.contentColor || '#000');

const startRef: Record<string, InstanceType<ScUpload>> = {};

function getScUploadRef(el: any) {
  if (el && el.name) {
    startRef[el.name] = el;
  }
}

async function checkRepectFiles(config: { origin?: string; prop: string; checkLocal?: boolean }) {
  const { prop, origin, checkLocal = true } = config;
  const target = props.config.data.find((item) => item.prop === prop);

  if (!target) {
    throw new Error('请检查 prop 是否正确');
  }

  const repectFiles = await startRef[prop].checkRepectFiles(origin, checkLocal);

  if (target && target.listeners && target.listeners.checkRepect) {
    target.listeners.checkRepect(prop, repectFiles);
  }

  return {
    prop,
    repectFiles,
  };
}

watch(
  () => props.config.form,
  (newValue) => {
    if (isEqual(form.value, newValue)) return;

    form.value = newValue;
  },
  {
    immediate: true,
    deep: true,
  },
);

watch(
  form,
  (newValue) => {
    const _config = props.config;

    // 修复 formData 不更新问题
    Object.keys(newValue as Record<string, any>).forEach((i) => {
      formRef.value?.setValue(i, newValue[i]);
    });

    if (isEqual(props.config.form, newValue)) return;

    _config.form = newValue;
  },
  {
    deep: true,
    immediate: true,
  },
);

watch(
  () => props.config.data,
  () => {
    setRules();
  },
  {
    deep: true,
  },
);

const message = ref<InstanceType<ScMessage> | null>(null);

function showError(errorMessage: string) {
  message.value.open(errorMessage, 'error');
}

function handleValidateError(error: { key: string; errorMessage: string }[]) {
  let index = 0;
  props.config.rules.some((item) => {
    index = error.findIndex((e) => e.key === item.name);
    return index !== -1;
  });
  const { errorMessage } = index !== -1 ? error[index] : { errorMessage: 'error' };

  if (message.value) {
    showError(errorMessage);
  } else {
    Toast.error(errorMessage);
  }
}

function isRequired(prop: DataType) {
  return !!prop?.tag?.attr?.required;
}

function handleScSearchOpen() {
  if (scSearchRef.value) {
    scSearchRef.value[0].open();
  }
}

function num2px(num: number | string) {
  if (typeof num === 'number') {
    return `${num}px`;
  }
  return num;
}

function localLabelWidth(prop: DataType) {
  if (prop.itemAttrs?.labelPosition === 'top') return '100%';

  const labelWidth = prop.itemAttrs?.labelWidth || props.config.formAttrs?.labelWidth || 65;
  const countWidth = prop.tag.attr?.maxCount ? `${prop.tag.attr?.maxCount}`.length * 2 * 14 : 0;
  return num2px(+labelWidth + countWidth);
}

function handleEmitEvent(e: any, prop: DataType, type = 'click') {
  const fn = prop.listeners && prop.listeners[type];

  if (fn && typeof fn === 'function') {
    fn(prop.prop, form.value[prop.prop], e);
  }
}

async function validate() {
  if (!formRef.value) return false;

  try {
    await formRef.value.validate();
    return true;
  } catch (error) {
    if (Array.isArray(error)) {
      handleValidateError(error);
    }
    return false;
  }
}

function setRules() {
  const rules = handleRules(props.config.rules);
  // 第一次设置不生效，需要重新设置
  formRef.value?.setRules(rules);

  nextTick(() => {
    formRef.value?.setRules(rules);
  });
}

const scNationwideRef = ref();

function geNationwideRef() {
  return scNationwideRef;
}

// #ifdef MP-WEIXIN
onReady(() => {
  setRules();
});
// #endif
// #ifndef MP-WEIXIN
onMounted(() => {
  setRules();
});
// #endif

defineExpose({ validate, setRules, checkRepectFiles, showError, geNationwideRef, startRef });
</script>
<!-- #ifdef MP-WEIXIN -->
<script lang="ts">
export default {
  options: { virtualHost: true, styleIsolation: 'shared' },
};
</script>
<!-- #endif -->
<template>
  <view class="sc-form">
    <uni-forms ref="formRef" v-bind="handleAttrs(config.formAttrs)" :model="form">
      <view class="flex flex-col">
        <view
          v-for="item of config.data"
          v-show="!item.isHide"
          :key="item.prop"
          :style="{ order: item.order || 0 }"
          :class="{
            'sc-form-item-left': item.itemAttrs?.['labelPosition'] === 'left',
            'sc-form-item-top': item.itemAttrs?.['labelPosition'] === 'top',
            'requesth-hide': !isRequired(item),
            [config.formAttrs?.itemClass || '']: true,
            [item.tag?.attr?.class || '']: true,
            [item.prop]: true,
          }">
          <uni-forms-item
            v-if="item.tag.tagType === 'text'"
            :key="item.prop"
            v-bind="handleAttrs(item.itemAttrs)"
            :required="true"
            :name="!item.isHide ? item.prop : ''"
            :label="item.label">
            <sc-input
              v-bind="item.tag.attr"
              v-model="form[item.prop]"
              :is-hide="item.isHide"
              :form="form"
              :prop="item.prop"
              @blur="handleEmitEvent($event, item, 'blur')"
              @change="handleEmitEvent($event, item, 'change')"
              @click="handleEmitEvent($event, item)"></sc-input>
          </uni-forms-item>

          <uni-forms-item
            v-if="item.tag.tagType === 'textarea'"
            :key="item.prop"
            v-bind="item.itemAttrs"
            :required="true"
            :name="!item.isHide ? item.prop : ''"
            :label="item.label">
            <sc-textarea
              class="w-full"
              v-bind="item.tag.attr"
              :key="item.prop"
              v-model="form[item.prop]"
              :is-hide="item.isHide"
              :form="form"
              :prop="item.prop"
              @change="handleEmitEvent($event, item, 'change')"
              @click="handleEmitEvent($event, item)"></sc-textarea>
          </uni-forms-item>

          <uni-forms-item
            v-if="item.tag.tagType === 'data-checkbox'"
            :key="item.prop"
            v-bind="item.itemAttrs"
            :required="true"
            :name="!item.isHide ? item.prop : ''"
            :label="item.label">
            <sc-data-checkbox
              v-bind="item.tag.attr"
              v-model="form[item.prop]"
              :is-hide="item.isHide"
              :range="item.tag.options"
              :form="form"
              :prop="item.prop"
              @change="handleEmitEvent($event, item, 'change')"></sc-data-checkbox>
          </uni-forms-item>

          <uni-forms-item
            v-if="item.tag.tagType === 'region'"
            :key="item.prop"
            v-bind="item.itemAttrs"
            :required="true"
            :name="!item.isHide ? item.prop : ''"
            :label="item.label">
            <sc-region
              v-bind="item.tag.attr"
              v-model="form[item.prop]"
              :form="form"
              :prop="item.prop"
              icon-name="i-ion-chevron-forward-outline"
              @change="handleEmitEvent($event, item, 'change')"
              @click="handleEmitEvent($event, item)"></sc-region>
          </uni-forms-item>

          <!-- #ifdef H5 -->
          <uni-forms-item
            v-if="item.tag.tagType === 'nationwide'"
            :key="item.prop"
            v-bind="item.itemAttrs"
            :required="true"
            :name="!item.isHide ? item.prop : ''"
            :label="item.label">
            <sc-nationwide
              ref="scNationwideRef"
              v-bind="item.tag.attr"
              v-model="form[item.prop]"
              :form="form"
              :prop="item.prop"
              @click="handleEmitEvent($event, item)"
              @nodeclick="handleEmitEvent($event, item, 'nodeclick')"
              @confirm="handleEmitEvent($event, item, 'confirm')"></sc-nationwide>
          </uni-forms-item>
          <!-- #endif -->

          <uni-forms-item
            v-if="item.tag.tagType === 'search-select'"
            :key="item.prop"
            v-bind="item.itemAttrs"
            :required="true"
            :name="!item.isHide ? item.prop : ''"
            :label="item.label">
            <sc-input
              v-bind="item.tag.attr"
              class="w-full pointer-events-none"
              disabled
              :placeholder="item.tag.attr?.placeholder"
              :form="form"
              :prop="item.prop"
              @click="handleScSearchOpen"></sc-input>
            <sc-search
              ref="scSearchRef"
              :key="item.prop"
              v-bind="item.tag.attr"
              v-model="form[item.prop]"
              :form="form"
              :prop="item.prop"></sc-search>
          </uni-forms-item>

          <uni-forms-item
            v-if="item.tag.tagType === 'upload'"
            :key="item.prop"
            v-bind="item.itemAttrs"
            :required="true"
            :name="!item.isHide ? item.prop : ''"
            :label="item.label">
            <template #label>
              <view
                :style="{ width: localLabelWidth(item) }"
                class="flex justify-between uni-forms-item__label">
                <view :class="replaceJs(item.tag.attr?.placeholderTop ? `flex flex-col` : 'flex')">
                  <view class="flex">
                    <view v-if="isRequired(item)" class="font-bold text-red mr-1">*</view>
                    <view class="whitespace-nowrap">{{ item.label }}</view>
                    <text
                      v-if="item.tag.attr?.maxCount"
                      :class="item.tag.attr?.maxCountClass || ''"
                      class="px-0.5 text-[#80858e] text-14 sc-upload__count-class">
                      ({{
                        Array.isArray(form[item.prop]) ? form[item.prop].filter(Boolean).length : 0
                      }}/{{ item.tag.attr?.maxCount }})
                    </text>
                  </view>
                  <view
                    v-if="item.tag.attr?.placeholder"
                    class="sc-upload__placeholder-class font-normal scale-[0.8] whitespace-nowrap text-gray-300 flex-1"
                    :class="replaceJs(item.tag.attr?.placeholderClass || '')">
                    {{ item.tag.attr?.placeholder }}
                  </view>
                </view>
              </view>
            </template>
            <view class="mt-2">
              <sc-upload
                v-bind="item.tag.attr"
                :ref="getScUploadRef"
                v-model="form[item.prop]"
                :name="item.prop"
                :form="form"
                :prop="item.prop"
                @success="handleEmitEvent($event, item, 'success')"
                @oversize="handleEmitEvent($event, item, 'oversize')"></sc-upload>
            </view>
          </uni-forms-item>

          <uni-forms-item
            v-if="item.tag.tagType === 'select'"
            :key="item.prop"
            v-bind="item.itemAttrs"
            :required="true"
            :name="!item.isHide ? item.prop : ''"
            :label="item.label">
            <sc-select
              v-bind="item.tag.attr"
              v-model="form[item.prop]"
              :is-hide="item.isHide"
              :form="form"
              :prop="item.prop"
              @click="handleEmitEvent($event, item)"></sc-select>
          </uni-forms-item>

          <uni-forms-item
            v-if="item.tag.tagType === 'date-picker'"
            :key="item.prop"
            v-bind="item.itemAttrs"
            :required="true"
            :name="!item.isHide ? item.prop : ''"
            :label="item.label">
            <sc-date-picker
              v-bind="item.tag.attr"
              v-model="form[item.prop]"
              :is-hide="item.isHide"
              :form="form"
              :prop="item.prop"
              @change="handleEmitEvent($event, item, 'change')"
              @click="handleEmitEvent($event, item)"></sc-date-picker>
          </uni-forms-item>

          <uni-forms-item
            v-if="item.tag.tagType === 'picker'"
            :key="item.prop"
            v-bind="item.itemAttrs"
            :required="true"
            :name="!item.isHide ? item.prop : ''"
            :label="item.label">
            <sc-picker
              v-bind="item.tag.attr"
              v-model="form[item.prop]"
              :is-hide="item.isHide"
              :ranges="item.tag.options"
              :form="form"
              :prop="item.prop"
              @change="handleEmitEvent($event, item, 'change')"
              @confirm="handleEmitEvent($event, item, 'confirm')"
              @cancel="handleEmitEvent($event, item, 'cancel')"
              @click="handleEmitEvent($event, item)"></sc-picker>
          </uni-forms-item>

          <template v-if="item.tag.tagType === 'component'">
            <uni-forms-item
              :key="item.prop"
              v-bind="item.itemAttrs"
              :required="true"
              :name="!item.isHide ? item.prop : ''"
              :label="item.label">
              <slot :name="item.prop"></slot>
            </uni-forms-item>
          </template>

          <template v-if="item.tag.tagType === 'custom'">
            <!-- #ifdef H5 -->
            <slot :name="item.prop" v-bind="{ ...item, form, value: form[item.prop] }"></slot>
            <!-- #endif -->
            <!-- #ifndef H5 -->
            <slot :name="item.prop"></slot>
            <!-- #endif -->
          </template>
        </view>

        <slot></slot>
      </view>
    </uni-forms>
    <sc-message key="sc-message" ref="message" :is-custom="config.isCustom"></sc-message>
  </view>
</template>

<style lang="scss">
.sc-form {
  @apply w-full;
  --window-top: 0;

  ::v-deep .uni-forms-item__error {
    display: none;
  }
  ::v-deep .uni-data-tree {
    @apply h-full;
    font-size: v-bind('fontSize') !important;

    .uni-data-tree-input {
      @apply h-full w-full flex items-center justify-start pl-[20rpx];
      & > view {
        @apply w-full;
      }
    }
  }

  ::v-deep .uni-easyinput__placeholder-class {
    @apply font-normal;
    font-size: v-bind('fontSize') !important;
    color: #d5d5d5;
  }

  ::v-deep .uni-forms-item__content {
    @apply flex items-center;
  }

  ::v-deep .uni-data-checklist {
    @apply pl-[20rpx];
  }

  ::v-deep .sc-upload__count-class span {
    width: auto !important;
  }

  .sc-upload__placeholder-class {
    font-size: v-bind('fontSize') !important;
    transform-origin: center left;
  }

  ::v-deep .uni-input-placeholder {
    @apply font-normal;
    font-size: v-bind('fontSize') !important;
    color: #d5d5d5;
  }

  .sc-form-item-left {
    ::v-deep .uni-forms-item {
      @apply flex-row;
      .uni-forms-item__label {
        padding-bottom: 0;
      }
    }
  }

  .sc-form-item-top {
    ::v-deep .uni-forms-item {
      @apply flex-col;
    }
  }

  ::v-deep .uni-forms-item__label {
    font-size: v-bind('fontSize') !important;
    color: v-bind('labelColor') !important;

    .label-text {
      @apply text-black;
      font-size: v-bind('fontSize') !important;
    }
  }

  ::v-deep .uni-input-input {
    font-size: v-bind('fontSize') !important;
    color: v-bind('contentColor') !important;
  }

  ::v-deep .uni-easyinput__content-textarea {
    font-size: v-bind('fontSize') !important;
  }

  ::v-deep .uni-forms-item__label {
    align-items: center;
    height: 72rpx;
    padding: 0 24rpx 0 0;
  }

  ::v-deep .uni-easyinput {
    .uni-input-placeholder,
    .uni-easyinput__placeholder-class,
    input {
      @apply font-normal text-placeholder;
      font-size: v-bind('fontSize') !important;
      color: #d5d5d5;
    }
    input {
      color: #000;
    }
  }

  ::v-deep .radio__inner-icon {
    width: 18rpx !important;
    height: 18rpx !important;
  }
  ::v-deep .radio__inner {
    width: 36rpx !important;
    height: 36rpx !important;
  }
  ::v-deep .checklist-box {
    margin-bottom: 30rpx !important;
    margin-right: 100rpx !important;
  }
  ::v-deep .checklist-text {
    font-size: 30rpx !important;
  }

  ::v-deep .uni-forms-item__content {
    & > view {
      width: 100%;
    }
  }

  .requesth-hide {
    :deep(.is-required) {
      visibility: hidden;
    }
  }
}
</style>
