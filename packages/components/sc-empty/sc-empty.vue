<script setup lang="ts">
import { computed, onMounted, ref } from 'vue';
import Register from '@rms/register';

interface PropsType {
  /** 底部文案 */
  text?: string;
  /** 是否显示 */
  show?: boolean;
  /** logo url */
  imgUrl?: string;
  /** logoClass */
  iconClass?: string;
  /** textClass */
  textClass?: string;
}

const props = withDefaults(defineProps<PropsType>(), {
  show: true,
  text: '暂无数据',
  imgUrl: '',
});

const register = ref<InstanceType<typeof Register> | null>(null);

const defalutImg = computed(() => {
  if (!register.value) return '';

  return `${register.value.RmsUtilsOss.imgPath}/noData.png`;
});

onMounted(() => {
  register.value = new Register();
});
</script>
<!-- #ifdef MP-WEIXIN -->
<script lang="ts">
export default {
  options: { virtualHost: true, styleIsolation: 'shared' },
};
</script>
<!-- #endif -->
<template>
  <view
    v-if="props.show"
    class="sc-empty w-full h-full flex flex-col items-center justify-center class">
    <slot>
      <slot name="img">
        <image
          class="w-36"
          :class="iconClass"
          mode="widthFix"
          :src="props.imgUrl || defalutImg"
          alt="" />
      </slot>
      <slot name="text">
        <text :class="textClass" class="text-gray-300 text-18 pt-2">{{ props.text }}</text>
      </slot>
    </slot>
  </view>
</template>

<style lang="scss" scoped></style>
