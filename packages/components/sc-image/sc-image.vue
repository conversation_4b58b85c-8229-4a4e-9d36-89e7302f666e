<script setup lang="ts">
import { Dictionary } from '@rms/types';
import { ref } from 'vue';

type ImageMode =
  | 'scaleToFill'
  | 'aspectFit'
  | 'aspectFill'
  | 'widthFix'
  | 'widthFix'
  | 'heightFix'
  | 'top'
  | 'bottom'
  | 'center'
  | 'left'
  | 'right'
  | 'top left'
  | 'top right'
  | 'bottom left'
  | 'bottom right';

interface IProps {
  /** 图片资源地址 */
  src?: string;
  /** 图片裁剪、缩放的模式 */
  mode?: ImageMode;
  'lazy-load'?: boolean;
  /** 在系统不支持webp的情况下是否单独启用webp。默认false，只支持网络资源。webp支持详见下面说明。微信小程序2.9.0 */
  webp?: boolean;
  /** 开启长按图片显示识别小程序码菜单。微信小程序2.7.0 */
  'show-menu-by-longpress'?: boolean;
  /** 是否能拖动图片。H5 3.1.1+ */
  draggable?: boolean;
  /** errorIcon */
  errorIcon?: string;
  /** sc-icon props */
  errorIconProp?: Dictionary;
}
const props = withDefaults(defineProps<IProps>(), {
  draggable: true,
  errorIcon: 'i-mdi-image-remove',
});
const isError = ref(false);

function onImageError() {
  isError.value = true;
}
</script>

<template>
  <sc-icon
    v-if="isError"
    v-bind="errorIconProp"
    class="text-gray-500 scale-75"
    name="i-mdi-image-remove"></sc-icon>
  <image v-else v-bind="props" @error="onImageError"></image>
</template>

<style lang="scss" scoped></style>
