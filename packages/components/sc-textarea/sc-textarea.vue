<script setup lang="ts">
import { computed, watch } from 'vue';

interface IFormProps {
  /** form */
  form?: any;
  /** prop */
  prop?: string;
  /** placeholder */
  placeholder?: string;
  /** 是否显示最大长度数字 */
  showLength?: boolean;
  /** 最大长度 */
  maxlength?: number | string;
  /** 边框 */
  inputBorder?: boolean;
  /** 是否禁用 */
  disabled?: boolean;
  /** 是否显示清除按钮 */
  clearable?: boolean;
  /** 是否隐藏 */
  isHide?: boolean;
  /** className */
  className?: string;
  /** 样式 */
  styles?: {
    borderColor?: string;
    disableColor?: string;
    color?: string;
  };
  modelValue?: string;
}

const emits = defineEmits<{
  (e: 'change', val: string): void;
  (e: 'update:modelValue', val: string): void;
}>();

const props = withDefaults(defineProps<IFormProps>(), {
  placeholder: '',
  showLength: false,
  maxlength: 200,
  inputBorder: false,
  clearable: false,
  prop: '',
  form: {},
  disabled: false,
  isHide: false,
  className: '',
  modelValue: '',
});

const classNames = computed(() => {
  return `${props.prop + props.className} w-full mt-2`;
});

const attrs = computed(() => {
  const _localProps = { ...props } as Partial<typeof props>;
  delete _localProps.form;
  delete _localProps.prop;
  delete _localProps.isHide;

  return { ..._localProps };
});

// watch(
//   () => props.modelValue,
//   (val) => {
//     props.form[props.prop] = val;
//   },
// );

function handleValueChange(val: string) {
  emits('change', val);
  emits('update:modelValue', val);
}
</script>

<template>
  <view v-show="!isHide" class="sc-textarea w-full box-border">
    <uni-easyinput
      v-bind="attrs"
      type="textarea"
      :class="classNames"
      :value="form[prop]"
      @input="handleValueChange" />
    <view v-if="showLength" class="absolute text-placeholder bottom-2 right-2">
      {{ form[prop]?.length || 0 }}/{{ maxlength }}
    </view>
  </view>
</template>

<style lang="scss" scoped>
.sc-textarea ::v-deep .uni-easyinput__content-textarea {
  padding: 2rpx 20rpx 22rpx;
}
</style>
