<script setup lang="ts">
import { computed, isRef, nextTick, Ref, ref, watch } from 'vue';
import { FormatDate } from '@shencom/utils';
import { replaceJs } from '@rms/utils';

interface IFormProps {
  /** form */
  form?: any;
  /** prop */
  prop?: string;
  /** 是否禁用 */
  disabled?: boolean;
  /** placeholder */
  placeholder?: string;
  /** 是否隐藏 */
  isHide?: boolean;
  /** 数据对应的字段 */
  rangeMap?: [string, string, string] | [string, string];
  /** itemHeight */
  itemHeight?: string;
  /** 数据 */
  ranges?: any[] | Ref<any[]>;
  /** 确认按钮颜色 */
  confirmColor?: string;
  /** 确认按钮文案 */
  confirmText?: string;
  /** 取消按钮颜色 */
  cancelColor?: string;
  /** 取消按钮文案 */
  cancelText?: string;
  /** modelValue */
  modelValue?: any;
  /** itemFontSize */
  itemFontSize?: string;
  /** 内容对齐方式 */
  align?: 'right' | 'left' | 'center';
  /** 类型 */
  type?: 'picker' | 'date';
  /** date 模式下设置最小年份 */
  startYear?: number;
  /** date 模式下设置最大年份 */
  endYear?: number;
  /** 是否显示边框 */
  border?: boolean;
  /** 是否点击遮罩层关闭 */
  closeOnClickMask?: boolean;
  /** 文案显示最大长度 */
  maxLength?: number;
  /** iconName */
  iconName?: string;
  /** iconClass */
  iconClass?: string;
  /** iconSizee */
  iconSize?: string | number;
}

const props = withDefaults(defineProps<IFormProps>(), {
  placeholder: '',
  form: {},
  prop: '',
  isHide: false,
  rangeMap: () => ['id', 'value', 'tip'],
  attr: undefined,
  disabled: false,
  value: 0,
  itemHeight: '80rpx',
  ranges: () => [],
  confirmColor: '#33cd5f',
  cancelColor: '#000',
  confirmText: '确定',
  cancelText: '取消',
  modelValue: '',
  itemFontSize: '40rpx',
  startYear: 1990,
  endYear: Number(FormatDate(new Date(), 'YYYY')),
  align: 'left',
  type: 'picker',
  border: false,
  closeOnClickMask: true,
  maxLength: 18,
  iconName: 'i-ion-chevron-forward-outline',
  iconClass: 'text-black',
  iconSize: 16,
});

const currentText = ref('');

const pickVal = ref<number[]>([0]);

const visible = ref(false);

const currentItem = ref<any>(null);

const lock = ref(false);

const emits = defineEmits<{
  (e: 'change', val: any, index?: number): void;
  (e: 'confirm', val: any, index?: number): void;
  (e: 'cancel', val: any): void;
  (e: 'update:modelValue', val: any): void;
}>();

const compRanges = computed(() => {
  pickVal.value = [0];
  const res = isRef(props.ranges) ? props.ranges.value : props.ranges;
  return res;
});

async function onChange(e: UniApp.Event) {
  pickVal.value = e.detail.value;
  if (props.type === 'date') {
    const [yIndex, mIndex] = e.detail.value;
    const year = dateObj.value.years[yIndex];
    const month = dateObj.value.months[mIndex];
    dateObj.value.year = year;
    dateObj.value.month = month;
    lock.value = false;
    return;
  }
  currentItem.value = compRanges.value && compRanges.value[e.detail.value[0]];

  emits('change', currentItem.value, e.detail.value[0]);
  lock.value = false;
}

function open() {
  visible.value = true;
  console.log(
    '%c [visible.value]-128',
    'font-size:13px; background:#336699; color:#fff;',
    visible.value,
  );
}

function close() {
  visible.value = false;
}

function handleMask() {
  if (props.closeOnClickMask) {
    onCancel();
  }
}

function onCancel() {
  emits('cancel', pickVal.value);
  close();
}

function handleOpenClick() {
  if (props.disabled) return;
  open();
}

async function onConfirm() {
  if (lock.value) return;

  if (props.type === 'date') {
    const { year, month } = dateObj.value;

    currentText.value = FormatDate(`${year}-${month}`, 'YYYY-MM');
    emits('update:modelValue', currentText.value);
    emits('confirm', currentText.value);
    close();
    return;
  }

  const resVal = compRanges.value[pickVal.value[0]];

  if (resVal.disabled) return;

  currentText.value = resVal[props.rangeMap[1]];

  emits('confirm', resVal, pickVal.value[0]);
  emits('update:modelValue', resVal[props.rangeMap[0]]);
  close();
}

const alignComp = computed(() => {
  if (props.align === 'left') {
    return replaceJs('justify-start');
  }

  if (props.align === 'right') {
    return replaceJs('justify-end');
  }
  return replaceJs('justify-center');
});

const ZIndexComp = computed(() => {
  return visible.value ? 99999999 : 1;
});

// 保证回显位置正确
const isInit = ref(false);

const dateObj = ref<{
  date: Date;
  years: string[];
  months: string[];
  year?: string;
  month?: string;
}>({
  date: new Date(),
  years: [],
  months: [],
  year: '',
  month: '',
});

// 处理回显数据
watch(
  () => props.modelValue,
  () => {
    isInit.value = false;
    init();
  },
  {
    immediate: true,
  },
);

watch(
  () => props.ranges,
  () => {
    isInit.value = false;
    init();
  },
  {
    deep: true,
  },
);

async function init() {
  if (isInit.value) {
    return;
  }

  const { modelValue, rangeMap } = props;

  if (props.type === 'date') {
    handleDateInit();
    isInit.value = true;
    return;
  }

  const index = compRanges.value
    ? compRanges.value.findIndex((item: any) => item[rangeMap[0]] === modelValue)
    : -1;
  if (index !== -1) {
    pickVal.value = [index];
    const resVal = compRanges.value[index];

    currentText.value = resVal[props.rangeMap[1]];
    currentItem.value = resVal;
  } else {
    currentText.value = '';
    pickVal.value = [0];
  }
  isInit.value = true;
}

function handleDateInit() {
  const { modelValue, startYear, endYear } = props;

  const year = FormatDate(modelValue || new Date(), 'YYYY');
  const month = FormatDate(modelValue || new Date(), 'MM');
  const pickArr = [];
  if (endYear < startYear) {
    throw Error('结束年份不能小于开始年份');
  }

  dateObj.value.years = [];
  dateObj.value.months = [];

  for (let i = startYear; i <= endYear; i++) {
    if (i === +year) {
      pickArr.push(i - startYear);
    }

    dateObj.value.years.push(`${i}`);
  }

  for (let i = 1; i <= 12; i++) {
    if (i === +month) {
      pickArr.push(i - 1);
    }
    dateObj.value.months.push(`${i}`);
  }

  dateObj.value.year = year;
  dateObj.value.month = month;
  pickVal.value = pickArr;

  if (modelValue) {
    currentText.value = FormatDate(`${year}-${month}`, 'YYYY-MM');
  }
}

function touchstart() {
  lock.value = true;
}

function touchend() {
  setTimeout(() => {
    lock.value = false;
  }, 800);
}

defineExpose({ open, close });
</script>

<!-- #ifdef MP-WEIXIN -->
<script lang="ts">
export default {
  options: { virtualHost: true, styleIsolation: 'shared' },
};
</script>
<!-- #endif -->

<template>
  <!-- #ifdef MP-WEIXIN -->
  <view class="sc-picker w-full">
    <view v-show="!isHide" class="h-9 flex items-center" @click="handleOpenClick">
      <!-- #ifdef MP-WEIXIN -->
      <slot>
        <!-- #endif -->
        <!-- #ifndef MP-WEIXIN -->
        <slot :current-text="currentText" :placeholder="placeholder">
          <!-- #endif -->
          <view
            class="w-full flex pl-[20rpx] items-center"
            :class="[prop, border ? replaceJs('border border-[#e5e5e5] rounded-lg') : '']">
            <view
              class="flex-1 flex items-center"
              :class="{ 'text-[#D5D5D5]': disabled, [alignComp]: true }">
              <text v-if="currentText" class="uni-input-input">{{ currentText }}</text>
              <text v-else class="uni-easyinput__placeholder-class">{{ placeholder }}</text>
            </view>
            <slot prop="icon" name="icon">
              <view class="px-[20rpx] flex items-center">
                <sc-icon
                  v-if="iconName && !disabled"
                  :name="iconName"
                  :size="iconSize"
                  :class-name="iconClass"
                  :class="iconClass"></sc-icon>
              </view>
            </slot>
          </view>
          <!-- #ifndef MP-WEIXIN -->
        </slot>
        <!-- #endif -->
        <!-- #ifdef MP-WEIXIN -->
      </slot>
      <!-- #endif -->
    </view>

    <view
      class="mask"
      :class="{ visible: visible }"
      catchtouchmove="true"
      @click="handleMask"
      @touchmove.stop.prevent></view>
    <view class="sc-picker-cnt" :class="{ visible: visible }">
      <view
        v-if="type === 'picker' && currentItem && currentItem[rangeMap[1]]?.length > maxLength"
        class="text-center text-16 p-2">
        {{ currentItem[rangeMap[1]] }}
      </view>
      <view class="sc-picker-header" catchtouchmove="true" @touchmove.stop.prevent>
        <text :style="{ color: cancelColor }" @click.stop.prevent="onCancel">
          {{ cancelText }}
        </text>
        <view v-if="type === 'date'" class="text-16">
          {{ dateObj.year }}年{{ dateObj.month }}月
        </view>
        <text
          :style="{ color: currentItem?.disabled || lock ? '#D5D5D5' : confirmColor }"
          @click.stop.prevent="onConfirm">
          {{ confirmText }}
        </text>
      </view>
      <view v-if="type === 'picker'" class="sc-picker-wrapper">
        <view class="sc-picker-view" @touchstart="touchstart" @touchend="touchend">
          <picker-view
            v-if="isInit"
            class="d-picker-view"
            :indicator-style="itemHeight"
            immediate-change
            :value="pickVal"
            @change="onChange">
            <picker-view-column>
              <view
                v-for="(item, index) in compRanges"
                :key="index"
                class="sc-picker-item"
                :class="{ 'text-[#D5D5D5]': item.disabled }">
                {{ item[rangeMap[1]] || placeholder }}{{ rangeMap[2] ? item[rangeMap[2]] : '' }}
              </view>
            </picker-view-column>
          </picker-view>
        </view>
      </view>

      <view v-if="type === 'date'" class="sc-picker-wrapper">
        <view class="sc-picker-view" @touchstart="touchstart" @touchend="touchend">
          <picker-view
            v-if="isInit"
            class="d-picker-view"
            :indicator-style="itemHeight"
            immediate-change
            :value="pickVal"
            @change="onChange">
            <picker-view-column>
              <view v-for="(item, index) in dateObj.years" :key="index" class="sc-picker-item">
                {{ item }}年
              </view>
            </picker-view-column>
            <picker-view-column>
              <view v-for="(item, index) in dateObj.months" :key="index" class="sc-picker-item">
                {{ item }}月
              </view>
            </picker-view-column>
          </picker-view>
        </view>
      </view>
    </view>
  </view>
  <!-- #endif -->

  <!-- 传送 -->
  <!-- #ifndef MP-WEIXIN -->
  <view class="sc-picker w-full">
    <view v-show="!isHide" class="h-9 flex items-center" @click="handleOpenClick">
      <!-- #ifdef MP-WEIXIN -->
      <slot>
        <!-- #endif -->
        <!-- #ifndef MP-WEIXIN -->
        <slot :current-text="currentText" :placeholder="placeholder">
          <!-- #endif -->
          <view
            class="w-full flex pl-[20rpx] items-center"
            :class="[prop, border ? replaceJs('border border-[#e5e5e5] rounded-lg') : '']">
            <view
              class="flex-1 flex items-center"
              :class="{ 'text-[#D5D5D5]': disabled, [alignComp]: true }">
              <text v-if="currentText" class="uni-input-input">{{ currentText }}</text>
              <text v-else class="uni-easyinput__placeholder-class">{{ placeholder }}</text>
            </view>
            <slot prop="icon" name="icon">
              <view class="px-[20rpx] flex items-center">
                <sc-icon
                  v-if="iconName && !disabled"
                  :name="iconName"
                  :size="iconSize"
                  :class-name="iconClass"
                  :class="iconClass"></sc-icon>
              </view>
            </slot>
          </view>
          <!-- #ifndef MP-WEIXIN -->
        </slot>
        <!-- #endif -->
        <!-- #ifdef MP-WEIXIN -->
      </slot>
      <!-- #endif -->
    </view>

    <teleport to="body">
      <view class="sc-picker w-full">
        <view
          class="mask"
          :class="{ visible: visible }"
          catchtouchmove="true"
          @click="handleMask"
          @touchmove.stop.prevent></view>
        <view class="sc-picker-cnt" :class="{ visible: visible }">
          <view
            v-if="type === 'picker' && currentItem && currentItem[rangeMap[1]]?.length > maxLength"
            class="text-center text-16 p-2">
            {{ currentItem[rangeMap[1]] }}
          </view>
          <view class="sc-picker-header" catchtouchmove="true" @touchmove.stop.prevent>
            <text :style="{ color: cancelColor }" @click.stop.prevent="onCancel">
              {{ cancelText }}
            </text>
            <view v-if="type === 'date'" class="text-16">
              {{ dateObj.year }}年{{ dateObj.month }}月
            </view>
            <text
              :style="{ color: currentItem?.disabled || lock ? '#D5D5D5' : confirmColor }"
              @click.stop.prevent="onConfirm">
              {{ confirmText }}
            </text>
          </view>
          <view v-if="type === 'picker'" class="sc-picker-wrapper">
            <view class="sc-picker-view" @touchstart="touchstart" @touchend="touchend">
              <picker-view
                v-if="isInit"
                class="d-picker-view"
                :indicator-style="itemHeight"
                immediate-change
                :value="pickVal"
                @change="onChange">
                <picker-view-column>
                  <view
                    v-for="(item, index) in compRanges"
                    :key="index"
                    class="sc-picker-item"
                    :class="{ 'text-[#D5D5D5]': item.disabled }">
                    {{ item[rangeMap[1]] || placeholder }}{{ rangeMap[2] ? item[rangeMap[2]] : '' }}
                  </view>
                </picker-view-column>
              </picker-view>
            </view>
          </view>

          <view v-if="type === 'date'" class="sc-picker-wrapper">
            <view class="sc-picker-view" @touchstart="touchstart" @touchend="touchend">
              <picker-view
                v-if="isInit"
                class="d-picker-view"
                :indicator-style="itemHeight"
                immediate-change
                :value="pickVal"
                @change="onChange">
                <picker-view-column>
                  <view v-for="(item, index) in dateObj.years" :key="index" class="sc-picker-item">
                    {{ item }}年
                  </view>
                </picker-view-column>
                <picker-view-column>
                  <view v-for="(item, index) in dateObj.months" :key="index" class="sc-picker-item">
                    {{ item }}月
                  </view>
                </picker-view-column>
              </picker-view>
            </view>
          </view>
        </view>
      </view>
    </teleport>
  </view>
  <!-- #endif -->
</template>

<style lang="scss">
.sc-picker {
  z-index: v-bind(ZIndexComp);
  .mask {
    position: fixed;
    z-index: v-bind(ZIndexComp);
    top: 0;
    right: 0;
    left: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.6);
    visibility: hidden;
    opacity: 0;
    transition: all 0.3s ease;
  }
  .mask.visible {
    visibility: visible;
    opacity: 1;
  }
  .sc-picker-cnt {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    transition: all 0.3s ease;
    transform: translateY(100%);
    z-index: v-bind(ZIndexComp);
    background-color: #fff;
  }
  .sc-picker-cnt.visible {
    transform: translateY(0);
  }
  .sc-picker-header {
    display: flex;
    align-items: center;
    padding: 0 30rpx;
    height: 88rpx;
    background-color: #fff;
    position: relative;
    text-align: center;
    font-size: 32rpx;
    justify-content: space-between;
    border-bottom: solid 1px #eee;
    .sc-picker-btn {
      font-size: 30rpx;
    }
  }

  .sc-picker-hd:after {
    content: ' ';
    position: absolute;
    left: 0;
    bottom: 0;
    right: 0;
    height: 1px;
    border-bottom: 1px solid #e5e5e5;
    color: #e5e5e5;
    transform-origin: 0 100%;
    transform: scaleY(0.5);
  }
}

.sc-picker-flex2 {
  flex: 2;
}
.sc-picker-flex1 {
  flex: 1;
}
.sc-picker-view {
  width: 100%;
  height: 476rpx;
  overflow: hidden;
  background-color: rgba(255, 255, 255, 1);
  z-index: 666;
}
.d-picker-view {
  height: 100%;
}

.sc-picker-item {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-size: v-bind(itemFontSize);
}
</style>
