<script setup lang="ts">
import { computed } from 'vue';

interface IFormProps {
  /** form */
  form?: any;
  /** prop */
  prop?: string;
  /** placeholder */
  placeholder?: string;
  /** 是否隐藏 */
  isHide?: boolean;
  /** iconName */
  iconName?: string;
  /** iconClass */
  iconClass?: string;
  /** iconSize */
  iconSize?: string | number;
  /** 是否禁用 */
  disabled?: boolean;
}
const emits = defineEmits<{
  (e: 'click', ...args: any): void;
}>();

function handlClick(e: any) {
  if (props.disabled) return;

  emits('click', e);
}

const props = withDefaults(defineProps<IFormProps>(), {
  placeholder: '',
  isHide: false,
  iconName: 'i-ion-chevron-forward-outline',
  iconClass: 'text-black',
  iconSize: 16,
  disabled: false,
});
</script>
<!-- #ifdef MP-WEIXIN -->
<script lang="ts">
export default {
  options: { virtualHost: true, styleIsolation: 'shared' },
};
</script>
<!-- #endif -->
<template>
  <view v-show="!isHide" class="h-9 flex items-center w-full" :class="prop" @click="handlClick">
    <view class="h-full flex-1 flex items-center pl-[20rpx]">
      <text v-if="form[prop || '']" class="text-black text-15">{{ form[prop || ''] }}</text>
      <text v-else class="uni-easyinput__placeholder-class">{{ placeholder }}</text>
    </view>
    <slot name="icon">
      <view class="px-[20rpx]" data-name="icon">
        <sc-icon
          v-if="iconName && !disabled"
          :name="iconName"
          :size="iconSize"
          :class-name="iconClass"
          :class="iconClass"></sc-icon>
      </view>
    </slot>
  </view>
</template>

<style lang="scss" scoped></style>
