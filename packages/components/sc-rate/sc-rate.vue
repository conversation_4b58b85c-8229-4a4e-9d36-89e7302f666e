<script setup lang="ts">
interface IFormProps {
  /** form */
  form?: any;
  /** props */
  props?: string;
  /** 数据 */
  range?: any;
  /** 是否隐藏 */
  isHide?: boolean;
  /** attr */
  attr?: Record<string, any>;
}

const emits = defineEmits<{
  (e: 'change', val: number): void;
  (e: 'update:modelValue', val: number): void;
}>();

withDefaults(defineProps<IFormProps>(), {
  range: [],
  form: {},
  props: '',
  isHide: false,
  attr: undefined,
});

function handleValueChange(e: { value: number }) {
  emits('update:modelValue', e.value);
  emits('change', e.value);
}
</script>

<template>
  <uni-rate
    v-show="!isHide"
    :value="form[props]"
    v-bind="attr"
    @change="handleValueChange"></uni-rate>
</template>

<style lang="scss" scoped></style>
