<script setup lang="ts">
import { Init<PERSON>ap, exceptionHand<PERSON>, getLocationAdapter } from '@rms/utils';
import { LngLat } from '@rms/types';
import { ApiAMapGeocodeRegeo, ApiAMapInputtips } from '@shencom/api';
import { nextTick, shallowRef, ref } from 'vue';
import Register from '@rms/register';

const props = defineProps<{
  /** 高德服务端key */
  geoServiceKey: string;
  /** 高德key */
  geoKey: string;
  /** 初始化位置 */
  initLocation?: LngLat;
  /** 默认搜索关键字 */
  defaultSearch?: string;
}>();

const emits = defineEmits<{
  (e: 'success', checked: any): void;
  (e: 'close'): void;
}>();

const list = ref<any[]>([]);
const keyword = ref('');
const timeId = ref<ReturnType<typeof setTimeout> | null>(null);
const checked = ref<any>({
  locaiton: '',
});

const mapContext = shallowRef<AMap.Map | null>(null);

const marker = ref<AMap.Marker>();

function inputValue(e: string) {
  if (timeId.value) {
    clearTimeout(timeId.value);
  }

  timeId.value = setTimeout(() => {
    getPlaces(e);
    timeId.value = null;
  }, 600);
}

function cancel() {
  keyword.value = '';
  list.value = [];
  if (marker.value) {
    marker.value.setMap(null);
  }
  close();

  emits('close');
}

function toCancel() {
  cancel();
}

function confirm() {
  if (checked.value) {
    let address;
    if (checked.value.addressLocal) {
      address = checked.value.addressLocal;
    } else {
      address = `${checked.value.district}${
        checked.value.address ? `-${checked.value.address}` : ''
      }`;
    }

    const location = checked.value.location.split(',');

    emits('success', {
      address,
      latitude: location[1],
      longitude: location[0],
    });
    cancel();
  }
}

function toChecked(obj: any) {
  checked.value = obj;

  if (marker.value) {
    marker.value.setMap(null);
    const tempArr = obj.location.split(',');
    addMarker(tempArr[0], tempArr[1]);
  }
}

async function initMapWeb() {
  /** 监听点击 */
  mapContext.value?.on('click', (e) => {
    if (marker.value) {
      marker.value.setMap(null);
    }
    addMarker(e.lnglat.lng, e.lnglat.lat);
    getNowPlace(`${e.lnglat.lng},${e.lnglat.lat}`);
  });

  /** 初始化默认值 */
  if (props.initLocation) {
    const { lat, lng } = props.initLocation;
    addMarker(lng, lat);
    getNowPlace(`${lng},${lat}`);
    return;
  }

  /** 如果没有默认值取当前定位 */
  // AMap.plugin('AMap.Geolocation', () => {
  //   const geolocation = new AMap.Geolocation({
  //     // 是否使用高精度定位，默认：true
  //     enableHighAccuracy: true,
  //     // 设置定位超时时间，默认：无穷大
  //     timeout: 10000,
  //     // 定位按钮的停靠位置的偏移量，默认：Pixel(10, 20)
  //     buttonOffset: new AMap.Pixel(10, 20),
  //     //  定位成功后调整地图视野范围使定位位置及精度范围视野内可见，默认：false
  //     zoomToAccuracy: true,
  //     //  定位按钮的排放位置,  RB表示右下
  //     buttonPosition: 'RB',
  //   });
  // });

  const [lng, lat] = await getLocationAdapter();

  addMarker(lng, lat);
  getNowPlace(`${lng},${lat}`);

  /** 获取当前位置 */
  async function getNowPlace(location: string) {
    const res = await ApiAMapGeocodeRegeo({
      key: props.geoServiceKey,
      location,
    });

    const { status, regeocode } = res;
    if (+status) {
      checked.value = {
        adcode: regeocode.addressComponent.adcode,
        city: regeocode.addressComponent.city,
        district: regeocode.addressComponent.district,
        location,
        addressLocal: regeocode.formatted_address,
      };

      const street = regeocode.formatted_address;

      getPlaces(keyword.value || street);
    } else {
      exceptionHandler(
        new Error(`高德逆解析失败: ${JSON.stringify({ key: props.geoServiceKey, ...res })}`),
      );
    }
  }
}

function addMarker(lng: number, lat: number) {
  const icon = new AMap.Icon({
    // 图标的取图地址
    image: '//a.amap.com/jsapi_demos/static/demo-center/icons/poi-marker-default.png',
    // 图标所用图片大小
    imageSize: new AMap.Size(32, 42),
  });

  marker.value = new AMap.Marker({
    icon,
    position: [lng, lat],
  });
  marker.value.setMap(mapContext.value);
  mapContext.value?.setCenter([lng, lat]);
}

/** 获取附件建筑地点 */
function getPlaces(keywords: string) {
  console.log('%c [ keywords ]-173', 'font-size:13px; background:pink; color:#bf2c9f;', keywords);
  const register = new Register();
  register.RmsHttp.get(
    `/_AMapServiceWeb/v3/assistant/inputtips?keywords=${keywords}&city=深圳`,
  ).then((result: any) => {
    if (result.status === '1') {
      list.value = result.tips.filter((item: any) => item.location && item.location.length > 0);
    }
  });
  // @ts-ignore
  if (data.status === '1') {
    // @ts-ignore
    list.value = data.tips.filter((item: any) => item.location && item.location.length > 0);
  }
}

const mapRef = ref();
const showPlace = ref(false);

function open() {
  mapRef.value?.open();
  showPlace.value = true;
  keyword.value = props.defaultSearch || '';
  nextTick(async () => {
    await InitMap({
      elm: 'map2',
      complete: async (map) => {
        mapContext.value = map;
      },
    });

    initMapWeb();
  });
}

function close() {
  showPlace.value = false;
  mapRef.value?.close();
}

defineExpose({ open, close });
</script>

<template>
  <!-- #ifdef MP-WEIXIN -->
  <uni-popup ref="mapRef">
    <view v-if="showPlace" class="w-screen h-screen bg-white">
      <view class="amap-choose">
        <view class="top-button">
          <sc-button type="red" size="small" @click="toCancel">取消</sc-button>
          <sc-button type="green" size="small" @click="confirm">完成</sc-button>
        </view>
        <view id="map2" class="map-box"></view>
        <view class="content">
          <view class="search">
            <view>
              <uni-easyinput
                v-model="keyword"
                :input-border="false"
                prefix-icon="search"
                placeholder="请输入"
                @input="inputValue"></uni-easyinput>
            </view>
            <text @tap="cancel">取消</text>
          </view>
          <view class="main">
            <view
              v-for="(item, index) in list"
              :key="index"
              @tap="
                toChecked({
                  ...item,
                  index,
                })
              ">
              <view>
                <view>{{ item.name }}</view>
                <text class="detail">
                  {{ item.district }}{{ item.address ? '-' + item.address : '' }}
                </text>
              </view>
              <view>
                <sc-icon
                  v-if="item.location === checked.location && checked.index === index"
                  class="text-green-500"
                  name="i-mdi:check-bold"></sc-icon>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </uni-popup>
  <!-- #endif -->
  <!-- #ifndef MP-WEIXIN -->
  <teleport to="body">
    <uni-popup ref="mapRef">
      <view v-if="showPlace" class="w-screen h-screen bg-white">
        <view class="amap-choose">
          <view class="top-button">
            <sc-button type="red" size="small" @click="toCancel">取消</sc-button>
            <sc-button type="green" size="small" @click="confirm">完成</sc-button>
          </view>
          <view id="map2" class="map-box"></view>
          <view class="content">
            <view class="search">
              <view>
                <uni-easyinput
                  v-model="keyword"
                  :input-border="false"
                  prefix-icon="search"
                  placeholder="请输入"
                  @input="inputValue"></uni-easyinput>
              </view>
              <text @tap="cancel">取消</text>
            </view>
            <view class="main">
              <view
                v-for="(item, index) in list"
                :key="index"
                @tap="
                  toChecked({
                    ...item,
                    index,
                  })
                ">
                <view>
                  <view>{{ item.name }}</view>
                  <text class="detail">
                    {{ item.district }}{{ item.address ? '-' + item.address : '' }}
                  </text>
                </view>
                <view>
                  <sc-icon
                    v-if="item.location === checked.location && checked.index === index"
                    class="text-green-500"
                    name="i-mdi:check-bold"></sc-icon>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </uni-popup>
  </teleport>
  <!-- #endif -->
</template>

<style lang="scss">
.amap-choose {
  position: fixed;
  top: 0;
  width: 100%;
  height: 100vh;
  z-index: 99999;

  ::v-deep .uni-easyinput__content {
    background: rgb(255, 255, 255, 0) !important;
  }
  .top-button {
    top: 0;
    box-sizing: border-box;
    padding: 24rpx;
    position: fixed;
    width: 100%;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    z-index: 100;
    .cancel {
      // color: white;
    }
    .confirm {
      // background-color: #ff6000;
      color: white;
      padding: 8rpx 14rpx;
      border-radius: 8rpx;
    }
  }

  .map-box {
    height: 60%;
    // background-color: pink;
    #map {
      width: 100%;
      height: 100%;
    }
    :v-deep .amap-logo {
      display: none !important;
    }
    :v-deep .amap-copyright {
      display: none !important;
    }
  }

  .content {
    position: fixed;
    bottom: 0;
    border-radius: 24rpx 24rpx 0 0;
    height: 42%;
    width: 100%;
    background: white;
    box-sizing: border-box;
    padding: 24rpx;
    .search {
      width: 100%;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
      & > view {
        width: 88%;
        height: 64rpx;
        background: #f4f4f4;
        border-radius: 14rpx;
        box-sizing: border-box;
        padding: 8rpx 24rpx;
        display: flex;
        flex-direction: row;
        align-items: center;
        image {
          width: 30rpx;
          height: 25rpx;
        }
        input {
          margin-left: 8rpx;
          flex: 1;
        }
      }
    }

    .main {
      margin-top: 20rpx;
      height: calc(100% - 88rpx);
      // background: pink;
      overflow-y: auto;
      & > view {
        box-sizing: border-box;
        padding: 20rpx;
        width: 100%;
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: space-between;
        .detail {
          color: #707070;
        }
      }
    }
  }
}
</style>
