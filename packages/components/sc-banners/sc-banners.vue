<script lang="ts" setup>
import { ref } from 'vue';
import { Banner } from '@rms/types';
import { ServiceGetBanner } from '@rms/service';
import { replaceJs, exceptionHandler } from '@rms/utils';
import Register from '@rms/register';

interface IProps {
  group: string;
  imageClass?: string;
  indicatorDots?: boolean;
  interval?: number;
  autoplay?: boolean;
  current?: number;
  className?: string;
}

interface IEmits {
  (e: 'click', banner: Banner, type?: 'authorize'): void;
}

const props = withDefaults(defineProps<IProps>(), {
  indicatorDots: true,
  imageClass: '',
  autoplay: true,
  interval: 5000,
  current: 0,
});

const emits = defineEmits<IEmits>();

const banners = ref<Banner[]>([]);

const init = async (isInit = false) => {
  await getBanner(isInit);
};

const getBanner = async (isInit = false) => {
  const data = await getBannerItem(isInit);
  banners.value = data;
};

const getBannerItem = async (isInit = false) => {
  try {
    if (!props.group) return [];

    const allData = (await ServiceGetBanner(props.group, isInit)) || {};

    return allData[props.group] || [];
  } catch (error) {
    exceptionHandler(error);
    return [];
  }
};

const onRouter = (item: Banner) => {
  if (!item.link && item.type !== 'event') return;
  const register = new Register();

  if (!register.RmsUserInfo.isLogin() && Boolean(+item.permit)) {
    emits('click', item, 'authorize');
    return;
  }

  emits('click', item);
};

defineExpose({ init });
</script>

<!-- #ifdef MP-WEIXIN -->
<script lang="ts">
export default {
  behaviors: ['wx://form-field-button'],
  externalClasses: ['class'],
  options: {
    virtualHost: true,
  },
};
</script>
<!-- #endif -->

<template>
  <swiper
    class="w-full class"
    :class="[replaceJs(className || ''), replaceJs(imageClass)]"
    :indicator-dots="indicatorDots"
    circular
    :autoplay="autoplay"
    :interval="interval"
    :current="current">
    <template v-for="item in banners" :key="item.id">
      <swiper-item>
        <image
          :src="item.remoteUrl"
          class="w-full h-full"
          :class="replaceJs(imageClass)"
          @click="onRouter(item)" />
      </swiper-item>
    </template>
  </swiper>
</template>
