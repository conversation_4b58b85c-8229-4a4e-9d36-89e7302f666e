<script setup lang="ts">
import { onMounted, ref } from 'vue';
import { getBarHeight } from '@rms/utils';

type MsgType = 'success' | 'error' | 'warn' | 'info';

const props = defineProps<{
  /** 页面是否 custom */
  isCustom?: boolean;
}>();

const msgType = ref<MsgType>('success');

const messageText = ref('');

const messageRef = ref<UniApp.UI.Popup | null>(null);

/** 触发组件 */
function open(message: string, type: MsgType = 'success') {
  if (!messageRef.value) return;

  if (messageRef.value.showPopup) {
    return;
  }

  msgType.value = type;
  messageText.value = message;
  messageRef.value.open();
}

const paddingTop = ref('0px');

async function handleCustomPage() {
  if (props.isCustom) {
    const { paddingTop: pt } = await getBarHeight();
    paddingTop.value = `${pt}px`;
  }
}
onMounted(() => {
  handleCustomPage();
});

defineExpose({ open });
</script>
<!-- #ifdef MP-WEIXIN -->
<script lang="ts">
export default {
  options: { virtualHost: true, styleIsolation: 'shared' },
};
</script>
<!-- #endif -->

<template>
  <!-- #ifdef MP-WEIXIN -->
  <uni-popup ref="messageRef" type="message">
    <uni-popup-message
      :type="msgType"
      class="sc-message"
      :message="messageText"
      :duration="2000"></uni-popup-message>
  </uni-popup>
  <!-- #endif -->
  <!-- #ifndef MP-WEIXIN -->
  <teleport to="body">
    <uni-popup ref="messageRef" type="message">
      <uni-popup-message
        :type="msgType"
        class="sc-message"
        :message="messageText"
        :duration="2000"></uni-popup-message>
    </uni-popup>
  </teleport>
  <!-- #endif -->
</template>

<style lang="scss">
::v-deep .uni-popup-message {
  padding-top: v-bind('paddingTop');
}
</style>
