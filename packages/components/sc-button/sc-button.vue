<script lang="ts" setup>
import { computed, toRefs } from 'vue';
import { replaceJs } from '@rms/utils';

const themeMap = {
  primary: 'primary',
  red: 'red',
  green: 'green',
  yellow: 'yellow',
  grey: 'grey',
  default: 'default',
} as const;

interface IProps {
  size?: 'large' | 'small' | 'mini' | 'normal';
  scope?: string;
  openType?: string;
  formType?: string;
  type?: keyof typeof themeMap;
  disabled?: boolean;
  square?: boolean;
  plain?: boolean;
  loading?: boolean;
  round?: boolean;
  block?: boolean;
  icon?: string;
  iconPrefix?: string;
  className?: string;
  buttonId?: string;
}
interface IEmits {
  (event: 'click'): void;
  (event: 'getphonenumber', e: any): void;
  (event: 'opensetting', e: any): void;
  (event: 'getuserinfo', e: any): void;
  (event: 'getAuthorize', e: any): void;
  (event: 'chooseavatar', e: any): void;
  (event: 'agreeprivacyauthorization', ...args: any): void;
}

const props = withDefaults(defineProps<IProps>(), {
  size: 'normal',
  scope: '',
  openType: '',
  formType: '',
  type: 'primary',
  icon: '',
  iconPrefix: '',
  className: '',
  buttonId: '',
  disabled: false,
  square: false,
  plain: false,
  loading: false,
  round: false,
  block: false,
});

const emits = defineEmits<IEmits>();

const theme = computed(() => {
  return `sc-button--${themeMap[props.type] || 'default'}`;
});

const fontSize = computed(() => {
  const size = {
    large: 'large',
    small: 'small',
    mini: 'mini',
    normal: 'normal',
  };
  return `sc-button--${size[props.size || 'normal']}`;
});

const classNames = computed(() => {
  const { block, disabled, round, plain, square, className } = toRefs(props);

  return replaceJs(
    [
      'sc-button',
      className.value,
      fontSize.value,
      theme.value,
      block.value ? 'sc-button--block' : '',
      disabled.value ? 'sc-button--disabled' : '',
      round.value ? 'sc-button--round' : '',
      plain.value ? 'sc-button--plain' : '',
      square.value ? 'sc-button--square' : '',
      'class',
    ].join(' '),
  );
});

const onClick = () => {
  emits('click');
};

const opensetting = (e: any) => {
  emits('opensetting', e);
};

const getphonenumber = (e: any) => {
  emits('getphonenumber', e);
};

const getuserinfo = (e: any) => {
  emits('getuserinfo', e);
};

const chooseAvatar = (e: any) => {
  emits('chooseavatar', e);
};

const getAuthorize = (e: any) => {
  emits('getAuthorize', e);
};

const agreeprivacyauthorization = (...args: any) => {
  emits('agreeprivacyauthorization', ...args);
};
</script>

<!-- #ifdef MP-WEIXIN -->
<script lang="ts">
export default {
  behaviors: ['wx://form-field-button'],
  externalClasses: ['class'],
  options: {
    virtualHost: true,
  },
};
</script>
<!-- #endif -->

<template>
  <button
    :id="buttonId"
    :open-type="openType"
    :form-type="formType"
    :disabled="disabled"
    :loading="loading"
    :class="[classNames]"
    :scope="scope"
    @click="onClick"
    @opensetting="opensetting"
    @getphonenumber="getphonenumber"
    @getuserinfo="getuserinfo"
    @chooseavatar="chooseAvatar"
    @agreeprivacyauthorization="agreeprivacyauthorization"
    @getAuthorize="getAuthorize">
    <div class="sc-button__content">
      <uni-icons
        v-if="icon"
        :type="icon"
        :custom-prefix="iconPrefix"
        class-name="sc-button__icon"></uni-icons>

      <span v-else class="sc-button__text">
        <slot />
      </span>
    </div>
  </button>
</template>

<style lang="scss" scoped>
.sc-button {
  position: relative;
  display: inline-block;
  border: none;
  box-sizing: border-box;
  height: 88rpx;
  margin: 0;
  padding: 0;
  font-size: 32rpx;
  line-height: 1.2;
  border-radius: 5rpx;
  cursor: pointer;
  transition: opacity 0.2s;
  display: flex;
  justify-content: center;
  align-items: center;

  &::after {
    content: ' ';
    position: absolute;
    top: 50%;
    left: 50%;
    background-color: $uni-text-color;
    border-color: $uni-text-color;
    width: 100%;
    height: 100%;
    border: inherit;
    border-radius: 5rpx;
    transform: scale(0.5);
    transform: translate(-50%, -50%);
    opacity: 0;
    pointer-events: none;
    box-sizing: border-box;
    border-width: 1px;
  }

  &:active {
    opacity: 0.7;
  }

  &__content {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  &--round {
    border-radius: 100px;
    &::after {
      border-radius: 100px;
    }
  }
  &--square,
  &::after {
    border-radius: 0;
  }
  &--block {
    width: 100%;
  }

  &--primary,
  &--primary[disabled] {
    color: $uni-text-color-inverse;
    background-color: $uni-color-primary;
    border: 1rpx solid $uni-color-primary;
    &::after {
      border-color: $uni-color-primary;
    }
  }
  &--red,
  &--red[disabled] {
    color: $uni-text-color-inverse;
    background-color: $uni-color-error;
    border: 1rpx solid $uni-color-error;
    &::after {
      border-color: $uni-color-error;
    }
  }
  &--green,
  &--green[disabled] {
    color: $uni-text-color-inverse;
    background-color: $uni-color-success;
    border: 1rpx solid $uni-color-success;
    &::after {
      border-color: $uni-color-success;
    }
  }
  &--yellow,
  &--yellow[disabled] {
    color: $uni-text-color-inverse;
    background-color: $uni-color-warning;
    border: 1rpx solid $uni-color-warning;
    &::after {
      border-color: $uni-color-warning;
    }
  }
  &--grey,
  &--grey[disabled] {
    color: $uni-text-color-inverse;
    border: 1rpx solid;
    background: #444;
    &::after {
      @apply border-gray-300;
    }
  }
  &--default,
  &--default[disabled] {
    color: $uni-text-color;
    border: 1rpx solid #ccc;
    background-color: $uni-text-color-inverse;
  }

  &--large {
    width: 100%;
    font-size: 32rpx;
    height: 100rpx;
  }
  &--normal {
    padding: 0 30rpx;
    font-size: 28rpx;
  }
  &--small {
    font-size: 24rpx;
    height: 64rpx;
    padding: 0 16rpx;
  }
  &--mini {
    font-size: 20rpx;
    height: 48rpx;
    padding: 0 8rpx;
  }
  &__icon {
    font-size: 1.2em;
    line-height: inherit;
  }
  &--disabled {
    cursor: not-allowed;
    opacity: 0.5;
  }
  &--plain {
    background-color: $uni-text-color-inverse;
    @at-root {
      &.sc-button {
        &--primary {
          color: $uni-color-primary;
        }
        &--red {
          color: $uni-color-error;
        }
        &--green {
          color: $uni-color-success;
        }
        &--yellow {
          color: $uni-color-warning;
        }
        &--grey {
          color: $uni-text-color-disable;
        }
        &--default {
          color: $uni-text-color;
        }
      }
    }
  }
}
</style>
