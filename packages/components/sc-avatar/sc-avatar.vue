<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import Register from '@rms/register';

interface IProps {
  src?: string;
  size?: number | string;
  className?: string;
}

const props = withDefaults(defineProps<IProps>(), {
  size: 50,
  src: '',
  className: '',
});

const register = ref<InstanceType<typeof Register> | null>(null);

const size = computed(() => {
  return `${uni.upx2px(Number(props.size || 0) * 2)}px`;
});

const avatar = computed(() => {
  if (!register.value) return '/static/images/logo.png';
  return props.src || register.value.RmsUtilsOss.logo;
});

onMounted(() => {
  register.value = new Register();
});
</script>
<!-- #ifdef MP-WEIXIN -->
<script lang="ts">
export default {
  externalClasses: ['class'],
  options: { virtualHost: true },
};
</script>
<!-- #endif -->
<template>
  <image :src="avatar" :class="className" class="sc-avatar class"></image>
</template>

<style lang="scss" scoped>
.sc-avatar {
  width: v-bind(size);
  height: v-bind(size);
  border-radius: 50%;
}
</style>
