<script setup lang="ts">
import { ref, watch, onMounted } from 'vue';
import { replaceJs } from '@rms/utils';
import Register from '@rms/register';

interface IProp {
  /** 错误信息1 */
  errmsg?: string;
  /** 错误信息2  */
  errcon?: string;
  /** icon */
  icon?: string;
  /** 是否下拉刷新 */
  refresherEnabled?: boolean;
  /** 图片 class */
  imgClass?: string;
  /** modelValue */
  modelValue: boolean;
}

interface IEmit {
  (e: 'refresh'): void;
}

const emits = defineEmits<IEmit>();

const props = withDefaults(defineProps<IProp>(), {
  errmsg: '暂无权限',
  errcon: '信息不对，下拉一下试试？或联系管理员！',
  icon: '',
  refresherEnabled: true,
  imgClass: '',
});

const show = ref(false);

watch(
  () => props.modelValue,
  (val) => {
    show.value = val;
  },
  {
    immediate: true,
  },
);

const iconUrl = ref('');

const refresherTriggered = ref(false);

const refresherrefresh = async () => {
  refresherTriggered.value = true;
  emits('refresh');
  setTimeout(() => {
    refresherTriggered.value = false;
  }, 500);
};

onMounted(() => {
  const register = new Register();
  iconUrl.value = `${register.RmsUtilsOss.imgPath}/bank/home/<USER>
});
</script>

<template>
  <scroll-view
    v-show="show"
    :refresher-enabled="refresherEnabled"
    refresher-background="#f3f4f6"
    :refresher-triggered="refresherTriggered"
    scroll-y
    class="w-screen h-screen absolute top-0 left-0 bg-gray-100 z-[999]"
    @refresherrefresh="refresherrefresh">
    <sc-empty :show="show" :img-url="icon">
      <slot name="img">
        <image
          class="w-36"
          :class="replaceJs(imgClass)"
          mode="widthFix"
          :src="icon || iconUrl"
          alt="" />
      </slot>
      <slot>
        <view class="flex flex-col items-center">
          <view class="text-16 py-5">{{ errmsg }}</view>
          <view class="text-placeholder">{{ errcon }}</view>
        </view>
      </slot>
    </sc-empty>
  </scroll-view>
</template>

<style lang="scss" scoped></style>
