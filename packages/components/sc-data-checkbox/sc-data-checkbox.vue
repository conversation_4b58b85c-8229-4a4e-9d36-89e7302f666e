<script setup lang="ts">
import { computed } from 'vue';

interface IFormProps {
  /** form */
  form?: any;
  /** prop */
  prop?: string;
  /** 是否显示必填符号 */
  required?: boolean;
  /** 数据 */
  range?: any;
  /** 是否隐藏 */
  isHide?: boolean;
  /** mode */
  mode?: 'default' | 'list' | 'button' | 'tag';
  /** 是否多选 */
  multiple?: boolean;
  /** 选中颜色 */
  selectedColor?: string;
  /** 选中文本颜色，如不填写则自动显示 */
  selectedTextColor?: string;
  /** modelValue */
  modelValue?: number;
}

const emits = defineEmits<{
  (e: 'change', val: number): void;
  (e: 'update:modelValue', val: number): void;
}>();

const props = withDefaults(defineProps<IFormProps>(), {
  range: [],
  form: {},
  prop: '',
  mode: 'default',
  isHide: false,
  selectedColor: '#007aff',
  selectedTextColor: '#333',
  modelValue: undefined,
  required: false,
});

const attrs = computed(() => {
  const _localProps = { ...props } as Partial<typeof props>;
  delete _localProps.form;
  delete _localProps.prop;
  delete _localProps.isHide;

  return { ..._localProps };
});

function handleValueChange(e: { detail: { value: number } }) {
  emits('update:modelValue', e.detail.value);
  emits('change', e.detail.value);
}
</script>

<!-- #ifdef MP-WEIXIN -->
<script lang="ts">
export default {
  options: { virtualHost: true, styleIsolation: 'shared' },
};
</script>
<!-- #endif -->

<template>
  <uni-data-checkbox
    v-show="!isHide"
    :value="form[prop] || modelValue"
    v-bind="attrs"
    :localdata="range"
    @change="handleValueChange"></uni-data-checkbox>
</template>

<style lang="scss" scoped></style>
