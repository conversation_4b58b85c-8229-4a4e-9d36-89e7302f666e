<script setup lang="ts">
import { computed } from 'vue';

interface IProps {
  url: string;
  opacity?: string | number;
  zIndex?: number;
  size?: string;
  positionX?: string;
  positionY?: string;
  repeat?: string;
  className?: string;
}

const props = withDefaults(defineProps<IProps>(), {
  opacity: 1,
  zIndex: -1,
  size: '50%',
  positionX: '50%',
  positionY: '50%',
  repeat: 'no-repeat',
  className: '',
});

const image = computed(() => {
  let url = props.url;
  if (props.url.includes('.//')) {
    // 修复小程序图片路径问题
    url = props.url.slice(2);
  } else if (/^\.\/static/.test(props.url)) {
    // 兼容独立部署根路径无法自动解析
    url = '\.' + props.url;
  }
  return `url(${url})`;
});

const opacity = computed(() => Number(props.opacity) || 0.5);
const zIndex = computed(() => Number(props.zIndex) || -1);
</script>
<!-- #ifdef MP-WEIXIN -->
<script lang="ts">
export default {
  externalClasses: ['class'],
  options: { virtualHost: true },
};
</script>
<!-- #endif -->
<template>
  <view class="sc-background-image class" :class="className">
    <slot></slot>
  </view>
</template>

<style lang="scss" scoped>
.sc-background-image {
  position: relative;
  z-index: 0;
  height: 100%;
  width: 100%;
  &::before {
    content: '';
    display: block;
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background-image: v-bind('image');
    background-size: v-bind('size');
    background-repeat: v-bind('repeat');
    background-position-x: v-bind('positionX');
    background-position-y: v-bind('positionY');
    opacity: v-bind('opacity');
    pointer-events: none;
    z-index: v-bind('zIndex');
  }
}
</style>
