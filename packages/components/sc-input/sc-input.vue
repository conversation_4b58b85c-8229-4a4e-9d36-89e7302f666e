<script setup lang="ts">
import { computed, nextTick } from 'vue';

interface IFormProps {
  /** form */
  form?: any;
  /** prop */
  prop?: string;
  /** type */
  type?: 'text' | 'textarea' | 'password' | 'number' | 'idcard' | 'digit';
  /** 是否自动增高输入区域，type为textarea时有效 */
  autoHeight?: boolean;
  /** placeholder的样式(内联样式，字符串)，如"color: #ddd" */
  placeholderStyle?: string;
  /** 是否自动获得焦点 */
  focus?: boolean;
  /** 最大输入长度，设置为 -1 的时候不限制最大长度 */
  maxlength?: number;
  /** 设置键盘右下角按钮的文字，仅在type="text"时生效 */
  confirmType?: string;
  /** 清除图标的大小，单位px */
  clearSize?: number;
  /** 是否自动去除空格，传入类型为 Boolean 时，自动去除前后空格 */
  trim?: 'both' | 'left' | 'right' | 'none' | '';
  /** 输入框的提示文字 */
  placeholder?: string;
  /** 是否显示input输入框的边框 */
  inputBorder?: boolean;
  /** 是否显示右侧清空内容的图标控件(输入框有内容且不禁用时显示)，点击可清空输入框内容 */
  clearable?: boolean;
  /** 是否不可输入 */
  disabled?: boolean;
  /** 是否隐藏 */
  isHide?: boolean;
  /** 文字位置 */
  align?: 'right' | 'left' | 'center';
  /** className */
  className?: string;
  /** 尾部图标 */
  iconName?: string;
  /** iconClass */
  iconClass?: string;
  /** iconSize */
  iconSize?: string | number;
  /** modelValue */
  modelValue?: string;
  /** 清除图标颜色 */
  primaryColor?: string;
}

const emits = defineEmits<{
  (e: 'change', val: string): void;
  (e: 'blur'): void;
  (e: 'click'): void;
  (e: 'update:modelValue', val: string): void;
}>();

const classNames = computed(() => {
  return `${props.prop + props.className} inputClass`;
});

const props = withDefaults(defineProps<IFormProps>(), {
  form: {},
  prop: '',
  type: 'text',
  placeholder: '',
  inputBorder: false,
  clearable: false,
  disabled: false,
  isHide: false,
  iconName: '',
  iconClass: 'text-gray-300',
  iconSize: 16,
  placeholderStyle: '',
  maxlength: 140,
  confirmType: '',
  clearSize: 15,
  trim: 'both',
  align: 'left',
  className: '',
  modelValue: '',
});

const attrs = computed(() => {
  const _localProps = { ...props } as Partial<typeof props>;
  delete _localProps.form;
  delete _localProps.prop;
  delete _localProps.isHide;

  return { ..._localProps };
});

const defaultValue = computed(() => props.form[props.prop]);

const align = computed(() => props.align);

function handlClick() {
  emits('click');
}

async function handleBlur() {
  await nextTick();
  emits('blur');
}

function handleValueChange(val: string) {
  emits('change', val);
  emits('update:modelValue', val);
}
</script>
<!-- #ifdef MP-WEIXIN -->
<script lang="ts">
export default {
  options: { virtualHost: true, styleIsolation: 'shared' },
};
</script>
<!-- #endif -->
<template>
  <view class="sc-input flex w-full h-full items-center" @click="handlClick">
    <uni-easyinput
      class="flex-1 h-full"
      trim
      v-bind="attrs"
      :class="classNames"
      :value="defaultValue"
      @blur="handleBlur"
      @input="handleValueChange" />
    <slot name="icon">
      <sc-icon
        v-if="iconName"
        :name="iconName"
        :size="iconSize"
        :class-name="iconClass"
        :class="iconClass"></sc-icon>
    </slot>
  </view>
</template>

<style lang="scss" scoped>
.inputClass {
  flex: 1;
  width: 100%;
  box-sizing: border-box;
  min-height: 36px;
  flex-direction: row;
  align-items: center;
}
.sc-input {
  :deep(.uni-easyinput__placeholder-class) {
    text-align: v-bind('align');
  }
  :deep(.uni-easyinput__content-input) {
    text-align: v-bind('align');
  }
}
</style>
