<script lang="ts" setup>
import { computed, ref } from 'vue';
import { exception<PERSON><PERSON><PERSON> } from '@rms/utils';
// #ifdef H5
import { IsWeixinH5, IsMiniProgram } from '@rms/utils';
import { IsH5 } from '@rms/utils';

const isWechat = ref(IsWeixinH5);
const isMiniProgram = ref(IsMiniProgram);
// #endif

interface IProps {
  template: string[];
}
interface IEmits {
  (event: 'click'): void;
  (event: 'error'): void;
  (event: 'success', d: Record<string, boolean>): void;
}

const props = withDefaults(defineProps<IProps>(), {});

const emits = defineEmits<IEmits>();

const templateIds = computed(() => {
  if (IsH5) return props.template.join(',');
  return props.template;
});

const subscribeMessage = (res: Record<string, any>) => {
  let flag = false;
  const result: Record<string, boolean> = {};
  for (let i = 0; i < props.template.length; i++) {
    const e = props.template[i];
    if (res[e]?.includes('accept')) {
      flag = true;
      result[e] = true;
    }
  }
  if (flag) emits('success', result);
  else emits('error');

  emits('click');
};

const click = () => {
  // #ifdef H5
  emits('click');
  // #endif

  // #ifdef MP-WEIXIN
  uni.requestSubscribeMessage({
    tmplIds: templateIds.value as string[],
    success(res: Record<string, any>) {
      subscribeMessage(res);
    },
    fail() {
      emits('error');
      emits('click');
    },
  });
  // #endif
};

//  #ifdef H5
const error = () => {
  emits('error');
  emits('click');
};

const success = (e: Record<string, any>) => {
  try {
    const data = e?.detail?.subscribeDetails || e?.subscribeDetails || '';
    const subs: Record<string, any> = (data && JSON.parse(data)) || {};
    subscribeMessage(subs);
  } catch (err) {
    exceptionHandler(err);
    emits('error');
    emits('click');
  }
};
// #endif
</script>

<template>
  <!-- #ifdef H5 -->
  <template v-if="isWechat">
    <view class="relative">
      <wx-open-subscribe
        v-if="!isMiniProgram"
        style="
          width: 100%;
          height: 100%;
          z-index: 2;
          position: absolute;
          right: 0;
          left: 0;
          top: 0;
          bottom: 0;
        "
        :template="templateIds"
        @error="error"
        @success="success($event)">
        <view is="vue:script" type="text/wxtag-template">
          <button
            style="
              position: absolute;
              right: 0;
              left: 0;
              top: 0;
              bottom: 0;
              z-index: 2;
              opacity: 0;
              width: 100%;
              height: 100%;
            "></button>
        </view>
      </wx-open-subscribe>
      <view @click="click">
        <slot></slot>
      </view>
    </view>
  </template>
  <template v-else>
    <view @click="click">
      <slot></slot>
    </view>
  </template>
  <!-- #endif -->
  <!-- #ifdef MP-WEIXIN -->
  <view @click="click">
    <slot></slot>
  </view>
  <!-- #endif -->
</template>
