<script setup lang="ts">
import { nextTick, ref, watch } from 'vue';
import { onPullDownRefresh } from '@dcloudio/uni-app';
import { ApiSortsHandler } from '@shencom/api';
import { replaceJs } from '@rms/utils';

export interface TabsDataProps {
  value: string;
  id: string;
}

export interface ConfigProps {
  /** 数据 */
  range?: any;
  /** type */
  type: 'picker' | 'region' | 'select';
  /** prop */
  prop: string;
  /** 默认文案 */
  defualt: string;
  /** 处理回调 */
  handle?: (data: any, item: ConfigProps) => any;
  /** 数据对应的key-value */
  map?: [string, string];
  /** attr */
  attr?: AnyObject;
  /** 是否排序 */
  sort?: boolean;
  /** className */
  className?: string;
  /** 是否有 border */
  isBorder?: boolean;
  /** 点击回调 */
  click?: () => void;
  /** icon 名称 */
  icon?: string;
  /** iconSiez */
  iconSize?: string | number;
  /** iconClass */
  iconClass?: string;
}

export interface IProps {
  config: {
    data: ConfigProps[];
  };
  className?: string;
  isSticky?: boolean;
}

export type FiltrateTabsType = IProps['config'];

const props = withDefaults(defineProps<IProps>(), {
  isSticky: true,
  className: '',
});

const emits = defineEmits<{
  (e: 'change', type: string, formData: string, all: any): void;
  (e: 'sort-change', type: string, sort?: SC.API.IndexSorts): void;
}>();

function defaultHandle(data: any, item: ConfigProps) {
  const { map = ['id', 'value'] } = item;

  return data[map[0]];
}

const form = ref<AnyObject>({});

const sortConfig = ref<any[]>([]);

watch(
  () => props.config,
  () => {
    if (props.config) {
      init();
    }
  },
  {
    deep: true,
    immediate: true,
  },
);

async function handlePickerChange(e: any, item: ConfigProps) {
  // 确保拿到最新的值
  await nextTick();

  const { prop, handle = defaultHandle } = item;

  const value = typeof handle === 'function' ? handle(form.value[prop], item) : item;

  nextTick(() => emits('change', prop, value, e));
}

function handleSortClick(item: ConfigProps, index: number) {
  if (sortConfig.value[index].current === 0) {
    sortConfig.value[index].sort = ApiSortsHandler(item.prop, 'ASC');
    sortConfig.value[index].current = 1;
  } else if (sortConfig.value[index].current === 1) {
    sortConfig.value[index].sort = ApiSortsHandler(item.prop, 'DESC');
    sortConfig.value[index].current = 2;
  } else {
    sortConfig.value[index].sort = null;
    sortConfig.value[index].current = 0;
  }

  const { prop } = item;

  nextTick(() => emits('sort-change', prop, sortConfig.value[index].sort));
}

function init() {
  props.config.data.forEach((item, index) => {
    form.value[item.prop] =
      item.type === 'region'
        ? []
        : {
            [!item.map ? 'id' : item.map[0]]: '',
            [!item.map ? 'value' : item.map[1]]: '',
          };

    sortConfig.value[index] = item.sort
      ? {
          sort: '',
          prop: item.prop,
          current: 0,
        }
      : null;
  });
}

onPullDownRefresh(() => {
  init();
});

function handleSelectClick(item: ConfigProps, index: number) {
  if (item.click) {
    item.click();
  }

  if (item.sort) {
    handleSortClick(item, index);
  }
}

defineExpose({ init });
</script>

<!-- #ifdef MP-WEIXIN -->
<script lang="ts">
export default {
  options: { virtualHost: true, styleIsolation: 'shared' },
};
</script>
<!-- #endif -->

<template>
  <view class="w-full top-0" :class="{ sticky: isSticky, [replaceJs(className)]: true }">
    <view class="flex rounded-lg bg-white justify-between">
      <view
        v-for="(item, index) of config.data"
        :key="index"
        class="flex flex-1 border-solid border-gray-200"
        :class="{ 'border-r': item.isBorder && index + 1 !== config.data.length }">
        <sc-picker
          v-if="item.type === 'picker'"
          v-bind="item.attr"
          v-model="form[item.prop]"
          :range-map="item.map"
          class="w-full h-full justify-center items-center"
          :ranges="item.range"
          @confirm="handlePickerChange($event, item)">
          <view class="flex items-center justify-center w-full">
            <view
              class="flex-1 justify-center flex items-center py-3"
              :class="replaceJs(item.className || '')">
              {{ form[item.prop][item.map![1] || 'value'] || item.defualt }}
              <sc-icon
                v-if="!form[item.prop][item.map![1] || 'value']"
                name="i-ion-chevron-down"
                :size="item.iconSize"
                :class-name="replaceJs(item.iconClass || '')"
                :class="replaceJs(item.iconClass || '')"></sc-icon>
            </view>
            <view
              v-if="sortConfig[index]"
              class="flex flex-col px-1.5 py-3"
              @click.stop="handleSortClick(item, index)">
              <sc-icon
                name="i-fontisto-caret-up"
                :class="sortConfig[index].current === 1 ? 'text-black' : 'text-gray-300'"
                :class-name="sortConfig[index].current === 1 ? 'text-black' : 'text-gray-300'"
                size="9"></sc-icon>
              <sc-icon
                name="i-fontisto-caret-down"
                :class="sortConfig[index].current === 2 ? 'text-black' : 'text-gray-300'"
                :class-name="sortConfig[index].current === 2 ? 'text-black' : 'text-gray-300'"
                size="9"></sc-icon>
            </view>
          </view>
        </sc-picker>

        <sc-region
          v-if="item.type === 'region'"
          v-model="form.regionId"
          v-bind="item.attr"
          defalut-color="#000"
          defalut-size="28rpx"
          item-class="text-center p-1"
          :placeholder="item.defualt"
          class="w-full h-full"
          @change="handlePickerChange($event, item)">
          <template #default="{ text }">
            <view class="flex items-center justify-center w-full">
              <view
                class="flex-1 justify-center flex items-center py-3"
                :class="replaceJs(item.className || '')">
                {{ text }}
              </view>
            </view>
          </template>
          <template #placeholder>
            <view class="flex items-center justify-center w-full">
              <view
                class="flex-1 justify-center flex items-center py-3"
                :class="replaceJs(item.className || '')">
                {{ item.defualt }}
                <sc-icon
                  name="i-ion-chevron-down"
                  :size="item.iconSize"
                  :class-name="replaceJs(item.iconClass || '')"
                  :class="replaceJs(item.iconClass || '')"></sc-icon>
              </view>
            </view>
          </template>
        </sc-region>

        <view
          v-if="item.type === 'select'"
          class="flex items-center justify-center w-full h-9"
          :class="replaceJs(item.className || '')"
          @click="handleSelectClick(item, index)">
          <view class="flex-1 justify-center flex items-center">
            {{ item.defualt }}
            <view :class="item.icon"></view>
            <view
              v-if="sortConfig[index]"
              class="flex flex-col px-1.5 py-3"
              @click.stop="handleSortClick(item, index)">
              <sc-icon
                name="i-fontisto-caret-up"
                :class="sortConfig[index].current === 1 ? 'text-black' : 'text-gray-300'"
                :class-name="sortConfig[index].current === 1 ? 'text-black' : 'text-gray-300'"
                size="9"></sc-icon>
              <sc-icon
                name="i-fontisto-caret-down"
                :class="sortConfig[index].current === 2 ? 'text-black' : 'text-gray-300'"
                :class-name="sortConfig[index].current === 2 ? 'text-black' : 'text-gray-300'"
                size="9"></sc-icon>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped></style>
