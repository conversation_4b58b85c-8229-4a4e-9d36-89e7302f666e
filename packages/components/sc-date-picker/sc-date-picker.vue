<script setup lang="ts">
import { computed, ref } from 'vue';
import { replaceJs } from '@rms/utils';

interface IFormProps {
  /** form */
  form?: any;
  /** prop */
  prop?: string;
  /** type */
  type?: 'date' | 'daterange' | 'datetime' | 'datetimerange';
  /** 是否显示必填符号 */
  required?: boolean;
  /** placeholder */
  placeholder?: string;
  /** 输入框border */
  inputBorder?: boolean;
  /** 是否显示清除按钮 */
  clearable?: boolean;
  /** 最大值，可以使用日期的字符串(String)、时间戳(Number) */
  end?: string | number;
  /** 最小值，可以使用日期的字符串(String)、时间戳(Number) */
  start?: string | number;
  /** 返回值格式 */
  returnType?: 'timestamp' | 'string' | 'date';
  /** 是否显示秒，只显示时分 */
  hideSecond?: boolean;
  /** 是否隐藏 */
  isHide?: boolean;
  /** classNames */
  classNames?: string;
  /** 是否不可选择 */
  disabled?: boolean;
  /** iconName */
  iconName?: string;
  /** iconClass */
  iconClass?: string;
  /** iconSize */
  iconSize?: string | number;
  /** 清除按钮 icon */
  clearName?: string;
  /** 选择范围时的分隔符 */
  rangeSeparator?: string;
  /** 文字位置 */
  align?: 'right' | 'left' | 'center';
}

const emits = defineEmits<{
  (e: 'change', val: string | string[]): void;
  (e: 'maskClick'): void;
  (e: 'update:modelValue', val: string | string[]): void;
}>();

const uniDateTimePickerRef = ref<AnyObject>();

const props = withDefaults(defineProps<IFormProps>(), {
  form: {},
  prop: '',
  type: 'date',
  returnType: 'string',
  placeholder: '',
  inputBorder: false,
  disabled: false,
  clearable: false,
  end: '',
  start: '',
  isHide: false,
  classNames: '',
  iconName: 'i-ion-chevron-forward-outline',
  iconClass: 'text-black',
  iconSize: 15,
  clearName: 'i-ri-close-circle-fill',
  rangeSeparator: '至',
  align: 'left',
  required: false,
  hideSecond: false,
});

const resultDate = computed(() => {
  const { type, prop, form, rangeSeparator } = props;
  if (type === 'datetimerange') {
    return [form[prop][0], form[prop][1]];
  }
  if (type === 'daterange') {
    return `${form[prop][0]} ${rangeSeparator} ${form[prop][1]}`;
  }

  return form[prop];
});

const alignComp = computed(() => {
  if (props.align === 'left') {
    return replaceJs('justify-start');
  }

  if (props.align === 'right') {
    return replaceJs('justify-end');
  }
  return replaceJs('justify-center');
});

function trim(val: string) {
  return val.trim();
}

function clear() {
  uniDateTimePickerRef.value?.clear();
}

function show() {
  uniDateTimePickerRef.value?.show();
}

function close() {
  uniDateTimePickerRef.value?.close();
}

function handleClearCLick() {
  clear();
  emits('update:modelValue', '');
}

function handleValueChange(val: string | string[]) {
  const value = typeof val === 'string' ? trim(val) : val;
  emits('update:modelValue', value);
  emits('change', value);
}

function handleMaskClick() {
  emits('maskClick');
}

defineExpose({ clear, show, close });
</script>
<!-- #ifdef MP-WEIXIN -->
<script lang="ts">
export default {
  options: { virtualHost: true, styleIsolation: 'shared' },
};
</script>
<!-- #endif -->
<template>
  <view>
    <uni-datetime-picker
      ref="uniDateTimePickerRef"
      v-bind="props"
      :border="false"
      :class="classNames"
      v-model="form[prop]"
      @change="handleValueChange"
      @mask-click="handleMaskClick">
      <slot class="w-full">
        <view class="h-9 flex items-center pl-[20rpx]" v-show="!isHide">
          <view class="h-full flex-1 flex items-center" :class="[alignComp]">
            <view
              v-if="form[prop] && form[prop].length"
              class="text-15 flex h-full relative items-center">
              <template v-if="type === 'datetimerange'">
                <view class="flex-1 h-full">
                  <view v-for="(time, index) of resultDate" :key="index">
                    {{ time }}
                  </view>
                </view>
                <view class="h-full flex items-center ml-2">
                  {{ rangeSeparator }}
                </view>
              </template>
              <template v-else>
                {{ resultDate }}
              </template>
            </view>
            <text v-else class="uni-easyinput__placeholder-class">{{ placeholder }}</text>
          </view>
          <template v-if="!disabled">
            <template v-if="clearName && form[prop] && form[prop].length && clearable">
              <slot name="clear">
                <view class="pl-[20rpx] flex items-center" @click.stop="handleClearCLick">
                  <sc-icon
                    :name="clearName"
                    :size="16"
                    class-name="text-gray-300 mr-3"
                    class="text-gray-300 mr-3"></sc-icon>
                </view>
              </slot>
            </template>
            <template v-else>
              <slot name="icon">
                <view class="px-[20rpx] flex items-center">
                  <sc-icon
                    :name="iconName"
                    :size="iconSize"
                    :class-name="iconClass"
                    :class="iconClass"></sc-icon>
                </view>
              </slot>
            </template>
          </template>
        </view>
      </slot>
    </uni-datetime-picker>
  </view>
</template>
