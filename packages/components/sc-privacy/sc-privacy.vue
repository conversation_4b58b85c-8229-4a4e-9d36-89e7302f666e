<script setup lang="ts">
import { watch, ref, onMounted } from 'vue';
interface IProp {
  name: string;
  content: string;
  title?: string;
}

interface IEmit {
  (e: 'fail'): void;
  (e: 'success', ...args: any): void;
}

const emits = defineEmits<IEmit>();
const props = withDefaults(defineProps<IProp>(), {
  title: '法律条款及隐私保护提示',
});

const popupRef = ref();

function toPrivacy() {
  uni.openPrivacyContract();
}

function handleRejectPrivacyAuthorization() {
  popupRef.value?.close();
  emits('fail');
}

function handleAgreePrivacyAuthorization(...args: any) {
  popupRef.value?.close();
  emits('success', ...args);
}

onMounted(() => {
  popupRef.value?.open();
});
</script>

<template>
  <uni-popup ref="popupRef" :mask-click="false">
    <view class="bg-white mx-5 rounded-2xl p-5">
      <view class="text-center text-black text-17">{{ title }}</view>
      <view class="indent-[2em] text-15 leading-7 pt-6 pb-10">
        <view>
          {{ content }}
        </view>
        <view>
          您可以阅读完整版
          <text class="underline text-blue-600" @click="toPrivacy">{{ name }}</text>
          。
        </view>
        <!-- <view>
          您可以通过
          <text class="text-red-600">“我的-设置--服务与协议-隐私政策”</text>
          再次查看隐私政策。
        </view> -->
      </view>

      <view class="flex justify-around">
        <sc-button round type="primary" plain @click="handleRejectPrivacyAuthorization">
          不同意，退出
        </sc-button>
        <sc-button
          round
          button-id="agree-btn"
          open-type="agreePrivacyAuthorization"
          type="primary"
          @agreeprivacyauthorization="handleAgreePrivacyAuthorization">
          同意，继续
        </sc-button>
      </view>
    </view>
  </uni-popup>
</template>

<style lang="scss" scoped></style>
