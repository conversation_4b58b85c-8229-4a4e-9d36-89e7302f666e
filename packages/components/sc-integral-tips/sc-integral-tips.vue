<script setup lang="ts">
import { ref, computed } from 'vue';
import { onLoad } from '@dcloudio/uni-app';
import { Menu } from '@rms/types';
import { ServiceGetMenu } from '@rms/service';
import Register from '@rms/register';

interface IEmits {
  (event: 'update:modelValue', e: boolean): void;
  (event: 'close'): void;
}

interface IProps {
  /** 配置项 */
  config: {
    credit?: number;
    isSupervise?: boolean;
    fitText?: string;
    noFitText?: string;
    groupKey?: string;
    mainLabel?: string;
    customClose?: boolean;
    menuIsReplace?: boolean;
  };
}

const defalutConfig = ref({
  credit: 0,
  isSupervise: false,
  fitText: '您的志愿督导时长未满2小时，无法获得积分~',
  noFitText: '您今日于该功能获取的碳积分已达上限~',
  groupKey: 'integral_tips_activity',
  mainLabel: '弹框主页',
  customClose: false,
  menuIsReplace: false,
});

const intergralConfig = computed(() => {
  return { ...defalutConfig.value, ...props.config };
});

const emits = defineEmits<IEmits>();

const props = defineProps<IProps>();

const iconImg = ref('');

const timer = ref<NodeJS.Timeout>({} as NodeJS.Timeout);

const time = 3000;

const popup = ref<{ open: () => void; close: () => void } | null>(null);

const menus = ref<Menu[]>([]);

const operatePopup = (type: boolean) => {
  if (type) {
    popup.value?.open();
    timeOffPopup();
  } else {
    popup.value?.close();
  }
};

const goPageShop = (menu: Menu) => {
  if (!menu || !menu.link) return;
  operatePopup(false);
  const register = new Register();
  register?.RmsNavigator.toMenuPage(menu, intergralConfig.value.menuIsReplace);
};

const onClose = () => {
  operatePopup(false);
  emits('close');
  if (!intergralConfig.value.customClose) {
    const register = new Register();
    register?.RmsNavigator.back(2);
  }
};

const timeOffPopup = () => {
  timer.value = setTimeout(() => {
    if (timer.value) {
      clearTimeout(timer.value);
    }
    operatePopup(false);
    clearTimeout(timer.value);
  }, time);
};

onLoad(async () => {
  const register = new Register();
  iconImg.value = `${register?.RmsUtilsOss.imgPath}/pages-reserve/img_pop.png`;
  const { groupKey } = intergralConfig.value;
  if (groupKey) {
    const data = await ServiceGetMenu(groupKey);
    if (data && data[groupKey]) {
      menus.value = data[groupKey];
    }
  }
});

defineExpose({
  operatePopup,
});
</script>

<template>
  <uni-popup ref="popup">
    <view class="text-center">
      <view class="relative w-[594rpx] mx-auto">
        <image class="w-[594rpx] h-[748rpx]" :src="iconImg"></image>
        <view class="absolute top-[40rpx] left-1/2 -translate-x-[50%] text-18 font-bold text-white">
          提交成功
        </view>
        <view
          v-if="intergralConfig.credit"
          class="absolute top-[337rpx] left-1/2 -translate-x-[50%] w-[540rpx] text-13 text-black">
          本次可获得
          <span class="text-[#2e96fe] font-bold">{{ intergralConfig.credit }}</span>
          碳积分，去商城兑换好礼吧~
        </view>
        <view
          v-else
          class="absolute top-[337rpx] left-1/2 -translate-x-[50%] w-[540rpx] text-13 text-black">
          {{ intergralConfig.isSupervise ? intergralConfig.fitText : intergralConfig.noFitText }}
        </view>
        <view
          v-if="menus && menus.length > 0"
          class="absolute left-1/2 -translate-x-[50%] bottom-[20rpx]">
          <image
            v-for="(item, index) in menus"
            :key="item.id"
            class="w-[540rpx] h-[140rpx]"
            :class="{ 'mb-1.5': index !== menus.length - 1 }"
            :src="item.remoteUrl"
            @click="goPageShop(item)" />
        </view>
      </view>
      <view class="mx-auto i-ion-close-circled text-white text-32 mt-3.5" @click="onClose"></view>
    </view>
  </uni-popup>
</template>

<style lang="scss" scoped></style>
