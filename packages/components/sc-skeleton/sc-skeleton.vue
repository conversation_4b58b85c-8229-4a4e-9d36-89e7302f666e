<template>
  <view
    class="tui-skeleton-cmomon tui-skeleton-box"
    :style="{ width: winWidth + 'px', height: winHeight + 'px', backgroundColor: backgroundColor }">
    <view
      v-for="(item, index) in skeletonElements"
      :key="index"
      class="tui-skeleton-cmomon"
      :class="{ 'tui-skeleton__active': active }"
      :style="{
        width: item.width + 'px',
        height: item.height + 'px',
        left: item.left + 'px',
        top: item.top + 'px',
        backgroundColor: skeletonBgColor,
        borderRadius: getRadius(item.skeletonType, borderRadius),
      }"></view>
    <view v-if="isLoading" class="tui-loading" :class="[getLoadingType(loadingType)]"></view>
  </view>
</template>
<!-- 组件文档 -->
<!-- https://thorui.cn/doc/docs/thorui/tui-skeleton.html -->
<script setup lang="ts">
import { ref, onMounted } from 'vue';

const winWidth = ref(375);
const winHeight = ref(800);
const skeletonElements = ref<any[]>([]);

interface PreloadDataProps {
  id: string;
  dataset: any;
  left: number;
  right: number;
  top: number;
  bottom: number;
  width: number;
  height: number;
  skeletonType: string;
}

interface IProps {
  selector: string;
  backgroundColor: string;
  skeletonBgColor: string;
  skeletonType: string[];
  borderRadius: string;
  preloadData: PreloadDataProps[];
  isLoading: boolean;
  loadingType: number;
  active: boolean;
}

const props = withDefaults(defineProps<IProps>(), {
  selector: 'tui-skeleton',
  backgroundColor: '#fff',
  skeletonBgColor: '#e9e9e9',
  skeletonType: () => ['rect', 'circular', 'fillet'],
  borderRadius: '16rpx',
  preloadData: () => [],
  isLoading: false,
  loadingType: 1,
  active: true,
});

const getLoadingType = (type: number) => {
  let value = 1;
  if (type && type > 0 && type < 11) {
    value = type;
  }
  return `tui-loading-${value}`;
};

const getRadius = (type: string, val: string) => {
  let radius = '0';
  if (type == 'circular') {
    radius = '50%';
  } else if (type == 'fillet') {
    radius = val;
  }
  return radius;
};

const isPreload = (init: boolean) => {
  const preloadData = props.preloadData || [];
  if (preloadData.length) {
    init && (skeletonElements.value = preloadData);
    return true;
  }
  return false;
};

const selectorQuery = async () => {
  const skeletonType = props.skeletonType || [];
  let nodes: any[] = [];
  for (const item of skeletonType) {
    let className = '';
    className = `.${item}`;
    if (~'rect_circular_fillet'.indexOf(item)) {
      className = `.${props.selector}-${item}`;
    }
    const res = (await nodesRef(className)) as any[];
    res.map((d) => {
      d.skeletonType = item;
    });
    nodes = nodes.concat(res);
  }
  skeletonElements.value = nodes;
};

const nodesRef = async (className: string) => {
  return await new Promise((resolve, reject) => {
    uni
      .createSelectorQuery()
      .selectAll(className)
      .boundingClientRect((res) => {
        if (res) {
          resolve(res);
        } else {
          reject(res);
        }
      })
      .exec();
  });
};

onMounted(() => {
  const res = uni.getSystemInfoSync();
  winWidth.value = res.windowWidth;
  winHeight.value = res.windowHeight;
  if (isPreload(true)) {
    return;
  }
  nodesRef(`.${props.selector}`).then((res: any) => {
    if (res && res[0]) {
      winHeight.value = res[0].height + Math.abs(res[0].top);
    }
  });
  selectorQuery();
});
</script>

<style scoped>
.tui-skeleton-cmomon {
  position: absolute;
  z-index: 99999;
}

.tui-skeleton-box {
  left: 0;
  top: 0;
}

.tui-loading {
  display: inline-block;
  vertical-align: middle;
  width: 40rpx;
  height: 40rpx;
  background: 0 0;
  border-radius: 50%;
  border: 2px solid;
  animation: tui-rotate 0.7s linear infinite;
  position: fixed;
  z-index: 999999;
  left: 50%;
  top: 50%;
  margin-left: -20rpx;
  margin-top: -20rpx;
}

.tui-loading-1 {
  border-color: #e5e5e5 #e5e5e5 #e5e5e5 #5677fc;
}

.tui-loading-2 {
  border-color: #e5e5e5 #e5e5e5 #e5e5e5 #8f8d8e;
}

.tui-loading-3 {
  border-color: rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.1) #fff;
}

.tui-loading-4 {
  border-color: #e5e5e5 #e5e5e5 #e5e5e5 #35b06a;
}

.tui-loading-5 {
  border-color: #e5e5e5 #e5e5e5 #e5e5e5 #fc872d;
}

.tui-loading-6 {
  border-color: #e5e5e5 #e5e5e5 #e5e5e5 #eb0909;
}

.tui-loading-7 {
  border-color: #5677fc transparent #5677fc transparent;
}

.tui-loading-8 {
  border-color: #35b06a transparent #35b06a transparent;
}

.tui-loading-9 {
  border-color: #fc872d transparent #fc872d transparent;
}

.tui-loading-10 {
  border-color: #eb0909 transparent #eb0909 transparent;
}

@-webkit-keyframes tui-rotate {
  0% {
    transform: rotate(0);
  }

  100% {
    transform: rotate(360deg);
  }
}

@keyframes tui-rotate {
  0% {
    transform: rotate(0);
  }

  100% {
    transform: rotate(360deg);
  }
}

.tui-skeleton__active {
  background: linear-gradient(90deg, #f2f2f2 25%, #e6e6e6 37%, #f2f2f2 63%);
  animation: tui-active 1.4s ease infinite;
  background-size: 400% 100%;
}

@keyframes tui-active {
  0% {
    background-position: 100% 50%;
  }

  100% {
    background-position: 0 50%;
  }
}
</style>
