<script setup lang="ts">
import { computed } from 'vue';
import { replaceJs } from '@rms/utils';

type RangesProps = any;

interface IProps {
  /** 当前步骤 */
  active?: number;
  /** 排列方向 */
  direction?: 'row' | 'col';
  /** 数据 */
  ranges: RangesProps[];
  /** 当前活跃颜色 */
  activeColor?: string;
  /** 默认颜色 */
  normalColor?: string;
  /** iconSize */
  iconSize?: number;
  /** classname */
  classname?: string;
  /** 活跃 icon */
  iconActive?: string;
  /** 默认 icon */
  iconNormal?: string;
  /** 分界线颜色 */
  normalBorder?: string;
}

const props = withDefaults(defineProps<IProps>(), {
  direction: 'row',
  activeColor: '#387ef5',
  normalColor: '#fff',
  iconSize: 18,
  active: undefined,
  classname: '',
  iconActive: 'i-ion-ios-checkmark-circle',
  iconNormal: '',
  normalBorder: '#dcdcdc',
});

const activeComp = computed(() => {
  return props.active === undefined ? props.ranges.length - 1 : props.active;
});

const iconSizeComp = computed(() => {
  return `${props.iconSize}px`;
});

const normalBorderCpmp = computed(() => {
  return props.iconNormal ? '' : `2rpx solid ${props.normalBorder}`;
});

function handleColorStyle(index: number) {
  return activeComp.value === index ? { color: props.activeColor } : '';
}
</script>

<!-- #ifdef MP-WEIXIN -->
<script lang="ts">
export default {
  behaviors: ['wx://form-field-button'],
  externalClasses: ['class'],
  options: {
    virtualHost: true,
  },
};
</script>
<!-- #endif -->

<template>
  <view class="sc-steps w-full flex flex-col p-3 class" :class="replaceJs(classname)">
    <view
      v-for="(step, index) of ranges"
      :key="step.id || index"
      class="flex h-auto relative mb-1 min-h-[120rpx]">
      <view class="flex flex-col items-center mb-1 mr-2">
        <template v-if="activeComp === index">
          <sc-icon
            v-if="iconActive"
            :size="iconSize"
            class="sc-steps_icon__active sc-steps_icon__activeBg"
            :name="iconActive"></sc-icon>

          <view
            v-else
            class="sc-steps_icon__active sc-steps_icon__activeBg rounded-full mb-2"></view>
        </template>

        <template v-else>
          <sc-icon
            v-if="iconNormal"
            :size="iconSize"
            class="sc-steps_icon__normal sc-steps_icon__activeBg rounded-full mb-2"
            :name="iconNormal"></sc-icon>
          <view
            v-else
            class="sc-steps_icon__normal sc-steps_icon__normalBorder rounded-full mb-2"></view>
        </template>
        <view
          v-if="index + 1 !== ranges.length"
          class="w-[1rpx] ml-[2rpx] flex-1 bg-gray-500"></view>
      </view>
      <view class="flex-1 h-full">
        <slot :step="step" :index="index" :color-style="handleColorStyle(index)">
          <view class="sc-steps_item" :class="{ 'sc-steps_icon__active': activeComp === index }">
            {{ step.name }}
          </view>
        </slot>
      </view>
    </view>
  </view>
</template>

<style lang="scss">
.sc-steps {
  &_icon {
    &__active {
      color: v-bind(activeColor);
    }
    &__activeBg {
      background-color: v-bind(activeColor);
      width: v-bind(iconSizeComp);
      height: v-bind(iconSizeComp);
    }

    &__normalBorder {
      border: v-bind(normalBorderCpmp);
    }
    &__normal {
      background: v-bind(normalColor);
      width: v-bind(iconSizeComp);
      height: v-bind(iconSizeComp);
    }
  }
}
</style>
