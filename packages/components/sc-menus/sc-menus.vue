<script lang="ts" setup>
import { ref, computed, onMounted } from 'vue';
import { Dictionary, Menu } from '@rms/types';
import { replaceJs, exceptionHandler } from '@rms/utils';
import { ServiceGetMenu } from '@rms/service';
import { addUnit } from '../utils';
import Register from '@rms/register';

export type MenuItem = Menu;

/** 菜单点击事件
 * @param {MenuItem} menu 菜单
 * @param {boolean} isAuth 是否调起权限
 */
export interface MenuEvent {
  (menu: MenuItem, isAuth?: boolean): void;
}

interface IProps {
  /** 菜单标识 */
  group: string;
  /** name */
  name?: string;
  /** 展示类型 */
  type?: 'card' | 'cell' | 'tab' | '';
  /** 一行展示几个 */
  rowNumber?: number;
  /** 卡片高度，type为 tab 时，固定 30px */
  cardHeight?: number;
  /** icon 类名 */
  iconClass?: string;
  /** icon 大小 */
  iconSize?: string | number;
  /** 字体类名 */
  labelClass?: string;
  /** 距离 top  */
  top?: string;
  /** 距离 right */
  right?: string;
  /** 过滤菜单数据 */
  format?: (menu: Menu[], name: string) => Menu[] | Promise<Menu[]>;
  /** boxClass */
  boxClass?: string;
  /** itemClass */
  itemClass?: string;
}

interface IEmits {
  /**
   * @params menu 菜单数据
   * @params isAuth 是否需要登录权限
   */
  (e: 'click', menu: Menu, isAuth?: boolean): void;
}

const props = withDefaults(defineProps<IProps>(), {
  type: '',
  rowNumber: 4,
  cardHeight: 150,
  iconSize: 28,
  iconClass: 'icon-44',
  labelClass: '',
  top: '-2px',
  right: '2em',
  format: undefined,
  name: '',
  boxClass: '',
  itemClass: '',
});

const emit = defineEmits<IEmits>();

const menus = ref<Menu[][]>([]);

const slotMenus = computed(() => (menus.value.length ? menus.value : [[]]));

const rows = ref(4);

const length = ref(0);

const isCard = computed(() => props.type === 'card');

const isCell = computed(() => props.type === 'cell');

const isTab = computed(() => props.type === 'tab');

const itemWidth = computed(() => (isCell.value ? '' : `${(1 / rows.value) * 100}%`));

const iconSize = computed(() => {
  const size = addUnit(props.iconSize);
  return `width:${size};height:${size}`;
});

const cardHeight = computed(() => {
  let h = addUnit(props.cardHeight);

  switch (props.type) {
    case 'tab':
      h = addUnit(40);
      break;
    case 'cell':
      h = addUnit(50);
      break;
    default:
      break;
  }

  return `height:${h};`;
});

const init = async (isInit?: boolean, menu?: Dictionary<Menu[]> | Menu[]) => {
  await getMenu(menu, isInit);
};

const getMenu = async (menu = {}, isInit = false) => {
  try {
    const data = await getMenuItem(menu, isInit);
    const handle = props.format ? await props.format(data, props.name) : data;
    length.value = handle.length;
    setRows(handle.length);

    menus.value = handleMenus(handle);
  } catch (error) {
    exceptionHandler(error);
  }
};

const getMenuItem = async (menu: Dictionary<Menu[]> | Menu[], isInit = false) => {
  if (!props.group) return [];
  let data = (Array.isArray(menu) ? menu : menu[props.group]) || [];

  if (!data.length || isInit) {
    const allData = (await ServiceGetMenu(props.group, isInit)) || {};
    data = allData[props.group];
  }
  return data || [];
};

const handleMenus = (menu: Menu[] = []) => {
  const menusArr: Menu[][] = [];

  menu.forEach((item, index) => {
    const page = isCell.value ? 0 : Math.floor(index / rows.value);

    if (!menusArr[page]) menusArr[page] = [];
    menusArr[page].push(item);
  });

  return menusArr;
};

const setRows = (size: number) => {
  if (props.rowNumber) {
    rows.value = props.rowNumber;
  } else if (!(size % 4) || size > 9) {
    rows.value = 4;
  } else if (!(size % 3)) {
    rows.value = 3;
  } else if (!(size % 2)) {
    rows.value = 2;
  }
};

function checkLimit(item: Menu) {
  const register = new Register();
  if (Boolean(+item.permit)) return !register.RmsUserInfo.isLogin();

  if (!Boolean(+item.permit)) return false;
}

const onMenuRouter = (item: Menu) => {
  emit('click', item, checkLimit(item));
};

defineExpose({ init, checkLimit, menus });
</script>

<template>
  <slot :menus="slotMenus" :props="props">
    <view class="use-menus class" :class="{ 'use-menus-card': isCard }">
      <view
        v-for="(item, i) in menus"
        :key="i"
        class="w-full flex"
        :class="{ 'pt-5': i > 0, 'flex-col': isCell, [replaceJs(boxClass)]: boxClass }">
        <slot name="item" :menus="item" :check-limit="checkLimit">
          <view
            v-for="(menu, index) in item"
            :key="menu.id"
            class="use-menus_item flex flex-col justify-center items-center"
            :style="'width:' + itemWidth + ';'"
            :class="[
              replaceJs(menu.class || ''),
              { disabled: menu.disabled },
              replaceJs(itemClass),
            ]"
            @click="onMenuRouter(menu)">
            <template v-if="isCard">
              <slot name="item-card" :menu="menu" :index="index + i * rows">
                <view class="w-full" :style="cardHeight">
                  <image class="w-full !h-full" mode="widthFix" :src="menu.remoteUrl"></image>
                </view>
              </slot>
            </template>
            <template v-else-if="isTab">
              <slot name="item-tab" :menu="menu" :index="index + i * rows">
                <view class="flex items-center w-full">
                  <view class="flex-1 px-2 py-3 box-border flex items-center" :style="cardHeight">
                    <image class="h-full mr-1" mode="heightFix" :src="menu.remoteUrl"></image>
                    <text>{{ menu.label }}</text>
                  </view>
                  <view
                    :class="{
                      'border-r border-gray-300 h-[40rpx]': index !== rowNumber - 1,
                    }"></view>
                </view>
              </slot>
            </template>
            <template v-else-if="isCell">
              <slot name="item-cell" :menu="menu" :index="index + i * rows">
                <view class="flex items-center w-full">
                  <view
                    class="flex-1 py-[24rpx] box-border flex items-center border-gray-300"
                    :class="[index + 1 !== item.length ? replaceJs('border-b') : 'border-none']"
                    :style="cardHeight">
                    <image class="h-full mr-2.5" mode="heightFix" :src="menu.remoteUrl"></image>
                    <text class="text-[#909399] flex-1">{{ menu.label }}</text>
                    <sc-icon
                      class="text-gray-400"
                      size="13"
                      class-name="text-gray-400"
                      name="i-ion-chevron-forward"></sc-icon>
                  </view>
                </view>
              </slot>
            </template>
            <template v-else>
              <slot name="item-default" :menu="menu" :index="index + i * rows">
                <image
                  :class="props.iconClass"
                  :style="iconSize"
                  mode="aspectFit"
                  :src="menu.remoteUrl"></image>
                <view
                  v-if="menu.badge"
                  class="use-menus_item--badge"
                  :style="{ top: props.top, right: props.right }">
                  <slot name="badge" :menu="menu">
                    <text class="inline-block py-0.5 px-1.5 rounded-2xl bg-red text-white">
                      {{ menu.badge }}
                    </text>
                  </slot>
                </view>
                <text class="text-13 mt-2.5">
                  <text :class="labelClass">{{ menu.label }}</text>
                </text>
              </slot>
            </template>
          </view>

          <view
            v-if="i === menus.length - 1 && length % rows !== 0"
            class="use-menus_item flex flex-col justify-center items-center"
            :style="'width:' + itemWidth + ';'"></view>
        </slot>
      </view>
    </view>
  </slot>
</template>
<!-- #ifdef MP-WEIXIN -->
<script lang="ts">
export default {
  externalClasses: ['class'],
  options: { virtualHost: true },
};
</script>
<!-- #endif -->
<style lang="scss" scoped>
.use-menus {
  @apply flex h-full flex-wrap;
  &-card {
    @apply -my-2.5;
    .use-menus_item {
      @apply px-2.5;
    }
  }
  &_item {
    @apply relative;

    &.disabled {
      filter: grayscale(1);
    }
    &--badge {
      z-index: 9;
      position: absolute;
      // padding: 1px 3px;
      // border-radius: 20rpx;
      font-size: 20rpx;
      text-align: center;
      min-width: 20rpx;
      max-width: 5em;
      transform: translateX(45%);
    }
  }
}
</style>
