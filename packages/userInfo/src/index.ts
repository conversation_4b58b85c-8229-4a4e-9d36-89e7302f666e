import type { ScStorageBase } from '@shencom/utils';
import { FormatIntervalDate } from '@shencom/utils';
import { ServiceImgByIds } from '@rms/service';

// eslint-disable-next-line no-shadow
enum Sex {
  '未知' = 0,
  '男',
  '女',
  '保密',
}
export default class BaseUserInfo {
  private _prefix = '';

  private USER_INFO = `${this._prefix}INFO`;

  private USER_TOKEN = `${this._prefix}TOKEN`;

  private WX_TOKEN = `${this._prefix}WX_TOKEN`;

  private USER_REFRESH_TOKEN = `${this._prefix}REFRESH_TOKEN`;

  private SESSION_KEY = `${this._prefix}SESSION_KEY`;

  private _Storage: ScStorageBase;

  get sex() {
    return Sex;
  }

  constructor(Storage: ScStorageBase) {
    this._Storage = Storage;
  }

  getUserInfo() {
    return this._Storage.getUser<SC.User.Info>(this.USER_INFO);
  }

  private async handleAvatar(avatar: any): Promise<string> {
    if (avatar && !Number.isNaN(+avatar)) {
      const img = await ServiceImgByIds(avatar);
      return img[avatar]?.remoteUrl || avatar;
    }

    return avatar;
  }

  async setUserInfo(data: SC.User.Info) {
    const avatar = await this.handleAvatar(data.avatar);

    this._Storage.setUser(this.USER_INFO, { ...data, avatar });
  }

  removeUserInfo() {
    this._Storage.removeUser(this.USER_INFO);
  }

  getPhone() {
    const user = this.getUserInfo();
    return user && user.phone;
  }

  isPhone() {
    return Boolean(this.getPhone());
  }

  getUid() {
    const user = this.getUserInfo();
    return user && user.uid;
  }

  getOpenId() {
    const user = this.getUserInfo();
    return user && user.openid;
  }

  isOpenId() {
    return Boolean(this.getOpenId());
  }

  getAvatar() {
    const user = this.getUserInfo();
    return user && user.avatar;
  }

  isAvatar() {
    return Boolean(this.getAvatar());
  }

  getNickname() {
    const user = this.getUserInfo();
    return user && user.nickname;
  }

  isNickname() {
    return Boolean(this.getNickname());
  }

  getRealname() {
    const user = this.getUserInfo();
    return user && user.realname;
  }

  isRealname() {
    return Boolean(this.getRealname());
  }

  /** 是否登录 */
  isLogin() {
    const uid = this.getUid();
    const token = this.getToken();
    const phone = this.isPhone();
    return Boolean(token && phone && uid);
  }

  /** 存储token有效时间减去30分钟 */
  setToken(data: string, time: number = Date.now() + 1000 * 60 * 60 * 8) {
    this._Storage.setUser(this.USER_TOKEN, data, FormatIntervalDate(time) - 30);
  }

  getToken() {
    return this._Storage.getUser<string>(this.USER_TOKEN);
  }

  /** 移除 Token */
  removeToken() {
    this._Storage.removeUser(this.USER_TOKEN);
  }

  /** 是否是系统用户 */
  isSysUser() {
    const info = this.getUserInfo();
    return Boolean(info && this.getUid());
  }

  /** 是否是微信用户 */
  isWxUser() {
    return !!this.getUserInfo() && (!this.isPhone() || !this.getUid());
  }

  setSession_key(data: string) {
    this._Storage.setUser(this.SESSION_KEY, data);
  }

  getSession_key() {
    return this._Storage.getUser<string>(this.SESSION_KEY);
  }

  removeSession_key() {
    this._Storage.removeUser(this.SESSION_KEY);
  }

  getWxToken() {
    return this._Storage.getUser<string>(this.WX_TOKEN);
  }

  setWxToken(data: string, time: number = Date.now() + 1000 * 60 * 60 * 8) {
    return this._Storage.setUser(this.WX_TOKEN, data, FormatIntervalDate(time) - 30);
  }

  removeWxToken() {
    this._Storage.removeUser(this.WX_TOKEN);
  }

  /** 存储refreshToken有效时间减去1天 */
  setRefreshToken(refreshToken: SC.User.RefreshToken) {
    const { value, expiration } = refreshToken;
    this._Storage.setUser(this.USER_REFRESH_TOKEN, value, FormatIntervalDate(expiration) - 60 * 24);
  }

  getRefreshToken() {
    return this._Storage.getUser<string>(this.USER_REFRESH_TOKEN);
  }

  hasAvatarAndName() {
    const avatar = this.isAvatar();
    const nickname = this.isNickname();
    const realname = this.isRealname();

    return avatar && (nickname || realname);
  }

  /** 移除 RefreshToken */
  removeRefreshToken() {
    this._Storage.removeUser(this.USER_REFRESH_TOKEN);
  }

  // /** 删除user_*** 的存储 */
  // removeAllUser(key: ) {
  //   this._Storage.removeUser();
  // }

  /** 删除user_*** 的存储 */
  clearAllUser() {
    this._Storage.clearUser();
  }

  /** 设置接口返回系统用户信息 */
  setResponseSysUserInfo(data: SC.User.RootInfo) {
    const info = data.additionalInformation;

    const { phone, uid } = info;

    if (phone && uid) {
      this.setToken('bearer ' + data.value, data.expiration);
    } else {
      this.removeToken();
      this.setWxToken(data.value, data.expiration);
    }

    this.setRefreshToken(data.refreshToken);

    const { sessionKey } = info;
    this.setSession_key(sessionKey!);

    this.setUserInfo(info);

    return info;
  }

  /** 设置 token，refreshToken */
  setRootToken(data: SC.User.TokenRoot, isSystemLogin: boolean) {
    const { value, expiration } = data;

    if (isSystemLogin) {
      this.setToken('bearer ' + value, expiration);
      this.setRefreshToken(data.refreshToken);
    } else {
      this.setWxToken(value, expiration);
    }
  }

  /** 设置 token，refreshToken，userInfo */
  async setRootInfo(data: SC.User.RootInfo) {
    const info = data.additionalInformation;

    const isSystemLogin = !!info.uid;

    this.setRootToken(data, isSystemLogin);

    await this.setUserInfo(info);
  }
}
