{"compilerOptions": {"target": "esnext", "useDefineForClassFields": true, "module": "esnext", "moduleResolution": "node", "strict": true, "jsx": "preserve", "sourceMap": true, "resolveJsonModule": true, "esModuleInterop": true, "lib": ["esnext", "DOM"], "baseUrl": "./src", "types": ["@dcloudio/types", "node", "@rms/types/base"], "paths": {"@rms/router": ["./src"]}}, "include": ["./**/*.vue", "*.ts", "**/*.ts", "./**/*.d.ts"]}