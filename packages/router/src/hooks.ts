import Register from '@rms/register';
import { IsH5 } from '@rms/utils';
import { cloneDeep } from 'lodash-es';
import { getCurrentInstance, nextTick } from 'vue';
import { Dictionary } from '@rms/types';

uni.cacheCtx = uni.cacheCtx || {
  bm: [],
  m: [],
  onShow: [],
  onLoad: [],
  onReady: [],
};

export function useLogin(uid: number, root = false) {
  if (uid && root) {
    if (uni.pageInstance) {
      uni.pageInstance[uid] = getCurrentInstance();
    } else {
      uni.pageInstance = {
        [uid]: getCurrentInstance(),
      };
    }
    // 执行情况 uninitalized | init | reload
    uni.pageInstance[uid].executionSituation = 'uninitalized';
  }

  async function callback() {
    const cache = cloneDeep(uni.pageInstance[`${uid}__cache`]);
    uni.pageInstance[uid].bm = cache.bm;
    uni.pageInstance[uid].m = cache.m;
    uni.pageInstance[uid].onShow = cache.onShow;
    uni.pageInstance[uid].onLoad = cache.onLoad;
    uni.pageInstance[uid].onReady = cache.onReady;

    // #ifdef H5
    const temp = uni.pageInstance[uid];
    uni.pageInstance[uid] = cloneDeep(uni.pageInstance[uid]);
    // #endif

    uni.$emit(`__login_success__${uni.pageInstance[uid].uid}`);

    nextTick(() => {
      [...uni.pageInstance[uid].bm].filter(Boolean).forEach((fn) => {
        fn();
      });

      setTimeout(
        () => {
          const page: any = getCurrentPages()[getCurrentPages().length - 1];
          const options = page.options || page?.$page?.options || {};

          [...uni.pageInstance[uid].onLoad].filter(Boolean).forEach((fn) => {
            fn({ ...options });
          });

          [
            ...uni.pageInstance[uid].m,
            ...uni.pageInstance[uid].onShow,
            ...uni.pageInstance[uid].onReady,
          ]
            .filter(Boolean)
            .forEach((fn) => {
              fn();
            });

          // #ifdef H5
          uni.pageInstance[uid] = temp;
          // #endif
        },
        IsH5 ? 0 : 200,
      );
    });
  }

  function remove(options: NonNullable<Parameters<typeof uni.scLogin>[0]>) {
    const register = new Register();

    const { h5 } = register.RmsAppConfig.login;

    const enableSccode = IsH5 && h5?.sccode;

    const executionSituation = uni.pageInstance[uid].executionSituation;

    const page: Dictionary = getCurrentPages()[getCurrentPages().length - 1];

    const pageOptions = page.options || page?.$page?.options || {};
    const { sccode, uid: userId, appid, content, nonce, signature, timestamp } = pageOptions;

    const isSccodeLogin = enableSccode && sccode && register.RmsUserInfo.getUid() !== userId;
    const isOAuthLogin = !!(appid && content && nonce && signature && timestamp);
    const isExternalLogin = isSccodeLogin || isOAuthLogin;

    if (
      register.RmsUserInfo.isLogin() &&
      !isExternalLogin &&
      executionSituation !== 'reload' &&
      !options.refresh &&
      !options.force
    ) {
      uni.initLoginOptions = null;

      uni.$emit(`__login_success__${uni.pageInstance[uid].uid}`);
      // 执行情况 uninitalized | init | reload
      uni.pageInstance[uid].executionSituation = 'init';
      return;
    }
    if (uni.pageInstance[uid]) {
      console.log('%c [uid]-112', 'font-size:13px; background:#336699; color:#fff;', uid);
      console.log(
        '%c [uni.pageInstance[uid]]-119',
        'font-size:13px; background:#336699; color:#fff;',
        uni.pageInstance,
      );
      // uniapp 和 vue3 生命周期
      uni.cacheCtx.bm = uni.pageInstance[uid].bm || [];
      uni.cacheCtx.m = uni.pageInstance[uid].m || [];
      uni.cacheCtx.onShow = uni.pageInstance[uid].onShow || [];
      uni.cacheCtx.onLoad = uni.pageInstance[uid].onLoad || [];
      uni.cacheCtx.onReady = uni.pageInstance[uid].onReady || [];

      if (!uni.pageInstance[`${uid}__cache`]) {
        uni.pageInstance[`${uid}__cache`] = cloneDeep(uni.cacheCtx);
      }

      uni.pageInstance[uid].bm = [];
      uni.pageInstance[uid].m = [];
      uni.pageInstance[uid].onShow = [];
      uni.pageInstance[uid].onLoad = [];
      uni.pageInstance[uid].onReady = [];
    }

    // 初始化登录
    uni.initLoginOptions = { ...options, force: isExternalLogin || options.force };
    console.log(
      '%c [uni.initLoginOptions]-129',
      'font-size:13px; background:#336699; color:#fff;',
      uni.initLoginOptions,
    );
  }

  return {
    remove,
    callback,
  };
}
