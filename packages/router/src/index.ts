import { GetUrlParam } from '@rms/utils';
import Register from '@rms/register';
import { pathJoin, initRoutes, getCurrentRoute } from './utils';

export * from './utils';
export * from './hooks';

function checkAuth(url: string) {
  const routes = initRoutes();
  const currentPage = getCurrentPages();
  const curRoute = getCurrentPages()[currentPage.length - 1].route as string;

  // 处理相对路径
  if (url.startsWith('.')) {
    url = pathJoin(curRoute, '../' + url);
  }

  const targetRoute = routes.find((r) => r.path.includes(url));
  return !!targetRoute?.meta?.skip;
}

function pageInit(option: any) {
  const { path, query } = option;
  const register = new Register();
  const route = getCurrentRoute(path);

  if (typeof register.RmsRouter?.paegsInit === 'function') {
    register.RmsRouter.paegsInit(option);
  }

  if (route?.redirect === path) {
    new Error('不能配置重定向到自己');
  }

  if (route?.redirect) {
    console.log(`${path} >>> 重定向 >>> ${route.redirect}`);
    register.RmsNavigator.replace('/' + route.redirect, query);
  }
}

interface RouterInfo {
  method: 'push' | 'replace';
  name: 'navigateTo' | 'redirectTo' | 'switchTab';
}

export const routeInterceptor = (option: any) => {
  /** 初次进入页面路由拦截 */
  pageInit(option);

  /** 跳转前拦截 */
  function invoke(this: RouterInfo, res: { url: string }) {
    const query = GetUrlParam(res.url);
    const url = res.url.split('?')[0];

    const isSkip = checkAuth(url);
    const register = new Register();
    const route = getCurrentRoute(url);

    // 处理子模块路由拦截
    if (
      route &&
      typeof register.RmsRouter?.invoke === 'function' &&
      !register.RmsRouter.invoke(url, query, route)
    ) {
      return false;
    }

    if (route?.redirect === url) {
      new Error('不能配置重定向到自己');
    }

    // 处理重定向
    if (route?.redirect) {
      console.log(`${url} >>> 重定向 >>> ${route.redirect}`);

      register.RmsNavigator[this.method]('/' + route.redirect, query);
      return false;
    }

    // 处理登录拦截
    if (!isSkip && !register.RmsUserInfo.isLogin()) {
      uni.scLogin({
        url: res.url,
      });
      return false;
    }

    return true;
  }

  /** 成功跳转拦截 */
  function success(res: { url: string }) {
    console.log('nav-success :>> ', res);

    const register = new Register();
    register.RmsRouter?.success(res.url);
  }

  /** 失败跳转拦截 */
  function fail(error: any) {
    console.log('nav-error :>> ', error);

    const register = new Register();
    register.RmsRouter?.fail(error);
  }

  const redirectTo: RouterInfo = {
    method: 'replace',
    name: 'redirectTo',
  };

  const navigateTo: RouterInfo = {
    method: 'push',
    name: 'navigateTo',
  };

  const switchTab: RouterInfo = {
    method: 'push',
    name: 'switchTab',
  };

  uni.addInterceptor('redirectTo', {
    invoke: invoke.bind(redirectTo),
    fail: fail.bind(redirectTo),
    success: success.bind(redirectTo),
  });
  uni.addInterceptor('navigateTo', {
    invoke: invoke.bind(navigateTo),
    fail: fail.bind(navigateTo),
    success: success.bind(navigateTo),
  });
  uni.addInterceptor('switchTab', {
    invoke: invoke.bind(switchTab),
    fail: fail.bind(switchTab),
    success: success.bind(switchTab),
  });
};
