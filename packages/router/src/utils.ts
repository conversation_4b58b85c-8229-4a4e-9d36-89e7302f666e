import Register from '@rms/register';
import { Dictionary } from '@rms/types';
import { IsDev, IsH5, <PERSON><PERSON><PERSON>xinH5, exceptionHand<PERSON> } from '@rms/utils';
import { nextTick } from 'vue';

export type RouteMeta = Record<string, any> & {
  auth?: boolean;
};

/** 路由信息 */
export type Route = {
  path: string;
  title: string;
  name?: string;
  meta: RouteMeta;
  redirect?: string;
  isCustom?: boolean;
};

/** 子模块路由拦截方法 */
export interface RouterMethods {
  invoke: (url: string, query: Dictionary, route: Route) => boolean;
  success: (url: string) => any;
  fail: (error: any) => any;
  paegsInit: (options: any) => any;
}

export type Style = {
  navigationBarTitleText: string;
  navigationStyle?: string;
};

export type TarBarRoute = {
  iconPath: string;
  pagePath: string;
  selectedIconPath: string;
  text: string;
};

export type MainConfig = {
  path: string;
  style: Style;
  name?: string;
  meta?: RouteMeta;
  redirect?: string;
};

export type SubConfig = {
  root: string;
  pages: MainConfig[];
};

export type TanBarConfig = {
  list: MainConfig[];
};

/**
 * 路径拼接
 * @param args 路径
 * @returns
 */
export function pathJoin(...args: string[]) {
  let endsl = /\/$/,
    bgnsl = /^\//,
    updir = /\/?[^\.\/]*\/\.\./,
    cwdir = /\/\.\//;
  let a = args,
    x = a.length,
    p = a[x - 1].match(endsl) ? '/' : '';

  while (x--) {
    p = (!x || a[x].match(bgnsl) ? '' : '/') + a[x].replace(endsl, '') + p;
  }

  while (updir.test(p)) p = p.replace(updir, '');
  while (cwdir.test(p)) p = p.replace(cwdir, '/');

  return p;
}

export function getMainPages() {
  const register = new Register();
  const pages = register.RmsPages;
  return (pages.pages as MainConfig[]).map((v) => ({
    path: `/${v.path}`,
    title: v?.style?.navigationBarTitleText || '-',
    meta: v.meta || {},
    name: v.name,
    redirect: v?.redirect,
    isCustom: v?.style?.navigationStyle === 'custom',
  }));
}

export function getSubPackages() {
  const register = new Register();
  const pages = register.RmsPages;
  return (pages.subPackages as SubConfig[]).flatMap((v) =>
    v.pages.map((e) => ({
      path: `/${v.root}/${e.path}`,
      title: e?.style?.navigationBarTitleText || '',
      meta: e.meta || {},
      name: e.name,
      redirect: e?.redirect,
      isCustom: e?.style?.navigationStyle === 'custom',
    })),
  );
}

export function getTabbarPackages() {
  const register = new Register();
  const pages = register.RmsPages;
  const routes = initRoutes();
  return (pages?.tabBar?.list as TarBarRoute[])?.map((item) => {
    const targetRoute = routes.find((i) => i.path.includes(item.pagePath));
    return targetRoute;
  });
}

export function isTarBarPage(page: string) {
  return !!getTabbarPackages()?.find((item) => item?.path.includes(page));
}

/** 合并路由对象 */
export function initRoutes() {
  const mainPages = getMainPages();
  const subPackages = getSubPackages();
  const routes: Route[] = mainPages.concat(subPackages);

  return routes;
}

function handlePath(url = '') {
  return url.startsWith('/') ? url : '/' + url;
}

/** 根据 path 获取对应的路由对象 */
export function getCurrentRoute(url: string) {
  const routes = initRoutes();
  return routes.find((r) => handlePath(url).startsWith(handlePath(r.path)));
}

export function getRouteSkip(url: string) {
  const currentRoute = getCurrentRoute(url);
  return !!currentRoute?.meta.skip || url.includes('login/index');
}

export function getCurrentPagesRoute() {
  const currentPage = getCurrentPages();

  if (!currentPage?.length) return '';

  return currentPage[currentPage.length - 1].route || '';
}

export async function callMainHooks() {
  try {
    await nextTick();

    const register = new Register();

    if (!register.RmsUserInfo.isLogin()) return;

    const page: any = getCurrentPages()[getCurrentPages().length - 1];
    if (page) {
      // uniapp 和 vue3 生命周期
      const ctx = page?.$ || page?.$vm.$ || {};
      const onLoad = ctx.onLoad || [];
      // const onShow = ctx.onShow || [];
      const onBeforeMount = ctx.bm || [];
      const onReady = IsH5 ? [] : ctx.onReady || [];
      const onMounted = ctx.m || [];
      // const onActivated = ctx.a || [];

      [...onLoad].filter(Boolean).forEach((fn) => {
        fn({ ...page.options, isLogin: true });
      });

      [...onBeforeMount, ...onReady, ...onMounted].filter(Boolean).forEach((fn) => {
        fn(true);
      });
    }
  } catch (error) {
    exceptionHandler(error);
  }
}

export function isNeedSilentLogin() {
  const register = new Register();
  return IsDev || !IsH5 || !uni.silentLogin || register.RmsUserInfo.isLogin();
}

export function isSilentLogin() {
  return !IsDev && IsWeixinH5 && uni.silentLogin;
}

export function handleSameUser(uid: string) {
  if (!uid) return;

  const register = new Register();
  const targetUid = register.RmsUserInfo.getUid();

  if (targetUid !== uid) {
    register.RmsStorage.clear();
  }
}

export function checkAuth(url: string) {
  if (!url) return false;

  const routes = initRoutes();
  const currentPage = getCurrentPages();
  const curRoute = getCurrentPages()[currentPage.length - 1].route as string;

  // 处理相对路径
  if (url.startsWith('.')) {
    url = pathJoin(curRoute, '../' + url);
  }

  const targetRoute = routes.find((r) => r.path.includes(url));
  return !!targetRoute?.meta?.skip;
}
