import { Http } from '../@types';

interface ReqRegionList {
  /* 根节点id
   * 默认为2 返回深圳市数据 不携带深圳市
   * 如果需要广东省可传 1
   */
  rootId?: number;
  /* 深度
   * 不传root 或 root 为 0 时
   * deep 为 0  不返回任何数据
   * deep 为 1  返回当前根节点下一级的数据 以此类推
   * root为 1 时
   * deep 为 0  只返回当前根节点数据
   * deep 为 1  返回当前根节点下一级的数据 以此类推
   */
  deep?: number;
  /* 是否携带根节点：0 不携带、1 携带
   * 默认为0 不携带
   */
  root?: 0 | 1;
}

export interface ResRegionList {
  title: string;
  id: string;
  // 子级下的父级id
  _id?: string;
  pId: string;
  level?: 1 | 2 | 3 | 4;
  children?: ResRegionList[];
}

/**
 * 区域树形接口 - 无权限
 * @return {PromiseRequest<SC.OssSignature>}
 */
export const BaseRegionList = (http: Http, url: string) => {
  const api = `${url}/service-guanclassify-recycle/common/basic/data/region/index`;
  return (body?: ReqRegionList, header?: Record<string, any>) => {
    return http.post<ResRegionList[]>(api, body || {}, {
      headers: { Authorization: null, ...header },
    });
  };
};

interface ReqRegionGuangzhouTree {
  level?: number;
  pId?: string;
  returnAll?: boolean;
  cityTop?: boolean;
}

/**
 * 广州专用区域树形接口 - 无权限
 * @return {PromiseRequest<SC.OssSignature>}
 */
export const BaseRegionGuangzhouList = (http: Http, url: string) => {
  const api = `${url}/service-suiclassify-base/sui/base/common/region/tree`;
  return (body?: ReqRegionGuangzhouTree, header?: Record<string, any>) => {
    return http.post<ResRegionList[]>(api, body || {}, {
      headers: { Authorization: null, ...header },
    });
  };
};

interface BaseRegionNationwideListReq {
  rootId?: string;
  deep?: number;
}

/**
 * 全国区域 - 无权限
 * @return {PromiseRequest<SC.OssSignature>}
 */
export const BaseRegionNationwideList = (http: Http, url: string) => {
  const api = `${url}/service-volunteer-srv/common/basic/data/region/index`;
  return (body?: BaseRegionNationwideListReq, header?: Record<string, any>) => {
    return http.post<ResRegionList[]>(api, body || {}, {
      headers: { Authorization: null, ...header },
    });
  };
};

interface ReqGetNationalRegionLinkage {
  id?: string;
  level?: number;
}
export interface ResGetNationalRegionLinkage {
  areaCode: string;
  cityCode: string;
  id: string;
  lat: number;
  level: number;
  lng: number;
  mergerName: string;
  name: string;
  pId: string;
  parentCode: string;
  pinyin: string;
  shortName: string;
  zipCode: string;
}

export const BaseNationalRegionLinkage = (http: Http, url: string) => {
  const api = `${url}/service-inspection/national/region/linkage`;
  return async (body?: ReqGetNationalRegionLinkage, headers?: Record<string, any>) => {
    return (
      await http.post<ResGetNationalRegionLinkage[]>(api, body || {}, {
        headers,
      })
    ).data;
  };
};
