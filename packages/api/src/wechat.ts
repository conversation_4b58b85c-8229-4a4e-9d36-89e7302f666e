import Register from '@rms/register';
import { Http } from '../@types';

/** 获取微信 jssdk config */
export function BaseGetWechatJsConfig(http: Http, url: string) {
  return async () => {
    const register = new Register();
    const scid = register.RmsAppConfig.base.scid;
    const api = `${url}/service-uaa/wechat/jsconfig`;

    const { data } = await http.get<wx.ConfigOptions>(api, { scid });
    return data;
  };
}
