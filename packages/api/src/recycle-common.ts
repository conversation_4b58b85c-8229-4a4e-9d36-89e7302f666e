import { Dictionary, IndexBodyInterface, IndexInterface } from '@rms/types';
import { IsPro, exceptionHandler } from '@rms/utils';
import { Http } from '@types';

export function BaseSchoolListIndex(http: Http, url: string) {
  return async (body: IndexBodyInterface & { keywords: string }) => {
    const api = `${url}${IsPro ? '/rms-v1' : '/v1'}/rms/api/common/school`;

    try {
      const { data } = await http.post<IndexInterface<any>>(api, body);

      return data;
    } catch (error) {
      exceptionHandler(error);
      throw Error('列表获取失败');
    }
  };
}

export interface CommunityList {
  childType: string;
  communityName: string;
  districtName: string;
  id: string;
  name: string;
  placeTypeName: string;
  regionCid: string;
  regionId: string;
  regionPid: string;
  streetName: string;
  type: string;
  address: string;
}

export function BaseCommunityListIndex(http: Http, url: string) {
  return async (body: IndexBodyInterface & Dictionary) => {
    const api = `${url}/service-suiclassify-base/sui/base/common/place/index`;

    try {
      const { data } = await http.post<IndexInterface<CommunityList>>(api, {
        type: 'RESIDENCE_ZONE',
        ...body,
      });
      return data;
    } catch (error) {
      exceptionHandler(error);
      throw Error('列表获取失败');
    }
  };
}

interface ResLocation {
  regeocode: {
    addressComponent: {
      city: string;
      district: string;
      province: string;
      township: string;
      neighborhood: {
        name: string;
      };
      building: {
        name: string;
      };
      streetNumber: {
        direction: string;
        distance: string;
        location: string;
        number: string;
        street: string;
      };
    };
    formatted_address: string;
  };
}

export function BaseGetLocation(http: Http, geoKey: string) {
  return async (lng: number, lat: number) => {
    const api = 'https://restapi.amap.com/v3/geocode/regeo';

    try {
      const { data } = await http.request<ResLocation>({
        url: api,
        params: {
          location: `${lng},${lat}`,
          key: geoKey,
        },

        headers: {
          Authorization: '',
        },
      });

      return data;
    } catch (error) {
      exceptionHandler(error);
      throw error;
      // return error;
    }
  };
}
