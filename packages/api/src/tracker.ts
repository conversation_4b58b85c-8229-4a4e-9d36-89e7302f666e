import { IsDev } from '@rms/utils';
import { Http } from '../@types';

export enum OperateType {
  /** 新增 */
  'insert',
  /** 更新 */
  'update',
  /** 选择 */
  'select',
  /** 删除 */
  'delete',
  /** 其他 */
  'other',
  /** 访问 */
  'visit',
  /** 预约 */
  'appoint',
}

export type ReqTracking = {
  /**
   * 操作类型
   * @param insert 新增
   * @param update 更新
   * @param select 选择
   * @param delete 删除
   * @param other 其他
   * @param visit 访问
   * @param appoint 预约
   */
  type: keyof typeof OperateType;
  /** 访问来源 */
  bizSource: string;
  /** 应用模块标识符 */
  moduleCode: string;
};

export const BaseEventTrack = (http: Http, url: string) => {
  const api = `${IsDev ? '/java' : url}/service-log/opslog/log.gif`;

  return (body: ReqTracking, headers?: Record<string, any>) => {
    // 使用 xhr 请求进行埋点
    return http.get(api, body || {}, headers);

    // 使用 gif 图片请求进行埋点
    // return new Promise<void>((resolve, reject) => {
    //   const params = Object.entries(body).reduce((pre, [key, value], index) => {
    //     if (value) pre += `${index ? '&' : '?'}${key}=${value}`;
    //     return pre;
    //   }, '');
    //   // TODO: 参数添加 scid、token
    //   const src = api + params;
    //   console.log('src: ', src);
    //   const gif = new Image();
    //   gif.src = src;
    //   gif.onload = () => {
    //     resolve();
    //   };
    //   gif.onerror = (err) => {
    //     reject(err);
    //   };
    // });
  };
};
