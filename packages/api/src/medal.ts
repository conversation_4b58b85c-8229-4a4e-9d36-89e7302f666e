import { exceptionHandler } from '@rms/utils';
import { Http } from '@types';

export interface ResMedalLookData {
  createdAt: number;
  id: string;
  isLook: number;
  medalBgImg: string;
  medalIcon: string;
  medalId: string;
  medalIssueDate: string;
  medalName: string;
  medalPopupBgImg: string;
  phone: string;
  realname: string;
  updatedAt: number;
  userId: string;
}

export function BaseGetMedalLook(http: Http, url: string) {
  return async (body: { business: string }) => {
    try {
      const api = `${url}/service-social-supervisor/fn/rmsv3/user/medal/look`;
      const { data } = await http.post<ResMedalLookData>(api, body);
      return data;
    } catch (error) {
      exceptionHandler(error);
      throw error;
    }
  };
}

interface ResMedalShowData {
  bgImg: string;
  businessList: [{ appName: string; business: string }, {}];
  createdAt: number;
  icon: string;
  iconSvg: string;
  id: string;
  isIntroduce: number;
  issueDate: string;
  issueEndDate: number;
  issueStartDate: number;
  issueUnit: string;
  name: string;
  popupBgImg: string;
  selectionRule: string;
  updatedAt: number;
}

export function BaseGetMedalShow(http: Http, url: string) {
  return async (body: { id: string }) => {
    try {
      const api = `${url}/service-social-supervisor/fn/rmsv3/medal/show`;
      const { data } = await http.post<ResMedalShowData>(api, body);
      return data;
    } catch (error) {
      exceptionHandler(error);
      throw error;
    }
  };
}
