import { Http, FunParameters } from '../@types';
import {
  BaseNationalRegionLinkage,
  BaseRegionGuangzhouList,
  BaseRegionList,
  BaseRegionNationwideList,
} from './region';
import { IsDev, IsWeixin } from '@rms/utils';
import type { DeepParameters } from '@shencom/typing';
import {
  BaseResourceBanner,
  BaseResourceFile,
  BaseResourceMenu,
  BaseResourceMenuJava,
  BaseResourceBannerJava,
} from './resource';
import { BaseGetWxCode, BaseGetWxInfo } from './login';
import { BaseGetMedalLook, BaseGetMedalShow } from './medal';
import { BaseEventTrack } from './tracker';
import type { ResGetNationalRegionLinkage } from './region';
import { BaseGetWechatJsConfig } from './wechat';
export { ResGetNationalRegionLinkage };

let _api = '';
let _http: Http;
export function httpInit(http: Http, url: string) {
  _http = http;
  _api = url;
}

function checkParam(cb?: any) {
  if ((IsDev || IsWeixin) && !_api) throw new Error('未初始化, api 为空');
  if (!_http) throw new Error('未初始化, http 为空');
  if (typeof cb === 'function') cb();
}

// -------------------------------------
/**
 *  菜单
 * @param
 * @returns
 */
export function ApiResourceMenu(...args: DeepParameters<typeof BaseResourceMenu>) {
  checkParam();
  return BaseResourceMenu(_http, _api)(...args);
}

/**
 *  菜单java
 * @param
 * @returns
 */
export function ApiResourceMenuJava(...args: DeepParameters<typeof BaseResourceMenuJava>) {
  checkParam();
  return BaseResourceMenuJava(_http, _api)(...args);
}

/**
 *  轮播图
 * @param
 * @returns
 */
export function ApiResourceBanner(...args: DeepParameters<typeof BaseResourceBanner>) {
  checkParam();
  return BaseResourceBanner(_http, _api)(...args);
}

/**
 *  轮播图java
 * @param
 * @returns
 */
export function ApiResourceBannerJava(...args: DeepParameters<typeof BaseResourceBannerJava>) {
  checkParam();
  return BaseResourceBannerJava(_http, _api)(...args);
}

/** 获取微信静默授权 code */
export function ApiGetWxCode(...args: FunParameters<typeof BaseGetWxCode>) {
  checkParam();
  return BaseGetWxCode(_http, _api)(...args);
}

/** 微信静默授权 */
export function ApiGetWxInfo(...args: FunParameters<typeof BaseGetWxInfo>) {
  checkParam();
  return BaseGetWxInfo(_http, _api)(...args);
}

/**
 *  资源
 * @param
 * @returns
 */
export function ApiResourceFile(...args: DeepParameters<typeof BaseResourceFile>) {
  checkParam();
  return BaseResourceFile(_http, _api)(...args);
}

/** 地区 */
export function ApiRegionList(...args: FunParameters<typeof BaseRegionList>) {
  checkParam();
  return BaseRegionList(_http, _api)(...args);
}

/** 广州区域接口 */
export function ApiRegionGuangzhouList(...args: FunParameters<typeof BaseRegionGuangzhouList>) {
  checkParam();
  return BaseRegionGuangzhouList(_http, _api)(...args);
}

/** 广州区域接口 */
export function ApiRegionNationwideList(...args: FunParameters<typeof BaseRegionNationwideList>) {
  checkParam();
  return BaseRegionNationwideList(_http, _api)(...args);
}

/** 获取未查看勋章 */
export function ApiGetMedalLook(...args: FunParameters<typeof BaseGetMedalLook>) {
  checkParam();
  return BaseGetMedalLook(_http, _api)(...args);
}

/** 查看勋章 */
export function ApiGetMedalShow(...args: FunParameters<typeof BaseGetMedalShow>) {
  checkParam();
  return BaseGetMedalShow(_http, _api)(...args);
}

/** 事件埋点 */
export function ApiEventTrack(...args: FunParameters<typeof BaseEventTrack>) {
  checkParam();
  return BaseEventTrack(_http, _api)(...args);
}

export function ApiGetNationalRegionLinkage(
  ...args: FunParameters<typeof BaseNationalRegionLinkage>
) {
  checkParam();
  return BaseNationalRegionLinkage(_http, _api)(...args);
}

/** 获取微信 jssdk config */
export function ApiGetWechatJsConfig(...args: FunParameters<typeof BaseGetWechatJsConfig>) {
  checkParam();
  return BaseGetWechatJsConfig(_http, _api)(...args);
}
