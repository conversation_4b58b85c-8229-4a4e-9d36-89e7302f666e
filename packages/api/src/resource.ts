import { Banner, IndexBodyInterface, IndexInterface, Menu } from '@rms/types';
import { Http } from '../@types';
import { IsPro } from '@rms/utils';

interface ReqResourceMenu {
  group: string[];
  size: number;
}

interface ReqResourceFile extends IndexBodyInterface {
  sorts?: SC.API.IndexSorts;
  query?: SC.API.IndexQuery;
}

interface IResource {
  createdAt: number;
  id: string;
  name: string;
  type: number;
  updatedAt: number;
  url: string;
  menuNames?: string;
}

export const BaseResourceMenu = (http: Http, url: string) => {
  const api = `${url}/${IsPro ? 'recycle-manage-system' : 'v2'}/rms/api/common/icon/menu/index`;

  return (body?: ReqResourceMenu, headers?: Record<string, any>) => {
    return http.post<Menu[]>(api, body || {}, {
      headers,
    });
  };
};

export const BaseResourceMenuJava = (http: Http, url: string) => {
  const api = `${url}/service-guanclassify-recycle/common/icon/menu/index`;

  return (body?: ReqResourceMenu, headers?: Record<string, any>) => {
    return http.post<Menu[]>(api, body || {}, {
      headers,
    });
  };
};

export const BaseResourceBanner = (http: Http, url: string) => {
  const api = `${url}/${IsPro ? 'recycle-manage-system' : 'v2'}/rms/api/common/banner/index`;

  return (body?: ReqResourceMenu, headers?: Record<string, any>) => {
    return http.post<Banner[]>(api, body || {}, {
      headers,
    });
  };
};

export const BaseResourceBannerJava = (http: Http, url: string) => {
  const api = `${url}/service-guanclassify-recycle/common/banner/index`;

  return (body?: ReqResourceMenu, headers?: Record<string, any>) => {
    return http.post<Banner[]>(api, body || {}, {
      headers,
    });
  };
};

export const BaseResourceFile = (http: Http, url: string) => {
  const api = `${url}/service-rms/fn/rmsv3/resource/index`;

  return (body?: ReqResourceFile, headers?: Record<string, any>) => {
    return http.post<IndexInterface<IResource>>(api, body || {}, {
      headers,
    });
  };
};
