import { GetUrl<PERSON>aram, ValidateURL, exceptionHandler } from '@rms/utils';
import { Http, Unfurl } from '../@types';
import Register from '@rms/register';

// 微信静默授权
type ResRefreshToken = {
  user: SC.User.Info;
  sctoken: string;
  expiration: number;
  refreshToken: SC.User.RefreshToken;
};
function handleInfo(data: ResRefreshToken): SC.User.RootInfo {
  return {
    additionalInformation: data.user,
    expiration: data.expiration,
    refreshToken: data.refreshToken,
    value: data.sctoken,
  };
}

export function BaseGetWxInfo(http: Http, url: string) {
  return async (code: string) => {
    try {
      const api = `${url}/service-uaa/csm/wechat/auth`;
      const register = new Register();
      const { data } = await http.get<ResRefreshToken>(api, {
        code,
        scid: register.RmsAppConfig.base.scid,
      });

      return handleInfo(data);
    } catch (error) {
      exceptionHand<PERSON>(error);
      throw error;
    }
  };
}

// 获取微信code
export function BaseGetWxCode(http: Http, url: string) {
  return async () => {
    const api = `${url}/service-uaa/sc/wechat/auth`;
    const register = new Register();

    try {
      await http.get(
        api,
        { scid: register.RmsAppConfig.base.scid },
        { headers: { Authorization: null } },
      );
    } catch (error: any) {
      const { errcode, data } = error.data;
      if (errcode === '500013') {
        if (ValidateURL(data.redirect_url)) {
          setTimeout(() => {
            const redirect_url = GetUrlParam(data.redirect_url).redirect_uri;
            const path = window.location.href;

            console.log(
              '%c uni.initLoginOptions: ',
              'color:#40b883;font-weight:bold',
              uni.initLoginOptions,
            );
            const { url = '' } = uni.initLoginOptions || {};
            const redirect = url ? path.replace(window.location.hash, '#' + url) : path;
            console.log('%c redirect: ', 'color:#40b883;font-weight:bold', redirect);

            window.location.replace(
              data.redirect_url.replace(redirect_url, encodeURIComponent(redirect)),
            );
          });
        }
      }
    }
  };
}
